import os
import django
import traceback
import json
import random
import string

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kelem_sms.settings')
django.setup()

from school_registration.models import SchoolRegistrationRequest
from django.utils.text import slugify
from tenants.models import School, Domain
from django_tenants.utils import schema_context
from django.contrib.auth import get_user_model
from django.db import connection

def generate_random_string(length=8):
    """Generate a random string of fixed length."""
    letters = string.ascii_lowercase
    return ''.join(random.choice(letters) for i in range(length))

def clean_up_test_data(schema_name):
    """Clean up test data."""
    print(f"Cleaning up test data for schema: {schema_name}")
    
    # Delete the school if it exists
    try:
        school = School.objects.filter(schema_name=schema_name).first()
        if school:
            print(f"Deleting school: {school.name}")
            school.delete()
    except Exception as e:
        print(f"Error deleting school: {str(e)}")
    
    # Delete the registration request if it exists
    try:
        registration = SchoolRegistrationRequest.objects.filter(schema_name=schema_name).first()
        if registration:
            print(f"Deleting registration request: {registration.name}")
            registration.delete()
    except Exception as e:
        print(f"Error deleting registration request: {str(e)}")
    
    # Drop the schema if it exists
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT schema_name FROM information_schema.schemata WHERE schema_name = %s", [schema_name])
            if cursor.fetchone():
                print(f"Dropping schema: {schema_name}")
                cursor.execute(f"DROP SCHEMA {schema_name} CASCADE")
    except Exception as e:
        print(f"Error dropping schema: {str(e)}")

def create_test_school():
    """Create a test school and check if admin user is created."""
    # Generate a unique schema name
    random_suffix = generate_random_string()
    schema_name = f"test_school_{random_suffix}"
    school_name = f"Test School {random_suffix.upper()}"
    
    print(f"Creating test school with schema: {schema_name}")
    
    # Clean up any existing test data
    clean_up_test_data(schema_name)
    
    # Create a school registration request
    try:
        registration = SchoolRegistrationRequest(
            name=school_name,
            slug=schema_name,
            address="123 Test Street",
            city="Test City",
            state="Test State",
            country="Ethiopia",
            postal_code="12345",
            contact_person="Test Admin",
            contact_email=f"testadmin_{random_suffix}@example.com",
            contact_phone="+251123456789",
            website="https://testschool.com",
            established_year=2020,
            school_type="Primary",
            student_capacity=500,
            description="A test school for testing admin user creation",
            admin_email=f"testadmin_{random_suffix}@example.com",
            admin_password="testpassword123",
            schema_name=schema_name,
            domain=f"{schema_name}.kelemsms.com"
        )
        registration.save()
        print(f"Created school registration request with ID: {registration.id}")
        
        # Import the SchoolRegistrationViewSet
        from school_registration.views import SchoolRegistrationViewSet
        
        # Create an instance of the viewset
        viewset = SchoolRegistrationViewSet()
        
        # Call the auto_approve_registration method
        tenant_info = viewset.auto_approve_registration(registration)
        print(f"Auto-approved school registration. Tenant info: {tenant_info}")
        
        # Check if the school was created
        school = School.objects.filter(schema_name=schema_name).first()
        if school:
            print(f"School created with ID: {school.id}")
            
            # Check if admin user was created
            with schema_context(schema_name):
                User = get_user_model()
                admin_users = User.objects.filter(user_type='admin', is_superuser=True)
                print(f"Found {admin_users.count()} admin users in schema {schema_name}")
                
                for user in admin_users:
                    print(f"Admin user: {user.email}, ID: {user.id}")
                
                if admin_users.count() == 0:
                    print("ERROR: No admin users were created!")
                    return False
                else:
                    print("SUCCESS: Admin user was created successfully!")
                    return True
        else:
            print("ERROR: School was not created!")
            return False
    except Exception as e:
        print(f"Error creating test school: {str(e)}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== Testing Admin User Creation ===")
    success = create_test_school()
    
    if success:
        print("\n=== TEST PASSED: Admin user was created successfully! ===")
    else:
        print("\n=== TEST FAILED: Admin user was not created! ===")
