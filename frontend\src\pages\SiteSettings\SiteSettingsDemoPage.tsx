import React from 'react'
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Chip,
  Button,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
} from '@mui/material'
import {
  Settings as SettingsIcon,
  Palette as PaletteIcon,
  Home as HomeIcon,
  Build as BuildIcon,
  CheckCircle as CheckIcon,
  Star as StarIcon,
  Lightbulb as LightbulbIcon,
  Speed as SpeedIcon,
  Security as SecurityIcon,
  Devices as DevicesIcon,
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'

const SiteSettingsDemoPage: React.FC = () => {
  const navigate = useNavigate()

  const features = [
    {
      icon: <SettingsIcon />,
      title: 'General Settings',
      description: 'Configure site information, contact details, and SEO settings',
      status: 'Available',
    },
    {
      icon: <PaletteIcon />,
      title: 'Branding & Theme',
      description: 'Customize colors, fonts, logos, and visual design elements',
      status: 'Available',
    },
    {
      icon: <HomeIcon />,
      title: 'Hero Section',
      description: 'Manage homepage content and call-to-action buttons',
      status: 'Available',
    },
    {
      icon: <BuildIcon />,
      title: 'Maintenance Mode',
      description: 'Control site availability and maintenance messaging',
      status: 'Available',
    },
  ]

  const benefits = [
    {
      icon: <SpeedIcon />,
      title: 'Real-time Updates',
      description: 'Changes apply immediately across the platform',
    },
    {
      icon: <SecurityIcon />,
      title: 'Secure Configuration',
      description: 'Admin-only access with proper validation',
    },
    {
      icon: <DevicesIcon />,
      title: 'Responsive Design',
      description: 'Optimized for all devices and screen sizes',
    },
    {
      icon: <LightbulbIcon />,
      title: 'User-Friendly',
      description: 'Intuitive interface with helpful guidance',
    },
  ]

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4, textAlign: 'center' }}>
        <Typography variant="h3" component="h1" sx={{ fontWeight: 700, mb: 2 }}>
          Site Settings Management
        </Typography>
        <Typography variant="h6" color="text.secondary" sx={{ mb: 3, maxWidth: 800, mx: 'auto' }}>
          Complete control over your platform's appearance, content, and functionality through an intuitive dashboard interface
        </Typography>
        <Button
          variant="contained"
          size="large"
          onClick={() => navigate('/dashboard/site-settings')}
          sx={{ px: 4, py: 1.5 }}
        >
          Open Site Settings
        </Button>
      </Box>

      {/* Current Configuration */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12}>
          <Alert severity="info" sx={{ mb: 3 }}>
            <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
              Current Configuration
            </Typography>
            <Typography variant="body2">
              Site Name: <strong>Kelem SMS</strong> • 
              Primary Color: <strong>#2E7D32</strong> • 
              Theme: <strong>Ethiopian-Inspired</strong>
            </Typography>
          </Alert>
        </Grid>
      </Grid>

      {/* Features Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12}>
          <Typography variant="h4" sx={{ fontWeight: 600, mb: 3, textAlign: 'center' }}>
            Available Features
          </Typography>
        </Grid>
        {features.map((feature, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card sx={{ height: '100%', '&:hover': { boxShadow: 3 } }}>
              <CardContent sx={{ textAlign: 'center', p: 3 }}>
                <Box sx={{ mb: 2, color: 'primary.main' }}>
                  {React.cloneElement(feature.icon, { sx: { fontSize: 48 } })}
                </Box>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                  {feature.title}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {feature.description}
                </Typography>
                <Chip
                  label={feature.status}
                  color="success"
                  size="small"
                  icon={<CheckIcon />}
                />
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Benefits */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Key Benefits"
              subheader="Why use the Site Settings dashboard"
            />
            <CardContent>
              <List>
                {benefits.map((benefit, index) => (
                  <React.Fragment key={index}>
                    <ListItem>
                      <ListItemIcon sx={{ color: 'primary.main' }}>
                        {benefit.icon}
                      </ListItemIcon>
                      <ListItemText
                        primary={benefit.title}
                        secondary={benefit.description}
                        primaryTypographyProps={{ fontWeight: 600 }}
                      />
                    </ListItem>
                    {index < benefits.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="What You Can Customize"
              subheader="Complete control over your platform"
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                    🎨 Visual Branding
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Colors, fonts, logos, border radius, shadows, and custom CSS
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                    📝 Content Management
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Site information, hero section, contact details, and SEO settings
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                    ⚙️ Feature Controls
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Registration, trials, contact forms, maintenance mode, and more
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                    📱 Responsive Design
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    All customizations work seamlessly across desktop, tablet, and mobile
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Call to Action */}
      <Paper sx={{ p: 4, textAlign: 'center', background: 'linear-gradient(135deg, #2E7D32 0%, #4CAF50 50%, #FFC107 100%)', color: 'white' }}>
        <Typography variant="h5" sx={{ fontWeight: 700, mb: 2 }}>
          Ready to Customize Your Platform?
        </Typography>
        <Typography variant="body1" sx={{ mb: 3, opacity: 0.9 }}>
          Access the comprehensive site settings dashboard and make your platform truly yours
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
          <Button
            variant="contained"
            size="large"
            onClick={() => navigate('/dashboard/site-settings')}
            sx={{
              bgcolor: 'white',
              color: 'primary.main',
              '&:hover': { bgcolor: 'grey.100' },
              px: 4,
              py: 1.5,
            }}
          >
            Open Site Settings
          </Button>
          <Button
            variant="outlined"
            size="large"
            onClick={() => navigate('/dashboard')}
            sx={{
              borderColor: 'white',
              color: 'white',
              '&:hover': { borderColor: 'white', bgcolor: 'rgba(255,255,255,0.1)' },
              px: 4,
              py: 1.5,
            }}
          >
            Back to Dashboard
          </Button>
        </Box>
      </Paper>
    </Container>
  )
}

export default SiteSettingsDemoPage
