import axios from 'axios'

const API_BASE_URL = 'http://localhost:8001/api/system'

// Create axios instance for system config API calls
const systemConfigApi = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Add auth token to requests
systemConfigApi.interceptors.request.use((config) => {
  const token = localStorage.getItem('token')
  if (token) {
    config.headers.Authorization = `Token ${token}`
  }
  return config
})

// Response interceptor for error handling
systemConfigApi.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('System Config API Error:', error)
    return Promise.reject(error)
  }
)

// Types
export interface SystemMetric {
  name: string
  value: number
  unit: string
  status: 'good' | 'warning' | 'critical'
  threshold: number
  timestamp: string
}

export interface SystemService {
  name: string
  status: 'running' | 'stopped' | 'error'
  port: number
  uptime: string
  memory: string
  cpu: string
  pid?: number
  restart_count: number
  last_restart: string
}

export interface SystemLog {
  id: string
  timestamp: string
  level: 'info' | 'warning' | 'error' | 'debug'
  service: string
  message: string
  details?: string
}

export interface SystemConfig {
  max_memory_usage: number
  max_cpu_usage: number
  max_disk_usage: number
  log_retention_days: number
  backup_retention_days: number
  enable_auto_backup: boolean
  enable_monitoring: boolean
  enable_alerts: boolean
  alert_email: string
  maintenance_window: string
  time_zone: string
  max_concurrent_users: number
  session_timeout: number
  enable_debug_mode: boolean
}

export interface SystemStats {
  total_users: number
  active_sessions: number
  total_requests_today: number
  average_response_time: number
  error_rate: number
  uptime: string
  last_backup: string
  disk_space_used: number
  disk_space_total: number
}

export interface BackupInfo {
  id: string
  filename: string
  size: number
  created_at: string
  type: 'manual' | 'automatic'
  status: 'completed' | 'in_progress' | 'failed'
}

// API Functions
export const getSystemMetrics = async (): Promise<SystemMetric[]> => {
  try {
    const response = await systemConfigApi.get('/metrics/')
    return response.data
  } catch (error) {
    console.error('Error fetching system metrics:', error)
    throw error
  }
}

export const getSystemServices = async (): Promise<SystemService[]> => {
  try {
    const response = await systemConfigApi.get('/services/')
    return response.data
  } catch (error) {
    console.error('Error fetching system services:', error)
    throw error
  }
}

export const getSystemLogs = async (limit: number = 50, level?: string): Promise<SystemLog[]> => {
  try {
    const params = new URLSearchParams({ limit: limit.toString() })
    if (level) params.append('level', level)
    
    const response = await systemConfigApi.get(`/logs/?${params}`)
    return response.data.results || response.data
  } catch (error) {
    console.error('Error fetching system logs:', error)
    throw error
  }
}

export const getSystemConfig = async (): Promise<SystemConfig> => {
  try {
    const response = await systemConfigApi.get('/config/')
    return response.data
  } catch (error) {
    console.error('Error fetching system config:', error)
    throw error
  }
}

export const updateSystemConfig = async (config: Partial<SystemConfig>): Promise<SystemConfig> => {
  try {
    const response = await systemConfigApi.patch('/config/', config)
    return response.data
  } catch (error) {
    console.error('Error updating system config:', error)
    throw error
  }
}

export const getSystemStats = async (): Promise<SystemStats> => {
  try {
    const response = await systemConfigApi.get('/stats/')
    return response.data
  } catch (error) {
    console.error('Error fetching system stats:', error)
    throw error
  }
}

export const controlService = async (serviceName: string, action: 'start' | 'stop' | 'restart'): Promise<SystemService> => {
  try {
    const response = await systemConfigApi.post(`/services/${serviceName}/${action}/`)
    return response.data
  } catch (error) {
    console.error(`Error ${action}ing service ${serviceName}:`, error)
    throw error
  }
}

export const createBackup = async (type: 'full' | 'database' | 'files' = 'full'): Promise<BackupInfo> => {
  try {
    const response = await systemConfigApi.post('/backup/', { type })
    return response.data
  } catch (error) {
    console.error('Error creating backup:', error)
    throw error
  }
}

export const getBackups = async (): Promise<BackupInfo[]> => {
  try {
    const response = await systemConfigApi.get('/backups/')
    return response.data.results || response.data
  } catch (error) {
    console.error('Error fetching backups:', error)
    throw error
  }
}

export const restoreBackup = async (backupId: string): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await systemConfigApi.post(`/backups/${backupId}/restore/`)
    return response.data
  } catch (error) {
    console.error('Error restoring backup:', error)
    throw error
  }
}

export const deleteBackup = async (backupId: string): Promise<void> => {
  try {
    await systemConfigApi.delete(`/backups/${backupId}/`)
  } catch (error) {
    console.error('Error deleting backup:', error)
    throw error
  }
}

export const clearCache = async (cacheType: 'all' | 'redis' | 'database' = 'all'): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await systemConfigApi.post('/cache/clear/', { type: cacheType })
    return response.data
  } catch (error) {
    console.error('Error clearing cache:', error)
    throw error
  }
}

export const optimizeDatabase = async (): Promise<{ success: boolean; message: string; stats: any }> => {
  try {
    const response = await systemConfigApi.post('/database/optimize/')
    return response.data
  } catch (error) {
    console.error('Error optimizing database:', error)
    throw error
  }
}

export const restartSystem = async (): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await systemConfigApi.post('/restart/')
    return response.data
  } catch (error) {
    console.error('Error restarting system:', error)
    throw error
  }
}

export const clearLogs = async (olderThan?: string): Promise<{ success: boolean; deleted_count: number }> => {
  try {
    const data = olderThan ? { older_than: olderThan } : {}
    const response = await systemConfigApi.post('/logs/clear/', data)
    return response.data
  } catch (error) {
    console.error('Error clearing logs:', error)
    throw error
  }
}

export const exportSystemConfig = async (format: 'json' | 'yaml' = 'json'): Promise<Blob> => {
  try {
    const response = await systemConfigApi.get(`/config/export/?format=${format}`, {
      responseType: 'blob'
    })
    return response.data
  } catch (error) {
    console.error('Error exporting system config:', error)
    throw error
  }
}

export const importSystemConfig = async (file: File): Promise<{ 
  success: boolean; 
  message: string;
  changes: Record<string, any>
}> => {
  try {
    const formData = new FormData()
    formData.append('file', file)
    
    const response = await systemConfigApi.post('/config/import/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    return response.data
  } catch (error) {
    console.error('Error importing system config:', error)
    throw error
  }
}

export const getSystemHealth = async (): Promise<{
  status: 'healthy' | 'warning' | 'critical'
  checks: Array<{
    name: string
    status: 'pass' | 'fail' | 'warn'
    message: string
    details?: any
  }>
}> => {
  try {
    const response = await systemConfigApi.get('/health/')
    return response.data
  } catch (error) {
    console.error('Error fetching system health:', error)
    throw error
  }
}

// Utility functions
export const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export const formatUptime = (seconds: number): string => {
  const days = Math.floor(seconds / 86400)
  const hours = Math.floor((seconds % 86400) / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  
  if (days > 0) {
    return `${days}d ${hours}h ${minutes}m`
  } else if (hours > 0) {
    return `${hours}h ${minutes}m`
  } else {
    return `${minutes}m`
  }
}

export const getStatusColor = (status: string): 'success' | 'warning' | 'error' | 'default' => {
  switch (status) {
    case 'running':
    case 'good':
    case 'healthy':
    case 'pass':
      return 'success'
    case 'warning':
    case 'warn':
      return 'warning'
    case 'error':
    case 'critical':
    case 'stopped':
    case 'fail':
      return 'error'
    default:
      return 'default'
  }
}

export const transformSystemConfigForForm = (config: SystemConfig) => ({
  maxMemoryUsage: config.max_memory_usage,
  maxCpuUsage: config.max_cpu_usage,
  maxDiskUsage: config.max_disk_usage,
  logRetentionDays: config.log_retention_days,
  backupRetentionDays: config.backup_retention_days,
  enableAutoBackup: config.enable_auto_backup,
  enableMonitoring: config.enable_monitoring,
  enableAlerts: config.enable_alerts,
  alertEmail: config.alert_email,
  maintenanceWindow: config.maintenance_window,
  timeZone: config.time_zone,
  maxConcurrentUsers: config.max_concurrent_users,
  sessionTimeout: config.session_timeout,
  enableDebugMode: config.enable_debug_mode,
})

export const transformFormDataToSystemConfig = (formData: any): Partial<SystemConfig> => ({
  max_memory_usage: formData.maxMemoryUsage,
  max_cpu_usage: formData.maxCpuUsage,
  max_disk_usage: formData.maxDiskUsage,
  log_retention_days: formData.logRetentionDays,
  backup_retention_days: formData.backupRetentionDays,
  enable_auto_backup: formData.enableAutoBackup,
  enable_monitoring: formData.enableMonitoring,
  enable_alerts: formData.enableAlerts,
  alert_email: formData.alertEmail,
  maintenance_window: formData.maintenanceWindow,
  time_zone: formData.timeZone,
  max_concurrent_users: formData.maxConcurrentUsers,
  session_timeout: formData.sessionTimeout,
  enable_debug_mode: formData.enableDebugMode,
})
