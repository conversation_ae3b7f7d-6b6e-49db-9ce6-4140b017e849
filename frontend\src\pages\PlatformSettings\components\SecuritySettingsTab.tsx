import React, { useState } from 'react'
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardHeader,
  TextField,
  Button,
  Typography,
  Switch,
  FormControlLabel,
  Alert,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material'
import {
  Security as SecurityIcon,
  Shield as ShieldIcon,
  Lock as LockIcon,
  Key as KeyIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
} from '@mui/icons-material'

interface SecuritySettingsTabProps {
  onSaveSuccess: () => void
  onSaveError: (error: string) => void
}

const SecuritySettingsTab: React.FC<SecuritySettingsTabProps> = ({
  onSaveSuccess,
  onSaveError,
}) => {
  const [formData, setFormData] = useState({
    enableTwoFactor: false,
    sessionTimeout: 30,
    maxLoginAttempts: 5,
    passwordMinLength: 8,
    requireSpecialChars: true,
    enableCaptcha: true,
    allowedDomains: '',
    enableAuditLog: true,
    enableRateLimiting: true,
    rateLimitRequests: 100,
    rateLimitWindow: 15,
  })

  const handleInputChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : 
                  event.target.type === 'number' ? parseInt(event.target.value) : event.target.value
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = async () => {
    try {
      // This would integrate with a security settings API
      console.log('Saving security settings:', formData)
      onSaveSuccess()
    } catch (error) {
      onSaveError('Failed to save security settings')
    }
  }

  const securityChecks = [
    { name: 'HTTPS Enabled', status: true, description: 'SSL/TLS encryption is active' },
    { name: 'Database Encryption', status: true, description: 'Sensitive data is encrypted at rest' },
    { name: 'Regular Backups', status: true, description: 'Automated daily backups configured' },
    { name: 'Security Headers', status: true, description: 'HTTP security headers are set' },
    { name: 'Vulnerability Scanning', status: false, description: 'Regular security scans needed' },
  ]

  return (
    <Box>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
          Security Settings
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Configure security policies and access controls for your platform
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Authentication Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Authentication"
              subheader="User authentication and access control"
              avatar={<LockIcon color="primary" />}
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.enableTwoFactor}
                        onChange={handleInputChange('enableTwoFactor')}
                      />
                    }
                    label="Enable Two-Factor Authentication"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Session Timeout (minutes)"
                    type="number"
                    value={formData.sessionTimeout}
                    onChange={handleInputChange('sessionTimeout')}
                    helperText="Automatic logout after inactivity"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Max Login Attempts"
                    type="number"
                    value={formData.maxLoginAttempts}
                    onChange={handleInputChange('maxLoginAttempts')}
                    helperText="Account lockout after failed attempts"
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.enableCaptcha}
                        onChange={handleInputChange('enableCaptcha')}
                      />
                    }
                    label="Enable CAPTCHA"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Password Policy */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Password Policy"
              subheader="Password requirements and security"
              avatar={<KeyIcon color="primary" />}
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Minimum Password Length"
                    type="number"
                    value={formData.passwordMinLength}
                    onChange={handleInputChange('passwordMinLength')}
                    inputProps={{ min: 6, max: 32 }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.requireSpecialChars}
                        onChange={handleInputChange('requireSpecialChars')}
                      />
                    }
                    label="Require Special Characters"
                  />
                </Grid>
                <Grid item xs={12}>
                  <Alert severity="info">
                    <Typography variant="body2">
                      Strong password policy helps protect user accounts from unauthorized access.
                    </Typography>
                  </Alert>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Rate Limiting */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Rate Limiting"
              subheader="API and request rate limiting"
              avatar={<ShieldIcon color="primary" />}
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.enableRateLimiting}
                        onChange={handleInputChange('enableRateLimiting')}
                      />
                    }
                    label="Enable Rate Limiting"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Max Requests"
                    type="number"
                    value={formData.rateLimitRequests}
                    onChange={handleInputChange('rateLimitRequests')}
                    disabled={!formData.enableRateLimiting}
                    helperText="Maximum requests per time window"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Time Window (minutes)"
                    type="number"
                    value={formData.rateLimitWindow}
                    onChange={handleInputChange('rateLimitWindow')}
                    disabled={!formData.enableRateLimiting}
                    helperText="Time window for rate limiting"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Access Control */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Access Control"
              subheader="Domain and IP restrictions"
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Allowed Domains"
                    value={formData.allowedDomains}
                    onChange={handleInputChange('allowedDomains')}
                    multiline
                    rows={3}
                    placeholder="example.com&#10;subdomain.example.com"
                    helperText="One domain per line (optional)"
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.enableAuditLog}
                        onChange={handleInputChange('enableAuditLog')}
                      />
                    }
                    label="Enable Audit Logging"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Security Status */}
        <Grid item xs={12}>
          <Card>
            <CardHeader
              title="Security Status"
              subheader="Current security configuration status"
              avatar={<SecurityIcon color="primary" />}
            />
            <CardContent>
              <List>
                {securityChecks.map((check, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      {check.status ? (
                        <CheckIcon color="success" />
                      ) : (
                        <WarningIcon color="warning" />
                      )}
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="body1">{check.name}</Typography>
                          <Chip
                            label={check.status ? 'Active' : 'Needs Attention'}
                            color={check.status ? 'success' : 'warning'}
                            size="small"
                          />
                        </Box>
                      }
                      secondary={check.description}
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Security Recommendations */}
        <Grid item xs={12}>
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1 }}>
              Security Recommendations
            </Typography>
            <Typography variant="body2" component="div">
              <ul style={{ margin: 0, paddingLeft: 20 }}>
                <li>Enable two-factor authentication for all admin accounts</li>
                <li>Regularly update passwords and review user access</li>
                <li>Monitor audit logs for suspicious activity</li>
                <li>Keep the platform and dependencies up to date</li>
                <li>Implement regular security assessments</li>
              </ul>
            </Typography>
          </Alert>
        </Grid>
      </Grid>

      {/* Action Buttons */}
      <Box sx={{ mt: 4, display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
        <Button
          variant="outlined"
          onClick={() => window.location.reload()}
        >
          Reset
        </Button>
        <Button
          variant="contained"
          onClick={handleSave}
          sx={{
            background: 'linear-gradient(45deg, #6366f1 30%, #8b5cf6 90%)',
            boxShadow: '0 3px 5px 2px rgba(99, 102, 241, .3)',
          }}
        >
          Save Security Settings
        </Button>
      </Box>
    </Box>
  )
}

export default SecuritySettingsTab
