import os
import django
import json
import requests
import sys

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kelem_sms.settings')
django.setup()

from django.contrib.auth import get_user_model
from django_tenants.utils import schema_context
from tenants.models import School
from authentication.models import User

def check_user_in_schemas(email):
    """Check which schemas a user exists in."""
    print(f"Checking schemas for user: {email}")
    
    # Check public schema
    try:
        user = User.objects.filter(email=email).first()
        if user:
            print(f"User found in public schema with ID: {user.id}")
            print(f"  User type: {user.user_type}")
            print(f"  Is superuser: {user.is_superuser}")
            print(f"  Is staff: {user.is_staff}")
            print(f"  Is active: {user.is_active}")
        else:
            print("User not found in public schema")
    except Exception as e:
        print(f"Error checking public schema: {str(e)}")
    
    # Check all tenant schemas
    for school in School.objects.all():
        try:
            with schema_context(school.schema_name):
                user = User.objects.filter(email=email).first()
                if user:
                    print(f"User found in schema {school.schema_name} (School: {school.name}) with ID: {user.id}")
                    print(f"  User type: {user.user_type}")
                    print(f"  Is superuser: {user.is_superuser}")
                    print(f"  Is staff: {user.is_staff}")
                    print(f"  Is active: {user.is_active}")
                else:
                    print(f"User not found in schema {school.schema_name} (School: {school.name})")
        except Exception as e:
            print(f"Error checking schema {school.schema_name}: {str(e)}")

def test_login(email, password):
    """Test logging in with the given credentials."""
    print(f"Testing login for user: {email}")
    
    # Make a request to the login API
    url = "http://localhost:8000/auth/api/login/"
    headers = {"Content-Type": "application/json"}
    data = {"email": email, "password": password}
    
    try:
        response = requests.post(url, headers=headers, data=json.dumps(data))
        print(f"Status code: {response.status_code}")
        print(f"Response: {response.json()}")
        
        if response.status_code == 200 and response.json().get('success'):
            print("Login successful!")
            return True
        else:
            print("Login failed!")
            return False
    except Exception as e:
        print(f"Error testing login: {str(e)}")
        return False

def main():
    if len(sys.argv) < 2:
        print("Usage: python test_tenant_login.py <email> [password]")
        return
    
    email = sys.argv[1]
    password = sys.argv[2] if len(sys.argv) > 2 else None
    
    # Check which schemas the user exists in
    check_user_in_schemas(email)
    
    # Test login if password is provided
    if password:
        test_login(email, password)

if __name__ == "__main__":
    main()
