import React, { memo, useMemo, useCallback, useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  CardHeader,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar,
  Typography,
  Box,
  Skeleton,
} from '@mui/material'
import { debounce, throttle } from '../utils/performance'

// Optimized Card component with memoization
export const OptimizedCard = memo(({ 
  title, 
  subtitle, 
  content, 
  actions,
  loading = false,
  ...props 
}: {
  title: string
  subtitle?: string
  content: React.ReactNode
  actions?: React.ReactNode
  loading?: boolean
  [key: string]: any
}) => {
  if (loading) {
    return (
      <Card {...props}>
        <CardHeader
          title={<Skeleton variant="text" width="60%" />}
          subheader={<Skeleton variant="text" width="40%" />}
        />
        <CardContent>
          <Skeleton variant="rectangular" height={100} />
        </CardContent>
      </Card>
    )
  }

  return (
    <Card {...props}>
      <CardHeader
        title={title}
        subheader={subtitle}
        action={actions}
      />
      <CardContent>
        {content}
      </CardContent>
    </Card>
  )
})

OptimizedCard.displayName = 'OptimizedCard'

// Optimized List component with virtualization support
export const OptimizedList = memo(({ 
  items, 
  renderItem, 
  loading = false,
  emptyMessage = 'No items found',
  maxHeight = 400,
  ...props 
}: {
  items: any[]
  renderItem: (item: any, index: number) => React.ReactNode
  loading?: boolean
  emptyMessage?: string
  maxHeight?: number
  [key: string]: any
}) => {
  if (loading) {
    return (
      <List {...props}>
        {Array.from({ length: 5 }).map((_, index) => (
          <ListItem key={index}>
            <ListItemIcon>
              <Skeleton variant="circular" width={40} height={40} />
            </ListItemIcon>
            <ListItemText
              primary={<Skeleton variant="text" width="60%" />}
              secondary={<Skeleton variant="text" width="40%" />}
            />
          </ListItem>
        ))}
      </List>
    )
  }

  if (items.length === 0) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <Typography variant="body2" color="text.secondary">
          {emptyMessage}
        </Typography>
      </Box>
    )
  }

  return (
    <List 
      {...props}
      sx={{ 
        maxHeight, 
        overflow: 'auto',
        ...props.sx 
      }}
    >
      {items.map((item, index) => renderItem(item, index))}
    </List>
  )
})

OptimizedList.displayName = 'OptimizedList'

// Optimized Search component with debouncing
export const OptimizedSearch = memo(({ 
  onSearch, 
  placeholder = 'Search...', 
  debounceMs = 300,
  ...props 
}: {
  onSearch: (query: string) => void
  placeholder?: string
  debounceMs?: number
  [key: string]: any
}) => {
  const [query, setQuery] = useState('')

  const debouncedSearch = useMemo(
    () => debounce(onSearch, debounceMs),
    [onSearch, debounceMs]
  )

  const handleChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value
    setQuery(value)
    debouncedSearch(value)
  }, [debouncedSearch])

  return (
    <input
      type="text"
      value={query}
      onChange={handleChange}
      placeholder={placeholder}
      {...props}
    />
  )
})

OptimizedSearch.displayName = 'OptimizedSearch'

// Optimized Image component with lazy loading
export const OptimizedImage = memo(({ 
  src, 
  alt, 
  width, 
  height, 
  loading = 'lazy',
  fallback,
  ...props 
}: {
  src: string
  alt: string
  width?: number
  height?: number
  loading?: 'lazy' | 'eager'
  fallback?: string
  [key: string]: any
}) => {
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)

  const handleLoad = useCallback(() => {
    setImageLoaded(true)
  }, [])

  const handleError = useCallback(() => {
    setImageError(true)
  }, [])

  if (imageError && fallback) {
    return (
      <img
        src={fallback}
        alt={alt}
        width={width}
        height={height}
        {...props}
      />
    )
  }

  return (
    <Box sx={{ position: 'relative', display: 'inline-block' }}>
      {!imageLoaded && (
        <Skeleton
          variant="rectangular"
          width={width}
          height={height}
          sx={{ position: 'absolute', top: 0, left: 0 }}
        />
      )}
      <img
        src={src}
        alt={alt}
        width={width}
        height={height}
        loading={loading}
        onLoad={handleLoad}
        onError={handleError}
        style={{ 
          opacity: imageLoaded ? 1 : 0,
          transition: 'opacity 0.3s ease-in-out'
        }}
        {...props}
      />
    </Box>
  )
})

OptimizedImage.displayName = 'OptimizedImage'

// Optimized Table component with virtual scrolling
export const OptimizedTable = memo(({ 
  columns, 
  data, 
  loading = false,
  onRowClick,
  maxHeight = 400,
  ...props 
}: {
  columns: Array<{ key: string; label: string; render?: (value: any, row: any) => React.ReactNode }>
  data: any[]
  loading?: boolean
  onRowClick?: (row: any) => void
  maxHeight?: number
  [key: string]: any
}) => {
  const handleRowClick = useCallback((row: any) => {
    onRowClick?.(row)
  }, [onRowClick])

  if (loading) {
    return (
      <Box sx={{ maxHeight, overflow: 'auto' }}>
        <table {...props}>
          <thead>
            <tr>
              {columns.map((column) => (
                <th key={column.key}>{column.label}</th>
              ))}
            </tr>
          </thead>
          <tbody>
            {Array.from({ length: 5 }).map((_, index) => (
              <tr key={index}>
                {columns.map((column) => (
                  <td key={column.key}>
                    <Skeleton variant="text" />
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </Box>
    )
  }

  return (
    <Box sx={{ maxHeight, overflow: 'auto' }}>
      <table {...props}>
        <thead>
          <tr>
            {columns.map((column) => (
              <th key={column.key}>{column.label}</th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((row, index) => (
            <tr 
              key={index} 
              onClick={() => handleRowClick(row)}
              style={{ cursor: onRowClick ? 'pointer' : 'default' }}
            >
              {columns.map((column) => (
                <td key={column.key}>
                  {column.render ? column.render(row[column.key], row) : row[column.key]}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </Box>
  )
})

OptimizedTable.displayName = 'OptimizedTable'

// Higher-order component for performance monitoring
export const withPerformanceMonitoring = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  componentName: string
) => {
  const WithPerformanceMonitoring = memo((props: P) => {
    useEffect(() => {
      const startTime = performance.now()
      
      return () => {
        const endTime = performance.now()
        if (process.env.NODE_ENV === 'development') {
          console.log(`${componentName} lifecycle time: ${endTime - startTime}ms`)
        }
      }
    }, [])

    return <WrappedComponent {...props} />
  })

  WithPerformanceMonitoring.displayName = `withPerformanceMonitoring(${componentName})`
  return WithPerformanceMonitoring
}

// Hook for optimized state updates
export const useOptimizedState = <T>(initialValue: T, updateDelay = 100) => {
  const [value, setValue] = useState(initialValue)
  const [debouncedValue, setDebouncedValue] = useState(initialValue)

  const debouncedSetValue = useMemo(
    () => debounce((newValue: T) => setDebouncedValue(newValue), updateDelay),
    [updateDelay]
  )

  const updateValue = useCallback((newValue: T) => {
    setValue(newValue)
    debouncedSetValue(newValue)
  }, [debouncedSetValue])

  return [value, debouncedValue, updateValue] as const
}

// Hook for throttled scroll handling
export const useThrottledScroll = (callback: (scrollY: number) => void, delay = 100) => {
  const throttledCallback = useMemo(
    () => throttle(callback, delay),
    [callback, delay]
  )

  useEffect(() => {
    const handleScroll = () => {
      throttledCallback(window.scrollY)
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [throttledCallback])
}

// Hook for intersection observer (lazy loading)
export const useIntersectionObserver = (
  ref: React.RefObject<Element>,
  options: IntersectionObserverInit = {}
) => {
  const [isIntersecting, setIsIntersecting] = useState(false)

  useEffect(() => {
    const element = ref.current
    if (!element) return

    const observer = new IntersectionObserver(
      ([entry]) => setIsIntersecting(entry.isIntersecting),
      options
    )

    observer.observe(element)
    return () => observer.disconnect()
  }, [ref, options])

  return isIntersecting
}

// Optimized Avatar component
export const OptimizedAvatar = memo(({ 
  src, 
  alt, 
  size = 40, 
  fallback,
  ...props 
}: {
  src?: string
  alt: string
  size?: number
  fallback?: React.ReactNode
  [key: string]: any
}) => {
  const [imageError, setImageError] = useState(false)

  const handleError = useCallback(() => {
    setImageError(true)
  }, [])

  if (!src || imageError) {
    return (
      <Avatar sx={{ width: size, height: size }} {...props}>
        {fallback || alt.charAt(0).toUpperCase()}
      </Avatar>
    )
  }

  return (
    <Avatar
      src={src}
      alt={alt}
      sx={{ width: size, height: size }}
      onError={handleError}
      {...props}
    />
  )
})

OptimizedAvatar.displayName = 'OptimizedAvatar'
