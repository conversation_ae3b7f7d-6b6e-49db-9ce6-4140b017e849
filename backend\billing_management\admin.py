from django.contrib import admin
from .models import SubscriptionPlan, PlanFeature, Subscription, Invoice, InvoiceItem, Payment

@admin.register(SubscriptionPlan)
class SubscriptionPlanAdmin(admin.ModelAdmin):
    list_display = ['name', 'price', 'currency', 'billing_cycle', 'category', 'is_active', 'is_featured', 'sort_order']
    list_filter = ['billing_cycle', 'category', 'is_active', 'is_featured', 'currency']
    search_fields = ['name', 'description']
    ordering = ['sort_order', 'name']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'category', 'sort_order')
        }),
        ('Pricing', {
            'fields': ('price', 'currency', 'billing_cycle', 'setup_fee', 'trial_days')
        }),
        ('Limits', {
            'fields': ('max_users', 'max_storage')
        }),
        ('Features', {
            'fields': ('features_json',)
        }),
        ('Status', {
            'fields': ('is_active', 'is_featured')
        }),
    )

@admin.register(PlanFeature)
class PlanFeatureAdmin(admin.ModelAdmin):
    list_display = ['plan', 'name', 'included', 'created_at']
    list_filter = ['included', 'plan']
    search_fields = ['name', 'description', 'plan__name']

@admin.register(Subscription)
class SubscriptionAdmin(admin.ModelAdmin):
    list_display = ['tenant', 'plan', 'status', 'start_date', 'end_date', 'auto_renew']
    list_filter = ['status', 'auto_renew', 'plan', 'start_date']
    search_fields = ['tenant__name', 'plan__name']
    date_hierarchy = 'start_date'
    
    fieldsets = (
        ('Subscription Details', {
            'fields': ('tenant', 'plan', 'status')
        }),
        ('Dates', {
            'fields': ('start_date', 'end_date', 'trial_end_date', 'next_billing_date')
        }),
        ('Settings', {
            'fields': ('auto_renew',)
        }),
    )

class InvoiceItemInline(admin.TabularInline):
    model = InvoiceItem
    extra = 1

@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    list_display = ['invoice_number', 'tenant', 'amount', 'currency', 'status', 'issue_date', 'due_date']
    list_filter = ['status', 'currency', 'issue_date', 'due_date']
    search_fields = ['invoice_number', 'tenant__name']
    date_hierarchy = 'issue_date'
    inlines = [InvoiceItemInline]
    
    fieldsets = (
        ('Invoice Details', {
            'fields': ('invoice_number', 'tenant', 'subscription')
        }),
        ('Amount', {
            'fields': ('amount', 'currency')
        }),
        ('Status & Dates', {
            'fields': ('status', 'issue_date', 'due_date', 'paid_date')
        }),
        ('Notes', {
            'fields': ('notes',)
        }),
    )

@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ['id', 'tenant', 'amount', 'currency', 'status', 'payment_method', 'processed_at']
    list_filter = ['status', 'payment_method', 'currency', 'processed_at']
    search_fields = ['tenant__name', 'transaction_id', 'invoice__invoice_number']
    date_hierarchy = 'processed_at'
    
    fieldsets = (
        ('Payment Details', {
            'fields': ('invoice', 'subscription', 'tenant')
        }),
        ('Amount', {
            'fields': ('amount', 'currency')
        }),
        ('Payment Info', {
            'fields': ('status', 'payment_method', 'transaction_id', 'processed_at')
        }),
        ('Failure Info', {
            'fields': ('failure_reason',)
        }),
    )
