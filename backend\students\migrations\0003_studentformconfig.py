# Generated by Django 5.1.7 on 2025-04-12 08:29

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('students', '0002_alter_guardian_options_alter_student_options_and_more'),
        ('tenants', '0003_merge_20250409_1906'),
    ]

    operations = [
        migrations.CreateModel(
            name='StudentFormConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field_name', models.CharField(help_text='Field name in the API', max_length=100)),
                ('display_name', models.CharField(help_text='Display name shown to users', max_length=100)),
                ('field_type', models.CharField(choices=[('text', 'Text'), ('number', 'Number'), ('email', 'Email'), ('phone', 'Phone'), ('date', 'Date'), ('select', 'Select'), ('textarea', 'Text Area'), ('checkbox', 'Checkbox'), ('radio', 'Radio'), ('file', 'File Upload')], max_length=20)),
                ('section', models.CharField(choices=[('personal', 'Personal Information'), ('contact', 'Contact Information'), ('academic', 'Academic Information'), ('guardian', 'Guardian Information'), ('health', 'Health Information'), ('emergency', 'Emergency Contact'), ('documents', 'Documents'), ('other', 'Other Information')], max_length=20)),
                ('status', models.CharField(choices=[('required', 'Required'), ('optional', 'Optional'), ('hidden', 'Hidden')], default='optional', max_length=20)),
                ('order', models.PositiveSmallIntegerField(default=0, help_text='Display order within section')),
                ('options', models.JSONField(blank=True, help_text='Options for select, radio, etc.', null=True)),
                ('help_text', models.CharField(blank=True, max_length=255, null=True)),
                ('default_value', models.CharField(blank=True, max_length=255, null=True)),
                ('validation_regex', models.CharField(blank=True, help_text='Regex pattern for validation', max_length=255, null=True)),
                ('is_custom', models.BooleanField(default=False, help_text='Whether this is a custom field')),
                ('tenant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='student_form_configs', to='tenants.school')),
            ],
            options={
                'ordering': ['section', 'order'],
                'unique_together': {('tenant', 'field_name')},
            },
        ),
    ]
