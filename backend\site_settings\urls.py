from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views

app_name = 'site_settings'

# Create a router for API views
router = DefaultRouter()
router.register(r'settings', views.SiteSettingsViewSet, basename='settings')
router.register(r'branding', views.BrandingSettingsViewSet, basename='branding')
router.register(r'hero', views.HeroSectionViewSet, basename='hero')
router.register(r'maintenance', views.MaintenanceModeViewSet, basename='maintenance')
router.register(r'config', views.SiteConfigViewSet, basename='config')

urlpatterns = [
    # API endpoints
    path('', include(router.urls)),
]
