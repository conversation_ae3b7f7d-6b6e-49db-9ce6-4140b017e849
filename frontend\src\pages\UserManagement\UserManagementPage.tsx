import React, { useState, useEffect } from 'react'
import {
  Box,
  Container,
  Typography,
  Paper,
  Tabs,
  Tab,
  Alert,
  Snackbar,
  CircularProgress,
  Backdrop,
} from '@mui/material'
import {
  People as PeopleIcon,
  PersonAdd as PersonAddIcon,
  Group as GroupIcon,
  Analytics as AnalyticsIcon,
  Security as SecurityIcon,
  VpnKey as PermissionIcon,
} from '@mui/icons-material'
import { Button } from '@mui/material'
import { useNavigate } from 'react-router-dom'
import UserListTab from './components/UserListTab'
import UserStatsTab from './components/UserStatsTab'
import GroupsTab from './components/GroupsTab'
import PermissionsTab from './components/PermissionsTab'
import CreateUserDialog from './components/CreateUserDialog'

interface TabPanelProps {
  children?: React.ReactNode
  index: number
  value: number
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`user-management-tabpanel-${index}`}
      aria-labelledby={`user-management-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  )
}

function a11yProps(index: number) {
  return {
    id: `user-management-tab-${index}`,
    'aria-controls': `user-management-tabpanel-${index}`,
  }
}

const UserManagementPage: React.FC = () => {
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState(0)
  const [loading, setLoading] = useState(false)
  const [createUserOpen, setCreateUserOpen] = useState(false)
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error' | 'warning' | 'info',
  })

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue)
  }

  const showSnackbar = (message: string, severity: 'success' | 'error' | 'warning' | 'info' = 'success') => {
    setSnackbar({ open: true, message, severity })
  }

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false })
  }

  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1)
  }

  const handleUserCreated = () => {
    setCreateUserOpen(false)
    handleRefresh()
    showSnackbar('User created successfully!', 'success')
  }

  const handleUserAction = (message: string, severity: 'success' | 'error' | 'warning' | 'info' = 'success') => {
    showSnackbar(message, severity)
    handleRefresh()
  }

  const tabs = [
    {
      label: 'Users',
      icon: <PeopleIcon />,
      component: (
        <UserListTab
          refreshTrigger={refreshTrigger}
          onUserAction={handleUserAction}
          onCreateUser={() => setCreateUserOpen(true)}
        />
      ),
    },
    {
      label: 'Analytics',
      icon: <AnalyticsIcon />,
      component: (
        <UserStatsTab
          refreshTrigger={refreshTrigger}
        />
      ),
    },
    {
      label: 'Groups',
      icon: <GroupIcon />,
      component: (
        <GroupsTab
          refreshTrigger={refreshTrigger}
          onGroupAction={handleUserAction}
        />
      ),
    },
    {
      label: 'Permissions',
      icon: <PermissionIcon />,
      component: (
        <PermissionsTab
          refreshTrigger={refreshTrigger}
          onPermissionAction={handleUserAction}
        />
      ),
    },
  ]

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: '#f5f5f5' }}>
      {/* White Header Section */}
      <Box
        sx={{
          bgcolor: 'white',
          color: '#1f2937',
          py: 4,
          px: 3,
          mb: 3,
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          borderRadius: 0,
        }}
      >
        <Container maxWidth="xl">
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box>
              <Typography variant="h4" component="h1" sx={{ fontWeight: 700, mb: 1 }}>
                User Management
              </Typography>
              <Typography variant="body1" sx={{ color: '#6b7280' }}>
                Manage users, permissions, and access control for your platform
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="outlined"
                startIcon={<SecurityIcon />}
                onClick={() => navigate('/dashboard/user-management/access')}
                sx={{
                  color: '#6366f1',
                  borderColor: '#6366f1',
                  fontWeight: 600,
                  px: 3,
                  py: 1.5,
                }}
              >
                Access Control
              </Button>
              <Button
                variant="contained"
                startIcon={<SecurityIcon />}
                onClick={() => navigate('/dashboard/user-management/security')}
                sx={{
                  background: 'linear-gradient(45deg, #6366f1 30%, #8b5cf6 90%)',
                  boxShadow: '0 3px 5px 2px rgba(99, 102, 241, .3)',
                  color: 'white',
                  fontWeight: 600,
                  px: 3,
                  py: 1.5,
                }}
              >
                Security Center
              </Button>
            </Box>
          </Box>
        </Container>
      </Box>

      <Container maxWidth="lg" sx={{ pb: 4 }}>
        {/* Main Content - White Card */}
        <Paper
          sx={{
            width: '100%',
            bgcolor: 'white',
            borderRadius: 0,
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
            overflow: 'hidden'
          }}
        >
          {/* Tabs */}
          <Box sx={{ borderBottom: 1, borderColor: 'divider', bgcolor: 'white' }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              aria-label="user management tabs"
              variant="scrollable"
              scrollButtons="auto"
              sx={{
                '& .MuiTab-root': {
                  minHeight: 64,
                  textTransform: 'none',
                  fontWeight: 600,
                  color: '#6b7280',
                  '&.Mui-selected': {
                    color: '#6366f1',
                  },
                },
                '& .MuiTabs-indicator': {
                  backgroundColor: '#6366f1',
                  height: 3,
                },
              }}
            >
              {tabs.map((tab, index) => (
                <Tab
                  key={index}
                  icon={tab.icon}
                  label={tab.label}
                  iconPosition="start"
                  {...a11yProps(index)}
                />
              ))}
            </Tabs>
          </Box>

          {/* Tab Panels */}
          {tabs.map((tab, index) => (
            <TabPanel key={index} value={activeTab} index={index}>
              <Box sx={{ bgcolor: 'white', p: 3 }}>
                {tab.component}
              </Box>
            </TabPanel>
          ))}
        </Paper>
      </Container>

      {/* Create User Dialog */}
      <CreateUserDialog
        open={createUserOpen}
        onClose={() => setCreateUserOpen(false)}
        onUserCreated={handleUserCreated}
        onError={(error) => showSnackbar(error, 'error')}
      />

      {/* Loading Backdrop */}
      <Backdrop
        sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1 }}
        open={loading}
      >
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <CircularProgress color="inherit" />
          <Typography sx={{ mt: 2 }}>Processing...</Typography>
        </Box>
      </Backdrop>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  )
}

export default UserManagementPage
