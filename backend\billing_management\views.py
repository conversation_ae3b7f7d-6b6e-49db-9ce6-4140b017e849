from django.shortcuts import render
from django.db.models import Sum, Count, Avg, Q
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.generics import ListAPIView

from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from .models import SubscriptionPlan, PlanFeature, Subscription, Invoice, InvoiceItem, Payment
from .serializers import (
    SubscriptionPlanSerializer, PlanFeatureSerializer, SubscriptionSerializer,
    InvoiceSerializer, InvoiceItemSerializer, PaymentSerializer, BillingMetricsSerializer
)

class IsSuperAdmin(permissions.BasePermission):
    """Permission to check if the user is a super admin."""

    def has_permission(self, request, view):
        # Check if the user is authenticated and is a superuser
        return request.user and request.user.is_authenticated and request.user.is_superuser

class SubscriptionPlanViewSet(viewsets.ModelViewSet):
    """API endpoint for subscription plans."""
    queryset = SubscriptionPlan.objects.all()
    serializer_class = SubscriptionPlanSerializer
    permission_classes = [IsSuperAdmin]
    
    @swagger_auto_schema(
        operation_summary="List subscription plans",
        operation_description="Get a list of all subscription plans.",
        responses={200: SubscriptionPlanSerializer(many=True)}
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema(
        operation_summary="Create subscription plan",
        operation_description="Create a new subscription plan.",
        request_body=SubscriptionPlanSerializer,
        responses={201: SubscriptionPlanSerializer}
    )
    def create(self, request, *args, **kwargs):
        print(f"Creating subscription plan with data: {request.data}")
        return super().create(request, *args, **kwargs)
    
    @swagger_auto_schema(
        operation_summary="Get subscription plan",
        operation_description="Retrieve a specific subscription plan.",
        responses={200: SubscriptionPlanSerializer}
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    
    @swagger_auto_schema(
        operation_summary="Update subscription plan",
        operation_description="Update a subscription plan.",
        request_body=SubscriptionPlanSerializer,
        responses={200: SubscriptionPlanSerializer}
    )
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)
    
    @swagger_auto_schema(
        operation_summary="Delete subscription plan",
        operation_description="Delete a subscription plan.",
        responses={204: 'Plan deleted successfully'}
    )
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)
    
    @swagger_auto_schema(
        operation_summary="Toggle plan status",
        operation_description="Toggle the active status of a plan.",
        responses={200: openapi.Response('Status toggled', SubscriptionPlanSerializer)}
    )
    @action(detail=True, methods=['post'])
    def toggle_status(self, request, pk=None):
        """Toggle the active status of a plan."""
        plan = self.get_object()
        plan.is_active = not plan.is_active
        plan.save()
        serializer = self.get_serializer(plan)
        return Response(serializer.data)
    
    @swagger_auto_schema(
        operation_summary="Toggle featured status",
        operation_description="Toggle the featured status of a plan.",
        responses={200: openapi.Response('Featured status toggled', SubscriptionPlanSerializer)}
    )
    @action(detail=True, methods=['post'])
    def toggle_featured(self, request, pk=None):
        """Toggle the featured status of a plan."""
        plan = self.get_object()
        plan.is_featured = not plan.is_featured
        plan.save()
        serializer = self.get_serializer(plan)
        return Response(serializer.data)


class PublicSubscriptionPlanListView(ListAPIView):
    """Public API endpoint for listing active subscription plans (no authentication required)."""
    serializer_class = SubscriptionPlanSerializer
    permission_classes = [permissions.AllowAny]

    def get_queryset(self):
        """Return only active subscription plans for public viewing."""
        return SubscriptionPlan.objects.filter(is_active=True).order_by('sort_order', 'name')

    @swagger_auto_schema(
        operation_summary="List public subscription plans",
        operation_description="Get a list of all active subscription plans available for public viewing.",
        responses={200: SubscriptionPlanSerializer(many=True)}
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class SubscriptionViewSet(viewsets.ModelViewSet):
    """API endpoint for subscriptions."""
    queryset = Subscription.objects.all()
    serializer_class = SubscriptionSerializer
    permission_classes = [IsSuperAdmin]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by tenant
        tenant_id = self.request.query_params.get('tenant')
        if tenant_id:
            queryset = queryset.filter(tenant_id=tenant_id)
        
        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        return queryset

class InvoiceViewSet(viewsets.ModelViewSet):
    """API endpoint for invoices."""
    queryset = Invoice.objects.all()
    serializer_class = InvoiceSerializer
    permission_classes = [IsSuperAdmin]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by tenant
        tenant_id = self.request.query_params.get('tenant')
        if tenant_id:
            queryset = queryset.filter(tenant_id=tenant_id)
        
        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        return queryset
    
    @action(detail=True, methods=['post'])
    def mark_paid(self, request, pk=None):
        """Mark an invoice as paid."""
        invoice = self.get_object()
        invoice.status = 'paid'
        invoice.paid_date = timezone.now()
        invoice.save()
        serializer = self.get_serializer(invoice)
        return Response(serializer.data)

class PaymentViewSet(viewsets.ModelViewSet):
    """API endpoint for payments."""
    queryset = Payment.objects.all()
    serializer_class = PaymentSerializer
    permission_classes = [IsSuperAdmin]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by tenant
        tenant_id = self.request.query_params.get('tenant')
        if tenant_id:
            queryset = queryset.filter(tenant_id=tenant_id)
        
        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        return queryset

class BillingMetricsView(APIView):
    """API endpoint for billing metrics and analytics."""
    permission_classes = [IsSuperAdmin]
    
    @swagger_auto_schema(
        operation_summary="Get billing metrics",
        operation_description="Get comprehensive billing metrics and analytics.",
        responses={200: BillingMetricsSerializer}
    )
    def get(self, request):
        """Get billing metrics."""
        try:
            # Calculate metrics
            now = timezone.now()
            current_month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            current_year_start = now.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
            
            # Revenue metrics
            total_revenue = Payment.objects.filter(status='completed').aggregate(
                total=Sum('amount')
            )['total'] or Decimal('0.00')
            
            monthly_revenue = Payment.objects.filter(
                status='completed',
                processed_at__gte=current_month_start
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
            
            # Subscription metrics
            total_subscriptions = Subscription.objects.count()
            active_subscriptions = Subscription.objects.filter(status='active').count()
            trial_subscriptions = Subscription.objects.filter(status='trial').count()
            cancelled_subscriptions = Subscription.objects.filter(status='cancelled').count()
            
            # Calculate MRR (Monthly Recurring Revenue)
            monthly_subscriptions = Subscription.objects.filter(
                status='active',
                plan__billing_cycle='monthly'
            ).select_related('plan')
            
            yearly_subscriptions = Subscription.objects.filter(
                status='active',
                plan__billing_cycle='yearly'
            ).select_related('plan')
            
            mrr = sum(sub.plan.price for sub in monthly_subscriptions)
            mrr += sum(sub.plan.price / 12 for sub in yearly_subscriptions)  # Convert yearly to monthly
            
            # Calculate ARR (Annual Recurring Revenue)
            arr = mrr * 12
            
            # Calculate churn rate (simplified)
            last_month_start = (current_month_start - timedelta(days=1)).replace(day=1)
            last_month_cancelled = Subscription.objects.filter(
                status='cancelled',
                updated_at__gte=last_month_start,
                updated_at__lt=current_month_start
            ).count()
            
            last_month_active = Subscription.objects.filter(
                created_at__lt=current_month_start,
                status__in=['active', 'cancelled']
            ).count()
            
            churn_rate = (last_month_cancelled / last_month_active * 100) if last_month_active > 0 else 0
            
            # Calculate ARPU (Average Revenue Per User)
            arpu = (total_revenue / total_subscriptions) if total_subscriptions > 0 else Decimal('0.00')
            
            # Calculate LTV (simplified)
            avg_subscription_length = 12  # months (placeholder)
            ltv = arpu * avg_subscription_length
            
            # Calculate conversion rate
            trial_to_paid = Subscription.objects.filter(
                status='active',
                created_at__gte=current_month_start
            ).count()
            conversion_rate = (trial_to_paid / trial_subscriptions * 100) if trial_subscriptions > 0 else 0
            
            # Invoice metrics
            total_invoices = Invoice.objects.count()
            paid_invoices = Invoice.objects.filter(status='paid').count()
            overdue_invoices = Invoice.objects.filter(
                status='sent',
                due_date__lt=now
            ).count()
            
            # Payment metrics
            total_payments = Payment.objects.count()
            successful_payments = Payment.objects.filter(status='completed').count()
            failed_payments = Payment.objects.filter(status='failed').count()
            
            metrics = {
                'total_revenue': total_revenue,
                'monthly_recurring_revenue': mrr,
                'annual_recurring_revenue': arr,
                'total_subscriptions': total_subscriptions,
                'active_subscriptions': active_subscriptions,
                'trial_subscriptions': trial_subscriptions,
                'cancelled_subscriptions': cancelled_subscriptions,
                'churn_rate': churn_rate,
                'average_revenue_per_user': arpu,
                'lifetime_value': ltv,
                'conversion_rate': conversion_rate,
                'total_invoices': total_invoices,
                'paid_invoices': paid_invoices,
                'overdue_invoices': overdue_invoices,
                'total_payments': total_payments,
                'successful_payments': successful_payments,
                'failed_payments': failed_payments,
                'timestamp': now,
            }
            
            serializer = BillingMetricsSerializer(metrics)
            return Response(serializer.data)
            
        except Exception as e:
            print(f"Error calculating billing metrics: {str(e)}")
            import traceback
            print(traceback.format_exc())
            
            return Response(
                {'error': f'Failed to calculate billing metrics: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
