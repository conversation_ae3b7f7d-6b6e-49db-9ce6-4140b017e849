from django.contrib.auth import get_user_model
from django.contrib.auth.backends import ModelBackend
from django_tenants.utils import schema_context, get_tenant_model
from django.db import connection
from rest_framework.authtoken.models import Token

User = get_user_model()
TenantModel = get_tenant_model()

class TokenAuthBackend(ModelBackend):
    """
    Authentication backend that authenticates users based on a token.
    """

    def authenticate(self, request=None, token=None, **kwargs):
        if not token:
            # Try to get token from Authorization header
            if request and 'HTTP_AUTHORIZATION' in request.META:
                auth = request.META['HTTP_AUTHORIZATION'].split()
                if len(auth) == 2 and auth[0].lower() == 'token':
                    token = auth[1]

        if not token:
            return None

        print(f"TokenAuthBackend: Authenticating with token: {token[:10]}...")

        try:
            token_obj = Token.objects.get(key=token)
            user = token_obj.user
            print(f"TokenAuthBackend: Found user: {user.email}, is_superuser={user.is_superuser}")

            # Store the schema name where the user was found
            if hasattr(connection, 'schema_name'):
                user._schema_name = connection.schema_name
                print(f"TokenAuthBackend: Set _schema_name attribute to {connection.schema_name}")

            return user
        except Token.DoesNotExist:
            print(f"TokenAuthBackend: Token not found")
            return None
        except Exception as e:
            print(f"TokenAuthBackend: Error authenticating with token: {str(e)}")
            return None

class TenantAwareAuthBackend(ModelBackend):
    """
    Authentication backend that is aware of tenants.
    It tries to authenticate the user in the current schema first,
    and if that fails, it tries to authenticate the user in all other schemas.
    """

    def authenticate(self, request, email=None, password=None, **kwargs):
        if email is None:
            email = kwargs.get('username')
        if email is None or password is None:
            return None

        print(f"TenantAwareAuthBackend: Authenticating user {email}")
        print(f"Current schema: {connection.schema_name}")

        # First, try to authenticate in the current schema
        try:
            user = User.objects.get(email=email)
            print(f"Found user in current schema {connection.schema_name}: {user.email}, is_superuser={user.is_superuser}")

            if user.check_password(password):
                print(f"Password check successful for user in schema {connection.schema_name}")
                # Store the schema name where the user was found
                if hasattr(connection, 'schema_name'):
                    user._schema_name = connection.schema_name
                    print(f"Set _schema_name attribute to {connection.schema_name}")

                # For superusers in the public schema, set the public tenant in the session
                if user.is_superuser and connection.schema_name == 'public' and request and hasattr(request, 'session'):
                    try:
                        public_tenant = TenantModel.objects.get(schema_name='public')
                        request.session['tenant_id'] = public_tenant.id
                        print(f"Set public tenant_id in session for superuser: {public_tenant.id}")
                    except TenantModel.DoesNotExist:
                        print("Public tenant not found")

                return user
            else:
                print(f"Password check failed for user in schema {connection.schema_name}")
        except User.DoesNotExist:
            print(f"User not found in current schema {connection.schema_name}")
            pass

        # If that fails, try to authenticate in all other schemas
        print(f"Trying to authenticate in other schemas...")
        for tenant in TenantModel.objects.exclude(schema_name=connection.schema_name):
            print(f"Checking schema {tenant.schema_name}...")
            try:
                with schema_context(tenant.schema_name):
                    user = User.objects.get(email=email)
                    print(f"Found user in schema {tenant.schema_name}: {user.email}, is_superuser={user.is_superuser}")

                    if user.check_password(password):
                        print(f"Password check successful for user in schema {tenant.schema_name}")
                        # Store the schema name where the user was found
                        user._schema_name = tenant.schema_name
                        print(f"Set _schema_name attribute to {tenant.schema_name}")

                        # Store the tenant ID in the session if request is provided
                        if request and hasattr(request, 'session'):
                            request.session['tenant_id'] = tenant.id
                            print(f"Set tenant_id in session: {tenant.id}")

                        return user
                    else:
                        print(f"Password check failed for user in schema {tenant.schema_name}")
            except User.DoesNotExist:
                print(f"User not found in schema {tenant.schema_name}")
                continue
            except Exception as e:
                print(f"Error checking schema {tenant.schema_name}: {str(e)}")
                continue

        # If we get here, the user doesn't exist in any schema or the password is wrong
        print(f"Authentication failed for user {email}")
        return None
