import React, { useState, useEffect } from 'react'
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
  Paper,
  Chip,
  LinearProgress,
} from '@mui/material'
import {
  People as PeopleIcon,
  PersonAdd as PersonAddIcon,
  AdminPanelSettings as AdminIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Schedule as ScheduleIcon,
  Security as SecurityIcon,
} from '@mui/icons-material'
// Charts temporarily disabled - install recharts if needed
// import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts'
import { getUserStats, type UserStats } from '../../../services/userManagementService'

interface UserStatsTabProps {
  refreshTrigger: number
}

const UserStatsTab: React.FC<UserStatsTabProps> = ({ refreshTrigger }) => {
  const [stats, setStats] = useState<UserStats | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchStats = async () => {
    setLoading(true)
    setError(null)
    try {
      const data = await getUserStats()
      setStats(data)
    } catch (err) {
      setError('Failed to fetch user statistics')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchStats()
  }, [refreshTrigger])

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
        <CircularProgress />
      </Box>
    )
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        {error}
      </Alert>
    )
  }

  if (!stats) {
    return (
      <Alert severity="info">
        No statistics available
      </Alert>
    )
  }

  // Prepare data for charts
  const statusData = [
    { name: 'Active', value: stats.activeUsers, color: '#4caf50' },
    { name: 'Inactive', value: stats.inactiveUsers, color: '#f44336' },
  ]

  const userTypeData = Object.entries(stats.userTypes).map(([type, count]) => ({
    name: type,
    count,
  }))

  const roleData = [
    { name: 'Superusers', value: stats.superusers, color: '#f44336' },
    { name: 'Staff', value: stats.staffUsers - stats.superusers, color: '#ff9800' },
    { name: 'Regular Users', value: stats.totalUsers - stats.staffUsers, color: '#2196f3' },
  ]

  const StatCard: React.FC<{
    title: string
    value: number
    icon: React.ReactNode
    color: string
    subtitle?: string
    trend?: 'up' | 'down' | 'neutral'
    percentage?: number
  }> = ({ title, value, icon, color, subtitle, trend, percentage }) => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ 
            p: 1, 
            borderRadius: 2, 
            bgcolor: `${color}15`,
            color: color,
            mr: 2 
          }}>
            {icon}
          </Box>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h4" sx={{ fontWeight: 700, color }}>
              {value.toLocaleString()}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {title}
            </Typography>
          </Box>
          {trend && percentage !== undefined && (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              {trend === 'up' ? (
                <TrendingUpIcon color="success" />
              ) : trend === 'down' ? (
                <TrendingDownIcon color="error" />
              ) : null}
              <Typography 
                variant="body2" 
                color={trend === 'up' ? 'success.main' : trend === 'down' ? 'error.main' : 'text.secondary'}
                sx={{ ml: 0.5 }}
              >
                {percentage > 0 ? '+' : ''}{percentage}%
              </Typography>
            </Box>
          )}
        </Box>
        {subtitle && (
          <Typography variant="body2" color="text.secondary">
            {subtitle}
          </Typography>
        )}
      </CardContent>
    </Card>
  )

  const activePercentage = stats.totalUsers > 0 ? (stats.activeUsers / stats.totalUsers) * 100 : 0
  const staffPercentage = stats.totalUsers > 0 ? (stats.staffUsers / stats.totalUsers) * 100 : 0

  return (
    <Box sx={{ maxWidth: '100%', mx: 'auto' }}>
      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Users"
            value={stats.totalUsers}
            icon={<PeopleIcon />}
            color="#2196f3"
            subtitle="All registered users"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Active Users"
            value={stats.activeUsers}
            icon={<TrendingUpIcon />}
            color="#4caf50"
            subtitle={`${activePercentage.toFixed(1)}% of total users`}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="New This Month"
            value={stats.newUsersThisMonth}
            icon={<PersonAddIcon />}
            color="#ff9800"
            subtitle="Recently joined users"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Recent Logins"
            value={stats.recentLogins}
            icon={<ScheduleIcon />}
            color="#9c27b0"
            subtitle="Last 7 days"
          />
        </Grid>
      </Grid>

      {/* Detailed Statistics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* User Status Distribution */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                User Status Distribution
              </Typography>
              <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Grid container spacing={2}>
                  {statusData.map((item, index) => (
                    <Grid item xs={6} key={index}>
                      <Box sx={{ textAlign: 'center', p: 2 }}>
                        <Typography variant="h3" sx={{ fontWeight: 700, color: item.color }}>
                          {item.value}
                        </Typography>
                        <Typography variant="body1" sx={{ fontWeight: 600 }}>
                          {item.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {stats.totalUsers > 0 ? ((item.value / stats.totalUsers) * 100).toFixed(1) : 0}%
                        </Typography>
                      </Box>
                    </Grid>
                  ))}
                </Grid>
              </Box>
              <Box sx={{ mt: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Active Users</Typography>
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                    {stats.activeUsers} ({activePercentage.toFixed(1)}%)
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={activePercentage} 
                  sx={{ height: 8, borderRadius: 4, bgcolor: 'grey.200' }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* User Types */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                User Types
              </Typography>
              <Box sx={{ height: 300, p: 2 }}>
                {userTypeData.map((item, index) => (
                  <Box key={index} sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body1" sx={{ fontWeight: 600 }}>
                        {item.name}
                      </Typography>
                      <Typography variant="body1" sx={{ fontWeight: 600 }}>
                        {item.count}
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={stats.totalUsers > 0 ? (item.count / stats.totalUsers) * 100 : 0}
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Role Distribution & Security Overview */}
      <Grid container spacing={3}>
        {/* Role Distribution */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                Role Distribution
              </Typography>
              <Box sx={{ space: 2 }}>
                {roleData.map((role, index) => (
                  <Box key={index} sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">{role.name}</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        {role.value}
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={stats.totalUsers > 0 ? (role.value / stats.totalUsers) * 100 : 0}
                      sx={{ 
                        height: 8, 
                        borderRadius: 4, 
                        bgcolor: 'grey.200',
                        '& .MuiLinearProgress-bar': {
                          bgcolor: role.color
                        }
                      }}
                    />
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Security Overview */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                Security Overview
              </Typography>
              <Box sx={{ space: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <AdminIcon sx={{ color: 'error.main', mr: 2 }} />
                  <Box>
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      {stats.superusers} Superusers
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Users with full system access
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <SecurityIcon sx={{ color: 'warning.main', mr: 2 }} />
                  <Box>
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      {stats.staffUsers} Staff Members
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Users with administrative privileges
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <PeopleIcon sx={{ color: 'info.main', mr: 2 }} />
                  <Box>
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      {stats.totalUsers - stats.staffUsers} Regular Users
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Standard user accounts
                    </Typography>
                  </Box>
                </Box>

                <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    Security Status
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    <Chip 
                      label={`${staffPercentage.toFixed(1)}% Staff`} 
                      color={staffPercentage > 20 ? 'warning' : 'success'} 
                      size="small" 
                    />
                    <Chip 
                      label={`${stats.inactiveUsers} Inactive`} 
                      color={stats.inactiveUsers > 0 ? 'warning' : 'success'} 
                      size="small" 
                    />
                    <Chip 
                      label={`${((stats.recentLogins / stats.totalUsers) * 100).toFixed(1)}% Recent Activity`} 
                      color="info" 
                      size="small" 
                    />
                  </Box>
                </Paper>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  )
}

export default UserStatsTab
