from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from .models import SiteSettings, BrandingSettings, HeroSection, MaintenanceMode
from .serializers import (
    SiteSettingsSerializer, BrandingSettingsSerializer,
    HeroSectionSerializer, MaintenanceModeSerializer,
    SiteConfigSummarySerializer
)


class IsSuperUser(permissions.BasePermission):
    """Permission to check if user is superuser"""
    
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.is_superuser


class SiteSettingsViewSet(viewsets.ModelViewSet):
    """ViewSet for managing site settings"""
    queryset = SiteSettings.objects.all()
    serializer_class = SiteSettingsSerializer
    permission_classes = [IsSuperUser]
    
    def get_object(self):
        """Get or create the single site settings instance"""
        obj, created = SiteSettings.objects.get_or_create(pk=1)
        return obj
    
    def list(self, request, *args, **kwargs):
        """Return the single site settings instance"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)
    
    def create(self, request, *args, **kwargs):
        """Update the existing instance instead of creating new"""
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)
    
    def update(self, request, *args, **kwargs):
        """Update the site settings"""
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)


class BrandingSettingsViewSet(viewsets.ModelViewSet):
    """ViewSet for managing branding settings"""
    queryset = BrandingSettings.objects.all()
    serializer_class = BrandingSettingsSerializer
    permission_classes = [IsSuperUser]
    
    def get_object(self):
        """Get or create the single branding settings instance"""
        obj, created = BrandingSettings.objects.get_or_create(pk=1)
        return obj
    
    def list(self, request, *args, **kwargs):
        """Return the single branding settings instance"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)
    
    def create(self, request, *args, **kwargs):
        """Update the existing instance instead of creating new"""
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)
    
    def update(self, request, *args, **kwargs):
        """Update the branding settings"""
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)


class HeroSectionViewSet(viewsets.ModelViewSet):
    """ViewSet for managing hero section settings"""
    queryset = HeroSection.objects.all()
    serializer_class = HeroSectionSerializer
    permission_classes = [IsSuperUser]
    
    def get_object(self):
        """Get or create the single hero section instance"""
        obj, created = HeroSection.objects.get_or_create(pk=1)
        return obj
    
    def list(self, request, *args, **kwargs):
        """Return the single hero section instance"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)
    
    def create(self, request, *args, **kwargs):
        """Update the existing instance instead of creating new"""
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)
    
    def update(self, request, *args, **kwargs):
        """Update the hero section settings"""
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)


class MaintenanceModeViewSet(viewsets.ModelViewSet):
    """ViewSet for managing maintenance mode settings"""
    queryset = MaintenanceMode.objects.all()
    serializer_class = MaintenanceModeSerializer
    permission_classes = [IsSuperUser]
    
    def get_object(self):
        """Get or create the single maintenance mode instance"""
        obj, created = MaintenanceMode.objects.get_or_create(pk=1)
        return obj
    
    def list(self, request, *args, **kwargs):
        """Return the single maintenance mode instance"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)
    
    def create(self, request, *args, **kwargs):
        """Update the existing instance instead of creating new"""
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)
    
    def update(self, request, *args, **kwargs):
        """Update the maintenance mode settings"""
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)
    
    @swagger_auto_schema(
        operation_summary="Toggle maintenance mode",
        operation_description="Quick toggle for maintenance mode on/off",
        responses={200: MaintenanceModeSerializer}
    )
    @action(detail=False, methods=['post'])
    def toggle(self, request):
        """Toggle maintenance mode on/off"""
        instance = self.get_object()
        instance.maintenance_mode = not instance.maintenance_mode
        instance.save()
        
        serializer = self.get_serializer(instance)
        return Response({
            'message': f'Maintenance mode {"enabled" if instance.maintenance_mode else "disabled"}',
            'data': serializer.data
        })


class SiteConfigViewSet(viewsets.ViewSet):
    """ViewSet for getting all site configuration at once"""
    permission_classes = [IsSuperUser]
    
    @swagger_auto_schema(
        operation_summary="Get all site configuration",
        operation_description="Get all site settings, branding, hero section, and maintenance mode",
        responses={200: SiteConfigSummarySerializer}
    )
    def list(self, request):
        """Get all site configuration"""
        site_settings, _ = SiteSettings.objects.get_or_create(pk=1)
        branding_settings, _ = BrandingSettings.objects.get_or_create(pk=1)
        hero_section, _ = HeroSection.objects.get_or_create(pk=1)
        maintenance_mode, _ = MaintenanceMode.objects.get_or_create(pk=1)
        
        data = {
            'site_settings': SiteSettingsSerializer(site_settings).data,
            'branding_settings': BrandingSettingsSerializer(branding_settings).data,
            'hero_section': HeroSectionSerializer(hero_section).data,
            'maintenance_mode': MaintenanceModeSerializer(maintenance_mode).data,
        }
        
        return Response(data)
