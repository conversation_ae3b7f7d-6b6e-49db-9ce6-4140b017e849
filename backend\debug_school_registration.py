import os
import django
import traceback

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kelem_sms.settings')
django.setup()

from school_registration.models import SchoolRegistrationRequest
from tenants.models import School, Domain
from django_tenants.utils import schema_context
from django.contrib.auth import get_user_model

def check_existing_schools():
    """Check existing schools and their admin users."""
    schools = School.objects.all()
    print(f"Found {schools.count()} schools/tenants")
    
    for school in schools:
        print(f"\nSchool: {school.name} (Schema: {school.schema_name})")
        
        try:
            with schema_context(school.schema_name):
                User = get_user_model()
                admin_users = User.objects.filter(user_type='admin', is_superuser=True)
                print(f"  Found {admin_users.count()} admin users in schema {school.schema_name}")
                
                for user in admin_users:
                    print(f"  Admin user: {user.email}, ID: {user.id}")
        except Exception as e:
            print(f"  Error checking admin users: {str(e)}")
            traceback.print_exc()

def check_registration_requests():
    """Check existing registration requests."""
    requests = SchoolRegistrationRequest.objects.all()
    print(f"\nFound {requests.count()} registration requests")
    
    for req in requests:
        print(f"\nRequest: {req.name} (Status: {req.status})")
        print(f"  Schema: {req.schema_name}")
        print(f"  Domain: {req.domain}")
        print(f"  Admin Email: {req.admin_email}")
        print(f"  Admin Password: {'Set' if req.admin_password else 'Not set'}")
        
        # Check if a school exists for this request
        school = School.objects.filter(schema_name=req.schema_name).first()
        if school:
            print(f"  School exists with ID: {school.id}")
        else:
            print(f"  No school found for this request")

if __name__ == "__main__":
    print("=== Checking Existing Schools and Admin Users ===")
    check_existing_schools()
    
    print("\n=== Checking Registration Requests ===")
    check_registration_requests()
