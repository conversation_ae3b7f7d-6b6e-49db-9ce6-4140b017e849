from rest_framework import serializers
from .models import SiteSettings, BrandingSettings, HeroSection, MaintenanceMode


class SiteSettingsSerializer(serializers.ModelSerializer):
    """Serializer for site settings"""
    
    class Meta:
        model = SiteSettings
        fields = [
            'id', 'site_name', 'site_tagline', 'site_description',
            'contact_email', 'contact_phone', 'contact_address',
            'facebook_url', 'twitter_url', 'linkedin_url', 'youtube_url',
            'meta_title', 'meta_description', 'meta_keywords',
            'enable_registration', 'enable_trial', 'enable_contact_form',
            'enable_blog', 'enable_testimonials',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def update(self, instance, validated_data):
        # Set the updated_by field to the current user
        request = self.context.get('request')
        if request and request.user:
            validated_data['updated_by'] = request.user
        return super().update(instance, validated_data)


class BrandingSettingsSerializer(serializers.ModelSerializer):
    """Serializer for branding settings"""
    
    class Meta:
        model = BrandingSettings
        fields = [
            'id', 'primary_color', 'primary_light', 'primary_dark',
            'secondary_color', 'secondary_light', 'secondary_dark',
            'background_default', 'background_paper',
            'text_primary', 'text_secondary',
            'success_color', 'warning_color', 'error_color', 'info_color',
            'font_family_primary', 'font_family_secondary',
            'border_radius_small', 'border_radius_medium', 'border_radius_large',
            'enable_shadows', 'shadow_intensity', 'custom_css',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def validate_primary_color(self, value):
        """Validate hex color format"""
        if not value.startswith('#') or len(value) != 7:
            raise serializers.ValidationError("Color must be in hex format (#RRGGBB)")
        return value
    
    def validate_border_radius_small(self, value):
        """Validate border radius range"""
        if value < 0 or value > 50:
            raise serializers.ValidationError("Border radius must be between 0 and 50")
        return value
    
    def update(self, instance, validated_data):
        # Set the updated_by field to the current user
        request = self.context.get('request')
        if request and request.user:
            validated_data['updated_by'] = request.user
        return super().update(instance, validated_data)


class HeroSectionSerializer(serializers.ModelSerializer):
    """Serializer for hero section settings"""
    
    class Meta:
        model = HeroSection
        fields = [
            'id', 'title', 'subtitle', 'description',
            'primary_button_text', 'primary_button_url',
            'secondary_button_text', 'secondary_button_url',
            'background_type', 'show_stats', 'show_features',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def validate_title(self, value):
        """Validate title length"""
        if len(value.strip()) < 3:
            raise serializers.ValidationError("Title must be at least 3 characters long")
        return value
    
    def update(self, instance, validated_data):
        # Set the updated_by field to the current user
        request = self.context.get('request')
        if request and request.user:
            validated_data['updated_by'] = request.user
        return super().update(instance, validated_data)


class MaintenanceModeSerializer(serializers.ModelSerializer):
    """Serializer for maintenance mode settings"""
    
    class Meta:
        model = MaintenanceMode
        fields = [
            'id', 'maintenance_mode', 'maintenance_message',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def validate_maintenance_message(self, value):
        """Validate maintenance message"""
        if len(value.strip()) < 10:
            raise serializers.ValidationError("Maintenance message must be at least 10 characters long")
        return value
    
    def update(self, instance, validated_data):
        # Set the updated_by field to the current user
        request = self.context.get('request')
        if request and request.user:
            validated_data['updated_by'] = request.user
        return super().update(instance, validated_data)


class SiteConfigSummarySerializer(serializers.Serializer):
    """Serializer for site configuration summary"""
    site_settings = SiteSettingsSerializer(read_only=True)
    branding_settings = BrandingSettingsSerializer(read_only=True)
    hero_section = HeroSectionSerializer(read_only=True)
    maintenance_mode = MaintenanceModeSerializer(read_only=True)
