import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kelem_sms.settings')
django.setup()

from django.db import connection, transaction
from django_tenants.utils import get_tenant_model

def fix_circular_dependencies():
    """
    Fix circular dependencies between academics, courses, and students apps.
    """
    print("Starting circular dependencies fix...")

    # Get the tenant model
    TenantModel = get_tenant_model()

    # Get all tenants
    tenants = TenantModel.objects.all()

    # Fix circular dependencies for each tenant schema
    for tenant in tenants:
        print(f"Processing tenant: {tenant.name} ({tenant.schema_name})")

        # Connect to the tenant schema
        connection.set_tenant(tenant)

        # Use transaction management to ensure clean state
        with transaction.atomic():
            try:
                # Check if the academics_gradelevel table exists
                with connection.cursor() as cursor:
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables
                            WHERE table_schema = current_schema()
                            AND table_name = 'academics_gradelevel'
                        )
                    """)
                    academics_table_exists = cursor.fetchone()[0]

                if academics_table_exists:
                    print(f"academics_gradelevel table already exists in schema {tenant.schema_name}")
                    continue

                print(f"academics_gradelevel table does not exist in schema {tenant.schema_name}")
                print(f"Handling circular dependencies...")

            # First, make sure the django_migrations table exists
            with connection.cursor() as cursor:
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS django_migrations (
                        id bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
                        app character varying(255) NOT NULL,
                        name character varying(255) NOT NULL,
                        applied timestamp with time zone NOT NULL
                    )
                """)

            # Check if the courses_department table exists
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = current_schema()
                        AND table_name = 'courses_department'
                    )
                """)
                courses_table_exists = cursor.fetchone()[0]

            # If the courses_department table doesn't exist, create it
            if not courses_table_exists:
                print(f"courses_department table does not exist in schema {tenant.schema_name}")
                print(f"Creating courses_department table...")

                # Create the courses_department table
                with connection.cursor() as cursor:
                    cursor.execute("""
                        CREATE TABLE courses_department (
                            id bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
                            name character varying(100) NOT NULL,
                            code character varying(10) NOT NULL UNIQUE,
                            description text,
                            contact_email character varying(254),
                            contact_phone character varying(20),
                            office_location character varying(100),
                            website character varying(200),
                            is_active boolean NOT NULL,
                            created_at timestamp with time zone NOT NULL,
                            head_id bigint REFERENCES auth_user(id) DEFERRABLE INITIALLY DEFERRED
                        )
                    """)

                # Check if the courses_academicterm table exists
                with connection.cursor() as cursor:
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables
                            WHERE table_schema = current_schema()
                            AND table_name = 'courses_academicterm'
                        )
                    """)
                    academicterm_table_exists = cursor.fetchone()[0]

                # If the courses_academicterm table doesn't exist, create it
                if not academicterm_table_exists:
                    print(f"courses_academicterm table does not exist in schema {tenant.schema_name}")
                    print(f"Creating courses_academicterm table...")

                    # Create the courses_academicterm table
                    with connection.cursor() as cursor:
                        cursor.execute("""
                            CREATE TABLE courses_academicterm (
                                id bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
                                name character varying(50) NOT NULL,
                                term character varying(20) NOT NULL,
                                year integer NOT NULL,
                                start_date date NOT NULL,
                                end_date date NOT NULL,
                                is_current boolean NOT NULL
                            )
                        """)

                # Check if the courses_subject table exists
                with connection.cursor() as cursor:
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables
                            WHERE table_schema = current_schema()
                            AND table_name = 'courses_subject'
                        )
                    """)
                    subject_table_exists = cursor.fetchone()[0]

                # If the courses_subject table doesn't exist, create it
                if not subject_table_exists:
                    print(f"courses_subject table does not exist in schema {tenant.schema_name}")
                    print(f"Creating courses_subject table...")

                    # Create the courses_subject table
                    with connection.cursor() as cursor:
                        cursor.execute("""
                            CREATE TABLE courses_subject (
                                id bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
                                name character varying(100) NOT NULL,
                                code character varying(10) NOT NULL UNIQUE,
                                description text,
                                is_active boolean NOT NULL,
                                created_at timestamp with time zone NOT NULL,
                                department_id bigint NOT NULL REFERENCES courses_department(id) DEFERRABLE INITIALLY DEFERRED
                            )
                        """)

                # Check if the courses_course table exists
                with connection.cursor() as cursor:
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables
                            WHERE table_schema = current_schema()
                            AND table_name = 'courses_course'
                        )
                    """)
                    course_table_exists = cursor.fetchone()[0]

                # If the courses_course table doesn't exist, create it
                if not course_table_exists:
                    print(f"courses_course table does not exist in schema {tenant.schema_name}")
                    print(f"Creating courses_course table...")

                    # Create the courses_course table
                    with connection.cursor() as cursor:
                        cursor.execute("""
                            CREATE TABLE courses_course (
                                id bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
                                name character varying(100) NOT NULL,
                                code character varying(10) NOT NULL UNIQUE,
                                credits smallint NOT NULL,
                                description text,
                                syllabus character varying(100),
                                learning_outcomes text,
                                assessment_methods text,
                                is_active boolean NOT NULL,
                                created_at timestamp with time zone NOT NULL,
                                updated_at timestamp with time zone,
                                created_by_id bigint REFERENCES auth_user(id) DEFERRABLE INITIALLY DEFERRED,
                                department_id bigint NOT NULL REFERENCES courses_department(id) DEFERRABLE INITIALLY DEFERRED,
                                subject_id bigint NOT NULL REFERENCES courses_subject(id) DEFERRABLE INITIALLY DEFERRED
                            )
                        """)

                    # Create the courses_course_prerequisites table
                    with connection.cursor() as cursor:
                        cursor.execute("""
                            CREATE TABLE courses_course_prerequisites (
                                id bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
                                from_course_id bigint NOT NULL REFERENCES courses_course(id) DEFERRABLE INITIALLY DEFERRED,
                                to_course_id bigint NOT NULL REFERENCES courses_course(id) DEFERRABLE INITIALLY DEFERRED,
                                CONSTRAINT courses_course_prerequisites_from_course_id_to_course_id_key UNIQUE (from_course_id, to_course_id)
                            )
                        """)

                # Check if the courses_courseoffering table exists
                with connection.cursor() as cursor:
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables
                            WHERE table_schema = current_schema()
                            AND table_name = 'courses_courseoffering'
                        )
                    """)
                    courseoffering_table_exists = cursor.fetchone()[0]

                # If the courses_courseoffering table doesn't exist, create it
                if not courseoffering_table_exists:
                    print(f"courses_courseoffering table does not exist in schema {tenant.schema_name}")
                    print(f"Creating courses_courseoffering table...")

                    # Create the courses_courseoffering table
                    with connection.cursor() as cursor:
                        cursor.execute("""
                            CREATE TABLE courses_courseoffering (
                                id bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
                                section character varying(10) NOT NULL,
                                max_students smallint NOT NULL,
                                current_students smallint NOT NULL,
                                schedule character varying(100),
                                room character varying(50),
                                is_active boolean NOT NULL,
                                created_at timestamp with time zone NOT NULL,
                                academic_term_id bigint NOT NULL REFERENCES courses_academicterm(id) DEFERRABLE INITIALLY DEFERRED,
                                course_id bigint NOT NULL REFERENCES courses_course(id) DEFERRABLE INITIALLY DEFERRED,
                                instructor_id bigint REFERENCES auth_user(id) DEFERRABLE INITIALLY DEFERRED
                            )
                        """)

            # Mark the academics migration as applied
            with connection.cursor() as cursor:
                cursor.execute(
                    "INSERT INTO django_migrations (app, name, applied) VALUES (%s, %s, NOW()) ON CONFLICT DO NOTHING",
                    ['academics', '0001_initial']
                )

            # Mark the courses migration as applied
            with connection.cursor() as cursor:
                cursor.execute(
                    "INSERT INTO django_migrations (app, name, applied) VALUES (%s, %s, NOW()) ON CONFLICT DO NOTHING",
                    ['courses', '0001_initial']
                )
                cursor.execute(
                    "INSERT INTO django_migrations (app, name, applied) VALUES (%s, %s, NOW()) ON CONFLICT DO NOTHING",
                    ['courses', '0002_alter_academicterm_options_alter_course_options_and_more']
                )

            # Create the academics_academicyear table
            with connection.cursor() as cursor:
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS academics_academicyear (
                        id bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
                        name character varying(50) NOT NULL,
                        start_date date NOT NULL,
                        end_date date NOT NULL,
                        is_current boolean NOT NULL
                    )
                """)

            # Create the academics_program table
            with connection.cursor() as cursor:
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS academics_program (
                        id bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
                        name character varying(100) NOT NULL,
                        code character varying(20) NOT NULL UNIQUE,
                        level character varying(20) NOT NULL,
                        description text,
                        duration_years smallint NOT NULL,
                        is_active boolean NOT NULL,
                        created_at timestamp with time zone NOT NULL,
                        updated_at timestamp with time zone NOT NULL,
                        coordinator_id bigint REFERENCES auth_user(id) DEFERRABLE INITIALLY DEFERRED,
                        department_id bigint NOT NULL REFERENCES courses_department(id) DEFERRABLE INITIALLY DEFERRED
                    )
                """)

            # Create the academics_gradelevel table
            with connection.cursor() as cursor:
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS academics_gradelevel (
                        id bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
                        name character varying(50) NOT NULL,
                        code character varying(10) NOT NULL UNIQUE,
                        sequence smallint NOT NULL,
                        description text,
                        program_id bigint NOT NULL REFERENCES academics_program(id) DEFERRABLE INITIALLY DEFERRED
                    )
                """)

            # Create the academics_section table
            with connection.cursor() as cursor:
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS academics_section (
                        id bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
                        name character varying(50) NOT NULL,
                        max_students smallint NOT NULL,
                        room character varying(50),
                        is_active boolean NOT NULL,
                        academic_year_id bigint NOT NULL REFERENCES academics_academicyear(id) DEFERRABLE INITIALLY DEFERRED,
                        grade_level_id bigint NOT NULL REFERENCES academics_gradelevel(id) DEFERRABLE INITIALLY DEFERRED,
                        homeroom_teacher_id bigint REFERENCES auth_user(id) DEFERRABLE INITIALLY DEFERRED
                    )
                """)

            # Create the students_student table if it doesn't exist
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = current_schema()
                        AND table_name = 'students_student'
                    )
                """)
                students_table_exists = cursor.fetchone()[0]

            if not students_table_exists:
                print(f"students_student table does not exist in schema {tenant.schema_name}")
                print(f"Creating students_student table...")

                # Mark the students migration as applied
                with connection.cursor() as cursor:
                    cursor.execute(
                        "INSERT INTO django_migrations (app, name, applied) VALUES (%s, %s, NOW()) ON CONFLICT DO NOTHING",
                        ['students', '0001_initial']
                    )
                    cursor.execute(
                        "INSERT INTO django_migrations (app, name, applied) VALUES (%s, %s, NOW()) ON CONFLICT DO NOTHING",
                        ['students', '0002_alter_guardian_options_alter_student_options_and_more']
                    )

                print(f"Circular dependencies fixed for tenant: {tenant.schema_name}")
            except Exception as e:
                # Roll back the transaction on error
                print(f"Error fixing circular dependencies for tenant {tenant.schema_name}: {str(e)}")
                transaction.set_rollback(True)

    print("Circular dependencies fix completed.")

if __name__ == '__main__':
    fix_circular_dependencies()
