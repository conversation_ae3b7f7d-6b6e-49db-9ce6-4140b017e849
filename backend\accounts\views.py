from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from django.contrib.auth.forms import PasswordResetForm, SetPasswordForm
from django.contrib.auth.tokens import default_token_generator
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.utils.encoding import force_bytes
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings

from .models import User
from .decorators import admin_required, teacher_required, student_required, parent_required, staff_required

@csrf_exempt
@require_POST
def api_login(request):
    """API endpoint for user login."""
    email = request.POST.get('email')
    password = request.POST.get('password')

    if not email or not password:
        return JsonResponse({'success': False, 'message': 'Email and password are required'}, status=400)

    user = authenticate(request, email=email, password=password)

    if user is not None:
        login(request, user)
        return JsonResponse({
            'success': True,
            'user': {
                'id': user.id,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'user_type': user.user_type,
            }
        })
    else:
        return JsonResponse({'success': False, 'message': 'Invalid credentials'}, status=401)

@csrf_exempt
@require_POST
@login_required
def api_logout(request):
    """API endpoint for user logout."""
    logout(request)
    return JsonResponse({'success': True})

@csrf_exempt
@require_POST
def api_password_reset_request(request):
    """API endpoint to request a password reset."""
    email = request.POST.get('email')

    if not email:
        return JsonResponse({'success': False, 'message': 'Email is required'}, status=400)

    try:
        user = User.objects.get(email=email)

        # Generate password reset token
        token = default_token_generator.make_token(user)
        uid = urlsafe_base64_encode(force_bytes(user.pk))

        # Build reset URL
        reset_url = f"{settings.FRONTEND_URL}/reset-password/{uid}/{token}/"

        # Send email
        subject = 'Password Reset Request'
        message = render_to_string('accounts/password_reset_email.html', {
            'user': user,
            'reset_url': reset_url,
        })

        send_mail(
            subject,
            message,
            settings.DEFAULT_FROM_EMAIL,
            [user.email],
            fail_silently=False,
        )

        return JsonResponse({'success': True, 'message': 'Password reset email sent'})
    except User.DoesNotExist:
        # Don't reveal that the user doesn't exist
        return JsonResponse({'success': True, 'message': 'If your email is registered, you will receive a password reset link'})

@csrf_exempt
@require_POST
def api_password_reset_confirm(request):
    """API endpoint to confirm a password reset."""
    uid = request.POST.get('uid')
    token = request.POST.get('token')
    new_password = request.POST.get('new_password')

    if not uid or not token or not new_password:
        return JsonResponse({'success': False, 'message': 'All fields are required'}, status=400)

    try:
        # Decode the user ID
        user_id = urlsafe_base64_decode(uid).decode()
        user = User.objects.get(pk=user_id)

        # Check if the token is valid
        if default_token_generator.check_token(user, token):
            # Set the new password
            user.set_password(new_password)
            user.save()
            return JsonResponse({'success': True, 'message': 'Password has been reset successfully'})
        else:
            return JsonResponse({'success': False, 'message': 'Invalid or expired token'}, status=400)
    except (TypeError, ValueError, OverflowError, User.DoesNotExist):
        return JsonResponse({'success': False, 'message': 'Invalid reset link'}, status=400)

@login_required
def user_profile(request):
    """View for user profile."""
    return render(request, 'accounts/profile.html')

@admin_required
def admin_dashboard(request):
    """Dashboard for administrators."""
    return render(request, 'accounts/admin_dashboard.html')

@teacher_required
def teacher_dashboard(request):
    """Dashboard for teachers."""
    return render(request, 'accounts/teacher_dashboard.html')

@student_required
def student_dashboard(request):
    """Dashboard for students."""
    return render(request, 'accounts/student_dashboard.html')

@parent_required
def parent_dashboard(request):
    """Dashboard for parents."""
    return render(request, 'accounts/parent_dashboard.html')

@staff_required
def staff_dashboard(request):
    """Dashboard for staff members."""
    return render(request, 'accounts/staff_dashboard.html')
