{% extends "base.html" %}

{% block title %}Registration Details{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row">
        <div class="col-md-10 offset-md-1">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h3 class="mb-0">School Registration Details</h3>
                    <a href="{% url 'school_registration:registration_list' %}" class="btn btn-light">Back to List</a>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Registration Status</h5>
                            <div class="d-flex align-items-center">
                                {% if registration.status == 'pending' %}
                                <span class="badge bg-warning me-2">Pending</span>
                                <span>Awaiting review</span>
                                {% elif registration.status == 'approved' %}
                                <span class="badge bg-success me-2">Approved</span>
                                <span>Approved on {{ registration.processed_on|date:"M d, Y" }}</span>
                                {% elif registration.status == 'rejected' %}
                                <span class="badge bg-danger me-2">Rejected</span>
                                <span>Rejected on {{ registration.processed_on|date:"M d, Y" }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <h5>Requested On</h5>
                            <p>{{ registration.requested_on|date:"F d, Y H:i" }}</p>
                        </div>
                    </div>

                    {% if registration.status == 'pending' %}
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="d-flex justify-content-end">
                                <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#approveModal">
                                    Approve Registration
                                </button>
                                <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#rejectModal">
                                    Reject Registration
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    {% if registration.status == 'rejected' and registration.rejection_reason %}
                    <div class="alert alert-danger mb-4">
                        <h5>Rejection Reason:</h5>
                        <p class="mb-0">{{ registration.rejection_reason }}</p>
                    </div>
                    {% endif %}

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">School Information</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <th style="width: 40%">School Name:</th>
                                            <td>{{ registration.name }}</td>
                                        </tr>
                                        <tr>
                                            <th>School Type:</th>
                                            <td>{{ registration.school_type }}</td>
                                        </tr>
                                        <tr>
                                            <th>Established Year:</th>
                                            <td>{{ registration.established_year }}</td>
                                        </tr>
                                        <tr>
                                            <th>Student Capacity:</th>
                                            <td>{{ registration.student_capacity }}</td>
                                        </tr>
                                        <tr>
                                            <th>Description:</th>
                                            <td>{{ registration.description }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">Contact Information</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <th style="width: 40%">Contact Person:</th>
                                            <td>{{ registration.contact_person }}</td>
                                        </tr>
                                        <tr>
                                            <th>Email:</th>
                                            <td>{{ registration.contact_email }}</td>
                                        </tr>
                                        <tr>
                                            <th>Phone:</th>
                                            <td>{{ registration.contact_phone }}</td>
                                        </tr>
                                        <tr>
                                            <th>Website:</th>
                                            <td>
                                                {% if registration.website %}
                                                <a href="{{ registration.website }}" target="_blank">{{ registration.website }}</a>
                                                {% else %}
                                                Not provided
                                                {% endif %}
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">Address Information</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <th style="width: 20%">Address:</th>
                                            <td>{{ registration.address }}</td>
                                        </tr>
                                        <tr>
                                            <th>City:</th>
                                            <td>{{ registration.city }}</td>
                                        </tr>
                                        <tr>
                                            <th>State/Region:</th>
                                            <td>{{ registration.state }}</td>
                                        </tr>
                                        <tr>
                                            <th>Country:</th>
                                            <td>{{ registration.country }}</td>
                                        </tr>
                                        <tr>
                                            <th>Postal Code:</th>
                                            <td>{{ registration.postal_code|default:"Not provided" }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% if registration.status == 'approved' %}
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5 class="mb-0">Tenant Information</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <th style="width: 20%">Schema Name:</th>
                                            <td>{{ registration.schema_name }}</td>
                                        </tr>
                                        <tr>
                                            <th>Domain:</th>
                                            <td>{{ registration.domain }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Approve Modal -->
<div class="modal fade" id="approveModal" tabindex="-1" aria-labelledby="approveModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="approveModalLabel">Approve School Registration</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to approve the registration for <strong>{{ registration.name }}</strong>?</p>
                <p>This will create a new tenant for the school with the following information:</p>
                <ul>
                    <li><strong>Schema Name:</strong> {{ registration.slug }}</li>
                    <li><strong>Domain:</strong> {{ registration.slug }}.kelemsms.com</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="approveButton">Approve</button>
            </div>
        </div>
    </div>
</div>

<!-- Reject Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1" aria-labelledby="rejectModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rejectModalLabel">Reject School Registration</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to reject the registration for <strong>{{ registration.name }}</strong>?</p>
                <div class="mb-3">
                    <label for="rejectionReason" class="form-label">Rejection Reason:</label>
                    <textarea class="form-control" id="rejectionReason" rows="3" required></textarea>
                    <div class="form-text">Please provide a reason for rejecting this registration request.</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="rejectButton">Reject</button>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

        // Approve button
        const approveButton = document.getElementById('approveButton');
        if (approveButton) {
            approveButton.addEventListener('click', function() {
                console.log('Approving registration:', {{ registration.id }});
                const url = '/schools/api/admin/schools/{{ registration.id }}/approve/';
                console.log('Request URL:', url);

                fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrfToken
                    },
                    body: JSON.stringify({})
                })
                .then(response => {
                    console.log('Response status:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Response data:', data);
                    if (data.message) {
                        alert(data.message);
                        window.location.reload();
                    } else if (data.error) {
                        alert('Error: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Fetch error:', error);
                    alert('There was an error processing your request: ' + error.message);
                });
            });
        }

        // Reject button
        const rejectButton = document.getElementById('rejectButton');
        if (rejectButton) {
            rejectButton.addEventListener('click', function() {
                const rejectionReason = document.getElementById('rejectionReason').value;

                if (!rejectionReason.trim()) {
                    alert('Please provide a reason for rejection.');
                    return;
                }

                console.log('Rejecting registration:', {{ registration.id }});
                const url = '/schools/api/admin/schools/{{ registration.id }}/reject/';
                console.log('Request URL:', url);
                console.log('Rejection reason:', rejectionReason);

                fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrfToken
                    },
                    body: JSON.stringify({
                        rejection_reason: rejectionReason
                    })
                })
                .then(response => {
                    console.log('Response status:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Response data:', data);
                    if (data.message) {
                        alert(data.message);
                        window.location.reload();
                    } else if (data.error) {
                        alert('Error: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('There was an error processing your request. Please try again later.');
                    console.error('Error:', error);
                });
            });
        }
    });
</script>
{% endblock %}
{% endblock %}
