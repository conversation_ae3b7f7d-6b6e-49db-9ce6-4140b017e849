"""
URL configuration for the public schema.
"""
from django.contrib import admin
from django.urls import path, include

# Import Swagger URLs
from .swagger_urls import urlpatterns as swagger_urls

urlpatterns = [
    path('admin/', admin.site.urls),
    path('auth/', include('authentication.urls', namespace='authentication')),
    path('schools/', include('school_registration.urls', namespace='school_registration')),
    path('tenant-management/', include('tenant_management.urls', namespace='tenant_management')),
    path('billing-management/', include('billing_management.urls')),
    path('user-management/', include('user_management.urls', namespace='user_management')),
    path('api/site/', include('site_settings.urls', namespace='site_settings')),
    path('api-auth/', include('rest_framework.urls', namespace='rest_framework')),

    # API documentation URLs
    path('api/docs/', include(swagger_urls)),

    # Include students app URLs in the public schema
    path('students/', include('students.urls')),
]
