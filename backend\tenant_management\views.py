from django.shortcuts import render, get_object_or_404
from django.http import HttpResponse
from django.db.models import Count, Sum, Avg, Max, Q as models
from django.utils import timezone
import shutil
import os

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView

from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from tenants.models import School, Domain
from school_registration.models import SchoolRegistrationRequest
from .models import TenantMetrics, TenantActivity, TenantStatus
from .serializers import (
    SchoolSerializer, TenantMetricsSerializer, TenantActivitySerializer,
    TenantStatusSerializer, TenantDetailSerializer, TenantSummarySerializer,
    SystemMetricsSerializer
)
from .services import collect_tenant_metrics, collect_all_tenant_metrics, get_system_metrics, get_tenant_summary

class IsSuperAdmin(permissions.BasePermission):
    """Permission to check if the user is a super admin."""

    def has_permission(self, request, view):
        # Check if the user is authenticated and is a superuser
        if not request.user.is_authenticated:
            print(f"User not authenticated")
            return False

        is_super = getattr(request.user, 'is_superuser', False)

        # Also check if user is in public schema (for multi-tenant setup)
        schema_name = getattr(request.user, 'schema_name', None)
        is_public_schema = not schema_name or schema_name == 'public'

        # Log the authentication status for debugging
        print(f"User authenticated: {request.user.is_authenticated}")
        print(f"User: {request.user.username if hasattr(request.user, 'username') else 'Unknown'}")
        print(f"Is superuser: {is_super}")
        print(f"Schema: {schema_name}")
        print(f"Is public schema: {is_public_schema}")
        print(f"Auth header: {request.headers.get('Authorization', 'None')}")

        return is_super and is_public_schema

class TenantViewSet(viewsets.ModelViewSet):
    """API endpoint for managing tenants."""
    queryset = School.objects.all().prefetch_related('domains', 'status')
    serializer_class = SchoolSerializer
    permission_classes = [IsSuperAdmin]



    @swagger_auto_schema(
        operation_summary="List all tenants",
        operation_description="Returns a list of all tenants in the system.",
        manual_parameters=[
            openapi.Parameter('search', openapi.IN_QUERY, description="Search term for name, domain, or email", type=openapi.TYPE_STRING),
            openapi.Parameter('status', openapi.IN_QUERY, description="Filter by status", type=openapi.TYPE_STRING),
            openapi.Parameter('subscription_plan', openapi.IN_QUERY, description="Filter by subscription plan", type=openapi.TYPE_STRING),
            openapi.Parameter('include_archived', openapi.IN_QUERY, description="Include archived tenants", type=openapi.TYPE_BOOLEAN),
        ]
    )
    def list(self, request, *args, **kwargs):
        # Get existing tenants
        tenant_queryset = self.get_queryset()

        # Apply search filter to tenants
        search = request.query_params.get('search', None)
        if search:
            tenant_queryset = tenant_queryset.filter(
                models.Q(name__icontains=search) |
                models.Q(domains__domain__icontains=search) |
                models.Q(contact_email__icontains=search)
            ).distinct()

        # Apply status filter to tenants
        status_filter = request.query_params.get('status', None)
        if status_filter and status_filter != 'pending':
            tenant_queryset = tenant_queryset.filter(status__status=status_filter)

        # Apply subscription plan filter
        plan_filter = request.query_params.get('subscription_plan', None)
        if plan_filter:
            tenant_queryset = tenant_queryset.filter(status__subscription_plan=plan_filter)

        # Apply archived filter
        include_archived_param = request.query_params.get('include_archived', 'false')
        include_archived = include_archived_param.lower() == 'true' if isinstance(include_archived_param, str) else include_archived_param

        if not include_archived:
            tenant_queryset = tenant_queryset.filter(is_archived=False)

        # Get pending registrations
        pending_registrations = SchoolRegistrationRequest.objects.filter(status='pending')
        if search:
            pending_registrations = pending_registrations.filter(
                models.Q(name__icontains=search) |
                models.Q(contact_email__icontains=search)
            )

        # Serialize existing tenants
        tenant_serializer = self.get_serializer(tenant_queryset, many=True)
        tenant_data = tenant_serializer.data

        # Add pending registrations to the results if not filtering by non-pending status
        if not status_filter or status_filter == 'pending':
            for registration in pending_registrations:
                tenant_data.append({
                    'id': f'pending_{registration.id}',
                    'name': registration.name,
                    'schema_name': None,
                    'address': registration.address,
                    'contact_email': registration.contact_email,
                    'contact_phone': registration.contact_phone,
                    'domains': [],
                    'domain_url': None,
                    'status': 'pending',
                    'subscription_plan': None,
                    'contact_person': registration.contact_person,
                    'created_on': registration.requested_on.isoformat(),
                    'is_archived': False,
                    'registration_id': registration.id,
                })

        # If filtering specifically for pending, only return pending registrations
        if status_filter == 'pending':
            tenant_data = [item for item in tenant_data if item['status'] == 'pending']

        # Return the combined data
        return Response({
            'count': len(tenant_data),
            'next': None,
            'previous': None,
            'results': tenant_data
        })

    @swagger_auto_schema(
        operation_summary="Retrieve a tenant",
        operation_description="Returns detailed information about a specific tenant."
    )
    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = TenantDetailSerializer(instance)
        return Response(serializer.data)

    @swagger_auto_schema(
        operation_summary="Create a tenant",
        operation_description="Creates a new tenant in the system."
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Update a tenant",
        operation_description="Updates an existing tenant's information."
    )
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Partially update a tenant",
        operation_description="Partially updates an existing tenant's information."
    )
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Delete a tenant",
        operation_description="Deletes a tenant from the system."
    )
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)

    def get_serializer_class(self):
        if self.action == 'retrieve':
            return TenantDetailSerializer
        return SchoolSerializer

    @swagger_auto_schema(
        operation_summary="Collect metrics for a tenant",
        operation_description="Collects usage metrics for a specific tenant.",
        responses={
            200: openapi.Response(
                description="Metrics collected successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'metrics': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'tenant': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'tenant_name': openapi.Schema(type=openapi.TYPE_STRING),
                                'tenant_schema': openapi.Schema(type=openapi.TYPE_STRING),
                                'timestamp': openapi.Schema(type=openapi.TYPE_STRING, format='date-time'),
                                'total_users': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'active_users': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'database_size': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'file_storage_size': openapi.Schema(type=openapi.TYPE_INTEGER)
                            }
                        )
                    }
                )
            ),
            500: openapi.Response(
                description="Failed to collect metrics",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'error': openapi.Schema(type=openapi.TYPE_STRING)
                    }
                )
            )
        }
    )
    @action(detail=True, methods=['post'])
    def collect_metrics(self, request, pk=None):
        """Collect metrics for a specific tenant."""
        tenant = self.get_object()
        metrics = collect_tenant_metrics(tenant.id)

        if metrics:
            return Response({
                'message': f'Metrics collected successfully for {tenant.name}',
                'metrics': TenantMetricsSerializer(metrics[0]).data
            })
        else:
            return Response({
                'error': f'Failed to collect metrics for {tenant.name}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @swagger_auto_schema(
        operation_summary="Suspend a tenant",
        operation_description="Suspends a tenant, preventing users from accessing it.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'notes': openapi.Schema(type=openapi.TYPE_STRING, description="Reason for suspension")
            }
        ),
        responses={
            200: openapi.Response(
                description="Tenant suspended successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'status': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'tenant': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'tenant_name': openapi.Schema(type=openapi.TYPE_STRING),
                                'status': openapi.Schema(type=openapi.TYPE_STRING),
                                'subscription_plan': openapi.Schema(type=openapi.TYPE_STRING),
                                'max_users': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'max_storage': openapi.Schema(type=openapi.TYPE_INTEGER)
                            }
                        )
                    }
                )
            )
        }
    )
    @action(detail=True, methods=['post'])
    def suspend(self, request, pk=None):
        """Suspend a tenant."""
        tenant = self.get_object()
        tenant_status, created = TenantStatus.objects.get_or_create(
            tenant=tenant,
            defaults={
                'status': 'active',
                'admin_email': tenant.contact_email
            }
        )

        tenant_status.status = 'suspended'
        tenant_status.notes = request.data.get('notes', 'Suspended by administrator')
        tenant_status.save()

        TenantActivity.objects.create(
            tenant=tenant,
            activity_type='tenant_suspended',
            description=f'Tenant suspended: {tenant_status.notes}'
        )

        return Response({
            'message': f'Tenant {tenant.name} has been suspended',
            'status': TenantStatusSerializer(tenant_status).data
        })

    @swagger_auto_schema(
        operation_summary="Activate a tenant",
        operation_description="Activates a suspended tenant, allowing users to access it again.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'notes': openapi.Schema(type=openapi.TYPE_STRING, description="Notes about activation")
            }
        ),
        responses={
            200: openapi.Response(
                description="Tenant activated successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'status': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'tenant': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'tenant_name': openapi.Schema(type=openapi.TYPE_STRING),
                                'status': openapi.Schema(type=openapi.TYPE_STRING),
                                'subscription_plan': openapi.Schema(type=openapi.TYPE_STRING),
                                'max_users': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'max_storage': openapi.Schema(type=openapi.TYPE_INTEGER)
                            }
                        )
                    }
                )
            )
        }
    )
    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """Activate a tenant."""
        tenant = self.get_object()
        tenant_status, created = TenantStatus.objects.get_or_create(
            tenant=tenant,
            defaults={
                'status': 'active',
                'admin_email': tenant.contact_email
            }
        )

        tenant_status.status = 'active'
        tenant_status.notes = request.data.get('notes', 'Activated by administrator')
        tenant_status.save()

        TenantActivity.objects.create(
            tenant=tenant,
            activity_type='tenant_activated',
            description=f'Tenant activated: {tenant_status.notes}'
        )

        return Response({
            'message': f'Tenant {tenant.name} has been activated',
            'status': TenantStatusSerializer(tenant_status).data
        })

    @swagger_auto_schema(
        operation_summary="Update subscription details",
        operation_description="Updates subscription details for a tenant, including plan, dates, and limits.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'subscription_plan': openapi.Schema(type=openapi.TYPE_STRING, description="Subscription plan name"),
                'subscription_start_date': openapi.Schema(type=openapi.TYPE_STRING, format='date', description="Start date of subscription"),
                'subscription_end_date': openapi.Schema(type=openapi.TYPE_STRING, format='date', description="End date of subscription"),
                'max_users': openapi.Schema(type=openapi.TYPE_INTEGER, description="Maximum number of users allowed"),
                'max_storage': openapi.Schema(type=openapi.TYPE_INTEGER, description="Maximum storage allowed in bytes")
            }
        ),
        responses={
            200: openapi.Response(
                description="Subscription updated successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'status': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'tenant': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'tenant_name': openapi.Schema(type=openapi.TYPE_STRING),
                                'status': openapi.Schema(type=openapi.TYPE_STRING),
                                'subscription_plan': openapi.Schema(type=openapi.TYPE_STRING),
                                'max_users': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'max_storage': openapi.Schema(type=openapi.TYPE_INTEGER)
                            }
                        )
                    }
                )
            )
        }
    )
    @action(detail=True, methods=['post'])
    def update_subscription(self, request, pk=None):
        """Update subscription details for a tenant."""
        tenant = self.get_object()
        tenant_status, created = TenantStatus.objects.get_or_create(
            tenant=tenant,
            defaults={
                'status': 'active',
                'admin_email': tenant.contact_email
            }
        )

        # Update subscription details
        subscription_plan = request.data.get('subscription_plan')
        if subscription_plan:
            tenant_status.subscription_plan = subscription_plan

        subscription_start_date = request.data.get('subscription_start_date')
        if subscription_start_date:
            tenant_status.subscription_start_date = subscription_start_date

        subscription_end_date = request.data.get('subscription_end_date')
        if subscription_end_date:
            tenant_status.subscription_end_date = subscription_end_date

        max_users = request.data.get('max_users')
        if max_users:
            tenant_status.max_users = max_users

        max_storage = request.data.get('max_storage')
        if max_storage:
            tenant_status.max_storage = max_storage

        tenant_status.save()

        TenantActivity.objects.create(
            tenant=tenant,
            activity_type='subscription_updated',
            description=f'Subscription updated to {tenant_status.subscription_plan}'
        )

        return Response({
            'message': f'Subscription updated for {tenant.name}',
            'status': TenantStatusSerializer(tenant_status).data
        })

class TenantMetricsViewSet(viewsets.ReadOnlyModelViewSet):
    """API endpoint for tenant metrics."""
    queryset = TenantMetrics.objects.all().order_by('-timestamp')
    serializer_class = TenantMetricsSerializer
    permission_classes = [IsSuperAdmin]

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by tenant
        tenant_id = self.request.query_params.get('tenant_id')
        if tenant_id:
            queryset = queryset.filter(tenant_id=tenant_id)

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        if start_date:
            queryset = queryset.filter(timestamp__gte=start_date)

        end_date = self.request.query_params.get('end_date')
        if end_date:
            queryset = queryset.filter(timestamp__lte=end_date)

        return queryset

class TenantActivityViewSet(viewsets.ReadOnlyModelViewSet):
    """API endpoint for tenant activities."""
    queryset = TenantActivity.objects.all().order_by('-timestamp')
    serializer_class = TenantActivitySerializer
    permission_classes = [IsSuperAdmin]

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by tenant
        tenant_id = self.request.query_params.get('tenant_id')
        if tenant_id:
            queryset = queryset.filter(tenant_id=tenant_id)

        # Filter by activity type
        activity_type = self.request.query_params.get('activity_type')
        if activity_type:
            queryset = queryset.filter(activity_type=activity_type)

        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        if start_date:
            queryset = queryset.filter(timestamp__gte=start_date)

        end_date = self.request.query_params.get('end_date')
        if end_date:
            queryset = queryset.filter(timestamp__lte=end_date)

        return queryset

class TenantStatusViewSet(viewsets.ModelViewSet):
    """API endpoint for tenant statuses."""
    queryset = TenantStatus.objects.all().order_by('-last_updated')
    serializer_class = TenantStatusSerializer
    permission_classes = [IsSuperAdmin]

    def create(self, request, *args, **kwargs):
        """Custom create method with debugging."""
        print("CUSTOM CREATE METHOD CALLED!!!")
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(
        operation_summary="Remove tenant status",
        operation_description="Removes a tenant status record.",
        responses={
            200: openapi.Response(
                description="Tenant status removed successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                    }
                )
            ),
            404: 'Tenant status not found'
        }
    )
    @action(detail=True, methods=['post'])
    def remove(self, request, pk=None):
        """Remove a tenant status record."""
        tenant_status = self.get_object()
        tenant_id = tenant_status.tenant.id
        tenant_name = tenant_status.tenant.name if hasattr(tenant_status.tenant, 'name') else f'Tenant {tenant_id}'

        try:
            # Delete the tenant status
            tenant_status.delete()

            return Response({
                'message': f'Tenant status for {tenant_name} (ID: {tenant_id}) has been removed',
            })

        except Exception as e:
            import traceback
            print(f"Error removing tenant status: {str(e)}")
            print(traceback.format_exc())

            return Response(
                {'message': f'Failed to remove tenant status: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by tenant
        tenant_id = self.request.query_params.get('tenant')
        if tenant_id:
            queryset = queryset.filter(tenant_id=tenant_id)

        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        return queryset

    @swagger_auto_schema(
        operation_summary="Suspend subscription",
        operation_description="Suspends a tenant's subscription.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'reason': openapi.Schema(type=openapi.TYPE_STRING, description='Reason for suspension')
            },
            required=['reason']
        )
    )
    @action(detail=True, methods=['post'], url_path='suspend-subscription')
    def suspend_subscription(self, request, pk=None):
        """Suspend a tenant's subscription."""
        tenant_status = self.get_object()
        reason = request.data.get('reason')

        if not reason:
            return Response(
                {'message': 'Reason for suspension is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        tenant_status.status = 'suspended'
        tenant_status.notes = f"Subscription suspended: {reason}"
        tenant_status.save()

        # Create activity record
        TenantActivity.objects.create(
            tenant=tenant_status.tenant,
            activity_type='subscription_suspended',
            description=f'Subscription suspended: {reason}'
        )

        return Response({
            'message': f'Subscription for {tenant_status.tenant.name} has been suspended',
            'status': TenantStatusSerializer(tenant_status).data
        })

    @swagger_auto_schema(
        operation_summary="Activate subscription",
        operation_description="Activates a suspended tenant's subscription."
    )
    @action(detail=True, methods=['post'], url_path='activate-subscription')
    def activate_subscription(self, request, pk=None):
        """Activate a tenant's subscription."""
        tenant_status = self.get_object()

        tenant_status.status = 'active'
        tenant_status.notes = "Subscription activated"
        tenant_status.save()

        # Create activity record
        TenantActivity.objects.create(
            tenant=tenant_status.tenant,
            activity_type='subscription_activated',
            description='Subscription activated'
        )

        return Response({
            'message': f'Subscription for {tenant_status.tenant.name} has been activated',
            'status': TenantStatusSerializer(tenant_status).data
        })

    @swagger_auto_schema(
        operation_summary="Archive a tenant",
        operation_description="Archives a tenant, making it inaccessible but preserving its data.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'reason': openapi.Schema(type=openapi.TYPE_STRING, description='Reason for archiving the tenant'),
                'confirm': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Confirmation flag')
            },
            required=['reason', 'confirm']
        ),
        responses={
            200: openapi.Response(
                description="Tenant archived successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'tenant': openapi.Schema(type=openapi.TYPE_OBJECT)
                    }
                )
            ),
            400: 'Bad request if confirmation is missing',
            404: 'Tenant not found'
        }
    )
    @action(detail=True, methods=['post'])
    def archive(self, request, pk=None):
        """Archive a tenant."""
        tenant = self.get_object()

        # Check if confirmation is provided
        confirm = request.data.get('confirm')
        if not confirm:
            return Response(
                {'message': 'Confirmation is required to archive a tenant'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get reason for archiving
        reason = request.data.get('reason')
        if not reason:
            return Response(
                {'message': 'Reason is required to archive a tenant'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update tenant status to archived
        tenant_status, created = TenantStatus.objects.get_or_create(
            tenant=tenant,
            defaults={
                'status': 'active',
                'admin_email': tenant.contact_email or '',
            }
        )
        tenant_status.status = 'archived'
        tenant_status.notes = f"Archived: {reason}"
        tenant_status.save()

        # Update tenant model
        tenant.is_archived = True
        tenant.archived_on = timezone.now()
        tenant.archive_reason = reason
        tenant.save()

        # Create activity record
        TenantActivity.objects.create(
            tenant=tenant,
            activity_type='tenant_archived',
            description=f'Tenant archived: {reason}'
        )

        return Response({
            'message': f'Tenant {tenant.name} has been archived',
            'tenant': SchoolSerializer(tenant).data
        })

    @swagger_auto_schema(
        operation_summary="Unarchive a tenant",
        operation_description="Unarchives a tenant, making it accessible again.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'notes': openapi.Schema(type=openapi.TYPE_STRING, description='Notes about unarchiving')
            }
        ),
        responses={
            200: openapi.Response(
                description="Tenant unarchived successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'tenant': openapi.Schema(type=openapi.TYPE_OBJECT)
                    }
                )
            ),
            404: 'Tenant not found'
        }
    )
    @action(detail=True, methods=['post'])
    def unarchive(self, request, pk=None):
        """Unarchive a tenant."""
        tenant = self.get_object()

        # Check if tenant is archived
        if not tenant.is_archived:
            return Response(
                {'message': 'Tenant is not archived'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update tenant status to active
        tenant_status, created = TenantStatus.objects.get_or_create(
            tenant=tenant,
            defaults={
                'status': 'active',
                'admin_email': tenant.contact_email or '',
            }
        )
        tenant_status.status = 'active'

        # Add notes if provided
        notes = request.data.get('notes')
        if notes:
            tenant_status.notes = notes
        else:
            tenant_status.notes = "Tenant unarchived"

        tenant_status.save()

        # Update tenant model
        tenant.is_archived = False
        tenant.archived_on = None
        tenant.archive_reason = None
        tenant.save()

        # Create activity record
        TenantActivity.objects.create(
            tenant=tenant,
            activity_type='tenant_unarchived',
            description=f'Tenant unarchived{f": {notes}" if notes else ""}'
        )

        return Response({
            'message': f'Tenant {tenant.name} has been unarchived',
            'tenant': SchoolSerializer(tenant).data
        })

    @swagger_auto_schema(
        operation_summary="Remove a tenant",
        operation_description="Permanently removes a tenant and all its data. This action cannot be undone.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'confirm_name': openapi.Schema(type=openapi.TYPE_STRING, description='Type the tenant name to confirm deletion'),
                'confirm_permanent': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Confirm permanent deletion'),
                'create_backup': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Create a backup before deletion')
            },
            required=['confirm_name', 'confirm_permanent']
        ),
        responses={
            200: openapi.Response(
                description="Tenant removed successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING)
                    }
                )
            ),
            400: 'Bad request if confirmation is incorrect',
            404: 'Tenant not found'
        }
    )
    @action(detail=True, methods=['post'])
    def remove(self, request, pk=None):
        """Permanently remove a tenant."""
        tenant = self.get_object()

        # Check if tenant name confirmation is correct
        confirm_name = request.data.get('confirm_name')
        tenant_name = tenant.name if hasattr(tenant, 'name') else f'Tenant {tenant.id}'
        print(f"Received confirm_name: '{confirm_name}', tenant.name: '{tenant_name}'")

        if not confirm_name:
            return Response(
                {'message': 'Confirmation name is required.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Case-insensitive comparison
        if confirm_name.lower() != tenant_name.lower():
            return Response(
                {'message': f'Confirmation name does not match tenant name. Please type "{tenant_name}" to confirm.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if permanent deletion is confirmed
        confirm_permanent = request.data.get('confirm_permanent')
        if not confirm_permanent:
            return Response(
                {'message': 'You must confirm that you understand this action is permanent and cannot be undone.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create backup if requested
        create_backup = request.data.get('create_backup', False)
        backup_path = None
        if create_backup:
            try:
                # This is a placeholder for actual backup logic
                # In a real implementation, you would create a proper backup of the tenant's data
                from django.conf import settings
                import datetime

                backup_dir = os.path.join(settings.BASE_DIR, 'backups')
                os.makedirs(backup_dir, exist_ok=True)

                backup_filename = f"{tenant.schema_name}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                backup_path = os.path.join(backup_dir, backup_filename)

                # This is just a placeholder - you would implement proper backup logic here
                with open(backup_path, 'w') as f:
                    f.write(f"Backup of tenant {tenant.name} created on {datetime.datetime.now().isoformat()}")

            except Exception as e:
                return Response(
                    {'message': f'Failed to create backup: {str(e)}'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

        # Store tenant info for the response
        tenant_name = tenant.name
        tenant_id = tenant.id

        try:
            # Create activity record before deletion
            TenantActivity.objects.create(
                tenant=tenant,
                activity_type='tenant_removed',
                description=f'Tenant permanently removed'
            )

            # Log the tenant information before deletion
            print(f"Attempting to delete tenant: {tenant_name} (ID: {tenant_id})")

            # Delete the tenant with force_drop=True to ensure schema is dropped
            # This will cascade delete all related objects including domains and status
            # and drop the schema
            tenant.delete(force_drop=True)

            print(f"Tenant {tenant_name} successfully deleted")

            return Response({
                'message': f'Tenant {tenant_name} (ID: {tenant_id}) has been permanently removed',
                'backup_created': create_backup,
                'backup_path': backup_path if create_backup else None
            })

        except Exception as e:
            import traceback
            print(f"Error removing tenant: {str(e)}")
            print(traceback.format_exc())

            return Response(
                {'message': f'Failed to remove tenant: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'], url_path='approve-registration/(?P<registration_id>[^/.]+)')
    def approve_registration(self, request, registration_id=None):
        """Approve a pending school registration"""
        try:
            registration = SchoolRegistrationRequest.objects.get(id=registration_id, status='pending')

            # Import the approval method from school_registration views
            from school_registration.views import SchoolRegistrationViewSet
            viewset = SchoolRegistrationViewSet()

            # Call the auto_approve_registration method
            tenant_info = viewset.auto_approve_registration(registration)

            return Response({
                'message': 'Registration approved successfully',
                'tenant': tenant_info
            })

        except SchoolRegistrationRequest.DoesNotExist:
            return Response(
                {'error': 'Registration not found or already processed'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'error': f'Failed to approve registration: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['post'], url_path='reject-registration/(?P<registration_id>[^/.]+)')
    def reject_registration(self, request, registration_id=None):
        """Reject a pending school registration"""
        try:
            registration = SchoolRegistrationRequest.objects.get(id=registration_id, status='pending')
            registration.status = 'rejected'
            registration.rejection_reason = request.data.get('reason', 'No reason provided')
            registration.save()

            return Response({'message': 'Registration rejected successfully'})

        except SchoolRegistrationRequest.DoesNotExist:
            return Response(
                {'error': 'Registration not found or already processed'},
                status=status.HTTP_404_NOT_FOUND
            )


class DashboardView(APIView):
    """API endpoint for dashboard data."""
    permission_classes = [IsSuperAdmin]

    @swagger_auto_schema(
        operation_summary="Get dashboard data",
        operation_description="Returns aggregated data for the tenant management dashboard, including tenant summary, system metrics, and recent activities.",
        responses={
            200: openapi.Response(
                description="Dashboard data retrieved successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'tenant_summary': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'total_tenants': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'active_tenants': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'suspended_tenants': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'trial_tenants': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'expired_tenants': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'total_users': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'total_students': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'total_courses': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'total_storage': openapi.Schema(type=openapi.TYPE_INTEGER),
                            }
                        ),
                        'system_metrics': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'cpu_percent': openapi.Schema(type=openapi.TYPE_NUMBER),
                                'memory_percent': openapi.Schema(type=openapi.TYPE_NUMBER),
                                'disk_percent': openapi.Schema(type=openapi.TYPE_NUMBER),
                                'db_connections': openapi.Schema(type=openapi.TYPE_INTEGER),
                            }
                        ),
                        'recent_activities': openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=openapi.Schema(type=openapi.TYPE_OBJECT)
                        )
                    }
                )
            )
        }
    )
    def get(self, request, format=None):
        """Get dashboard data."""
        try:
            print(f"Dashboard API called by user: {request.user}")

            # Get tenant summary
            tenant_summary = get_tenant_summary()
            print(f"Tenant summary: {tenant_summary}")

            # Get system metrics
            system_metrics = get_system_metrics()
            print(f"System metrics: {system_metrics}")

            # Get recent activities
            recent_activities = TenantActivity.objects.all().order_by('-timestamp')[:10]
            print(f"Recent activities count: {recent_activities.count()}")

            response_data = {
                'tenant_summary': tenant_summary,
                'system_metrics': system_metrics,
                'recent_activities': TenantActivitySerializer(recent_activities, many=True).data
            }

            print(f"Returning dashboard data successfully")
            return Response(response_data)

        except Exception as e:
            import traceback
            print(f"Error in dashboard API: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")

            # Return a default response instead of failing
            return Response({
                'tenant_summary': {
                    'total_tenants': 0,
                    'active_tenants': 0,
                    'suspended_tenants': 0,
                    'trial_tenants': 0,
                    'expired_tenants': 0,
                    'total_users': 0,
                    'total_students': 0,
                    'total_courses': 0,
                    'total_storage': 0,
                    'top_tenants_by_users': [],
                    'top_tenants_by_storage': [],
                },
                'system_metrics': {
                    'timestamp': timezone.now().isoformat(),
                    'cpu_percent': 0,
                    'memory_percent': 0,
                    'memory_used': 0,
                    'memory_total': 0,
                    'disk_percent': 0,
                    'disk_used': 0,
                    'disk_total': 0,
                    'db_connections': 0,
                    'bytes_sent': 0,
                    'bytes_recv': 0,
                },
                'recent_activities': [],
                'error': str(e)
            }, status=status.HTTP_200_OK)

class CollectAllMetricsView(APIView):
    """API endpoint to collect metrics for all tenants."""
    permission_classes = [IsSuperAdmin]

    @swagger_auto_schema(
        operation_summary="Collect metrics for all tenants",
        operation_description="Collects usage metrics for all tenants in the system.",
        responses={
            200: openapi.Response(
                description="Metrics collected successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'count': openapi.Schema(type=openapi.TYPE_INTEGER)
                    }
                )
            )
        }
    )
    def post(self, request, format=None):
        """Collect metrics for all tenants."""
        metrics = collect_all_tenant_metrics()

        return Response({
            'message': f'Metrics collected successfully for {len(metrics)} tenants',
            'count': len(metrics)
        })

# Template views for the dashboard
def dashboard(request):
    """Render the dashboard template."""
    if not request.user.is_superuser:
        return HttpResponse("Unauthorized", status=401)

    return render(request, 'tenant_management/dashboard.html')

def tenant_list(request):
    """Render the tenant list template."""
    if not request.user.is_superuser:
        return HttpResponse("Unauthorized", status=401)

    tenants = School.objects.all()
    return render(request, 'tenant_management/tenant_list.html', {'tenants': tenants})

def tenant_detail(request, tenant_id):
    """Render the tenant detail template."""
    if not request.user.is_superuser:
        return HttpResponse("Unauthorized", status=401)

    tenant = get_object_or_404(School, id=tenant_id)
    tenant_status, created = TenantStatus.objects.get_or_create(
        tenant=tenant,
        defaults={
            'status': 'active',
            'admin_email': tenant.contact_email
        }
    )

    metrics = TenantMetrics.objects.filter(tenant=tenant).order_by('-timestamp').first()
    activities = TenantActivity.objects.filter(tenant=tenant).order_by('-timestamp')[:10]

    return render(request, 'tenant_management/tenant_detail.html', {
        'tenant': tenant,
        'status': tenant_status,
        'metrics': metrics,
        'activities': activities
    })
