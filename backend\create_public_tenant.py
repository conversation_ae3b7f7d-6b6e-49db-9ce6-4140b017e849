import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kelem_sms.settings')
django.setup()

from tenants.models import School, Domain
from django.db import connection

def create_public_tenant():
    """Create the public tenant if it doesn't exist."""
    print("Checking if public tenant exists...")
    
    # Check if the public tenant exists
    public_tenant = School.objects.filter(schema_name='public').first()
    
    if public_tenant:
        print(f"Public tenant already exists with ID: {public_tenant.id}")
        print(f"Name: {public_tenant.name}")
        print(f"Schema name: {public_tenant.schema_name}")
        
        # Check if the public tenant has a domain
        domains = public_tenant.domains.all()
        if domains.exists():
            print(f"Public tenant has {domains.count()} domain(s):")
            for domain in domains:
                print(f"  - {domain.domain}")
        else:
            print("Public tenant has no domains. Creating default domain...")
            Domain.objects.create(
                domain='localhost',
                tenant=public_tenant,
                is_primary=True
            )
            print("Created default domain 'localhost' for public tenant.")
        
        return public_tenant
    
    # Create the public tenant
    print("Public tenant does not exist. Creating...")
    
    # Create the public tenant
    public_tenant = School.objects.create(
        name='Public',
        schema_name='public',
        # Add any other required fields
        address='System',
        city='System',
        state='System',
        country='System',
        postal_code='00000',
        phone_number='0000000000',
        email='<EMAIL>',
        website='http://localhost',
        status='active',
    )
    
    # Create a domain for the public tenant
    Domain.objects.create(
        domain='localhost',
        tenant=public_tenant,
        is_primary=True
    )
    
    print(f"Created public tenant with ID: {public_tenant.id}")
    print(f"Name: {public_tenant.name}")
    print(f"Schema name: {public_tenant.schema_name}")
    print(f"Domain: localhost")
    
    return public_tenant

if __name__ == "__main__":
    create_public_tenant()
