import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Avatar,
  Button,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  useTheme,
  useMediaQuery,
  Chip,
} from '@mui/material'
import {
  School as SchoolIcon,
  People as PeopleIcon,
  TrendingUp as TrendingUpIcon,
  Security as SecurityIcon,
  Support as SupportIcon,
  Language as LanguageIcon,
  CheckCircle as CheckCircleIcon,
  Star as StarIcon,
  LinkedIn as LinkedInIcon,
  Twitter as TwitterIcon,
  Email as EmailIcon,
} from '@mui/icons-material'

const AboutPage = () => {
  const navigate = useNavigate()
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))

  const stats = [
    { number: '50+', label: 'Schools Served', icon: <SchoolIcon /> },
    { number: '10,000+', label: 'Students Managed', icon: <PeopleIcon /> },
    { number: '1,000+', label: 'Teachers Empowered', icon: <SupportIcon /> },
    { number: '99.9%', label: 'Uptime Guarantee', icon: <TrendingUpIcon /> },
  ]

  const values = [
    {
      title: 'Innovation',
      description: 'We continuously innovate to provide cutting-edge solutions for modern educational challenges.',
      icon: <TrendingUpIcon color="primary" sx={{ fontSize: 40 }} />,
    },
    {
      title: 'Accessibility',
      description: 'Making quality education management tools accessible to schools of all sizes across Ethiopia.',
      icon: <LanguageIcon color="primary" sx={{ fontSize: 40 }} />,
    },
    {
      title: 'Security',
      description: 'Your data security is our top priority with enterprise-grade protection and privacy measures.',
      icon: <SecurityIcon color="primary" sx={{ fontSize: 40 }} />,
    },
    {
      title: 'Support',
      description: 'Dedicated customer support to ensure your success with comprehensive training and assistance.',
      icon: <SupportIcon color="primary" sx={{ fontSize: 40 }} />,
    },
  ]

  const team = [
    {
      name: 'Dr. Abebe Tadesse',
      role: 'Founder & CEO',
      bio: 'Former education administrator with 15+ years of experience in Ethiopian education system.',
      avatar: '/api/placeholder/150/150',
      social: {
        linkedin: '#',
        twitter: '#',
        email: '<EMAIL>'
      }
    },
    {
      name: 'Meron Haile',
      role: 'CTO',
      bio: 'Software engineer with expertise in educational technology and scalable systems.',
      avatar: '/api/placeholder/150/150',
      social: {
        linkedin: '#',
        twitter: '#',
        email: '<EMAIL>'
      }
    },
    {
      name: 'Dawit Bekele',
      role: 'Head of Product',
      bio: 'Product manager focused on creating intuitive solutions for educational institutions.',
      avatar: '/api/placeholder/150/150',
      social: {
        linkedin: '#',
        twitter: '#',
        email: '<EMAIL>'
      }
    },
    {
      name: 'Sara Mohammed',
      role: 'Head of Customer Success',
      bio: 'Dedicated to ensuring schools achieve their goals with our platform.',
      avatar: '/api/placeholder/150/150',
      social: {
        linkedin: '#',
        twitter: '#',
        email: '<EMAIL>'
      }
    },
  ]

  const milestones = [
    { year: '2020', event: 'Kelem SMS founded with a vision to digitize Ethiopian schools' },
    { year: '2021', event: 'First 10 schools onboarded, platform officially launched' },
    { year: '2022', event: 'Reached 25 schools, introduced mobile app and parent portal' },
    { year: '2023', event: 'Expanded to 40+ schools, launched advanced analytics features' },
    { year: '2024', event: 'Serving 50+ schools with 10,000+ students across Ethiopia' },
  ]

  const handleGetStarted = () => {
    navigate('/register-school')
  }

  const handleContactUs = () => {
    navigate('/contact')
  }

  return (
    <Box sx={{ bgcolor: 'background.default', minHeight: '100vh' }}>
      {/* Hero Section */}
      <Box
        sx={{
          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
          color: 'white',
          py: { xs: 8, md: 12 },
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <Container maxWidth="lg">
          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12} md={6}>
              <Typography
                variant="h2"
                component="h1"
                sx={{
                  fontWeight: 700,
                  mb: 3,
                  fontSize: { xs: '2.5rem', md: '3.5rem' },
                }}
              >
                About Kelem SMS
              </Typography>
              <Typography
                variant="h5"
                sx={{
                  mb: 4,
                  opacity: 0.9,
                  lineHeight: 1.6,
                }}
              >
                Empowering Ethiopian schools with modern, comprehensive student management solutions that drive educational excellence.
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Button
                  variant="contained"
                  size="large"
                  onClick={handleGetStarted}
                  sx={{
                    bgcolor: 'white',
                    color: 'primary.main',
                    py: 1.5,
                    px: 4,
                    fontWeight: 600,
                    '&:hover': {
                      bgcolor: 'grey.100',
                    },
                  }}
                >
                  Get Started
                </Button>
                <Button
                  variant="outlined"
                  size="large"
                  onClick={handleContactUs}
                  sx={{
                    borderColor: 'white',
                    color: 'white',
                    py: 1.5,
                    px: 4,
                    fontWeight: 600,
                    '&:hover': {
                      borderColor: 'white',
                      bgcolor: 'rgba(255, 255, 255, 0.1)',
                    },
                  }}
                >
                  Contact Us
                </Button>
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: { xs: 300, md: 400 },
                }}
              >
                <SchoolIcon sx={{ fontSize: { xs: 200, md: 300 }, opacity: 0.3 }} />
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Stats Section */}
      <Box sx={{ py: 8, bgcolor: 'background.paper' }}>
        <Container maxWidth="lg">
          <Grid container spacing={4}>
            {stats.map((stat, index) => (
              <Grid item xs={6} md={3} key={index}>
                <Box sx={{ textAlign: 'center' }}>
                  <Box sx={{ mb: 2, display: 'flex', justifyContent: 'center' }}>
                    {stat.icon}
                  </Box>
                  <Typography
                    variant="h3"
                    component="div"
                    sx={{
                      fontWeight: 700,
                      color: 'primary.main',
                      mb: 1,
                      fontSize: { xs: '2rem', md: '3rem' },
                    }}
                  >
                    {stat.number}
                  </Typography>
                  <Typography variant="subtitle1" color="text.secondary">
                    {stat.label}
                  </Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Mission & Vision Section */}
      <Box sx={{ py: 10 }}>
        <Container maxWidth="lg">
          <Grid container spacing={6}>
            <Grid item xs={12} md={6}>
              <Paper
                elevation={3}
                sx={{
                  p: 4,
                  height: '100%',
                  background: `linear-gradient(135deg, ${theme.palette.primary.light} 0%, ${theme.palette.primary.main} 100%)`,
                  color: 'white',
                }}
              >
                <Typography variant="h4" sx={{ fontWeight: 700, mb: 3 }}>
                  Our Mission
                </Typography>
                <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem' }}>
                  To revolutionize education management in Ethiopia by providing schools with 
                  powerful, user-friendly technology that enhances learning outcomes, streamlines 
                  administrative processes, and connects the entire school community.
                </Typography>
              </Paper>
            </Grid>
            <Grid item xs={12} md={6}>
              <Paper
                elevation={3}
                sx={{
                  p: 4,
                  height: '100%',
                  background: `linear-gradient(135deg, ${theme.palette.secondary.light} 0%, ${theme.palette.secondary.main} 100%)`,
                  color: 'white',
                }}
              >
                <Typography variant="h4" sx={{ fontWeight: 700, mb: 3 }}>
                  Our Vision
                </Typography>
                <Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem' }}>
                  To be the leading educational technology platform in Ethiopia, empowering 
                  every school to achieve excellence through innovative digital solutions that 
                  make quality education accessible to all students.
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Values Section */}
      <Box sx={{ py: 10, bgcolor: 'background.paper' }}>
        <Container maxWidth="lg">
          <Typography
            variant="h3"
            component="h2"
            sx={{
              fontWeight: 700,
              textAlign: 'center',
              mb: 6,
              fontSize: { xs: '2rem', md: '2.5rem' },
            }}
          >
            Our Core Values
          </Typography>
          <Grid container spacing={4}>
            {values.map((value, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Card
                  sx={{
                    height: '100%',
                    textAlign: 'center',
                    p: 3,
                    transition: 'transform 0.3s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: theme.shadows[10],
                    },
                  }}
                >
                  <CardContent>
                    <Box sx={{ mb: 3 }}>
                      {value.icon}
                    </Box>
                    <Typography variant="h5" sx={{ fontWeight: 600, mb: 2 }}>
                      {value.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                      {value.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Team Section */}
      <Box sx={{ py: 10 }}>
        <Container maxWidth="lg">
          <Typography
            variant="h3"
            component="h2"
            sx={{
              fontWeight: 700,
              textAlign: 'center',
              mb: 2,
              fontSize: { xs: '2rem', md: '2.5rem' },
            }}
          >
            Meet Our Team
          </Typography>
          <Typography
            variant="h6"
            color="text.secondary"
            sx={{ textAlign: 'center', mb: 6, maxWidth: 600, mx: 'auto' }}
          >
            Passionate educators and technologists working together to transform Ethiopian education
          </Typography>
          <Grid container spacing={4}>
            {team.map((member, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Card
                  sx={{
                    height: '100%',
                    textAlign: 'center',
                    transition: 'transform 0.3s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: theme.shadows[10],
                    },
                  }}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Avatar
                      sx={{
                        width: 100,
                        height: 100,
                        mx: 'auto',
                        mb: 2,
                        bgcolor: 'primary.main',
                        fontSize: '2rem',
                      }}
                    >
                      {member.name.split(' ').map(n => n[0]).join('')}
                    </Avatar>
                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                      {member.name}
                    </Typography>
                    <Chip
                      label={member.role}
                      color="primary"
                      size="small"
                      sx={{ mb: 2 }}
                    />
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3, lineHeight: 1.6 }}>
                      {member.bio}
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1 }}>
                      <Button size="small" color="primary">
                        <LinkedInIcon fontSize="small" />
                      </Button>
                      <Button size="small" color="primary">
                        <TwitterIcon fontSize="small" />
                      </Button>
                      <Button size="small" color="primary">
                        <EmailIcon fontSize="small" />
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Timeline Section */}
      <Box sx={{ py: 10, bgcolor: 'background.paper' }}>
        <Container maxWidth="lg">
          <Typography
            variant="h3"
            component="h2"
            sx={{
              fontWeight: 700,
              textAlign: 'center',
              mb: 6,
              fontSize: { xs: '2rem', md: '2.5rem' },
            }}
          >
            Our Journey
          </Typography>
          <Box sx={{ maxWidth: 800, mx: 'auto' }}>
            {milestones.map((milestone, index) => (
              <Box key={index} sx={{ display: 'flex', mb: 4, alignItems: 'flex-start' }}>
                <Box
                  sx={{
                    minWidth: 80,
                    mr: 3,
                    textAlign: 'center',
                  }}
                >
                  <Chip
                    label={milestone.year}
                    color="primary"
                    sx={{ fontWeight: 600, fontSize: '0.9rem' }}
                  />
                </Box>
                <Box
                  sx={{
                    flex: 1,
                    position: 'relative',
                    '&::before': index < milestones.length - 1 ? {
                      content: '""',
                      position: 'absolute',
                      left: -20,
                      top: 30,
                      width: 2,
                      height: 60,
                      bgcolor: 'primary.light',
                    } : {},
                  }}
                >
                  <Box
                    sx={{
                      position: 'absolute',
                      left: -26,
                      top: 8,
                      width: 14,
                      height: 14,
                      borderRadius: '50%',
                      bgcolor: 'primary.main',
                    }}
                  />
                  <Paper elevation={2} sx={{ p: 3, ml: 2 }}>
                    <Typography variant="body1" sx={{ lineHeight: 1.6 }}>
                      {milestone.event}
                    </Typography>
                  </Paper>
                </Box>
              </Box>
            ))}
          </Box>
        </Container>
      </Box>

      {/* Why Choose Us Section */}
      <Box sx={{ py: 10 }}>
        <Container maxWidth="lg">
          <Typography
            variant="h3"
            component="h2"
            sx={{
              fontWeight: 700,
              textAlign: 'center',
              mb: 6,
              fontSize: { xs: '2rem', md: '2.5rem' },
            }}
          >
            Why Choose Kelem SMS?
          </Typography>
          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <List>
                <ListItem sx={{ px: 0 }}>
                  <ListItemIcon>
                    <CheckCircleIcon color="success" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Built for Ethiopian Schools"
                    secondary="Designed specifically for the Ethiopian education system and curriculum"
                  />
                </ListItem>
                <ListItem sx={{ px: 0 }}>
                  <ListItemIcon>
                    <CheckCircleIcon color="success" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Multilingual Support"
                    secondary="Available in Amharic, English, and other local languages"
                  />
                </ListItem>
                <ListItem sx={{ px: 0 }}>
                  <ListItemIcon>
                    <CheckCircleIcon color="success" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Affordable Pricing"
                    secondary="Competitive pricing designed for schools of all sizes"
                  />
                </ListItem>
                <ListItem sx={{ px: 0 }}>
                  <ListItemIcon>
                    <CheckCircleIcon color="success" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Local Support Team"
                    secondary="Dedicated Ethiopian support team that understands your needs"
                  />
                </ListItem>
              </List>
            </Grid>
            <Grid item xs={12} md={6}>
              <List>
                <ListItem sx={{ px: 0 }}>
                  <ListItemIcon>
                    <CheckCircleIcon color="success" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Cloud-Based Security"
                    secondary="Enterprise-grade security with automatic backups and updates"
                  />
                </ListItem>
                <ListItem sx={{ px: 0 }}>
                  <ListItemIcon>
                    <CheckCircleIcon color="success" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Mobile Accessibility"
                    secondary="Access from anywhere with our mobile-responsive platform"
                  />
                </ListItem>
                <ListItem sx={{ px: 0 }}>
                  <ListItemIcon>
                    <CheckCircleIcon color="success" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Comprehensive Training"
                    secondary="Complete onboarding and ongoing training for your staff"
                  />
                </ListItem>
                <ListItem sx={{ px: 0 }}>
                  <ListItemIcon>
                    <CheckCircleIcon color="success" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Continuous Innovation"
                    secondary="Regular updates and new features based on user feedback"
                  />
                </ListItem>
              </List>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Call to Action Section */}
      <Box
        sx={{
          py: 8,
          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
          color: 'white',
          textAlign: 'center',
        }}
      >
        <Container maxWidth="md">
          <Typography
            variant="h3"
            component="h2"
            sx={{
              fontWeight: 700,
              mb: 3,
              fontSize: { xs: '2rem', md: '2.5rem' },
            }}
          >
            Ready to Transform Your School?
          </Typography>
          <Typography
            variant="h6"
            sx={{
              mb: 4,
              opacity: 0.9,
              maxWidth: 600,
              mx: 'auto',
              lineHeight: 1.6,
            }}
          >
            Join hundreds of schools across Ethiopia that trust Kelem SMS to manage their student information and drive educational excellence.
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              size="large"
              onClick={handleGetStarted}
              sx={{
                bgcolor: 'white',
                color: 'primary.main',
                py: 1.5,
                px: 4,
                fontWeight: 600,
                '&:hover': {
                  bgcolor: 'grey.100',
                },
              }}
            >
              Start Free Trial
            </Button>
            <Button
              variant="outlined"
              size="large"
              onClick={() => navigate('/pricing')}
              sx={{
                borderColor: 'white',
                color: 'white',
                py: 1.5,
                px: 4,
                fontWeight: 600,
                '&:hover': {
                  borderColor: 'white',
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                },
              }}
            >
              View Pricing
            </Button>
          </Box>
        </Container>
      </Box>
    </Box>
  )
}

export default AboutPage
