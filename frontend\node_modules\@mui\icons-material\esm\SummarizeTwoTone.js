"use client";

import createSvgIcon from './utils/createSvgIcon';
import { jsx as _jsx } from "react/jsx-runtime";
export default createSvgIcon([/*#__PURE__*/_jsx("path", {
  d: "M14 5H5v14h14v-9h-5zM8 17c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m0-4c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m0-4c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1",
  opacity: ".3"
}, "0"), /*#__PURE__*/_jsx("circle", {
  cx: "8",
  cy: "8",
  r: "1"
}, "1"), /*#__PURE__*/_jsx("path", {
  d: "M15 3H5c-1.1 0-1.99.9-1.99 2L3 19c0 1.1.89 2 1.99 2H19c1.1 0 2-.9 2-2V9zm4 16H5V5h9v5h5z"
}, "2"), /*#__PURE__*/_jsx("circle", {
  cx: "8",
  cy: "12",
  r: "1"
}, "3"), /*#__PURE__*/_jsx("circle", {
  cx: "8",
  cy: "16",
  r: "1"
}, "4")], 'SummarizeTwoTone');