from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group, Permission
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timedelta
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from .serializers import (
    UserListSerializer, UserDetailSerializer, UserCreateSerializer,
    UserUpdateSerializer, GroupSerializer, PermissionSerializer,
    UserStatsSerializer, BulkUserActionSerializer, PasswordResetSerializer
)

User = get_user_model()


class IsAdminUser(permissions.BasePermission):
    """Permission to check if user is admin or superuser"""
    
    def has_permission(self, request, view):
        return request.user.is_authenticated and (
            request.user.is_superuser or request.user.is_staff
        )


class UserManagementViewSet(viewsets.ModelViewSet):
    """ViewSet for managing users"""
    queryset = User.objects.all().select_related().prefetch_related('groups', 'user_permissions')
    permission_classes = [IsAdminUser]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['user_type', 'is_active', 'is_staff', 'is_superuser']
    search_fields = ['email', 'first_name', 'last_name', 'phone_number']
    ordering_fields = ['email', 'first_name', 'last_name', 'date_joined', 'last_login']
    ordering = ['-date_joined']
    
    def get_serializer_class(self):
        if self.action == 'list':
            return UserListSerializer
        elif self.action == 'create':
            return UserCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return UserUpdateSerializer
        elif self.action == 'reset_password':
            return PasswordResetSerializer
        elif self.action == 'bulk_action':
            return BulkUserActionSerializer
        return UserDetailSerializer
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter == 'active':
            queryset = queryset.filter(
                is_active=True,
                last_login__gte=timezone.now() - timedelta(days=7)
            )
        elif status_filter == 'inactive':
            queryset = queryset.filter(is_active=False)
        elif status_filter == 'idle':
            queryset = queryset.filter(
                is_active=True,
                last_login__lt=timezone.now() - timedelta(days=7)
            )
        elif status_filter == 'new':
            queryset = queryset.filter(last_login__isnull=True)
        
        # Filter by date range
        date_from = self.request.query_params.get('date_from')
        date_to = self.request.query_params.get('date_to')
        if date_from:
            queryset = queryset.filter(date_joined__gte=date_from)
        if date_to:
            queryset = queryset.filter(date_joined__lte=date_to)
        
        return queryset
    
    @swagger_auto_schema(
        operation_summary="Get user statistics",
        operation_description="Get comprehensive user statistics and metrics",
        responses={200: UserStatsSerializer}
    )
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get user statistics"""
        total_users = User.objects.count()
        active_users = User.objects.filter(is_active=True).count()
        inactive_users = User.objects.filter(is_active=False).count()
        superusers = User.objects.filter(is_superuser=True).count()
        staff_users = User.objects.filter(is_staff=True).count()
        
        # New users this month
        this_month = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        new_users_this_month = User.objects.filter(date_joined__gte=this_month).count()
        
        # Recent logins (last 7 days)
        week_ago = timezone.now() - timedelta(days=7)
        recent_logins = User.objects.filter(last_login__gte=week_ago).count()
        
        # User types distribution
        user_types = {}
        for choice in User.USER_TYPE_CHOICES:
            user_types[choice[1]] = User.objects.filter(user_type=choice[0]).count()
        
        stats = {
            'total_users': total_users,
            'active_users': active_users,
            'inactive_users': inactive_users,
            'superusers': superusers,
            'staff_users': staff_users,
            'new_users_this_month': new_users_this_month,
            'user_types': user_types,
            'recent_logins': recent_logins,
        }
        
        serializer = UserStatsSerializer(stats)
        return Response(serializer.data)
    
    @swagger_auto_schema(
        operation_summary="Reset user password",
        operation_description="Reset password for a specific user",
        request_body=PasswordResetSerializer,
        responses={200: openapi.Response('Password reset successful')}
    )
    @action(detail=True, methods=['post'])
    def reset_password(self, request, pk=None):
        """Reset user password"""
        user = self.get_object()
        serializer = self.get_serializer(data=request.data)
        
        if serializer.is_valid():
            new_password = serializer.validated_data['new_password']
            user.set_password(new_password)
            user.save()
            
            return Response({
                'message': f'Password reset successfully for {user.email}',
                'user_id': user.id
            })
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @swagger_auto_schema(
        operation_summary="Bulk user actions",
        operation_description="Perform bulk actions on multiple users",
        request_body=BulkUserActionSerializer,
        responses={200: openapi.Response('Bulk action completed')}
    )
    @action(detail=False, methods=['post'])
    def bulk_action(self, request):
        """Perform bulk actions on users"""
        serializer = self.get_serializer(data=request.data)
        
        if serializer.is_valid():
            user_ids = serializer.validated_data['user_ids']
            action_type = serializer.validated_data['action']
            
            users = User.objects.filter(id__in=user_ids)
            
            if action_type == 'activate':
                users.update(is_active=True)
                message = f'Activated {users.count()} users'
            elif action_type == 'deactivate':
                users.update(is_active=False)
                message = f'Deactivated {users.count()} users'
            elif action_type == 'delete':
                # Prevent deletion of superusers by non-superusers
                if not request.user.is_superuser:
                    users = users.filter(is_superuser=False)
                count = users.count()
                users.delete()
                message = f'Deleted {count} users'
            elif action_type == 'make_staff':
                users.update(is_staff=True)
                message = f'Made {users.count()} users staff'
            elif action_type == 'remove_staff':
                users.update(is_staff=False)
                message = f'Removed staff status from {users.count()} users'
            
            return Response({'message': message})
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @swagger_auto_schema(
        operation_summary="Toggle user active status",
        operation_description="Toggle the active status of a user",
        responses={200: openapi.Response('Status toggled successfully')}
    )
    @action(detail=True, methods=['post'])
    def toggle_active(self, request, pk=None):
        """Toggle user active status"""
        user = self.get_object()
        user.is_active = not user.is_active
        user.save()
        
        status_text = 'activated' if user.is_active else 'deactivated'
        return Response({
            'message': f'User {user.email} has been {status_text}',
            'is_active': user.is_active
        })


class GroupManagementViewSet(viewsets.ModelViewSet):
    """ViewSet for managing user groups"""
    queryset = Group.objects.all().prefetch_related('permissions', 'user_set')
    serializer_class = GroupSerializer
    permission_classes = [IsAdminUser]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name']
    ordering = ['name']

    @swagger_auto_schema(
        operation_summary="Get users in group",
        operation_description="Get all users assigned to a specific group",
        responses={200: UserListSerializer(many=True)}
    )
    @action(detail=True, methods=['get'])
    def users(self, request, pk=None):
        """Get users in a specific group"""
        group = self.get_object()
        users = group.user_set.all()

        # Apply pagination
        page = self.paginate_queryset(users)
        if page is not None:
            serializer = UserListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = UserListSerializer(users, many=True)
        return Response({'results': serializer.data})

    @swagger_auto_schema(
        operation_summary="Assign users to group",
        operation_description="Assign multiple users to a group",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_ids': openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_INTEGER),
                    description='List of user IDs to assign to the group'
                )
            },
            required=['user_ids']
        ),
        responses={200: openapi.Response('Users assigned successfully')}
    )
    @action(detail=True, methods=['post'])
    def assign_users(self, request, pk=None):
        """Assign users to a group"""
        group = self.get_object()
        user_ids = request.data.get('user_ids', [])

        if not user_ids:
            return Response(
                {'error': 'user_ids is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get valid users
        users = User.objects.filter(id__in=user_ids)
        if not users.exists():
            return Response(
                {'error': 'No valid users found'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Clear existing users and assign new ones
        group.user_set.set(users)

        return Response({
            'message': f'Successfully assigned {users.count()} users to group {group.name}',
            'assigned_users': users.count()
        })

    @swagger_auto_schema(
        operation_summary="Remove users from group",
        operation_description="Remove multiple users from a group",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_ids': openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_INTEGER),
                    description='List of user IDs to remove from the group'
                )
            },
            required=['user_ids']
        ),
        responses={200: openapi.Response('Users removed successfully')}
    )
    @action(detail=True, methods=['post'])
    def remove_users(self, request, pk=None):
        """Remove users from a group"""
        group = self.get_object()
        user_ids = request.data.get('user_ids', [])

        if not user_ids:
            return Response(
                {'error': 'user_ids is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get users to remove
        users_to_remove = User.objects.filter(id__in=user_ids)

        # Remove users from group
        for user in users_to_remove:
            group.user_set.remove(user)

        return Response({
            'message': f'Successfully removed {users_to_remove.count()} users from group {group.name}',
            'removed_users': users_to_remove.count()
        })


class PermissionViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for viewing permissions"""
    queryset = Permission.objects.all().select_related('content_type')
    serializer_class = PermissionSerializer
    permission_classes = [IsAdminUser]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['content_type']
    search_fields = ['name', 'codename']
    ordering = ['content_type__name', 'name']
