import axios from 'axios'

const API_BASE_URL = 'http://localhost:8001/user-management/api'

// Create axios instance for user management API calls
const userManagementApi = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Add auth token to requests
userManagementApi.interceptors.request.use((config) => {
  const token = localStorage.getItem('token')
  if (token) {
    config.headers.Authorization = `Token ${token}`
  }
  return config
})

// Types for user management
export interface User {
  id: number
  email: string
  firstName: string
  lastName: string
  fullName: string
  userType: string
  isActive: boolean
  isStaff: boolean
  isSuperuser: boolean
  dateJoined: string
  lastLogin: string | null
  lastLoginDisplay: string
  phoneNumber: string
  tenantCount: number
  status: 'active' | 'inactive' | 'idle' | 'new'
}

export interface UserDetail extends User {
  groups: string[]
  userPermissions: string[]
  tenantAccess: {
    type: string
    accessLevel: string
    tenantCount: number
  }
  activitySummary: {
    lastLogin: string | null
    dateJoined: string
    loginCount: number
    activeSessions: number
  }
}

export interface UserCreate {
  email: string
  firstName: string
  lastName: string
  userType: string
  phoneNumber?: string
  isActive: boolean
  isStaff: boolean
  isSuperuser: boolean
  password: string
  passwordConfirm: string
}

export interface UserUpdate {
  firstName: string
  lastName: string
  userType: string
  phoneNumber?: string
  isActive: boolean
  isStaff: boolean
  isSuperuser: boolean
}

export interface UserStats {
  totalUsers: number
  activeUsers: number
  inactiveUsers: number
  superusers: number
  staffUsers: number
  newUsersThisMonth: number
  userTypes: Record<string, number>
  recentLogins: number
}

export interface Group {
  id: number
  name: string
  permissions: string[]
  userCount: number
  description?: string
}

export interface GroupCreate {
  name: string
  description?: string
  permissions: string[]
}

export interface Permission {
  id: number
  name: string
  codename: string
  contentType: number
  contentTypeName: string
}

export interface BulkAction {
  userIds: number[]
  action: 'activate' | 'deactivate' | 'delete' | 'make_staff' | 'remove_staff'
}

export interface PasswordReset {
  newPassword: string
  newPasswordConfirm: string
}

export interface UserListParams {
  page?: number
  pageSize?: number
  search?: string
  userType?: string
  isActive?: boolean
  isStaff?: boolean
  isSuperuser?: boolean
  status?: 'active' | 'inactive' | 'idle' | 'new'
  dateFrom?: string
  dateTo?: string
  ordering?: string
}

// Transform snake_case API response to camelCase
const transformUser = (data: any): User => ({
  id: data.id,
  email: data.email,
  firstName: data.first_name || '',
  lastName: data.last_name || '',
  fullName: data.full_name || '',
  userType: data.user_type || '',
  isActive: data.is_active,
  isStaff: data.is_staff,
  isSuperuser: data.is_superuser,
  dateJoined: data.date_joined,
  lastLogin: data.last_login,
  lastLoginDisplay: data.last_login_display || 'Never',
  phoneNumber: data.phone_number || '',
  tenantCount: data.tenant_count || 0,
  status: data.status || 'new',
})

const transformUserDetail = (data: any): UserDetail => ({
  ...transformUser(data),
  groups: data.groups || [],
  userPermissions: data.user_permissions || [],
  tenantAccess: data.tenant_access || {
    type: 'regular',
    accessLevel: 'limited',
    tenantCount: 0,
  },
  activitySummary: data.activity_summary || {
    lastLogin: null,
    dateJoined: data.date_joined,
    loginCount: 0,
    activeSessions: 0,
  },
})

const transformUserStats = (data: any): UserStats => ({
  totalUsers: data.total_users,
  activeUsers: data.active_users,
  inactiveUsers: data.inactive_users,
  superusers: data.superusers,
  staffUsers: data.staff_users,
  newUsersThisMonth: data.new_users_this_month,
  userTypes: data.user_types || {},
  recentLogins: data.recent_logins,
})

// API Functions
export const getUsers = async (params: UserListParams = {}): Promise<{
  results: User[]
  count: number
  next: string | null
  previous: string | null
}> => {
  try {
    const queryParams = new URLSearchParams()
    
    if (params.page) queryParams.append('page', params.page.toString())
    if (params.pageSize) queryParams.append('page_size', params.pageSize.toString())
    if (params.search) queryParams.append('search', params.search)
    if (params.userType) queryParams.append('user_type', params.userType)
    if (params.isActive !== undefined) queryParams.append('is_active', params.isActive.toString())
    if (params.isStaff !== undefined) queryParams.append('is_staff', params.isStaff.toString())
    if (params.isSuperuser !== undefined) queryParams.append('is_superuser', params.isSuperuser.toString())
    if (params.status) queryParams.append('status', params.status)
    if (params.dateFrom) queryParams.append('date_from', params.dateFrom)
    if (params.dateTo) queryParams.append('date_to', params.dateTo)
    if (params.ordering) queryParams.append('ordering', params.ordering)
    
    const response = await userManagementApi.get(`/users/?${queryParams.toString()}`)
    
    return {
      results: response.data.results.map(transformUser),
      count: response.data.count,
      next: response.data.next,
      previous: response.data.previous,
    }
  } catch (error) {
    console.error('Error fetching users:', error)
    throw error
  }
}

export const getUser = async (id: number): Promise<UserDetail> => {
  try {
    const response = await userManagementApi.get(`/users/${id}/`)
    return transformUserDetail(response.data)
  } catch (error) {
    console.error('Error fetching user:', error)
    throw error
  }
}

export const createUser = async (userData: UserCreate): Promise<UserDetail> => {
  try {
    const apiData = {
      email: userData.email,
      first_name: userData.firstName,
      last_name: userData.lastName,
      user_type: userData.userType,
      phone_number: userData.phoneNumber,
      is_active: userData.isActive,
      is_staff: userData.isStaff,
      is_superuser: userData.isSuperuser,
      password: userData.password,
      password_confirm: userData.passwordConfirm,
    }
    
    const response = await userManagementApi.post('/users/', apiData)
    return transformUserDetail(response.data)
  } catch (error) {
    console.error('Error creating user:', error)
    throw error
  }
}

export const updateUser = async (id: number, userData: UserUpdate): Promise<UserDetail> => {
  try {
    const apiData = {
      first_name: userData.firstName,
      last_name: userData.lastName,
      user_type: userData.userType,
      phone_number: userData.phoneNumber,
      is_active: userData.isActive,
      is_staff: userData.isStaff,
      is_superuser: userData.isSuperuser,
    }
    
    const response = await userManagementApi.patch(`/users/${id}/`, apiData)
    return transformUserDetail(response.data)
  } catch (error) {
    console.error('Error updating user:', error)
    throw error
  }
}

export const deleteUser = async (id: number): Promise<void> => {
  try {
    await userManagementApi.delete(`/users/${id}/`)
  } catch (error) {
    console.error('Error deleting user:', error)
    throw error
  }
}

export const getUserStats = async (): Promise<UserStats> => {
  try {
    const response = await userManagementApi.get('/users/stats/')
    return transformUserStats(response.data)
  } catch (error) {
    console.error('Error fetching user stats:', error)
    throw error
  }
}

export const resetUserPassword = async (id: number, passwordData: PasswordReset): Promise<void> => {
  try {
    const apiData = {
      new_password: passwordData.newPassword,
      new_password_confirm: passwordData.newPasswordConfirm,
    }
    
    await userManagementApi.post(`/users/${id}/reset_password/`, apiData)
  } catch (error) {
    console.error('Error resetting password:', error)
    throw error
  }
}

export const toggleUserActive = async (id: number): Promise<{ message: string; isActive: boolean }> => {
  try {
    const response = await userManagementApi.post(`/users/${id}/toggle_active/`)
    return response.data
  } catch (error) {
    console.error('Error toggling user active status:', error)
    throw error
  }
}

export const bulkUserAction = async (actionData: BulkAction): Promise<{ message: string }> => {
  try {
    const apiData = {
      user_ids: actionData.userIds,
      action: actionData.action,
    }
    
    const response = await userManagementApi.post('/users/bulk_action/', apiData)
    return response.data
  } catch (error) {
    console.error('Error performing bulk action:', error)
    throw error
  }
}

export const getGroups = async (): Promise<Group[]> => {
  try {
    const response = await userManagementApi.get('/groups/')
    return response.data.results || response.data
  } catch (error) {
    console.error('Error fetching groups:', error)
    throw error
  }
}

export const getPermissions = async (): Promise<Permission[]> => {
  try {
    const response = await userManagementApi.get('/permissions/')
    return response.data.results || response.data
  } catch (error) {
    console.error('Error fetching permissions:', error)
    throw error
  }
}

export const createGroup = async (groupData: GroupCreate): Promise<Group> => {
  try {
    const apiData = {
      name: groupData.name,
      description: groupData.description,
      permissions: groupData.permissions,
    }

    const response = await userManagementApi.post('/groups/', apiData)
    return response.data
  } catch (error) {
    console.error('Error creating group:', error)
    throw error
  }
}

export const updateGroup = async (id: number, groupData: Partial<GroupCreate>): Promise<Group> => {
  try {
    const apiData = {
      name: groupData.name,
      description: groupData.description,
      permissions: groupData.permissions,
    }

    const response = await userManagementApi.patch(`/groups/${id}/`, apiData)
    return response.data
  } catch (error) {
    console.error('Error updating group:', error)
    throw error
  }
}

export const deleteGroup = async (id: number): Promise<void> => {
  try {
    await userManagementApi.delete(`/groups/${id}/`)
  } catch (error) {
    console.error('Error deleting group:', error)
    throw error
  }
}

export const getGroupUsers = async (groupId: number): Promise<User[]> => {
  try {
    const response = await userManagementApi.get(`/groups/${groupId}/users/`)
    return response.data.results.map(transformUser)
  } catch (error) {
    console.error('Error fetching group users:', error)
    throw error
  }
}

export const assignUsersToGroup = async (groupId: number, userIds: number[]): Promise<void> => {
  try {
    const apiData = {
      user_ids: userIds,
    }
    await userManagementApi.post(`/groups/${groupId}/assign-users/`, apiData)
  } catch (error) {
    console.error('Error assigning users to group:', error)
    throw error
  }
}

export const removeUsersFromGroup = async (groupId: number, userIds: number[]): Promise<void> => {
  try {
    const apiData = {
      user_ids: userIds,
    }
    await userManagementApi.post(`/groups/${groupId}/remove-users/`, apiData)
  } catch (error) {
    console.error('Error removing users from group:', error)
    throw error
  }
}
