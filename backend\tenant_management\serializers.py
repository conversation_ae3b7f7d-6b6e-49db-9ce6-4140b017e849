from rest_framework import serializers
from tenants.models import School, Domain
from .models import TenantMetrics, TenantActivity, TenantStatus

class DomainSerializer(serializers.ModelSerializer):
    class Meta:
        model = Domain
        fields = ['id', 'domain', 'is_primary']

class SchoolSerializer(serializers.ModelSerializer):
    domains = DomainSerializer(many=True, read_only=True)
    domain_url = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()
    subscription_plan = serializers.SerializerMethodField()
    contact_person = serializers.SerializerMethodField()

    class Meta:
        model = School
        fields = ['id', 'schema_name', 'name', 'address', 'contact_email', 'contact_phone', 'domains',
                 'domain_url', 'status', 'subscription_plan', 'contact_person', 'created_on']

    def get_domain_url(self, obj):
        primary_domain = obj.domains.filter(is_primary=True).first()
        if primary_domain:
            return primary_domain.domain
        return obj.domains.first().domain if obj.domains.exists() else ''

    def get_status(self, obj):
        try:
            return obj.status.status
        except:
            return 'active'

    def get_subscription_plan(self, obj):
        try:
            return obj.status.subscription_plan
        except:
            return 'free'

    def get_contact_person(self, obj):
        # This is a placeholder - you might want to implement a proper contact person field
        return obj.contact_email.split('@')[0] if obj.contact_email else ''

class TenantMetricsSerializer(serializers.ModelSerializer):
    tenant_name = serializers.CharField(source='tenant.name', read_only=True)
    tenant_schema = serializers.CharField(source='tenant.schema_name', read_only=True)

    class Meta:
        model = TenantMetrics
        fields = [
            'id', 'tenant', 'tenant_name', 'tenant_schema', 'timestamp',
            'total_users', 'active_users', 'admin_users', 'teacher_users',
            'student_users', 'parent_users', 'staff_users',
            'total_students', 'total_courses', 'total_departments', 'total_enrollments',
            'database_size', 'file_storage_size',
            'avg_response_time', 'peak_response_time', 'api_requests_count', 'error_count'
        ]
        read_only_fields = ['id', 'tenant', 'timestamp']

class TenantActivitySerializer(serializers.ModelSerializer):
    tenant_name = serializers.CharField(source='tenant.name', read_only=True)

    class Meta:
        model = TenantActivity
        fields = ['id', 'tenant', 'tenant_name', 'timestamp', 'activity_type', 'description', 'user_count']
        read_only_fields = ['id', 'tenant', 'timestamp']

class TenantStatusSerializer(serializers.ModelSerializer):
    tenant = serializers.PrimaryKeyRelatedField(queryset=School.objects.all())
    tenant_name = serializers.CharField(source='tenant.name', read_only=True)
    tenant_schema = serializers.CharField(source='tenant.schema_name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    is_subscription_valid = serializers.SerializerMethodField()

    class Meta:
        model = TenantStatus
        fields = [
            'id', 'tenant', 'tenant_name', 'tenant_schema', 'status', 'status_display',
            'last_updated', 'created_at', 'subscription_plan', 'subscription_start_date',
            'subscription_end_date', 'max_users', 'max_storage', 'admin_email',
            'admin_phone', 'is_featured', 'notes', 'is_subscription_valid'
        ]
        read_only_fields = ['id', 'last_updated', 'created_at']

    def validate(self, data):
        print(f"TenantStatusSerializer.validate called with data: {data}")
        tenant = data.get('tenant')
        print(f"Tenant from data: {tenant}")
        if not tenant:
            raise serializers.ValidationError("Tenant is required")
        return data

    def create(self, validated_data):
        print(f"TenantStatusSerializer.create called with validated_data: {validated_data}")
        tenant = validated_data.get('tenant')
        print(f"Tenant from validated_data: {tenant}")
        if not tenant:
            raise serializers.ValidationError("Tenant is required for creation")
        return super().create(validated_data)

    def get_is_subscription_valid(self, obj):
        return obj.is_subscription_valid()

class TenantDetailSerializer(serializers.ModelSerializer):
    domains = DomainSerializer(many=True, read_only=True)
    status = TenantStatusSerializer(read_only=True)
    latest_metrics = serializers.SerializerMethodField()
    recent_activities = serializers.SerializerMethodField()
    domain_url = serializers.SerializerMethodField()
    contact_person = serializers.SerializerMethodField()
    subscription_plan = serializers.SerializerMethodField()
    tenant_status = serializers.SerializerMethodField()
    is_subscription_valid = serializers.SerializerMethodField()

    class Meta:
        model = School
        fields = [
            'id', 'schema_name', 'name', 'address', 'contact_email', 'contact_phone', 'created_on',
            'domains', 'domain_url', 'status', 'latest_metrics', 'recent_activities',
            'contact_person', 'subscription_plan', 'tenant_status', 'is_subscription_valid'
        ]

    def get_latest_metrics(self, obj):
        metrics = TenantMetrics.objects.filter(tenant=obj).order_by('-timestamp').first()
        if metrics:
            return TenantMetricsSerializer(metrics).data
        return None

    def get_recent_activities(self, obj):
        activities = TenantActivity.objects.filter(tenant=obj).order_by('-timestamp')[:5]
        return TenantActivitySerializer(activities, many=True).data

    def get_domain_url(self, obj):
        primary_domain = obj.domains.filter(is_primary=True).first()
        if primary_domain:
            return primary_domain.domain
        return obj.domains.first().domain if obj.domains.exists() else ''

    def get_contact_person(self, obj):
        # This is a placeholder - you might want to implement a proper contact person field
        return obj.contact_email.split('@')[0] if obj.contact_email else ''

    def get_subscription_plan(self, obj):
        try:
            return obj.status.subscription_plan
        except:
            return 'free'

    def get_tenant_status(self, obj):
        try:
            return obj.status.status
        except:
            return 'active'

    def get_is_subscription_valid(self, obj):
        try:
            return obj.status.is_subscription_valid()
        except:
            return True

class TenantSummarySerializer(serializers.Serializer):
    total_tenants = serializers.IntegerField()
    active_tenants = serializers.IntegerField()
    suspended_tenants = serializers.IntegerField()
    trial_tenants = serializers.IntegerField()
    expired_tenants = serializers.IntegerField()
    total_users = serializers.IntegerField()
    total_students = serializers.IntegerField()
    total_courses = serializers.IntegerField()
    total_storage = serializers.IntegerField()
    top_tenants_by_users = serializers.ListField(
        child=serializers.ListField(
            child=serializers.IntegerField()
        )
    )
    top_tenants_by_storage = serializers.ListField(
        child=serializers.ListField(
            child=serializers.IntegerField()
        )
    )

class SystemMetricsSerializer(serializers.Serializer):
    timestamp = serializers.DateTimeField()
    cpu_percent = serializers.FloatField(required=False)
    memory_percent = serializers.FloatField(required=False)
    memory_used = serializers.IntegerField(required=False)
    memory_total = serializers.IntegerField(required=False)
    disk_percent = serializers.FloatField(required=False)
    disk_used = serializers.IntegerField(required=False)
    disk_total = serializers.IntegerField(required=False)
    bytes_sent = serializers.IntegerField(required=False)
    bytes_recv = serializers.IntegerField(required=False)
    db_connections = serializers.IntegerField(required=False)
    tenant_count = serializers.IntegerField(required=False)
    domain_count = serializers.IntegerField(required=False)
    error = serializers.CharField(required=False)
