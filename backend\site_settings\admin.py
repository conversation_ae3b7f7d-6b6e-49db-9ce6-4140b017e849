from django.contrib import admin
from .models import SiteSettings, BrandingSettings, HeroSection, MaintenanceMode


@admin.register(SiteSettings)
class SiteSettingsAdmin(admin.ModelAdmin):
    list_display = ['site_name', 'contact_email', 'enable_registration', 'updated_at']
    fieldsets = (
        ('Site Information', {
            'fields': ('site_name', 'site_tagline', 'site_description')
        }),
        ('Contact Information', {
            'fields': ('contact_email', 'contact_phone', 'contact_address')
        }),
        ('Social Media', {
            'fields': ('facebook_url', 'twitter_url', 'linkedin_url', 'youtube_url')
        }),
        ('SEO Settings', {
            'fields': ('meta_title', 'meta_description', 'meta_keywords')
        }),
        ('Feature Toggles', {
            'fields': ('enable_registration', 'enable_trial', 'enable_contact_form', 'enable_blog', 'enable_testimonials')
        }),
    )
    readonly_fields = ['created_at', 'updated_at']


@admin.register(BrandingSettings)
class BrandingSettingsAdmin(admin.ModelAdmin):
    list_display = ['primary_color', 'secondary_color', 'font_family_primary', 'updated_at']
    fieldsets = (
        ('Color Palette', {
            'fields': ('primary_color', 'primary_light', 'primary_dark', 'secondary_color', 'secondary_light', 'secondary_dark')
        }),
        ('Background Colors', {
            'fields': ('background_default', 'background_paper')
        }),
        ('Text Colors', {
            'fields': ('text_primary', 'text_secondary')
        }),
        ('Status Colors', {
            'fields': ('success_color', 'warning_color', 'error_color', 'info_color')
        }),
        ('Typography', {
            'fields': ('font_family_primary', 'font_family_secondary')
        }),
        ('Design Elements', {
            'fields': ('border_radius_small', 'border_radius_medium', 'border_radius_large', 'enable_shadows', 'shadow_intensity')
        }),
        ('Custom CSS', {
            'fields': ('custom_css',)
        }),
    )
    readonly_fields = ['created_at', 'updated_at']


@admin.register(HeroSection)
class HeroSectionAdmin(admin.ModelAdmin):
    list_display = ['title', 'background_type', 'show_stats', 'show_features', 'updated_at']
    fieldsets = (
        ('Content', {
            'fields': ('title', 'subtitle', 'description')
        }),
        ('Call-to-Action Buttons', {
            'fields': ('primary_button_text', 'primary_button_url', 'secondary_button_text', 'secondary_button_url')
        }),
        ('Display Options', {
            'fields': ('background_type', 'show_stats', 'show_features')
        }),
    )
    readonly_fields = ['created_at', 'updated_at']


@admin.register(MaintenanceMode)
class MaintenanceModeAdmin(admin.ModelAdmin):
    list_display = ['maintenance_mode', 'updated_at']
    fields = ['maintenance_mode', 'maintenance_message']
    readonly_fields = ['created_at', 'updated_at']
