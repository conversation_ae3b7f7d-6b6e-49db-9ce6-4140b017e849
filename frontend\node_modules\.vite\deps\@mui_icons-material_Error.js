"use client";
import {
  require_createSvgIcon
} from "./chunk-VKJE3PGV.js";
import "./chunk-5PQ5PKEE.js";
import "./chunk-BBWB4YLS.js";
import "./chunk-NSDJ2KRB.js";
import {
  require_interopRequireDefault
} from "./chunk-DIQD2LQY.js";
import "./chunk-HJS24R7O.js";
import "./chunk-Q7CPF5VB.js";
import "./chunk-K6FDVZ65.js";
import "./chunk-KGFDDYBK.js";
import {
  require_jsx_runtime
} from "./chunk-OT5EQO2H.js";
import "./chunk-OU5AQDZK.js";
import {
  __commonJS
} from "./chunk-EWTE5DHJ.js";

// node_modules/@mui/icons-material/Error.js
var require_Error = __commonJS({
  "node_modules/@mui/icons-material/Error.js"(exports) {
    var _interopRequireDefault = require_interopRequireDefault();
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _createSvgIcon = _interopRequireDefault(require_createSvgIcon());
    var _jsxRuntime = require_jsx_runtime();
    var _default = exports.default = (0, _createSvgIcon.default)((0, _jsxRuntime.jsx)("path", {
      d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m1 15h-2v-2h2zm0-4h-2V7h2z"
    }), "Error");
  }
});
export default require_Error();
//# sourceMappingURL=@mui_icons-material_Error.js.map
