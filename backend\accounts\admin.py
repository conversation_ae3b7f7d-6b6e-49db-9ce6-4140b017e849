from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.translation import gettext_lazy as _

from .models import User, AdminProfile, TeacherProfile, StudentProfile, ParentProfile, StaffProfile

class AdminProfileInline(admin.StackedInline):
    model = AdminProfile
    can_delete = False
    verbose_name_plural = 'Admin Profile'

class TeacherProfileInline(admin.StackedInline):
    model = TeacherProfile
    can_delete = False
    verbose_name_plural = 'Teacher Profile'

class StudentProfileInline(admin.StackedInline):
    model = StudentProfile
    can_delete = False
    verbose_name_plural = 'Student Profile'

class ParentProfileInline(admin.StackedInline):
    model = ParentProfile
    can_delete = False
    verbose_name_plural = 'Parent Profile'

class StaffProfileInline(admin.StackedInline):
    model = StaffProfile
    can_delete = False
    verbose_name_plural = 'Staff Profile'

@admin.register(User)
class UserAdmin(BaseUserAdmin):
    fieldsets = (
        (None, {'fields': ('email', 'password')}),
        (_('Personal info'), {'fields': ('first_name', 'last_name', 'phone_number')}),
        (_('User type'), {'fields': ('user_type',)}),
        (_('Permissions'), {'fields': ('is_active', 'is_staff', 'is_superuser',
                                       'groups', 'user_permissions')}),
    )
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'password1', 'password2', 'user_type'),
        }),
    )
    list_display = ('email', 'first_name', 'last_name', 'user_type', 'is_staff')
    list_filter = ('is_staff', 'is_superuser', 'is_active', 'user_type', 'groups')
    search_fields = ('email', 'first_name', 'last_name')
    ordering = ('email',)
    filter_horizontal = ('groups', 'user_permissions',)

    def get_inlines(self, request, obj=None):
        if obj:
            if obj.user_type == 'admin':
                return [AdminProfileInline]
            elif obj.user_type == 'teacher':
                return [TeacherProfileInline]
            elif obj.user_type == 'student':
                return [StudentProfileInline]
            elif obj.user_type == 'parent':
                return [ParentProfileInline]
            elif obj.user_type == 'staff':
                return [StaffProfileInline]
        return []

@admin.register(AdminProfile)
class AdminProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'department', 'is_system_admin')
    search_fields = ('user__email', 'user__first_name', 'user__last_name', 'department')
    list_filter = ('is_system_admin',)

@admin.register(TeacherProfile)
class TeacherProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'employee_id', 'department', 'qualification')
    search_fields = ('user__email', 'user__first_name', 'user__last_name', 'employee_id', 'department')

@admin.register(StudentProfile)
class StudentProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'student_id')
    search_fields = ('user__email', 'user__first_name', 'user__last_name', 'student_id')

@admin.register(ParentProfile)
class ParentProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'guardian_name', 'relationship')
    search_fields = ('user__email', 'user__first_name', 'user__last_name', 'guardian_name')

@admin.register(StaffProfile)
class StaffProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'employee_id', 'department', 'position')
    search_fields = ('user__email', 'user__first_name', 'user__last_name', 'employee_id', 'department', 'position')
