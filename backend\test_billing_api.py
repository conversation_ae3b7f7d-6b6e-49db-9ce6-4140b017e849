#!/usr/bin/env python
"""
Test script for billing management API
"""
import os
import sys
import django
import json
from decimal import Decimal

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kelem_sms.settings')
django.setup()

from billing_management.models import SubscriptionPlan, PlanFeature

def create_sample_plans():
    """Create sample subscription plans for testing"""
    
    # Basic Plan
    basic_features = [
        {"id": "1", "name": "Student Management", "description": "Manage student records and profiles", "included": True},
        {"id": "2", "name": "Grade Management", "description": "Track and manage student grades", "included": True},
        {"id": "3", "name": "Attendance Tracking", "description": "Monitor student attendance", "included": True},
    ]
    
    basic_plan, created = SubscriptionPlan.objects.get_or_create(
        name="Basic Plan",
        defaults={
            'description': 'Perfect for small schools getting started',
            'price': Decimal('1500.00'),  # ~30 USD in ETB
            'billing_cycle': 'monthly',
            'max_users': 50,
            'max_storage': 2 * 1024 * 1024 * 1024,  # 2GB
            'is_active': True,
            'is_featured': False,
            'trial_days': 14,
            'setup_fee': Decimal('0.00'),
            'currency': 'ETB',
            'category': 'basic',
            'sort_order': 1,
            'features_json': json.dumps(basic_features)
        }
    )
    
    if created:
        print(f"Created Basic Plan: {basic_plan}")
    else:
        print(f"Basic Plan already exists: {basic_plan}")
    
    # Premium Plan
    premium_features = [
        {"id": "1", "name": "Student Management", "description": "Manage student records and profiles", "included": True},
        {"id": "2", "name": "Grade Management", "description": "Track and manage student grades", "included": True},
        {"id": "3", "name": "Attendance Tracking", "description": "Monitor student attendance", "included": True},
        {"id": "4", "name": "Parent Portal", "description": "Parent access to student information", "included": True},
        {"id": "5", "name": "Teacher Dashboard", "description": "Comprehensive teacher tools", "included": True},
        {"id": "6", "name": "Reports & Analytics", "description": "Detailed reporting and analytics", "included": True},
        {"id": "7", "name": "Mobile App Access", "description": "Mobile application support", "included": True},
    ]
    
    premium_plan, created = SubscriptionPlan.objects.get_or_create(
        name="Premium Plan",
        defaults={
            'description': 'Advanced features for growing schools',
            'price': Decimal('4000.00'),  # ~80 USD in ETB
            'billing_cycle': 'monthly',
            'max_users': 200,
            'max_storage': 10 * 1024 * 1024 * 1024,  # 10GB
            'is_active': True,
            'is_featured': True,
            'trial_days': 14,
            'setup_fee': Decimal('0.00'),
            'currency': 'ETB',
            'category': 'premium',
            'sort_order': 2,
            'features_json': json.dumps(premium_features)
        }
    )
    
    if created:
        print(f"Created Premium Plan: {premium_plan}")
    else:
        print(f"Premium Plan already exists: {premium_plan}")
    
    # Enterprise Plan
    enterprise_features = [
        {"id": "1", "name": "Student Management", "description": "Manage student records and profiles", "included": True},
        {"id": "2", "name": "Grade Management", "description": "Track and manage student grades", "included": True},
        {"id": "3", "name": "Attendance Tracking", "description": "Monitor student attendance", "included": True},
        {"id": "4", "name": "Parent Portal", "description": "Parent access to student information", "included": True},
        {"id": "5", "name": "Teacher Dashboard", "description": "Comprehensive teacher tools", "included": True},
        {"id": "6", "name": "Reports & Analytics", "description": "Detailed reporting and analytics", "included": True},
        {"id": "7", "name": "Mobile App Access", "description": "Mobile application support", "included": True},
        {"id": "8", "name": "API Access", "description": "REST API for integrations", "included": True},
        {"id": "9", "name": "Custom Branding", "description": "White-label customization", "included": True},
        {"id": "10", "name": "Priority Support", "description": "24/7 priority customer support", "included": True},
        {"id": "11", "name": "Advanced Security", "description": "Enhanced security features", "included": True},
    ]
    
    enterprise_plan, created = SubscriptionPlan.objects.get_or_create(
        name="Enterprise Plan",
        defaults={
            'description': 'Complete solution for large educational institutions',
            'price': Decimal('10000.00'),  # ~200 USD in ETB
            'billing_cycle': 'monthly',
            'max_users': 1000,
            'max_storage': 50 * 1024 * 1024 * 1024,  # 50GB
            'is_active': True,
            'is_featured': False,
            'trial_days': 30,
            'setup_fee': Decimal('5000.00'),  # ~100 USD in ETB
            'currency': 'ETB',
            'category': 'enterprise',
            'sort_order': 3,
            'features_json': json.dumps(enterprise_features)
        }
    )
    
    if created:
        print(f"Created Enterprise Plan: {enterprise_plan}")
    else:
        print(f"Enterprise Plan already exists: {enterprise_plan}")

def list_plans():
    """List all subscription plans"""
    plans = SubscriptionPlan.objects.all()
    print(f"\nFound {plans.count()} subscription plans:")
    for plan in plans:
        print(f"- {plan.name}: ${plan.price}/{plan.billing_cycle} ({plan.category})")
        features = plan.features
        print(f"  Features: {len(features)} features")
        print(f"  Max Users: {plan.max_users}")
        print(f"  Storage: {plan.get_storage_gb():.1f} GB")
        print(f"  Active: {plan.is_active}, Featured: {plan.is_featured}")
        print()

if __name__ == "__main__":
    print("Creating sample subscription plans...")
    create_sample_plans()
    list_plans()
    print("Sample data created successfully!")
