import React from 'react'
import {
  Card,
  CardContent,
  Typography,
  Box,
  LinearProgress,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material'
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Info as InfoIcon,
} from '@mui/icons-material'

interface SecurityMetricsCardProps {
  title: string
  value: number | string
  subtitle?: string
  icon: React.ReactNode
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info'
  trend?: {
    direction: 'up' | 'down'
    percentage: number
    period: string
  }
  progress?: {
    value: number
    max: number
  }
  info?: string
  onClick?: () => void
}

const SecurityMetricsCard: React.FC<SecurityMetricsCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  color = 'primary',
  trend,
  progress,
  info,
  onClick,
}) => {
  const getColorValue = (colorName: string) => {
    switch (colorName) {
      case 'primary':
        return '#6366f1'
      case 'secondary':
        return '#8b5cf6'
      case 'success':
        return '#10b981'
      case 'warning':
        return '#f59e0b'
      case 'error':
        return '#ef4444'
      case 'info':
        return '#3b82f6'
      default:
        return '#6b7280'
    }
  }

  const getTrendColor = (direction: string) => {
    return direction === 'up' ? '#ef4444' : '#10b981'
  }

  return (
    <Card 
      sx={{ 
        height: '100%',
        cursor: onClick ? 'pointer' : 'default',
        transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
        '&:hover': onClick ? {
          transform: 'translateY(-2px)',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
        } : {},
      }}
      onClick={onClick}
    >
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 700, color: '#1f2937', mb: 0.5 }}>
              {value}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {title}
            </Typography>
            {subtitle && (
              <Typography variant="caption" color="text.secondary">
                {subtitle}
              </Typography>
            )}
          </Box>
          <Box sx={{ position: 'relative' }}>
            <Box sx={{ color: getColorValue(color), fontSize: 40 }}>
              {icon}
            </Box>
            {info && (
              <Tooltip title={info} placement="top">
                <IconButton
                  size="small"
                  sx={{
                    position: 'absolute',
                    top: -8,
                    right: -8,
                    bgcolor: 'background.paper',
                    boxShadow: 1,
                    width: 20,
                    height: 20,
                  }}
                >
                  <InfoIcon sx={{ fontSize: 12 }} />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        </Box>

        {/* Trend Indicator */}
        {trend && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
            {trend.direction === 'up' ? (
              <TrendingUpIcon sx={{ fontSize: 16, color: getTrendColor('up') }} />
            ) : (
              <TrendingDownIcon sx={{ fontSize: 16, color: getTrendColor('down') }} />
            )}
            <Typography 
              variant="caption" 
              sx={{ 
                color: getTrendColor(trend.direction),
                fontWeight: 600,
              }}
            >
              {trend.percentage}% {trend.direction === 'up' ? 'increase' : 'decrease'}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              vs {trend.period}
            </Typography>
          </Box>
        )}

        {/* Progress Bar */}
        {progress && (
          <Box sx={{ mt: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="caption" color="text.secondary">
                Progress
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {progress.value}/{progress.max}
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={(progress.value / progress.max) * 100}
              color={color as any}
              sx={{ height: 6, borderRadius: 3 }}
            />
          </Box>
        )}

        {/* Status Chip */}
        {typeof value === 'number' && value > 0 && color === 'error' && (
          <Box sx={{ mt: 2 }}>
            <Chip
              label="Requires Attention"
              size="small"
              color="error"
              variant="outlined"
            />
          </Box>
        )}

        {typeof value === 'number' && value === 0 && color === 'success' && (
          <Box sx={{ mt: 2 }}>
            <Chip
              label="All Clear"
              size="small"
              color="success"
              variant="outlined"
            />
          </Box>
        )}
      </CardContent>
    </Card>
  )
}

export default SecurityMetricsCard
