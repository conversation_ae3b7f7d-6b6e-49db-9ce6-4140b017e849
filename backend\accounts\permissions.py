from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.db.models import Q
from students.models import Student, Guardian
from courses.models import Department, Course, AcademicTerm, CourseOffering, Enrollment

def create_default_groups_and_permissions():
    """
    Create default groups and assign permissions.
    """
    # Create groups if they don't exist
    admin_group, _ = Group.objects.get_or_create(name='Administrators')
    teacher_group, _ = Group.objects.get_or_create(name='Teachers')
    student_group, _ = Group.objects.get_or_create(name='Students')
    parent_group, _ = Group.objects.get_or_create(name='Parents')
    staff_group, _ = Group.objects.get_or_create(name='Staff')
    
    # Get content types
    student_ct = ContentType.objects.get_for_model(Student)
    guardian_ct = ContentType.objects.get_for_model(Guardian)
    department_ct = ContentType.objects.get_for_model(Department)
    course_ct = ContentType.objects.get_for_model(Course)
    term_ct = ContentType.objects.get_for_model(AcademicTerm)
    offering_ct = ContentType.objects.get_for_model(CourseOffering)
    enrollment_ct = ContentType.objects.get_for_model(Enrollment)
    
    # Clear existing permissions for groups
    admin_group.permissions.clear()
    teacher_group.permissions.clear()
    student_group.permissions.clear()
    parent_group.permissions.clear()
    staff_group.permissions.clear()
    
    # Administrator permissions (full access)
    admin_permissions = Permission.objects.filter(
        Q(content_type=student_ct) |
        Q(content_type=guardian_ct) |
        Q(content_type=department_ct) |
        Q(content_type=course_ct) |
        Q(content_type=term_ct) |
        Q(content_type=offering_ct) |
        Q(content_type=enrollment_ct)
    )
    admin_group.permissions.add(*admin_permissions)
    
    # Teacher permissions
    teacher_permissions = [
        # View students
        Permission.objects.get(content_type=student_ct, codename='view_student'),
        # View guardians
        Permission.objects.get(content_type=guardian_ct, codename='view_guardian'),
        # View departments
        Permission.objects.get(content_type=department_ct, codename='view_department'),
        # View and change courses
        Permission.objects.get(content_type=course_ct, codename='view_course'),
        Permission.objects.get(content_type=course_ct, codename='change_course'),
        # View terms
        Permission.objects.get(content_type=term_ct, codename='view_academicterm'),
        # View and change course offerings
        Permission.objects.get(content_type=offering_ct, codename='view_courseoffering'),
        Permission.objects.get(content_type=offering_ct, codename='change_courseoffering'),
        # View, add, change enrollments
        Permission.objects.get(content_type=enrollment_ct, codename='view_enrollment'),
        Permission.objects.get(content_type=enrollment_ct, codename='add_enrollment'),
        Permission.objects.get(content_type=enrollment_ct, codename='change_enrollment'),
    ]
    teacher_group.permissions.add(*teacher_permissions)
    
    # Student permissions
    student_permissions = [
        # View own student record
        Permission.objects.get(content_type=student_ct, codename='view_student'),
        # View courses
        Permission.objects.get(content_type=course_ct, codename='view_course'),
        # View terms
        Permission.objects.get(content_type=term_ct, codename='view_academicterm'),
        # View course offerings
        Permission.objects.get(content_type=offering_ct, codename='view_courseoffering'),
        # View own enrollments
        Permission.objects.get(content_type=enrollment_ct, codename='view_enrollment'),
    ]
    student_group.permissions.add(*student_permissions)
    
    # Parent permissions
    parent_permissions = [
        # View student records of their children
        Permission.objects.get(content_type=student_ct, codename='view_student'),
        # View courses
        Permission.objects.get(content_type=course_ct, codename='view_course'),
        # View terms
        Permission.objects.get(content_type=term_ct, codename='view_academicterm'),
        # View course offerings
        Permission.objects.get(content_type=offering_ct, codename='view_courseoffering'),
        # View enrollments of their children
        Permission.objects.get(content_type=enrollment_ct, codename='view_enrollment'),
    ]
    parent_group.permissions.add(*parent_permissions)
    
    # Staff permissions
    staff_permissions = [
        # View students
        Permission.objects.get(content_type=student_ct, codename='view_student'),
        # View guardians
        Permission.objects.get(content_type=guardian_ct, codename='view_guardian'),
        # View departments
        Permission.objects.get(content_type=department_ct, codename='view_department'),
        # View courses
        Permission.objects.get(content_type=course_ct, codename='view_course'),
        # View terms
        Permission.objects.get(content_type=term_ct, codename='view_academicterm'),
        # View course offerings
        Permission.objects.get(content_type=offering_ct, codename='view_courseoffering'),
        # View enrollments
        Permission.objects.get(content_type=enrollment_ct, codename='view_enrollment'),
    ]
    staff_group.permissions.add(*staff_permissions)
    
    return {
        'admin': admin_group,
        'teacher': teacher_group,
        'student': student_group,
        'parent': parent_group,
        'staff': staff_group
    }
