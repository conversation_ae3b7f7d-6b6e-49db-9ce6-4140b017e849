import {
  Dashboard as DashboardIcon,
  School as SchoolIcon,
  People as PeopleIcon,
  Person as PersonIcon,
  PersonAdd as PersonAddIcon,
  Class as ClassIcon,
  Assignment as AssignmentIcon,
  Quiz as QuizIcon,
  Grade as GradeIcon,
  CalendarToday as CalendarIcon,
  Announcement as AnnouncementIcon,
  Chat as ChatIcon,
  LibraryBooks as LibraryIcon,
  Payment as PaymentIcon,
  Receipt as ReceiptIcon,
  AccountBalance as AccountBalanceIcon,
  Settings as SettingsIcon,
  Security as SecurityIcon,
  Backup as BackupIcon,
  Analytics as AnalyticsIcon,
  Report as ReportIcon,
  Business as BusinessIcon,
  SupervisorAccount as SupervisorAccountIcon,
  AdminPanelSettings as AdminPanelSettingsIcon,
  Notifications as NotificationsIcon,
  Info as InfoIcon,
  // Platform Management Icons
  Domain as TenantIcon,
  MonetizationOn as RevenueIcon,
  Groups as UsersIcon,
  Support as SupportIcon,
  Apartment as PlatformIcon,
} from '@mui/icons-material'

export interface NavigationItem {
  id: string
  title: string
  icon: React.ReactNode
  path?: string
  children?: NavigationItem[]
  badge?: number | string
  roles?: string[]
  permissions?: string[]
  divider?: boolean
  section?: string
}

export const navigationConfig: NavigationItem[] = [
  // Main Dashboard
  {
    id: 'dashboard',
    title: 'Dashboard',
    icon: <DashboardIcon />,
    path: '/dashboard',
    section: 'main'
  },

  // Academic Management (School/Tenant Only)
  {
    id: 'students',
    title: 'Students',
    icon: <PeopleIcon />,
    section: 'academic',
    roles: ['admin', 'teacher', 'staff'], // Exclude superuser
    children: [
      {
        id: 'students-list',
        title: 'All Students',
        icon: <PeopleIcon />,
        path: '/dashboard/students',
      },
      {
        id: 'students-add',
        title: 'Add Student',
        icon: <PersonAddIcon />,
        path: '/dashboard/students/add',
      },
      {
        id: 'students-bulk',
        title: 'Bulk Import',
        icon: <PersonIcon />,
        path: '/dashboard/students/bulk-import',
      },
      {
        id: 'students-reports',
        title: 'Student Reports',
        icon: <ReportIcon />,
        children: [
          {
            id: 'students-attendance',
            title: 'Attendance Report',
            icon: <CalendarIcon />,
            path: '/dashboard/students/reports/attendance',
          },
          {
            id: 'students-performance',
            title: 'Performance Report',
            icon: <AnalyticsIcon />,
            path: '/dashboard/students/reports/performance',
          },
        ]
      },
    ]
  },
  {
    id: 'courses',
    title: 'Courses',
    icon: <ClassIcon />,
    section: 'academic',
    roles: ['admin', 'teacher', 'staff'], // Exclude superuser
    children: [
      {
        id: 'courses-list',
        title: 'All Courses',
        icon: <ClassIcon />,
        path: '/dashboard/courses',
      },
      {
        id: 'courses-add',
        title: 'Add Course',
        icon: <ClassIcon />,
        path: '/dashboard/courses/add',
      },
      {
        id: 'courses-schedule',
        title: 'Schedule',
        icon: <CalendarIcon />,
        path: '/dashboard/courses/schedule',
      },
    ]
  },
  {
    id: 'assessments',
    title: 'Assessments',
    icon: <AssignmentIcon />,
    section: 'academic',
    roles: ['admin', 'teacher'], // Exclude superuser
    children: [
      {
        id: 'assessments-list',
        title: 'All Assessments',
        icon: <AssignmentIcon />,
        path: '/dashboard/assessments',
      },
      {
        id: 'assessments-create',
        title: 'Create Assessment',
        icon: <QuizIcon />,
        path: '/dashboard/assessments/create',
      },
      {
        id: 'assessments-grades',
        title: 'Grades',
        icon: <GradeIcon />,
        path: '/dashboard/assessments/grades',
      },
    ]
  },

  // Communication (School/Tenant Only)
  {
    id: 'communication',
    title: 'Communication',
    icon: <ChatIcon />,
    section: 'communication',
    roles: ['admin', 'teacher', 'staff'], // Exclude superuser
    children: [
      {
        id: 'announcements',
        title: 'Announcements',
        icon: <AnnouncementIcon />,
        path: '/dashboard/announcements',
        badge: 3
      },
      {
        id: 'messages',
        title: 'Messages',
        icon: <ChatIcon />,
        path: '/dashboard/messages',
        badge: 12
      },
      {
        id: 'notifications',
        title: 'Notifications',
        icon: <NotificationsIcon />,
        path: '/dashboard/notifications',
        badge: 5
      },
    ]
  },

  // Resources (School/Tenant Only)
  {
    id: 'library',
    title: 'Library',
    icon: <LibraryIcon />,
    path: '/dashboard/library',
    section: 'resources',
    roles: ['admin', 'teacher', 'staff', 'student'] // Exclude superuser
  },
  {
    id: 'calendar',
    title: 'Calendar',
    icon: <CalendarIcon />,
    path: '/dashboard/calendar',
    section: 'resources',
    roles: ['admin', 'teacher', 'staff', 'student'] // Exclude superuser
  },

  // Financial (School/Tenant Only)
  {
    id: 'finance',
    title: 'Finance',
    icon: <PaymentIcon />,
    section: 'finance',
    roles: ['admin', 'finance'], // Already excludes superuser
    children: [
      {
        id: 'finance-fees',
        title: 'Fee Management',
        icon: <PaymentIcon />,
        path: '/dashboard/finance/fees',
      },
      {
        id: 'finance-payments',
        title: 'Payments',
        icon: <ReceiptIcon />,
        path: '/dashboard/finance/payments',
      },
      {
        id: 'finance-reports',
        title: 'Financial Reports',
        icon: <AccountBalanceIcon />,
        path: '/dashboard/finance/reports',
      },
    ]
  },

  // Analytics & Reports
  {
    id: 'analytics',
    title: 'Analytics',
    icon: <AnalyticsIcon />,
    section: 'reports',
    roles: ['admin'],
    children: [
      {
        id: 'analytics-overview',
        title: 'Overview',
        icon: <AnalyticsIcon />,
        path: '/dashboard/analytics',
      },
      {
        id: 'analytics-students',
        title: 'Student Analytics',
        icon: <PeopleIcon />,
        path: '/dashboard/analytics/students',
      },
      {
        id: 'analytics-courses',
        title: 'Course Analytics',
        icon: <ClassIcon />,
        path: '/dashboard/analytics/courses',
      },
    ]
  },
  {
    id: 'reports',
    title: 'Reports',
    icon: <ReportIcon />,
    path: '/dashboard/reports',
    section: 'reports',
    roles: ['admin', 'teacher']
  },

  // Platform Management (Platform Owner Only)
  {
    id: 'tenant-management',
    title: 'Tenant Management',
    icon: <TenantIcon />,
    section: 'platform',
    roles: ['superuser'],
    children: [
      {
        id: 'tenant-dashboard',
        title: 'Dashboard',
        icon: <DashboardIcon />,
        path: '/dashboard/tenant-management',
      },
      {
        id: 'tenant-list',
        title: 'All Schools',
        icon: <SchoolIcon />,
        path: '/dashboard/tenant-management/tenants',
      },
      {
        id: 'tenant-create',
        title: 'Add New School',
        icon: <BusinessIcon />,
        path: '/dashboard/tenant-management/create',
      },
    ]
  },

  {
    id: 'billing-management',
    title: 'Subscription & Billing',
    icon: <PaymentIcon />,
    section: 'platform',
    roles: ['superuser'],
    children: [
      {
        id: 'billing-overview',
        title: 'Billing Overview',
        icon: <RevenueIcon />,
        path: '/dashboard/billing-management',
      },
      {
        id: 'subscription-plans',
        title: 'Subscription Plans',
        icon: <ReceiptIcon />,
        path: '/dashboard/billing-management/plans',
      },
      {
        id: 'revenue-analytics',
        title: 'Revenue Analytics',
        icon: <AnalyticsIcon />,
        path: '/dashboard/billing-management/analytics',
      },
    ]
  },

  {
    id: 'user-management',
    title: 'Global User Management',
    icon: <UsersIcon />,
    section: 'platform',
    roles: ['superuser'],
    children: [
      {
        id: 'all-users',
        title: 'All Users',
        icon: <PeopleIcon />,
        path: '/dashboard/user-management',
      },
      {
        id: 'access-control',
        title: 'Access Control',
        icon: <AdminPanelSettingsIcon />,
        path: '/dashboard/user-management/access',
      },
      {
        id: 'security-policies',
        title: 'Security Policies',
        icon: <SecurityIcon />,
        path: '/dashboard/user-management/security',
      },
    ]
  },

  {
    id: 'platform-settings',
    title: 'Platform Settings',
    icon: <SettingsIcon />,
    section: 'platform',
    roles: ['superuser'],
    children: [
      {
        id: 'global-settings',
        title: 'Global Settings',
        icon: <SettingsIcon />,
        path: '/dashboard/platform-settings',
      },
      {
        id: 'feature-flags',
        title: 'Feature Flags',
        icon: <InfoIcon />,
        path: '/dashboard/platform-settings/features',
      },
      {
        id: 'system-config',
        title: 'System Configuration',
        icon: <BackupIcon />,
        path: '/dashboard/platform-settings/config',
      },
    ]
  },

  {
    id: 'platform-analytics',
    title: 'Monitoring & Analytics',
    icon: <AnalyticsIcon />,
    section: 'platform',
    roles: ['superuser'],
    children: [
      {
        id: 'platform-overview',
        title: 'Platform Overview',
        icon: <PlatformIcon />,
        path: '/dashboard/platform-analytics',
      },
      {
        id: 'usage-metrics',
        title: 'Usage Metrics',
        icon: <AnalyticsIcon />,
        path: '/dashboard/platform-analytics/usage',
      },
      {
        id: 'performance-monitoring',
        title: 'Performance Monitoring',
        icon: <ReportIcon />,
        path: '/dashboard/platform-analytics/performance',
      },
    ]
  },

  {
    id: 'support-center',
    title: 'Support & Communication',
    icon: <SupportIcon />,
    section: 'platform',
    roles: ['superuser'],
    children: [
      {
        id: 'support-tickets',
        title: 'Support Tickets',
        icon: <SupportIcon />,
        path: '/dashboard/support-center',
      },
      {
        id: 'announcements',
        title: 'Platform Announcements',
        icon: <AnnouncementIcon />,
        path: '/dashboard/support-center/announcements',
      },
      {
        id: 'communication-tools',
        title: 'Communication Tools',
        icon: <ChatIcon />,
        path: '/dashboard/support-center/communication',
      },
    ]
  },

  {
    id: 'compliance-center',
    title: 'Data Governance & Compliance',
    icon: <SecurityIcon />,
    section: 'platform',
    roles: ['superuser'],
    children: [
      {
        id: 'compliance-overview',
        title: 'Compliance Overview',
        icon: <SecurityIcon />,
        path: '/dashboard/compliance-center',
      },
      {
        id: 'data-privacy',
        title: 'Data Privacy (GDPR)',
        icon: <SecurityIcon />,
        path: '/dashboard/compliance-center/privacy',
      },
      {
        id: 'audit-logs',
        title: 'Audit Logs',
        icon: <ReportIcon />,
        path: '/dashboard/compliance-center/audits',
      },
    ]
  },

  // System Settings
  {
    id: 'settings',
    title: 'Settings',
    icon: <SettingsIcon />,
    section: 'system',
    roles: ['admin'],
    children: [
      {
        id: 'settings-general',
        title: 'General',
        icon: <SettingsIcon />,
        path: '/dashboard/settings',
      },
      {
        id: 'settings-users',
        title: 'User Management',
        icon: <SupervisorAccountIcon />,
        path: '/dashboard/settings/users',
      },
      {
        id: 'settings-security',
        title: 'Security',
        icon: <SecurityIcon />,
        path: '/dashboard/settings/security',
      },
      {
        id: 'settings-backup',
        title: 'Backup & Restore',
        icon: <BackupIcon />,
        path: '/dashboard/settings/backup',
      },
    ]
  },


]

export const sectionLabels = {
  main: 'Main',
  academic: 'Academic Management',
  communication: 'Communication',
  resources: 'Resources',
  finance: 'Finance',
  reports: 'Analytics & Reports',
  admin: 'Administration',
  platform: 'Platform Management',
  system: 'System',

}

// Helper function to filter navigation based on user role
export const filterNavigationByRole = (
  navigation: NavigationItem[],
  userRole: string,
  userPermissions: string[] = []
): NavigationItem[] => {
  return navigation.filter(item => {
    // If no roles specified, item is visible to all
    if (!item.roles || item.roles.length === 0) {
      return true
    }
    
    // Check if user role matches
    if (item.roles.includes(userRole)) {
      return true
    }
    
    // Check permissions if specified
    if (item.permissions && userPermissions.some(perm => item.permissions!.includes(perm))) {
      return true
    }
    
    return false
  }).map(item => ({
    ...item,
    children: item.children ? filterNavigationByRole(item.children, userRole, userPermissions) : undefined
  }))
}
