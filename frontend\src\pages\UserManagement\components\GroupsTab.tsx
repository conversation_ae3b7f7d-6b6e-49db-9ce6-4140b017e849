import React, { useState, useEffect } from 'react'
import {
  Box,
  Card,
  CardContent,
  <PERSON><PERSON>graphy,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Chip,
  CircularProgress,
  Alert,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  OutlinedInput,
  Checkbox,
  ListItemButton,
} from '@mui/material'
import {
  Group as GroupIcon,
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  People as PeopleIcon,
  PersonAdd as PersonAddIcon,
  Close as CloseIcon,
} from '@mui/icons-material'
import {
  getGroups,
  getPermissions,
  createGroup,
  updateGroup,
  deleteGroup,
  getUsers,
  getGroupUsers,
  assignUsersToGroup,
  type Group,
  type Permission,
  type GroupCreate,
  type User
} from '../../../services/userManagementService'

interface GroupsTabProps {
  refreshTrigger: number
  onGroupAction: (message: string, severity?: 'success' | 'error' | 'warning' | 'info') => void
}

// Use the GroupCreate interface from the service

const GroupsTab: React.FC<GroupsTabProps> = ({
  refreshTrigger,
  onGroupAction,
}) => {
  const [groups, setGroups] = useState<Group[]>([])
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(false)
  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [manageUsersDialogOpen, setManageUsersDialogOpen] = useState(false)
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null)
  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null)
  const [selectedUsers, setSelectedUsers] = useState<number[]>([])
  const [formData, setFormData] = useState<GroupCreate>({
    name: '',
    description: '',
    permissions: [],
  })

  const fetchData = async () => {
    setLoading(true)
    try {
      const [groupsData, permissionsData, usersData] = await Promise.all([
        getGroups(),
        getPermissions(),
        getUsers({ pageSize: 1000 }), // Get all users for assignment
      ])
      setGroups(groupsData)
      setPermissions(permissionsData)
      setUsers(usersData.results)
    } catch (error) {
      onGroupAction('Failed to fetch data', 'error')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [refreshTrigger])

  const handleCreateGroup = () => {
    setFormData({
      name: '',
      description: '',
      permissions: [],
    })
    setCreateDialogOpen(true)
  }

  const handleCloseCreateDialog = () => {
    setCreateDialogOpen(false)
    setFormData({
      name: '',
      description: '',
      permissions: [],
    })
  }

  const handleSubmitGroup = async () => {
    try {
      await createGroup(formData)
      onGroupAction('Group created successfully', 'success')
      handleCloseCreateDialog()
      fetchData()
    } catch (error) {
      onGroupAction('Failed to create group', 'error')
    }
  }

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, group: Group) => {
    setMenuAnchor(event.currentTarget)
    setSelectedGroup(group)
  }

  const handleMenuClose = () => {
    setMenuAnchor(null)
    setSelectedGroup(null)
  }

  const handleEditGroup = () => {
    // TODO: Implement edit functionality
    onGroupAction('Group editing not implemented yet', 'info')
    handleMenuClose()
  }

  const handleDeleteGroup = async () => {
    if (!selectedGroup) return

    try {
      await deleteGroup(selectedGroup.id)
      onGroupAction('Group deleted successfully', 'success')
      fetchData()
    } catch (error) {
      onGroupAction('Failed to delete group', 'error')
    }
    handleMenuClose()
  }

  const handleManageUsers = async () => {
    if (!selectedGroup) return

    try {
      // Get current users in the group
      const groupUsers = await getGroupUsers(selectedGroup.id)
      setSelectedUsers(groupUsers.map(user => user.id))
      setManageUsersDialogOpen(true)
    } catch (error) {
      // If API fails, start with empty selection
      setSelectedUsers([])
      setManageUsersDialogOpen(true)
    }
    handleMenuClose()
  }

  const handleCloseManageUsersDialog = () => {
    setManageUsersDialogOpen(false)
    setSelectedUsers([])
    setSelectedGroup(null)
  }

  const handleUserToggle = (userId: number) => {
    setSelectedUsers(prev =>
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    )
  }

  const handleSaveUserAssignments = async () => {
    if (!selectedGroup) return

    try {
      await assignUsersToGroup(selectedGroup.id, selectedUsers)
      onGroupAction('Users assigned to group successfully', 'success')
      handleCloseManageUsersDialog()
      fetchData()
    } catch (error) {
      onGroupAction('Failed to assign users to group', 'error')
    }
  }

  const handlePermissionToggle = (permissionId: string) => {
    setFormData(prev => ({
      ...prev,
      permissions: prev.permissions.includes(permissionId)
        ? prev.permissions.filter(id => id !== permissionId)
        : [...prev.permissions, permissionId]
    }))
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
        <CircularProgress />
      </Box>
    )
  }

  return (
    <Box sx={{ maxWidth: '100%', mx: 'auto' }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Box>
          <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
            User Groups
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Manage user groups and their permissions
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreateGroup}
          sx={{
            background: 'linear-gradient(45deg, #6366f1 30%, #8b5cf6 90%)',
            boxShadow: '0 3px 5px 2px rgba(99, 102, 241, .3)',
          }}
        >
          Create Group
        </Button>
      </Box>

      {/* Groups List */}
      <Card>
        <CardContent>
          {groups.length === 0 ? (
            <Alert severity="info" sx={{ textAlign: 'center' }}>
              <Typography variant="h6" sx={{ mb: 1 }}>
                No groups found
              </Typography>
              <Typography variant="body2">
                Groups help organize users with similar permissions. Create your first group to get started.
              </Typography>
            </Alert>
          ) : (
            <List>
              {groups.map((group, index) => (
                <ListItem key={group.id} divider={index < groups.length - 1}>
                  <ListItemIcon>
                    <GroupIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        {group.name}
                      </Typography>
                    }
                    secondary={
                      <Box component="div" sx={{ mt: 1 }}>
                        <Box component="div" sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                          <Box component="span" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            <PeopleIcon fontSize="small" color="action" />
                            <Typography component="span" variant="body2" color="text.secondary">
                              {group.userCount} users
                            </Typography>
                          </Box>
                          <Typography component="span" variant="body2" color="text.secondary">
                            {group.permissions.length} permissions
                          </Typography>
                        </Box>
                        <Box component="div" sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                          {group.permissions.slice(0, 4).map((permission, idx) => (
                            <Chip
                              key={idx}
                              label={permission}
                              size="small"
                              variant="outlined"
                              color="primary"
                            />
                          ))}
                          {group.permissions.length > 4 && (
                            <Chip
                              label={`+${group.permissions.length - 4} more`}
                              size="small"
                              variant="outlined"
                              color="secondary"
                            />
                          )}
                        </Box>
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <IconButton
                      edge="end"
                      onClick={(e) => handleMenuOpen(e, group)}
                    >
                      <MoreVertIcon />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          )}
        </CardContent>
      </Card>

      {/* Context Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleManageUsers}>
          <PersonAddIcon sx={{ mr: 1 }} fontSize="small" />
          Manage Users
        </MenuItem>
        <MenuItem onClick={handleEditGroup}>
          <EditIcon sx={{ mr: 1 }} fontSize="small" />
          Edit Group
        </MenuItem>
        <MenuItem onClick={handleDeleteGroup} sx={{ color: 'error.main' }}>
          <DeleteIcon sx={{ mr: 1 }} fontSize="small" />
          Delete Group
        </MenuItem>
      </Menu>

      {/* Create Group Dialog */}
      <Dialog
        open={createDialogOpen}
        onClose={handleCloseCreateDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Create New Group</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              label="Group Name"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              sx={{ mb: 3 }}
              required
            />
            <TextField
              fullWidth
              label="Description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              multiline
              rows={3}
              sx={{ mb: 3 }}
            />
            <Typography variant="h6" sx={{ mb: 2 }}>
              Permissions
            </Typography>
            <Box sx={{ maxHeight: 300, overflow: 'auto', border: '1px solid #e0e0e0', borderRadius: 1 }}>
              <List dense>
                {permissions.map((permission) => (
                  <ListItemButton
                    key={permission.id}
                    onClick={() => handlePermissionToggle(permission.id.toString())}
                  >
                    <Checkbox
                      checked={formData.permissions.includes(permission.id.toString())}
                      tabIndex={-1}
                      disableRipple
                    />
                    <ListItemText
                      primary={permission.name}
                      secondary={`${permission.contentTypeName} • ${permission.codename}`}
                    />
                  </ListItemButton>
                ))}
              </List>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseCreateDialog}>Cancel</Button>
          <Button
            onClick={handleSubmitGroup}
            variant="contained"
            disabled={!formData.name.trim()}
          >
            Create Group
          </Button>
        </DialogActions>
      </Dialog>

      {/* Manage Users Dialog */}
      <Dialog
        open={manageUsersDialogOpen}
        onClose={handleCloseManageUsersDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h6">
              Manage Users - {selectedGroup?.name}
            </Typography>
            <IconButton onClick={handleCloseManageUsersDialog}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Select users to assign to this group. Users will inherit all permissions from the group.
            </Typography>

            <Box sx={{
              maxHeight: 400,
              overflow: 'auto',
              border: '1px solid #e0e0e0',
              borderRadius: 1,
              bgcolor: 'background.paper'
            }}>
              <List dense>
                {users.map((user) => (
                  <ListItemButton
                    key={user.id}
                    onClick={() => handleUserToggle(user.id)}
                  >
                    <Checkbox
                      checked={selectedUsers.includes(user.id)}
                      tabIndex={-1}
                      disableRipple
                    />
                    <ListItemText
                      primary={`${user.firstName} ${user.lastName}`}
                      secondary={
                        <Box component="div">
                          <Typography component="div" variant="body2" color="text.secondary">
                            {user.email}
                          </Typography>
                          <Box component="div" sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
                            <Chip
                              label={user.userType}
                              size="small"
                              variant="outlined"
                              color="primary"
                            />
                            {user.isStaff && (
                              <Chip
                                label="Staff"
                                size="small"
                                variant="outlined"
                                color="secondary"
                              />
                            )}
                            {!user.isActive && (
                              <Chip
                                label="Inactive"
                                size="small"
                                variant="outlined"
                                color="error"
                              />
                            )}
                          </Box>
                        </Box>
                      }
                    />
                  </ListItemButton>
                ))}
                {users.length === 0 && (
                  <ListItem>
                    <ListItemText
                      primary={
                        <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 2 }}>
                          No users available
                        </Typography>
                      }
                    />
                  </ListItem>
                )}
              </List>
            </Box>

            <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
              <Typography variant="body2" color="text.secondary">
                <strong>{selectedUsers.length}</strong> users selected
              </Typography>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseManageUsersDialog}>Cancel</Button>
          <Button
            onClick={handleSaveUserAssignments}
            variant="contained"
            disabled={selectedUsers.length === 0}
          >
            Assign Users ({selectedUsers.length})
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default GroupsTab
