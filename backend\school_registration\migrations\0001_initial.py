# Generated by Django 5.1.7 on 2025-04-06 14:50

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='SchoolRegistrationRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(help_text='School/Institution name', max_length=100)),
                ('slug', models.SlugField(blank=True, help_text="Unique identifier for the school's URL", max_length=100, unique=True)),
                ('address', models.TextField(help_text='Physical address of the school')),
                ('city', models.Char<PERSON>ield(max_length=100)),
                ('state', models.Char<PERSON>ield(max_length=100)),
                ('country', models.Char<PERSON>ield(default='Ethiopia', max_length=100)),
                ('postal_code', models.Char<PERSON><PERSON>(blank=True, max_length=20, null=True)),
                ('contact_person', models.<PERSON><PERSON><PERSON><PERSON>(help_text='Name of the contact person', max_length=100)),
                ('contact_email', models.EmailField(help_text='Email address for school communications', max_length=254)),
                ('contact_phone', models.CharField(help_text='Phone number for school communications', max_length=17, validators=[django.core.validators.RegexValidator(message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.", regex='^\\+?1?\\d{9,15}$')])),
                ('website', models.URLField(blank=True, help_text='School website URL', null=True)),
                ('established_year', models.PositiveIntegerField(help_text='Year the school was established')),
                ('school_type', models.CharField(help_text='Type of school (e.g., Primary, Secondary, College)', max_length=50)),
                ('student_capacity', models.PositiveIntegerField(help_text='Maximum number of students')),
                ('description', models.TextField(help_text='Brief description of the school')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected')], default='pending', max_length=20)),
                ('requested_on', models.DateTimeField(auto_now_add=True)),
                ('processed_on', models.DateTimeField(blank=True, null=True)),
                ('rejection_reason', models.TextField(blank=True, help_text="Reason for rejection if status is 'rejected'", null=True)),
                ('schema_name', models.CharField(blank=True, help_text='Database schema name for the tenant', max_length=100, null=True)),
                ('domain', models.CharField(blank=True, help_text='Subdomain for the school', max_length=100, null=True)),
            ],
        ),
    ]
