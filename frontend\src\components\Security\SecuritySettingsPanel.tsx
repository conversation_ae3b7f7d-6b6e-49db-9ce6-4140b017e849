import React, { useState, useCallback } from 'react'
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  TextField,
  Switch,
  FormControlLabel,
  Button,
  Grid,
  Divider,
  Alert,
  Chip,
  IconButton,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
} from '@mui/material'
import {
  Lock as LockIcon,
  AccessTime as AccessTimeIcon,
  Shield as ShieldIcon,
  History as HistoryIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Security as SecurityIcon,
} from '@mui/icons-material'
import { SecuritySettings } from '../../services/securityService'

interface SecuritySettingsPanelProps {
  settings: SecuritySettings
  onUpdateSettings: (settings: SecuritySettings) => void
  loading?: boolean
}

const SecuritySettingsPanel: React.FC<SecuritySettingsPanelProps> = ({
  settings,
  onUpdateSettings,
  loading = false,
}) => {
  const [localSettings, setLocalSettings] = useState<SecuritySettings>(settings)
  const [hasChanges, setHasChanges] = useState(false)
  const [newIP, setNewIP] = useState('')

  const handleSettingChange = useCallback((section: keyof SecuritySettings, field: string, value: any) => {
    setLocalSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
      },
    }))
    setHasChanges(true)
  }, [])

  const handleSave = useCallback(() => {
    onUpdateSettings(localSettings)
    setHasChanges(false)
  }, [localSettings, onUpdateSettings])

  const handleReset = useCallback(() => {
    setLocalSettings(settings)
    setHasChanges(false)
  }, [settings])

  const handleAddIP = useCallback(() => {
    if (newIP && !localSettings.login_security.allowed_ips.includes(newIP)) {
      handleSettingChange('login_security', 'allowed_ips', [
        ...localSettings.login_security.allowed_ips,
        newIP,
      ])
      setNewIP('')
    }
  }, [newIP, localSettings.login_security.allowed_ips, handleSettingChange])

  const handleRemoveIP = useCallback((ip: string) => {
    handleSettingChange('login_security', 'allowed_ips', 
      localSettings.login_security.allowed_ips.filter(allowedIP => allowedIP !== ip)
    )
  }, [localSettings.login_security.allowed_ips, handleSettingChange])

  const getPasswordStrengthIndicator = () => {
    let strength = 0
    if (localSettings.password_policy.min_length >= 8) strength++
    if (localSettings.password_policy.require_uppercase) strength++
    if (localSettings.password_policy.require_lowercase) strength++
    if (localSettings.password_policy.require_numbers) strength++
    if (localSettings.password_policy.require_symbols) strength++

    const strengthLabels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong']
    const strengthColors = ['error', 'error', 'warning', 'info', 'success']
    
    return {
      label: strengthLabels[strength] || 'Very Weak',
      color: strengthColors[strength] || 'error',
      score: strength,
    }
  }

  const passwordStrength = getPasswordStrengthIndicator()

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">Security Configuration</Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={handleReset}
            disabled={!hasChanges || loading}
          >
            Reset
          </Button>
          <Button
            variant="contained"
            startIcon={<SaveIcon />}
            onClick={handleSave}
            disabled={!hasChanges || loading}
            sx={{
              background: hasChanges ? 'linear-gradient(45deg, #6366f1 30%, #8b5cf6 90%)' : undefined,
            }}
          >
            Save Changes
          </Button>
        </Box>
      </Box>

      {hasChanges && (
        <Alert severity="info" sx={{ mb: 3 }}>
          You have unsaved changes. Click "Save Changes" to apply them.
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Password Policy */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <LockIcon color="primary" />
                  <Typography variant="h6">Password Policy</Typography>
                  <Chip
                    label={passwordStrength.label}
                    size="small"
                    color={passwordStrength.color as any}
                  />
                </Box>
              }
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Minimum Length"
                    type="number"
                    value={localSettings.password_policy.min_length}
                    onChange={(e) => handleSettingChange('password_policy', 'min_length', parseInt(e.target.value))}
                    size="small"
                    inputProps={{ min: 4, max: 128 }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={localSettings.password_policy.require_uppercase}
                        onChange={(e) => handleSettingChange('password_policy', 'require_uppercase', e.target.checked)}
                      />
                    }
                    label="Require Uppercase Letters"
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={localSettings.password_policy.require_lowercase}
                        onChange={(e) => handleSettingChange('password_policy', 'require_lowercase', e.target.checked)}
                      />
                    }
                    label="Require Lowercase Letters"
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={localSettings.password_policy.require_numbers}
                        onChange={(e) => handleSettingChange('password_policy', 'require_numbers', e.target.checked)}
                      />
                    }
                    label="Require Numbers"
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={localSettings.password_policy.require_symbols}
                        onChange={(e) => handleSettingChange('password_policy', 'require_symbols', e.target.checked)}
                      />
                    }
                    label="Require Special Characters"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Password Expiry (days)"
                    type="number"
                    value={localSettings.password_policy.password_expiry_days}
                    onChange={(e) => handleSettingChange('password_policy', 'password_expiry_days', parseInt(e.target.value))}
                    size="small"
                    inputProps={{ min: 0, max: 365 }}
                    helperText="Set to 0 to disable password expiry"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Password History Count"
                    type="number"
                    value={localSettings.password_policy.password_history_count}
                    onChange={(e) => handleSettingChange('password_policy', 'password_history_count', parseInt(e.target.value))}
                    size="small"
                    inputProps={{ min: 0, max: 24 }}
                    helperText="Number of previous passwords to remember"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Session Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <AccessTimeIcon color="primary" />
                  <Typography variant="h6">Session Settings</Typography>
                </Box>
              }
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Session Timeout (minutes)"
                    type="number"
                    value={localSettings.session_settings.session_timeout_minutes}
                    onChange={(e) => handleSettingChange('session_settings', 'session_timeout_minutes', parseInt(e.target.value))}
                    size="small"
                    inputProps={{ min: 5, max: 1440 }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Max Concurrent Sessions"
                    type="number"
                    value={localSettings.session_settings.max_concurrent_sessions}
                    onChange={(e) => handleSettingChange('session_settings', 'max_concurrent_sessions', parseInt(e.target.value))}
                    size="small"
                    inputProps={{ min: 1, max: 10 }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={localSettings.session_settings.require_2fa}
                        onChange={(e) => handleSettingChange('session_settings', 'require_2fa', e.target.checked)}
                      />
                    }
                    label="Require Two-Factor Authentication"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Remember Me Duration (days)"
                    type="number"
                    value={localSettings.session_settings.remember_me_duration_days}
                    onChange={(e) => handleSettingChange('session_settings', 'remember_me_duration_days', parseInt(e.target.value))}
                    size="small"
                    inputProps={{ min: 1, max: 365 }}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Login Security */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <ShieldIcon color="primary" />
                  <Typography variant="h6">Login Security</Typography>
                </Box>
              }
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Max Failed Attempts"
                    type="number"
                    value={localSettings.login_security.max_failed_attempts}
                    onChange={(e) => handleSettingChange('login_security', 'max_failed_attempts', parseInt(e.target.value))}
                    size="small"
                    inputProps={{ min: 1, max: 20 }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Lockout Duration (minutes)"
                    type="number"
                    value={localSettings.login_security.lockout_duration_minutes}
                    onChange={(e) => handleSettingChange('login_security', 'lockout_duration_minutes', parseInt(e.target.value))}
                    size="small"
                    inputProps={{ min: 1, max: 1440 }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={localSettings.login_security.enable_captcha}
                        onChange={(e) => handleSettingChange('login_security', 'enable_captcha', e.target.checked)}
                      />
                    }
                    label="Enable CAPTCHA"
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={localSettings.login_security.enable_ip_whitelist}
                        onChange={(e) => handleSettingChange('login_security', 'enable_ip_whitelist', e.target.checked)}
                      />
                    }
                    label="Enable IP Whitelist"
                  />
                </Grid>

                {/* IP Whitelist Management */}
                {localSettings.login_security.enable_ip_whitelist && (
                  <Grid item xs={12}>
                    <Accordion>
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Typography variant="body2">
                          Manage IP Whitelist ({localSettings.login_security.allowed_ips.length} IPs)
                        </Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        <Box sx={{ mb: 2 }}>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <TextField
                              fullWidth
                              label="Add IP Address"
                              value={newIP}
                              onChange={(e) => setNewIP(e.target.value)}
                              size="small"
                              placeholder="***********"
                            />
                            <Button
                              variant="outlined"
                              startIcon={<AddIcon />}
                              onClick={handleAddIP}
                              disabled={!newIP}
                            >
                              Add
                            </Button>
                          </Box>
                        </Box>
                        <List dense>
                          {localSettings.login_security.allowed_ips.map((ip, index) => (
                            <ListItem key={index}>
                              <ListItemText
                                primary={ip}
                                primaryTypographyProps={{ fontFamily: 'monospace' }}
                              />
                              <ListItemSecondaryAction>
                                <Tooltip title="Remove IP">
                                  <IconButton
                                    size="small"
                                    onClick={() => handleRemoveIP(ip)}
                                    color="error"
                                  >
                                    <DeleteIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              </ListItemSecondaryAction>
                            </ListItem>
                          ))}
                        </List>
                        {localSettings.login_security.allowed_ips.length === 0 && (
                          <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 2 }}>
                            No IP addresses in whitelist
                          </Typography>
                        )}
                      </AccordionDetails>
                    </Accordion>
                  </Grid>
                )}
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Audit Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <HistoryIcon color="primary" />
                  <Typography variant="h6">Audit Settings</Typography>
                </Box>
              }
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Log Retention (days)"
                    type="number"
                    value={localSettings.audit_settings.log_retention_days}
                    onChange={(e) => handleSettingChange('audit_settings', 'log_retention_days', parseInt(e.target.value))}
                    size="small"
                    inputProps={{ min: 1, max: 2555 }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={localSettings.audit_settings.enable_real_time_alerts}
                        onChange={(e) => handleSettingChange('audit_settings', 'enable_real_time_alerts', e.target.checked)}
                      />
                    }
                    label="Enable Real-time Alerts"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Alert Email"
                    type="email"
                    value={localSettings.audit_settings.alert_email}
                    onChange={(e) => handleSettingChange('audit_settings', 'alert_email', e.target.value)}
                    size="small"
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={localSettings.audit_settings.log_failed_logins}
                        onChange={(e) => handleSettingChange('audit_settings', 'log_failed_logins', e.target.checked)}
                      />
                    }
                    label="Log Failed Login Attempts"
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={localSettings.audit_settings.log_data_access}
                        onChange={(e) => handleSettingChange('audit_settings', 'log_data_access', e.target.checked)}
                      />
                    }
                    label="Log Data Access Events"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  )
}

export default SecuritySettingsPanel
