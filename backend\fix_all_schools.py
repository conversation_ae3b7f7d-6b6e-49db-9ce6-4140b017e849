import os
import django
import traceback

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kelem_sms.settings')
django.setup()

from school_registration.models import SchoolRegistrationRequest
from tenants.models import School, Domain
from django_tenants.utils import schema_context
from django.contrib.auth import get_user_model
from django.utils import timezone
import secrets
import string

def create_admin_user_for_tenant(school, registration):
    """Create an admin user for the tenant."""
    print(f"Creating admin user for tenant {school.name} with schema {school.schema_name}")
    print(f"Registration ID: {registration.id}, Status: {registration.status}")
    print(f"Admin Email from registration: {registration.admin_email}")
    print(f"Admin Password provided: {'Yes' if registration.admin_password else 'No'}")

    # Use admin_password if provided, otherwise generate a random password
    if registration.admin_password:
        password = registration.admin_password
        print(f"Using provided admin password")
    else:
        password = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(12))
        print(f"Generated random password: {password}")

    try:
        # Use schema_context instead of manually setting the schema
        with schema_context(school.schema_name):
            # Get the User model inside the schema context
            User = get_user_model()
            print(f"User model class: {User.__name__}")
            print(f"User model fields: {[f.name for f in User._meta.get_fields()]}")

            # Use admin_email if provided, otherwise use contact_email
            email = registration.admin_email if registration.admin_email else registration.contact_email
            print(f"Using email {email} for admin user")

            # Parse the contact person name
            if ' ' in registration.contact_person:
                first_name = registration.contact_person.split()[0]
                last_name = registration.contact_person.split()[-1]
            else:
                first_name = registration.contact_person
                last_name = ''

            print(f"Creating user with first_name={first_name}, last_name={last_name}")

            # Check if any users exist in this schema
            print(f"Total users in schema: {User.objects.count()}")

            # Check if a user with this email already exists
            existing_user = User.objects.filter(email=email).first()
            if existing_user:
                print(f"User with email {email} already exists with ID {existing_user.id}")
                # Return the existing user
                return {
                    'email': existing_user.email,
                    'password': password,  # We don't know the actual password, so return the one we would have used
                    'first_name': existing_user.first_name,
                    'last_name': existing_user.last_name,
                    'note': 'User already existed'
                }

            # Create the admin user
            try:
                # Check if the User model has a create_user method
                print(f"User model has create_user method: {hasattr(User, 'create_user')}")
                print(f"User model has objects manager: {hasattr(User, 'objects')}")
                print(f"User objects manager has create_user method: {hasattr(User.objects, 'create_user')}")

                # Our custom User model uses email as the username field and has username=None
                # So we should never try to create a user with a username field
                admin_user = User.objects.create_user(
                    email=email,
                    password=password,
                    first_name=first_name,
                    last_name=last_name,
                    user_type='admin',
                    is_staff=True,
                    is_active=True,
                    is_superuser=True  # Make them a superuser for this tenant
                )
                print(f"Admin user created successfully with ID {admin_user.id}")

                # Return the admin user and password
                return {
                    'email': admin_user.email,
                    'password': password,
                    'first_name': admin_user.first_name,
                    'last_name': admin_user.last_name
                }
            except Exception as inner_e:
                print(f"Error creating user object: {str(inner_e)}")
                traceback.print_exc()
                raise
    except Exception as e:
        print(f"Error creating admin user: {str(e)}")
        traceback.print_exc()
        # Return a default response so the tenant creation can still proceed
        return {
            'email': registration.contact_email,
            'password': password,
            'error': str(e)
        }

def fix_all_schools():
    """Fix all schools by creating admin users for them."""
    print("=== Fixing All Schools ===")
    
    # Get all schools
    schools = School.objects.all()
    print(f"Found {schools.count()} schools/tenants")
    
    for school in schools:
        print(f"\nProcessing school: {school.name} with schema: {school.schema_name}")
        
        # Skip the public schema
        if school.schema_name == 'public':
            print("Skipping public schema")
            continue
        
        # Check if admin users exist
        with schema_context(school.schema_name):
            User = get_user_model()
            admin_users = User.objects.filter(user_type='admin', is_superuser=True)
            print(f"Found {admin_users.count()} admin users in schema {school.schema_name}")
            
            if admin_users.count() == 0:
                # Find the registration request for this school
                registration = SchoolRegistrationRequest.objects.filter(schema_name=school.schema_name).first()
                if registration:
                    print(f"Found registration request for {school.name}")
                    # Create admin user
                    admin_user = create_admin_user_for_tenant(school, registration)
                    print(f"Created admin user: {admin_user}")
                else:
                    print(f"No registration request found for {school.name}")
                    # Create a default admin user
                    admin_email = f"admin@{school.schema_name}.kelemsms.com"
                    password = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(12))
                    
                    # Create a temporary registration object
                    temp_registration = SchoolRegistrationRequest(
                        id=0,  # Temporary ID
                        name=school.name,
                        contact_person=f"Admin {school.name}",
                        contact_email=school.contact_email or admin_email,
                        admin_email=admin_email,
                        admin_password=password,
                        schema_name=school.schema_name,
                        status='approved',
                        processed_on=timezone.now()
                    )
                    
                    # Create admin user
                    admin_user = create_admin_user_for_tenant(school, temp_registration)
                    print(f"Created admin user with temporary registration: {admin_user}")

if __name__ == "__main__":
    fix_all_schools()
