from django.db import models
from django.contrib.auth.models import AbstractUser, BaseUserManager
from django.utils.translation import gettext_lazy as _

class UserManager(BaseUserManager):
    """Define a model manager for User model with no username field."""

    use_in_migrations = True

    def _create_user(self, email, password, **extra_fields):
        """Create and save a User with the given email and password."""
        if not email:
            raise ValueError('The given email must be set')
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_user(self, email, password=None, **extra_fields):
        """Create and save a regular User with the given email and password."""
        extra_fields.setdefault('is_staff', False)
        extra_fields.setdefault('is_superuser', False)
        return self._create_user(email, password, **extra_fields)

    def create_superuser(self, email, password, **extra_fields):
        """Create and save a SuperUser with the given email and password."""
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)

        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser must have is_superuser=True.')

        return self._create_user(email, password, **extra_fields)

class User(AbstractUser):
    """Custom User model with email as the unique identifier."""

    USER_TYPE_CHOICES = (
        ('admin', 'Administrator'),
        ('teacher', 'Teacher'),
        ('student', 'Student'),
        ('parent', 'Parent'),
        ('staff', 'Staff'),
    )

    username = None
    email = models.EmailField(_('email address'), unique=True)
    user_type = models.CharField(max_length=20, choices=USER_TYPE_CHOICES, default='student')
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    date_joined = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []

    objects = UserManager()

    def __str__(self):
        return self.email

    def get_full_name(self):
        return f"{self.first_name} {self.last_name}"

class AdminProfile(models.Model):
    """Profile for administrators."""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='admin_profile')
    department = models.CharField(max_length=100, blank=True, null=True)
    is_system_admin = models.BooleanField(default=False)

    def __str__(self):
        return f"Admin: {self.user.get_full_name()}"

class TeacherProfile(models.Model):
    """Profile for teachers."""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='teacher_profile')
    employee_id = models.CharField(max_length=20, unique=True)
    department = models.CharField(max_length=100, blank=True, null=True)
    # Remove direct reference to Course model to avoid circular dependency
    # subjects = models.ManyToManyField('courses.Course', blank=True, related_name='teachers')
    qualification = models.CharField(max_length=100, blank=True, null=True)
    date_of_joining = models.DateField(blank=True, null=True)

    def __str__(self):
        return f"Teacher: {self.user.get_full_name()} ({self.employee_id})"

class StudentProfile(models.Model):
    """Profile for students."""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='student_profile')
    # Use string reference to avoid circular dependency
    student_id = models.CharField(max_length=20, unique=True, help_text='Reference to student ID')

    def __str__(self):
        return f"Student: {self.user.get_full_name()} ({self.student_id})"

class ParentProfile(models.Model):
    """Profile for parents/guardians."""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='parent_profile')
    # Use simple fields to avoid circular dependency
    guardian_name = models.CharField(max_length=100, blank=True, null=True)
    relationship = models.CharField(max_length=50, blank=True, null=True)

    def __str__(self):
        return f"Parent: {self.user.get_full_name()}"

class StaffProfile(models.Model):
    """Profile for non-teaching staff."""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='staff_profile')
    employee_id = models.CharField(max_length=20, unique=True)
    department = models.CharField(max_length=100, blank=True, null=True)
    position = models.CharField(max_length=100, blank=True, null=True)
    date_of_joining = models.DateField(blank=True, null=True)

    def __str__(self):
        return f"Staff: {self.user.get_full_name()} ({self.employee_id})"
