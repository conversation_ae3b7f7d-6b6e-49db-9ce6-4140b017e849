from django.contrib import admin
from django.utils import timezone
from .models import TenantMetrics, TenantActivity, TenantStatus
from .services import collect_tenant_metrics

@admin.register(TenantMetrics)
class TenantMetricsAdmin(admin.ModelAdmin):
    list_display = ('tenant', 'timestamp', 'total_users', 'active_users', 'total_students', 'total_courses', 'database_size_mb', 'file_storage_size_mb')
    list_filter = ('tenant', 'timestamp')
    search_fields = ('tenant__name',)
    date_hierarchy = 'timestamp'
    readonly_fields = ('tenant', 'timestamp', 'total_users', 'active_users', 'admin_users', 'teacher_users',
                      'student_users', 'parent_users', 'staff_users', 'total_students', 'total_courses',
                      'total_departments', 'total_enrollments', 'database_size', 'file_storage_size',
                      'avg_response_time', 'peak_response_time', 'api_requests_count', 'error_count')

    def database_size_mb(self, obj):
        return f"{obj.database_size / 1024 / 1024:.2f} MB"
    database_size_mb.short_description = "Database Size (MB)"

    def file_storage_size_mb(self, obj):
        return f"{obj.file_storage_size / 1024 / 1024:.2f} MB"
    file_storage_size_mb.short_description = "File Storage Size (MB)"

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    actions = ['collect_metrics_for_selected']

    def collect_metrics_for_selected(self, request, queryset):
        tenant_ids = queryset.values_list('tenant_id', flat=True).distinct()
        metrics_count = 0

        for tenant_id in tenant_ids:
            metrics = collect_tenant_metrics(tenant_id)
            metrics_count += len(metrics)

        self.message_user(request, f"Collected metrics for {metrics_count} tenants.")
    collect_metrics_for_selected.short_description = "Collect fresh metrics for selected tenants"

@admin.register(TenantActivity)
class TenantActivityAdmin(admin.ModelAdmin):
    list_display = ('tenant', 'timestamp', 'activity_type', 'description', 'user_count')
    list_filter = ('tenant', 'activity_type', 'timestamp')
    search_fields = ('tenant__name', 'activity_type', 'description')
    date_hierarchy = 'timestamp'
    readonly_fields = ('tenant', 'timestamp', 'activity_type', 'description', 'user_count')

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

@admin.register(TenantStatus)
class TenantStatusAdmin(admin.ModelAdmin):
    list_display = ('tenant', 'status', 'subscription_plan', 'subscription_end_date', 'max_users', 'max_storage_gb', 'get_subscription_valid')
    list_filter = ('status', 'subscription_plan')
    search_fields = ('tenant__name', 'admin_email')
    readonly_fields = ('tenant', 'created_at', 'last_updated')
    fieldsets = (
        ('Tenant Information', {
            'fields': ('tenant', 'status', 'created_at', 'last_updated', 'notes')
        }),
        ('Subscription Details', {
            'fields': ('subscription_plan', 'subscription_start_date', 'subscription_end_date', 'max_users', 'max_storage')
        }),
        ('Contact Information', {
            'fields': ('admin_email', 'admin_phone')
        }),
        ('Settings', {
            'fields': ('is_featured',)
        }),
    )

    def max_storage_gb(self, obj):
        return f"{obj.max_storage / 1024 / 1024 / 1024:.2f} GB"
    max_storage_gb.short_description = "Max Storage (GB)"

    def get_subscription_valid(self, obj):
        return obj.is_subscription_valid()
    get_subscription_valid.short_description = "Subscription Valid"
    get_subscription_valid.boolean = True

    actions = ['activate_tenants', 'suspend_tenants', 'extend_subscription']

    def activate_tenants(self, request, queryset):
        updated = queryset.update(status='active', last_updated=timezone.now())
        self.message_user(request, f"{updated} tenants have been activated.")
    activate_tenants.short_description = "Activate selected tenants"

    def suspend_tenants(self, request, queryset):
        updated = queryset.update(status='suspended', last_updated=timezone.now())
        self.message_user(request, f"{updated} tenants have been suspended.")
    suspend_tenants.short_description = "Suspend selected tenants"

    def extend_subscription(self, request, queryset):
        for status in queryset:
            if status.subscription_end_date:
                # Extend by 1 year
                if status.subscription_end_date < timezone.now().date():
                    # If expired, extend from today
                    status.subscription_end_date = (timezone.now() + timezone.timedelta(days=365)).date()
                else:
                    # If not expired, extend from current end date
                    status.subscription_end_date = (status.subscription_end_date + timezone.timedelta(days=365))
            else:
                # If no end date, set to 1 year from now
                status.subscription_end_date = (timezone.now() + timezone.timedelta(days=365)).date()

            status.save()

        self.message_user(request, f"Extended subscription for {queryset.count()} tenants by 1 year.")
    extend_subscription.short_description = "Extend subscription by 1 year"
