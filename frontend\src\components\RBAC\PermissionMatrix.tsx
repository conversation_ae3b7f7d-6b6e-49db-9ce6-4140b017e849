import React, { useState, useEffect, useMemo } from 'react'
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Checkbox,
  Chip,
  IconButton,
  Tooltip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  Switch,
  FormControlLabel,
} from '@mui/material'
import {
  ExpandMore as ExpandMoreIcon,
  Security as SecurityIcon,
  Info as InfoIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material'
import { Role, Permission, PermissionMatrix as PermissionMatrixType, ResourceType, ActionType } from '../../types/rbac'

interface PermissionMatrixProps {
  roles: Role[]
  permissions: Permission[]
  matrix: PermissionMatrixType[]
  onUpdateMatrix: (matrix: PermissionMatrixType[]) => void
  onSaveChanges: () => void
  loading?: boolean
  readOnly?: boolean
}

const PermissionMatrix: React.FC<PermissionMatrixProps> = ({
  roles,
  permissions,
  matrix,
  onUpdateMatrix,
  onSaveChanges,
  loading = false,
  readOnly = false,
}) => {
  const [localMatrix, setLocalMatrix] = useState<PermissionMatrixType[]>(matrix)
  const [hasChanges, setHasChanges] = useState(false)
  const [selectedResource, setSelectedResource] = useState<ResourceType | 'all'>('all')
  const [showInheritedPermissions, setShowInheritedPermissions] = useState(true)
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set())
  const [dialogOpen, setDialogOpen] = useState(false)
  const [selectedCell, setSelectedCell] = useState<{
    roleId: string
    resourceType: ResourceType
    action: ActionType
  } | null>(null)

  useEffect(() => {
    setLocalMatrix(matrix)
    setHasChanges(false)
  }, [matrix])

  const resourceTypes = useMemo(() => {
    return Object.values(ResourceType).sort()
  }, [])

  const actionTypes = useMemo(() => {
    return Object.values(ActionType).sort()
  }, [])

  const filteredMatrix = useMemo(() => {
    if (selectedResource === 'all') return localMatrix
    return localMatrix.filter(item => item.resource_type === selectedResource)
  }, [localMatrix, selectedResource])

  const getPermissionStatus = (roleId: string, resourceType: ResourceType, action: ActionType) => {
    const role = roles.find(r => r.id === roleId)
    if (!role) return { hasPermission: false, inherited: false, direct: false }

    const permission = permissions.find(p => 
      p.resource_type === resourceType && 
      p.action === action
    )
    if (!permission) return { hasPermission: false, inherited: false, direct: false }

    const directPermission = role.permissions.includes(permission.id)
    const inheritedPermission = role.inherited_permissions.includes(permission.id)
    const effectivePermission = role.effective_permissions.includes(permission.id)

    return {
      hasPermission: effectivePermission,
      inherited: inheritedPermission && !directPermission,
      direct: directPermission,
    }
  }

  const handlePermissionToggle = (roleId: string, resourceType: ResourceType, action: ActionType) => {
    if (readOnly) return

    const permission = permissions.find(p => 
      p.resource_type === resourceType && 
      p.action === action
    )
    if (!permission) return

    const role = roles.find(r => r.id === roleId)
    if (!role) return

    const currentStatus = getPermissionStatus(roleId, resourceType, action)
    
    // Update the role's permissions
    const updatedRoles = roles.map(r => {
      if (r.id === roleId) {
        const newPermissions = currentStatus.direct
          ? r.permissions.filter(p => p !== permission.id)
          : [...r.permissions, permission.id]
        
        return {
          ...r,
          permissions: newPermissions,
        }
      }
      return r
    })

    setHasChanges(true)
    // Trigger parent update
    onUpdateMatrix(localMatrix)
  }

  const handleCellClick = (roleId: string, resourceType: ResourceType, action: ActionType) => {
    setSelectedCell({ roleId, resourceType, action })
    setDialogOpen(true)
  }

  const handleBulkAssign = (resourceType: ResourceType, action: ActionType, assign: boolean) => {
    if (readOnly) return

    const permission = permissions.find(p => 
      p.resource_type === resourceType && 
      p.action === action
    )
    if (!permission) return

    roles.forEach(role => {
      const currentStatus = getPermissionStatus(role.id, resourceType, action)
      if (assign && !currentStatus.direct) {
        handlePermissionToggle(role.id, resourceType, action)
      } else if (!assign && currentStatus.direct) {
        handlePermissionToggle(role.id, resourceType, action)
      }
    })
  }

  const getPermissionIcon = (status: { hasPermission: boolean; inherited: boolean; direct: boolean }) => {
    if (status.direct) {
      return <CheckCircleIcon color="success" fontSize="small" />
    } else if (status.inherited) {
      return <CheckCircleIcon color="info" fontSize="small" />
    } else {
      return null
    }
  }

  const getPermissionTooltip = (status: { hasPermission: boolean; inherited: boolean; direct: boolean }) => {
    if (status.direct) {
      return 'Direct permission'
    } else if (status.inherited) {
      return 'Inherited permission'
    } else {
      return 'No permission'
    }
  }

  const getRoleHierarchyLevel = (role: Role) => {
    return role.level || 0
  }

  const sortedRoles = useMemo(() => {
    return [...roles].sort((a, b) => {
      const levelDiff = getRoleHierarchyLevel(b) - getRoleHierarchyLevel(a)
      if (levelDiff !== 0) return levelDiff
      return a.name.localeCompare(b.name)
    })
  }, [roles])

  const getConflictWarnings = () => {
    const warnings: string[] = []
    
    // Check for roles with excessive permissions
    roles.forEach(role => {
      if (role.effective_permissions.length > 50) {
        warnings.push(`Role "${role.name}" has ${role.effective_permissions.length} permissions (consider review)`)
      }
    })

    // Check for orphaned permissions
    permissions.forEach(permission => {
      const isUsed = roles.some(role => 
        role.effective_permissions.includes(permission.id)
      )
      if (!isUsed && !permission.is_system) {
        warnings.push(`Permission "${permission.name}" is not assigned to any role`)
      }
    })

    return warnings
  }

  const warnings = getConflictWarnings()

  return (
    <Box>
      {/* Header Controls */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">Permission Matrix</Typography>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <FormControl size="small" sx={{ minWidth: 150 }}>
            <InputLabel>Resource Type</InputLabel>
            <Select
              value={selectedResource}
              onChange={(e) => setSelectedResource(e.target.value as ResourceType | 'all')}
              label="Resource Type"
            >
              <MenuItem value="all">All Resources</MenuItem>
              {resourceTypes.map(type => (
                <MenuItem key={type} value={type}>
                  {type.replace('_', ' ').toUpperCase()}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          <FormControlLabel
            control={
              <Switch
                checked={showInheritedPermissions}
                onChange={(e) => setShowInheritedPermissions(e.target.checked)}
              />
            }
            label="Show Inherited"
          />

          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => {
              setLocalMatrix(matrix)
              setHasChanges(false)
            }}
            disabled={!hasChanges || loading}
          >
            Reset
          </Button>

          <Button
            variant="contained"
            startIcon={<SaveIcon />}
            onClick={onSaveChanges}
            disabled={!hasChanges || loading || readOnly}
            sx={{
              background: hasChanges ? 'linear-gradient(45deg, #6366f1 30%, #8b5cf6 90%)' : undefined,
            }}
          >
            Save Changes
          </Button>
        </Box>
      </Box>

      {/* Warnings */}
      {warnings.length > 0 && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="body2" sx={{ fontWeight: 600, mb: 1 }}>
            Permission Matrix Warnings:
          </Typography>
          <ul style={{ margin: 0, paddingLeft: 20 }}>
            {warnings.slice(0, 3).map((warning, index) => (
              <li key={index}>
                <Typography variant="body2">{warning}</Typography>
              </li>
            ))}
          </ul>
          {warnings.length > 3 && (
            <Typography variant="body2" sx={{ mt: 1, fontStyle: 'italic' }}>
              +{warnings.length - 3} more warnings
            </Typography>
          )}
        </Alert>
      )}

      {/* Changes Indicator */}
      {hasChanges && (
        <Alert severity="info" sx={{ mb: 3 }}>
          You have unsaved changes. Click "Save Changes" to apply them.
        </Alert>
      )}

      {/* Permission Matrix Table */}
      <Card>
        <CardContent sx={{ p: 0 }}>
          <TableContainer sx={{ maxHeight: 600 }}>
            <Table stickyHeader size="small">
              <TableHead>
                <TableRow>
                  <TableCell sx={{ fontWeight: 600, minWidth: 200 }}>
                    Role / Permission
                  </TableCell>
                  {actionTypes.map(action => (
                    <TableCell 
                      key={action} 
                      align="center" 
                      sx={{ fontWeight: 600, minWidth: 100 }}
                    >
                      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 0.5 }}>
                        <Typography variant="caption" sx={{ fontWeight: 600 }}>
                          {action.toUpperCase()}
                        </Typography>
                        {!readOnly && (
                          <Box sx={{ display: 'flex', gap: 0.5 }}>
                            <Tooltip title="Assign to all">
                              <IconButton
                                size="small"
                                onClick={() => {
                                  if (selectedResource !== 'all') {
                                    handleBulkAssign(selectedResource as ResourceType, action, true)
                                  }
                                }}
                                disabled={selectedResource === 'all'}
                              >
                                <CheckCircleIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Remove from all">
                              <IconButton
                                size="small"
                                onClick={() => {
                                  if (selectedResource !== 'all') {
                                    handleBulkAssign(selectedResource as ResourceType, action, false)
                                  }
                                }}
                                disabled={selectedResource === 'all'}
                              >
                                <VisibilityOffIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        )}
                      </Box>
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredMatrix.map(resourceMatrix => (
                  <React.Fragment key={resourceMatrix.resource_type}>
                    {/* Resource Type Header */}
                    <TableRow sx={{ bgcolor: 'action.hover' }}>
                      <TableCell colSpan={actionTypes.length + 1}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <SecurityIcon color="primary" />
                          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                            {resourceMatrix.resource_type.replace('_', ' ').toUpperCase()}
                          </Typography>
                        </Box>
                      </TableCell>
                    </TableRow>

                    {/* Role Rows */}
                    {sortedRoles.map(role => (
                      <TableRow key={`${resourceMatrix.resource_type}-${role.id}`} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Box sx={{ ml: getRoleHierarchyLevel(role) * 2 }}>
                              <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                {role.display_name || role.name}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                Level {getRoleHierarchyLevel(role)} • {role.users_count} users
                              </Typography>
                            </Box>
                            {role.is_system && (
                              <Chip label="System" size="small" color="warning" />
                            )}
                          </Box>
                        </TableCell>

                        {actionTypes.map(action => {
                          const status = getPermissionStatus(role.id, resourceMatrix.resource_type, action)
                          const actionConfig = resourceMatrix.actions[action]

                          return (
                            <TableCell key={action} align="center">
                              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5 }}>
                                <Tooltip title={getPermissionTooltip(status)}>
                                  <Checkbox
                                    checked={status.hasPermission}
                                    indeterminate={status.inherited && showInheritedPermissions}
                                    onChange={() => handlePermissionToggle(role.id, resourceMatrix.resource_type, action)}
                                    disabled={readOnly || role.is_system}
                                    size="small"
                                    color={status.inherited ? 'info' : 'primary'}
                                  />
                                </Tooltip>
                                
                                {getPermissionIcon(status)}
                                
                                {actionConfig?.conditions && actionConfig.conditions.length > 0 && (
                                  <Tooltip title="Has conditions">
                                    <IconButton
                                      size="small"
                                      onClick={() => handleCellClick(role.id, resourceMatrix.resource_type, action)}
                                    >
                                      <InfoIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                )}
                              </Box>
                            </TableCell>
                          )
                        })}
                      </TableRow>
                    ))}
                  </React.Fragment>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Permission Details Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Permission Details
        </DialogTitle>
        <DialogContent>
          {selectedCell && (
            <Box sx={{ pt: 2 }}>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Role
                  </Typography>
                  <Typography variant="body1">
                    {roles.find(r => r.id === selectedCell.roleId)?.display_name}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Resource
                  </Typography>
                  <Typography variant="body1">
                    {selectedCell.resourceType.replace('_', ' ').toUpperCase()}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Action
                  </Typography>
                  <Typography variant="body1">
                    {selectedCell.action.toUpperCase()}
                  </Typography>
                </Grid>
              </Grid>

              {/* Permission conditions would be displayed here */}
              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle2" sx={{ mb: 2 }}>
                  Conditions
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  No conditions configured for this permission.
                </Typography>
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default PermissionMatrix
