# Generated by Django 5.1.7 on 2025-04-06 19:40

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('tenants', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='TenantActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('activity_type', models.CharField(help_text='Type of activity', max_length=50)),
                ('description', models.TextField(help_text='Description of the activity')),
                ('user_count', models.PositiveIntegerField(default=0, help_text='Number of users involved in the activity')),
                ('tenant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activities', to='tenants.school')),
            ],
            options={
                'verbose_name': 'Tenant Activity',
                'verbose_name_plural': 'Tenant Activities',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='TenantMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('total_users', models.PositiveIntegerField(default=0, help_text='Total number of users in the tenant')),
                ('active_users', models.PositiveIntegerField(default=0, help_text='Number of active users in the last 30 days')),
                ('admin_users', models.PositiveIntegerField(default=0, help_text='Number of admin users')),
                ('teacher_users', models.PositiveIntegerField(default=0, help_text='Number of teacher users')),
                ('student_users', models.PositiveIntegerField(default=0, help_text='Number of student users')),
                ('parent_users', models.PositiveIntegerField(default=0, help_text='Number of parent users')),
                ('staff_users', models.PositiveIntegerField(default=0, help_text='Number of staff users')),
                ('total_students', models.PositiveIntegerField(default=0, help_text='Total number of students')),
                ('total_courses', models.PositiveIntegerField(default=0, help_text='Total number of courses')),
                ('total_departments', models.PositiveIntegerField(default=0, help_text='Total number of departments')),
                ('total_enrollments', models.PositiveIntegerField(default=0, help_text='Total number of enrollments')),
                ('database_size', models.BigIntegerField(default=0, help_text="Size of the tenant's database in bytes")),
                ('file_storage_size', models.BigIntegerField(default=0, help_text="Size of the tenant's file storage in bytes")),
                ('avg_response_time', models.FloatField(default=0.0, help_text='Average API response time in milliseconds')),
                ('peak_response_time', models.FloatField(default=0.0, help_text='Peak API response time in milliseconds')),
                ('api_requests_count', models.PositiveIntegerField(default=0, help_text='Number of API requests in the last 24 hours')),
                ('error_count', models.PositiveIntegerField(default=0, help_text='Number of errors in the last 24 hours')),
                ('tenant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='metrics', to='tenants.school')),
            ],
            options={
                'verbose_name': 'Tenant Metrics',
                'verbose_name_plural': 'Tenant Metrics',
                'ordering': ['-timestamp'],
                'get_latest_by': 'timestamp',
            },
        ),
        migrations.CreateModel(
            name='TenantStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('active', 'Active'), ('suspended', 'Suspended'), ('maintenance', 'Maintenance'), ('trial', 'Trial'), ('expired', 'Expired')], default='active', max_length=20)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('subscription_plan', models.CharField(default='free', help_text='Subscription plan name', max_length=50)),
                ('subscription_start_date', models.DateField(blank=True, null=True)),
                ('subscription_end_date', models.DateField(blank=True, null=True)),
                ('max_users', models.PositiveIntegerField(default=100, help_text='Maximum number of users allowed')),
                ('max_storage', models.BigIntegerField(default=1073741824, help_text='Maximum storage allowed in bytes (default: 1GB)')),
                ('admin_email', models.EmailField(help_text='Email of the tenant administrator', max_length=254)),
                ('admin_phone', models.CharField(blank=True, help_text='Phone number of the tenant administrator', max_length=20, null=True)),
                ('is_featured', models.BooleanField(default=False, help_text='Whether this tenant is featured')),
                ('notes', models.TextField(blank=True, help_text='Additional notes about the tenant', null=True)),
                ('tenant', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='status', to='tenants.school')),
            ],
            options={
                'verbose_name': 'Tenant Status',
                'verbose_name_plural': 'Tenant Statuses',
            },
        ),
    ]
