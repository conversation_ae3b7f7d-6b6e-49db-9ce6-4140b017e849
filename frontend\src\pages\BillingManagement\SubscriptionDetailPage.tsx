import { useState, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import { useToast } from '../../components/ToastProvider'
import {
  Box,
  Typography,
  Paper,
  LinearProgress,
  Alert,
} from '@mui/material'
import BillingManagementLayout from '../../components/Layout/BillingManagementLayout'

const SubscriptionDetailPage = () => {
  const { id } = useParams<{ id: string }>()
  const toast = useToast()
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    setTimeout(() => setLoading(false), 1000)
  }, [])

  if (loading) {
    return (
      <BillingManagementLayout
        title="Subscription Details"
        subtitle="Loading subscription..."
        breadcrumbs={[
          { label: 'Subscriptions', href: '/dashboard/billing-management/subscriptions' },
          { label: 'Subscription Details' }
        ]}
      >
        <LinearProgress />
      </BillingManagementLayout>
    )
  }

  return (
    <BillingManagementLayout
      title="Subscription Details"
      subtitle={`Subscription ID: ${id}`}
      breadcrumbs={[
        { label: 'Subscriptions', href: '/dashboard/billing-management/subscriptions' },
        { label: 'Subscription Details' }
      ]}
    >
      <Paper sx={{ p: 4, textAlign: 'center' }}>
        <Alert severity="info" sx={{ mb: 3 }}>
          Subscription detail functionality will be implemented here.
        </Alert>
        <Typography variant="h6" color="text.secondary">
          Coming Soon: Subscription Details
        </Typography>
      </Paper>
    </BillingManagementLayout>
  )
}

export default SubscriptionDetailPage
