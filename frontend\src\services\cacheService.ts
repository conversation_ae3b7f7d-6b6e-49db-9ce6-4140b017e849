// API Response Caching Service
interface CacheEntry {
  data: any
  timestamp: number
  expiry: number
}

class CacheService {
  private cache = new Map<string, CacheEntry>()
  private defaultTTL = 5 * 60 * 1000 // 5 minutes

  // Set cache entry with TTL
  set(key: string, data: any, ttl: number = this.defaultTTL): void {
    const entry: CacheEntry = {
      data,
      timestamp: Date.now(),
      expiry: Date.now() + ttl
    }
    this.cache.set(key, entry)
  }

  // Get cache entry if not expired
  get(key: string): any | null {
    const entry = this.cache.get(key)
    if (!entry) return null

    if (Date.now() > entry.expiry) {
      this.cache.delete(key)
      return null
    }

    return entry.data
  }

  // Check if key exists and is not expired
  has(key: string): boolean {
    const entry = this.cache.get(key)
    if (!entry) return false

    if (Date.now() > entry.expiry) {
      this.cache.delete(key)
      return false
    }

    return true
  }

  // Delete specific cache entry
  delete(key: string): void {
    this.cache.delete(key)
  }

  // Clear all cache
  clear(): void {
    this.cache.clear()
  }

  // Clear expired entries
  clearExpired(): void {
    const now = Date.now()
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiry) {
        this.cache.delete(key)
      }
    }
  }

  // Get cache statistics
  getStats(): { size: number; expired: number; memory: number } {
    const now = Date.now()
    let expired = 0
    let memory = 0

    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiry) {
        expired++
      }
      memory += JSON.stringify(entry).length
    }

    return {
      size: this.cache.size,
      expired,
      memory
    }
  }

  // Invalidate cache entries by pattern
  invalidatePattern(pattern: string): void {
    const regex = new RegExp(pattern)
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.cache.delete(key)
      }
    }
  }
}

// Create singleton instance
export const cacheService = new CacheService()

// Request deduplication service
class RequestDeduplicationService {
  private pendingRequests = new Map<string, Promise<any>>()

  async deduplicate<T>(key: string, requestFn: () => Promise<T>): Promise<T> {
    // If request is already pending, return the existing promise
    if (this.pendingRequests.has(key)) {
      return this.pendingRequests.get(key)!
    }

    // Create new request
    const promise = requestFn()
      .finally(() => {
        // Remove from pending requests when completed
        this.pendingRequests.delete(key)
      })

    this.pendingRequests.set(key, promise)
    return promise
  }

  // Clear all pending requests
  clear(): void {
    this.pendingRequests.clear()
  }

  // Get pending request count
  getPendingCount(): number {
    return this.pendingRequests.size
  }
}

export const requestDeduplicationService = new RequestDeduplicationService()

// Enhanced API service with caching and deduplication
export const createCachedApiCall = <T>(
  apiCall: (...args: any[]) => Promise<T>,
  cacheKeyGenerator: (...args: any[]) => string,
  ttl: number = 5 * 60 * 1000 // 5 minutes
) => {
  return async (...args: any[]): Promise<T> => {
    const cacheKey = cacheKeyGenerator(...args)
    
    // Check cache first
    const cachedData = cacheService.get(cacheKey)
    if (cachedData) {
      console.log(`Cache hit for key: ${cacheKey}`)
      return cachedData
    }

    // Use request deduplication
    return requestDeduplicationService.deduplicate(cacheKey, async () => {
      console.log(`Cache miss for key: ${cacheKey}, making API call`)
      const data = await apiCall(...args)
      
      // Cache the result
      cacheService.set(cacheKey, data, ttl)
      return data
    })
  }
}

// Browser storage cache for persistence
class PersistentCacheService {
  private storageKey = 'kelem_api_cache'

  // Save cache to localStorage
  save(): void {
    try {
      const cacheData = Array.from(cacheService['cache'].entries())
      localStorage.setItem(this.storageKey, JSON.stringify(cacheData))
    } catch (error) {
      console.warn('Failed to save cache to localStorage:', error)
    }
  }

  // Load cache from localStorage
  load(): void {
    try {
      const stored = localStorage.getItem(this.storageKey)
      if (stored) {
        const cacheData = JSON.parse(stored)
        const now = Date.now()
        
        // Only restore non-expired entries
        for (const [key, entry] of cacheData) {
          if (now < entry.expiry) {
            cacheService['cache'].set(key, entry)
          }
        }
      }
    } catch (error) {
      console.warn('Failed to load cache from localStorage:', error)
    }
  }

  // Clear persistent cache
  clear(): void {
    localStorage.removeItem(this.storageKey)
  }
}

export const persistentCacheService = new PersistentCacheService()

// Initialize cache service
export const initCacheService = () => {
  // Load persistent cache on startup
  persistentCacheService.load()

  // Save cache periodically
  setInterval(() => {
    persistentCacheService.save()
  }, 60000) // Every minute

  // Clear expired entries periodically
  setInterval(() => {
    cacheService.clearExpired()
  }, 300000) // Every 5 minutes

  // Save cache before page unload
  window.addEventListener('beforeunload', () => {
    persistentCacheService.save()
  })

  // Log cache statistics in development
  if (process.env.NODE_ENV === 'development') {
    setInterval(() => {
      const stats = cacheService.getStats()
      console.log('Cache stats:', stats)
    }, 30000) // Every 30 seconds
  }
}

// Cache invalidation helpers
export const invalidateStudentCache = () => {
  cacheService.invalidatePattern('students.*')
}

export const invalidateTeacherCache = () => {
  cacheService.invalidatePattern('teachers.*')
}

export const invalidateClassCache = () => {
  cacheService.invalidatePattern('classes.*')
}

export const invalidateUserCache = () => {
  cacheService.invalidatePattern('users.*')
}

// Preload critical data
export const preloadCriticalData = async () => {
  const criticalEndpoints = [
    { key: 'students.list', call: () => fetch('/api/students/').then(r => r.json()) },
    { key: 'classes.list', call: () => fetch('/api/classes/').then(r => r.json()) },
    { key: 'teachers.list', call: () => fetch('/api/teachers/').then(r => r.json()) },
  ]

  const promises = criticalEndpoints.map(async ({ key, call }) => {
    try {
      if (!cacheService.has(key)) {
        const data = await call()
        cacheService.set(key, data, 10 * 60 * 1000) // 10 minutes for critical data
      }
    } catch (error) {
      console.warn(`Failed to preload ${key}:`, error)
    }
  })

  await Promise.allSettled(promises)
}

// Background refresh for critical data
export const startBackgroundRefresh = () => {
  const refreshInterval = 5 * 60 * 1000 // 5 minutes
  
  setInterval(async () => {
    if (document.visibilityState === 'visible') {
      await preloadCriticalData()
    }
  }, refreshInterval)
}

// Memory management
export const manageMemory = () => {
  const maxCacheSize = 100 // Maximum number of cache entries
  const maxMemoryUsage = 10 * 1024 * 1024 // 10MB

  setInterval(() => {
    const stats = cacheService.getStats()
    
    // Clear expired entries first
    cacheService.clearExpired()
    
    // If still over limits, clear oldest entries
    if (stats.size > maxCacheSize || stats.memory > maxMemoryUsage) {
      const entries = Array.from(cacheService['cache'].entries())
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp)
      
      // Remove oldest 25% of entries
      const toRemove = Math.floor(entries.length * 0.25)
      for (let i = 0; i < toRemove; i++) {
        cacheService.delete(entries[i][0])
      }
      
      console.log(`Cleared ${toRemove} cache entries for memory management`)
    }
  }, 60000) // Check every minute
}
