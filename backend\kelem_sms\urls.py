"""
URL configuration for kelem_sms project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.http import HttpResponse
from django.conf import settings
from django.conf.urls.static import static

# Import custom admin site
from tenant_management.admin_site import tenant_admin_site

# Import Swagger URLs
from .swagger_urls import urlpatterns as swagger_urls

def home_view(request):
    return HttpResponse("<h1>Welcome to Kelem Student Management System</h1>")

urlpatterns = [
    path('', home_view, name='home'),
    path('admin/', admin.site.urls),
    path('tenant-admin/', tenant_admin_site.urls),
    path('auth/', include('authentication.urls', namespace='authentication')),
    path('schools/', include('school_registration.urls', namespace='school_registration')),
    path('tenant-management/', include('tenant_management.urls', namespace='tenant_management')),
    path('billing-management/', include('billing_management.urls')),
    path('api/site/', include('site_settings.urls', namespace='site_settings')),
    path('user-management/', include('user_management.urls', namespace='user_management')),
    path('api-auth/', include('rest_framework.urls', namespace='rest_framework')),

    # App URLs
    path('students/', include('students.urls', namespace='students')),
    path('courses/', include('courses.urls', namespace='courses')),
    path('teachers/', include('teachers.urls', namespace='teachers')),

    # API documentation URLs
    path('api/docs/', include(swagger_urls)),
]

# Serve static and media files in development
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
