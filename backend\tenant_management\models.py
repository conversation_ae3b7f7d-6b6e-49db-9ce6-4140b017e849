from django.db import models
from django.utils import timezone
from tenants.models import School

class TenantMetrics(models.Model):
    """Model to store tenant usage metrics."""
    tenant = models.ForeignKey(School, on_delete=models.CASCADE, related_name='metrics')
    timestamp = models.DateTimeField(default=timezone.now)

    # User metrics
    total_users = models.PositiveIntegerField(default=0, help_text="Total number of users in the tenant")
    active_users = models.PositiveIntegerField(default=0, help_text="Number of active users in the last 30 days")
    admin_users = models.PositiveIntegerField(default=0, help_text="Number of admin users")
    teacher_users = models.PositiveIntegerField(default=0, help_text="Number of teacher users")
    student_users = models.PositiveIntegerField(default=0, help_text="Number of student users")
    parent_users = models.PositiveIntegerField(default=0, help_text="Number of parent users")
    staff_users = models.PositiveIntegerField(default=0, help_text="Number of staff users")

    # Data metrics
    total_students = models.PositiveIntegerField(default=0, help_text="Total number of students")
    total_courses = models.PositiveIntegerField(default=0, help_text="Total number of courses")
    total_departments = models.PositiveIntegerField(default=0, help_text="Total number of departments")
    total_enrollments = models.PositiveIntegerField(default=0, help_text="Total number of enrollments")

    # Storage metrics (in bytes)
    database_size = models.BigIntegerField(default=0, help_text="Size of the tenant's database in bytes")
    file_storage_size = models.BigIntegerField(default=0, help_text="Size of the tenant's file storage in bytes")

    # Performance metrics
    avg_response_time = models.FloatField(default=0.0, help_text="Average API response time in milliseconds")
    peak_response_time = models.FloatField(default=0.0, help_text="Peak API response time in milliseconds")
    api_requests_count = models.PositiveIntegerField(default=0, help_text="Number of API requests in the last 24 hours")
    error_count = models.PositiveIntegerField(default=0, help_text="Number of errors in the last 24 hours")

    class Meta:
        verbose_name = "Tenant Metrics"
        verbose_name_plural = "Tenant Metrics"
        ordering = ['-timestamp']
        get_latest_by = 'timestamp'

    def __str__(self):
        return f"{self.tenant.name} Metrics - {self.timestamp.strftime('%Y-%m-%d %H:%M')}"

class TenantActivity(models.Model):
    """Model to store tenant activity logs."""
    tenant = models.ForeignKey(School, on_delete=models.CASCADE, related_name='activities')
    timestamp = models.DateTimeField(default=timezone.now)
    activity_type = models.CharField(max_length=50, help_text="Type of activity")
    description = models.TextField(help_text="Description of the activity")
    user_count = models.PositiveIntegerField(default=0, help_text="Number of users involved in the activity")

    class Meta:
        verbose_name = "Tenant Activity"
        verbose_name_plural = "Tenant Activities"
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.tenant.name} - {self.activity_type} - {self.timestamp.strftime('%Y-%m-%d %H:%M')}"

class TenantStatus(models.Model):
    """Model to store tenant status information."""
    STATUS_CHOICES = (
        ('active', 'Active'),
        ('suspended', 'Suspended'),
        ('maintenance', 'Maintenance'),
        ('trial', 'Trial'),
        ('expired', 'Expired'),
        ('archived', 'Archived'),
    )

    tenant = models.OneToOneField(School, on_delete=models.CASCADE, related_name='status')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    last_updated = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    # Subscription information
    subscription_plan = models.CharField(max_length=50, default='free', help_text="Subscription plan name")
    subscription_start_date = models.DateField(null=True, blank=True)
    subscription_end_date = models.DateField(null=True, blank=True)
    max_users = models.PositiveIntegerField(default=100, help_text="Maximum number of users allowed")
    max_storage = models.BigIntegerField(default=1073741824, help_text="Maximum storage allowed in bytes (default: 1GB)")

    # Contact information
    admin_email = models.EmailField(help_text="Email of the tenant administrator")
    admin_phone = models.CharField(max_length=20, blank=True, null=True, help_text="Phone number of the tenant administrator")

    # Additional settings
    is_featured = models.BooleanField(default=False, help_text="Whether this tenant is featured")
    notes = models.TextField(blank=True, null=True, help_text="Additional notes about the tenant")

    class Meta:
        verbose_name = "Tenant Status"
        verbose_name_plural = "Tenant Statuses"
        ordering = ['-last_updated']

    def __str__(self):
        return f"{self.tenant.name} - {self.get_status_display()}"

    def is_active(self):
        return self.status == 'active'

    def is_subscription_valid(self):
        """Check if the subscription is valid."""
        if self.status == 'suspended':
            return False

        if self.subscription_end_date and self.subscription_end_date < timezone.now().date():
            return False

        return True
