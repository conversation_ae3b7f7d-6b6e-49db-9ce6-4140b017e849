from django.urls import resolve
from django.http import HttpResponseForbidden
from django.conf import settings

class RoleBasedAccessMiddleware:
    """
    Middleware to enforce role-based access control.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        
    def __call__(self, request):
        # Get the resolved URL
        resolved_url = resolve(request.path_info)
        
        # Skip middleware for authentication views
        if resolved_url.app_name == 'accounts' and resolved_url.url_name in ['login', 'logout', 'password_reset']:
            return self.get_response(request)
        
        # Skip middleware for admin views if user is staff or superuser
        if request.path.startswith('/admin/') and (request.user.is_staff or request.user.is_superuser):
            return self.get_response(request)
        
        # Check if URL requires specific role
        url_name = resolved_url.url_name
        
        # Define role-based URL patterns
        admin_urls = getattr(settings, 'ADMIN_ONLY_URLS', [])
        teacher_urls = getattr(settings, 'TEACHER_ONLY_URLS', [])
        student_urls = getattr(settings, 'STUDENT_ONLY_URLS', [])
        parent_urls = getattr(settings, 'PARENT_ONLY_URLS', [])
        staff_urls = getattr(settings, 'STAFF_ONLY_URLS', [])
        
        # Check if user is authenticated
        if not request.user.is_authenticated:
            # Allow access to public URLs
            if url_name not in admin_urls + teacher_urls + student_urls + parent_urls + staff_urls:
                return self.get_response(request)
            else:
                # Redirect to login for protected URLs
                return HttpResponseForbidden("You must be logged in to access this page.")
        
        # Check role-based access
        user_type = request.user.user_type
        
        if url_name in admin_urls and user_type != 'admin' and not request.user.is_superuser:
            return HttpResponseForbidden("You don't have permission to access this page.")
        
        if url_name in teacher_urls and user_type != 'teacher' and not request.user.is_superuser:
            return HttpResponseForbidden("You don't have permission to access this page.")
        
        if url_name in student_urls and user_type != 'student' and not request.user.is_superuser:
            return HttpResponseForbidden("You don't have permission to access this page.")
        
        if url_name in parent_urls and user_type != 'parent' and not request.user.is_superuser:
            return HttpResponseForbidden("You don't have permission to access this page.")
        
        if url_name in staff_urls and user_type != 'staff' and not request.user.is_superuser:
            return HttpResponseForbidden("You don't have permission to access this page.")
        
        return self.get_response(request)
