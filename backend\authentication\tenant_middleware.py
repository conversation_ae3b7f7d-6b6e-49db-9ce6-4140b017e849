from django.utils.deprecation import MiddlewareMixin
from django_tenants.utils import get_tenant_model, get_tenant_domain_model
from django.db import connection
from rest_framework.authtoken.models import Token

class SessionBasedTenantMiddleware(MiddlewareMixin):
    """
    Middleware that sets the tenant based on the session.
    This middleware should be placed after the TenantMainMiddleware.
    """

    def process_request(self, request):
        # Skip if we're already in a tenant context
        if hasattr(request, 'tenant') and request.tenant.schema_name != 'public':
            return None

        # First try to get tenant_id from the request parameters
        tenant_id = request.GET.get('tenant_id')

        # If not in GET parameters, try to get from POST data
        if not tenant_id and request.method == 'POST':
            tenant_id = request.POST.get('tenant_id')

        # If not in POST data, try to get from JSON data
        if not tenant_id and request.content_type == 'application/json':
            try:
                import json
                data = json.loads(request.body)
                tenant_id = data.get('tenant_id')
            except:
                pass

        # If still no tenant_id, check if we have a session and tenant_id
        if not tenant_id and hasattr(request, 'session'):
            try:
                tenant_id = request.session.get('tenant_id')
            except Exception as e:
                print(f"Error accessing session: {str(e)}")

        # If still no tenant_id, try to get from token
        if not tenant_id and request.user.is_authenticated:
            # If the user is a tenant admin, use their tenant
            if hasattr(request.user, '_schema_name') and request.user._schema_name != 'public':
                try:
                    tenant = get_tenant_model().objects.get(schema_name=request.user._schema_name)
                    tenant_id = tenant.id
                except Exception as e:
                    print(f"Error getting tenant from user schema: {str(e)}")

        # If still no tenant_id, check the Authorization header for a token
        if not tenant_id:
            auth_header = request.headers.get('Authorization')
            if auth_header and auth_header.startswith('Token '):
                token_key = auth_header.split(' ')[1]
                try:
                    token = Token.objects.get(key=token_key)
                    user = token.user
                    # If the user is a tenant admin, use their tenant
                    if hasattr(user, '_schema_name') and user._schema_name != 'public':
                        try:
                            tenant = get_tenant_model().objects.get(schema_name=user._schema_name)
                            tenant_id = tenant.id
                        except Exception as e:
                            print(f"Error getting tenant from token user schema: {str(e)}")
                except Exception as e:
                    print(f"Error getting token: {str(e)}")

        if not tenant_id:
            return None

        # Get the tenant model
        TenantModel = get_tenant_model()

        try:
            # Get the tenant
            tenant = TenantModel.objects.get(id=tenant_id)

            # Set the tenant on the request
            request.tenant = tenant

            # Get a domain for this tenant
            DomainModel = get_tenant_domain_model()
            domain = DomainModel.objects.filter(tenant=tenant).first()

            if domain:
                request.domain = domain

            # Set the schema for this request
            connection.set_schema(request.tenant.schema_name)

        except TenantModel.DoesNotExist:
            # If the tenant doesn't exist, try to clear it from the session
            try:
                if hasattr(request, 'session'):
                    request.session['tenant_id'] = None
            except Exception as e:
                print(f"Error clearing invalid tenant from session: {str(e)}")
        except Exception as e:
            print(f"Error setting tenant: {str(e)}")

        return None
