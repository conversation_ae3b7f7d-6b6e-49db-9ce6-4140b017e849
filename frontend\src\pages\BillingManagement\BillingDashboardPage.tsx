import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useToast } from '../../components/ToastProvider'
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  Avatar,
  LinearProgress,
  Paper,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip,
  Alert,
  Fade,
  Stack,
  Divider,
} from '@mui/material'
import {
  TrendingUp as TrendingUpIcon,
  AttachMoney as MoneyIcon,
  Receipt as ReceiptIcon,
  CreditCard as CreditCardIcon,
  AccountBalance as BankIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  Analytics as AnalyticsIcon,
  Download as DownloadIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material'
import BillingManagementLayout from '../../components/Layout/BillingManagementLayout'
import {
  getBillingMetrics,
  getInvoices,
  formatCurrency as formatCurrencyService,
  BillingMetrics,
  Invoice
} from '../../services/billingService'

interface RecentTransaction {
  id: string
  tenantName: string
  amount: number
  status: 'completed' | 'pending' | 'failed'
  date: string
  plan: string
  type: 'subscription' | 'one-time' | 'refund'
}

interface TabPanelProps {
  children?: React.ReactNode
  index: number
  value: number
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`billing-tabpanel-${index}`}
      aria-labelledby={`billing-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  )
}

const BillingDashboardPage = () => {
  const navigate = useNavigate()
  const toast = useToast()
  const [loading, setLoading] = useState(true)
  const [tabValue, setTabValue] = useState(0)
  const [metrics, setMetrics] = useState<BillingMetrics | null>(null)
  const [recentTransactions, setRecentTransactions] = useState<RecentTransaction[]>([])

  useEffect(() => {
    fetchBillingData()
  }, [])

  const fetchBillingData = async () => {
    try {
      setLoading(true)

      // Try to fetch real data, fall back to mock data if API is not available
      try {
        const metricsData = await getBillingMetrics()
        setMetrics(metricsData)

        const invoicesData = await getInvoices({ page: 1 })
        // Convert invoices to recent transactions format
        const transactions = invoicesData.results.slice(0, 4).map(invoice => ({
          id: invoice.id,
          tenantName: invoice.tenantName,
          amount: invoice.amount,
          status: invoice.status === 'paid' ? 'completed' as const :
                  invoice.status === 'sent' ? 'pending' as const : 'failed' as const,
          date: invoice.issueDate,
          plan: invoice.plan,
          type: 'subscription' as const,
        }))
        setRecentTransactions(transactions)
      } catch (apiError) {
        console.log('API not available, using mock data')
        // Mock data fallback
        setMetrics({
          totalRevenue: 125000,
          monthlyRecurringRevenue: 24500,
          activeSubscriptions: 156,
          pendingPayments: 8,
          overdueInvoices: 3,
          churnRate: 2.5,
          averageRevenuePerUser: 157,
          conversionRate: 12.8,
          timestamp: new Date().toISOString(),
        })

        setRecentTransactions([
          {
            id: 'TXN-001',
            tenantName: 'Sunrise Academy',
            amount: 299,
            status: 'completed',
            date: '2024-01-15',
            plan: 'Premium',
            type: 'subscription',
          },
          {
            id: 'TXN-002',
            tenantName: 'Green Valley School',
            amount: 199,
            status: 'pending',
            date: '2024-01-14',
            plan: 'Standard',
            type: 'subscription',
          },
          {
            id: 'TXN-003',
            tenantName: 'Tech Institute',
            amount: 499,
            status: 'completed',
            date: '2024-01-13',
            plan: 'Enterprise',
            type: 'subscription',
          },
          {
            id: 'TXN-004',
            tenantName: 'Community College',
            amount: 150,
            status: 'failed',
            date: '2024-01-12',
            plan: 'Standard',
            type: 'subscription',
          },
        ])
      }
    } catch (error) {
      console.error('Error fetching billing data:', error)
      toast.error('Failed to load billing data')
    } finally {
      setLoading(false)
    }
  }

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success'
      case 'pending':
        return 'warning'
      case 'failed':
        return 'error'
      default:
        return 'default'
    }
  }

  const formatCurrency = (amount: number) => {
    return formatCurrencyService(amount)
  }

  if (loading) {
    return (
      <BillingManagementLayout
        title="Billing Dashboard"
        subtitle="Loading billing data..."
      >
        <Box sx={{ width: '100%', mt: 2 }}>
          <LinearProgress />
        </Box>
      </BillingManagementLayout>
    )
  }

  return (
    <BillingManagementLayout
      title="Billing Dashboard"
      subtitle="Manage subscriptions, payments, and revenue analytics"
      showBreadcrumbs={false}
    >
      {/* Action Buttons */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => navigate('/dashboard/billing-management/invoices/new')}
          sx={{
            background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
            boxShadow: '0 3px 5px 2px rgba(33, 203, 243, .3)',
          }}
        >
          Create Invoice
        </Button>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={fetchBillingData}
        >
          Refresh Data
        </Button>
        <Button
          variant="outlined"
          startIcon={<DownloadIcon />}
          onClick={() => navigate('/dashboard/billing-management/reports')}
        >
          Export Reports
        </Button>
        <Button
          variant="outlined"
          startIcon={<AnalyticsIcon />}
          onClick={() => navigate('/dashboard/billing-management/analytics')}
        >
          View Analytics
        </Button>
      </Box>

      {/* Metrics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Total Revenue Card */}
        <Grid item xs={12} md={6} lg={3}>
          <Card 
            sx={{ 
              height: '100%',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              position: 'relative',
              overflow: 'hidden',
            }}
          >
            <CardContent sx={{ position: 'relative', zIndex: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', mr: 2 }}>
                  <MoneyIcon />
                </Avatar>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Total Revenue
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    All Time
                  </Typography>
                </Box>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 700 }}>
                {formatCurrency(metrics?.totalRevenue || 0)}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                <TrendingUpIcon sx={{ fontSize: 16, mr: 0.5 }} />
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  +12.5% from last month
                </Typography>
              </Box>
            </CardContent>
            <Box 
              sx={{ 
                position: 'absolute', 
                top: -20, 
                right: -20, 
                width: 100, 
                height: 100, 
                borderRadius: '50%', 
                bgcolor: 'rgba(255,255,255,0.1)' 
              }} 
            />
          </Card>
        </Grid>

        {/* Monthly Recurring Revenue Card */}
        <Grid item xs={12} md={6} lg={3}>
          <Card 
            sx={{ 
              height: '100%',
              background: 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)',
              color: 'white',
              position: 'relative',
              overflow: 'hidden',
            }}
          >
            <CardContent sx={{ position: 'relative', zIndex: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', mr: 2 }}>
                  <ReceiptIcon />
                </Avatar>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Monthly Revenue
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    Recurring
                  </Typography>
                </Box>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 700 }}>
                {formatCurrency(metrics?.monthlyRecurringRevenue || 0)}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                <TrendingUpIcon sx={{ fontSize: 16, mr: 0.5 }} />
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  +8.2% from last month
                </Typography>
              </Box>
            </CardContent>
            <Box 
              sx={{ 
                position: 'absolute', 
                top: -20, 
                right: -20, 
                width: 100, 
                height: 100, 
                borderRadius: '50%', 
                bgcolor: 'rgba(255,255,255,0.1)' 
              }} 
            />
          </Card>
        </Grid>

        {/* Active Subscriptions Card */}
        <Grid item xs={12} md={6} lg={3}>
          <Card sx={{ height: '100%', position: 'relative', overflow: 'hidden' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                  <CreditCardIcon />
                </Avatar>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Active Subscriptions
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Current Period
                  </Typography>
                </Box>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main' }}>
                {metrics?.activeSubscriptions || 0}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                <CheckCircleIcon sx={{ fontSize: 16, mr: 0.5, color: 'success.main' }} />
                <Typography variant="body2" color="text.secondary">
                  {metrics?.conversionRate || 0}% conversion rate
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Pending Payments Card */}
        <Grid item xs={12} md={6} lg={3}>
          <Card sx={{ height: '100%', position: 'relative', overflow: 'hidden' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                  <ScheduleIcon />
                </Avatar>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Pending Payments
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Requires Attention
                  </Typography>
                </Box>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 700, color: 'warning.main' }}>
                {metrics?.pendingPayments || 0}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                <WarningIcon sx={{ fontSize: 16, mr: 0.5, color: 'error.main' }} />
                <Typography variant="body2" color="text.secondary">
                  {metrics?.overdueInvoices || 0} overdue invoices
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs Section */}
      <Paper sx={{ mb: 3, borderRadius: 3, boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)', overflow: 'hidden' }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="scrollable"
          scrollButtons="auto"
          sx={{
            bgcolor: 'background.paper',
            '& .MuiTab-root': {
              minWidth: 140,
              fontWeight: 600,
              fontSize: '0.95rem',
              textTransform: 'none',
              py: 3,
              px: 3,
              '&.Mui-selected': {
                fontWeight: 700,
              },
            },
            '& .MuiTabs-indicator': {
              height: 4,
              borderRadius: 2,
            },
          }}
        >
          <Tab label="Recent Transactions" icon={<ReceiptIcon />} iconPosition="start" />
          <Tab label="Subscription Plans" icon={<CreditCardIcon />} iconPosition="start" />
          <Tab label="Payment Methods" icon={<BankIcon />} iconPosition="start" />
          <Tab label="Revenue Analytics" icon={<AnalyticsIcon />} iconPosition="start" />
        </Tabs>

        {/* Recent Transactions Tab */}
        <TabPanel value={tabValue} index={0}>
          <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              Recent Transactions
            </Typography>
            <Button
              variant="outlined"
              size="small"
              onClick={() => navigate('/dashboard/billing-management/transactions')}
            >
              View All
            </Button>
          </Box>

          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ fontWeight: 600 }}>Transaction ID</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Tenant</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Amount</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Plan</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Status</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Date</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {recentTransactions.map((transaction) => (
                  <TableRow key={transaction.id} hover>
                    <TableCell>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', fontWeight: 600 }}>
                        {transaction.id}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {transaction.tenantName}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        {formatCurrency(transaction.amount)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={transaction.plan}
                        size="small"
                        color={
                          transaction.plan === 'Enterprise' ? 'secondary' :
                          transaction.plan === 'Premium' ? 'primary' :
                          'default'
                        }
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={transaction.status}
                        size="small"
                        color={getStatusColor(transaction.status) as any}
                        sx={{ textTransform: 'capitalize' }}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="text.secondary">
                        {new Date(transaction.date).toLocaleDateString()}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Tooltip title="View Details">
                          <IconButton
                            size="small"
                            onClick={() => navigate(`/dashboard/billing-management/transactions/${transaction.id}`)}
                          >
                            <ViewIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Download Receipt">
                          <IconButton size="small">
                            <DownloadIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        {/* Subscription Plans Tab */}
        <TabPanel value={tabValue} index={1}>
          <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              Subscription Plans
            </Typography>
            <Button
              variant="contained"
              size="small"
              startIcon={<AddIcon />}
              onClick={() => navigate('/dashboard/billing-management/plans/new')}
            >
              Create Plan
            </Button>
          </Box>

          <Grid container spacing={3}>
            {/* Basic Plan */}
            <Grid item xs={12} md={4}>
              <Card sx={{ height: '100%', position: 'relative' }}>
                <CardContent>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                    Basic Plan
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main', mb: 2 }}>
                    Br 5,000<Typography component="span" variant="body2">/month</Typography>
                  </Typography>
                  <Stack spacing={1} sx={{ mb: 3 }}>
                    <Typography variant="body2">• Up to 100 students</Typography>
                    <Typography variant="body2">• 5GB storage</Typography>
                    <Typography variant="body2">• Basic support</Typography>
                    <Typography variant="body2">• Core features</Typography>
                  </Stack>
                  <Chip label="45 Active" size="small" color="primary" />
                </CardContent>
              </Card>
            </Grid>

            {/* Standard Plan */}
            <Grid item xs={12} md={4}>
              <Card sx={{ height: '100%', position: 'relative', border: '2px solid', borderColor: 'primary.main' }}>
                <Box
                  sx={{
                    position: 'absolute',
                    top: -1,
                    left: '50%',
                    transform: 'translateX(-50%)',
                    bgcolor: 'primary.main',
                    color: 'white',
                    px: 2,
                    py: 0.5,
                    borderRadius: '0 0 8px 8px',
                    fontSize: '0.75rem',
                    fontWeight: 600,
                  }}
                >
                  POPULAR
                </Box>
                <CardContent>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                    Standard Plan
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main', mb: 2 }}>
                    Br 10,000<Typography component="span" variant="body2">/month</Typography>
                  </Typography>
                  <Stack spacing={1} sx={{ mb: 3 }}>
                    <Typography variant="body2">• Up to 500 students</Typography>
                    <Typography variant="body2">• 25GB storage</Typography>
                    <Typography variant="body2">• Priority support</Typography>
                    <Typography variant="body2">• Advanced features</Typography>
                  </Stack>
                  <Chip label="78 Active" size="small" color="primary" />
                </CardContent>
              </Card>
            </Grid>

            {/* Premium Plan */}
            <Grid item xs={12} md={4}>
              <Card sx={{ height: '100%', position: 'relative' }}>
                <CardContent>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                    Premium Plan
                  </Typography>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main', mb: 2 }}>
                    Br 15,000<Typography component="span" variant="body2">/month</Typography>
                  </Typography>
                  <Stack spacing={1} sx={{ mb: 3 }}>
                    <Typography variant="body2">• Unlimited students</Typography>
                    <Typography variant="body2">• 100GB storage</Typography>
                    <Typography variant="body2">• 24/7 support</Typography>
                    <Typography variant="body2">• All features</Typography>
                  </Stack>
                  <Chip label="33 Active" size="small" color="secondary" />
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Payment Methods Tab */}
        <TabPanel value={tabValue} index={2}>
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
            Payment Methods Configuration
          </Typography>
          <Alert severity="info" sx={{ mb: 3 }}>
            Configure payment gateways and methods for tenant subscriptions.
          </Alert>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                    Stripe Integration
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Primary payment processor for credit cards and subscriptions
                  </Typography>
                  <Chip label="Active" color="success" size="small" />
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                    PayPal Integration
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Alternative payment method for international customers
                  </Typography>
                  <Chip label="Inactive" color="default" size="small" />
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Revenue Analytics Tab */}
        <TabPanel value={tabValue} index={3}>
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
            Revenue Analytics
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                    Monthly Revenue Trend
                  </Typography>
                  <Box sx={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: 'grey.50', borderRadius: 1 }}>
                    <Typography color="text.secondary">Chart placeholder - integrate with charting library</Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                    Plan Distribution
                  </Typography>
                  <Box sx={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: 'grey.50', borderRadius: 1 }}>
                    <Typography color="text.secondary">Chart placeholder - integrate with charting library</Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>
      </Paper>
    </BillingManagementLayout>
  )
}

export default BillingDashboardPage
