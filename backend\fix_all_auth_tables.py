import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kelem_sms.settings')
django.setup()

from django.db import connection, transaction
from django_tenants.utils import get_tenant_model, schema_exists, get_public_schema_name

def fix_auth_tables_for_schema(schema_name):
    """Fix missing authentication tables for a specific schema."""
    print(f"Fixing authentication tables for schema: {schema_name}")
    
    # Set the schema
    with connection.cursor() as cursor:
        cursor.execute(f'SET search_path TO "{schema_name}"')
    
    # Use transaction management to ensure clean state
    with transaction.atomic():
        try:
            # Check if the authentication_user_groups table exists
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = current_schema()
                        AND table_name = 'authentication_user_groups'
                    )
                """)
                table_exists = cursor.fetchone()[0]
            
            # Create the authentication_user_groups table if it doesn't exist
            if not table_exists:
                print(f"Creating authentication_user_groups table in schema {schema_name}...")
                with connection.cursor() as cursor:
                    cursor.execute("""
                        CREATE TABLE authentication_user_groups (
                            id bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
                            user_id bigint NOT NULL,
                            group_id integer NOT NULL,
                            CONSTRAINT authentication_user_groups_user_id_group_id_key UNIQUE (user_id, group_id),
                            CONSTRAINT authentication_user_groups_group_id_fkey FOREIGN KEY (group_id) REFERENCES auth_group(id) DEFERRABLE INITIALLY DEFERRED,
                            CONSTRAINT authentication_user_groups_user_id_fkey FOREIGN KEY (user_id) REFERENCES authentication_user(id) DEFERRABLE INITIALLY DEFERRED
                        )
                    """)
                print(f"Created authentication_user_groups table in schema {schema_name}.")
            
            # Check if the authentication_user_user_permissions table exists
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = current_schema()
                        AND table_name = 'authentication_user_user_permissions'
                    )
                """)
                table_exists = cursor.fetchone()[0]
            
            # Create the authentication_user_user_permissions table if it doesn't exist
            if not table_exists:
                print(f"Creating authentication_user_user_permissions table in schema {schema_name}...")
                with connection.cursor() as cursor:
                    cursor.execute("""
                        CREATE TABLE authentication_user_user_permissions (
                            id bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
                            user_id bigint NOT NULL,
                            permission_id integer NOT NULL,
                            CONSTRAINT authentication_user_user_permissions_user_id_permission_id_key UNIQUE (user_id, permission_id),
                            CONSTRAINT authentication_user_user_permissions_permission_id_fkey FOREIGN KEY (permission_id) REFERENCES auth_permission(id) DEFERRABLE INITIALLY DEFERRED,
                            CONSTRAINT authentication_user_user_permissions_user_id_fkey FOREIGN KEY (user_id) REFERENCES authentication_user(id) DEFERRABLE INITIALLY DEFERRED
                        )
                    """)
                print(f"Created authentication_user_user_permissions table in schema {schema_name}.")
            
            print(f"Authentication tables fix completed for schema {schema_name}.")
        except Exception as e:
            # Roll back the transaction on error
            print(f"Error in fix_auth_tables for schema {schema_name}: {str(e)}")
            transaction.set_rollback(True)
            raise
        finally:
            # Reset the search path
            with connection.cursor() as cursor:
                cursor.execute('SET search_path TO DEFAULT')

def fix_all_auth_tables():
    """Fix missing authentication tables in all schemas."""
    print("Starting authentication tables fix for all schemas...")
    
    # Get the tenant model
    TenantModel = get_tenant_model()
    
    # Fix the public schema first
    public_schema_name = get_public_schema_name()
    fix_auth_tables_for_schema(public_schema_name)
    
    # Get all tenants
    tenants = TenantModel.objects.all()
    
    # Fix each tenant schema
    for tenant in tenants:
        print(f"Processing tenant: {tenant.name} ({tenant.schema_name})")
        
        # Check if the schema exists
        if not schema_exists(tenant.schema_name):
            print(f"Schema '{tenant.schema_name}' does not exist. Skipping.")
            continue
        
        # Fix the authentication tables for this tenant
        fix_auth_tables_for_schema(tenant.schema_name)
    
    print("Authentication tables fix completed for all schemas.")

if __name__ == '__main__':
    fix_all_auth_tables()
