import React, { useState, useEffect } from 'react'
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Avatar,
  IconButton,
  Menu,
  MenuList,
  MenuItem as MenuItemComponent,
  ListItemIcon,
  ListItemText,
  Checkbox,
  TablePagination,
  Alert,
  CircularProgress,
  Tooltip,
  Stack,
} from '@mui/material'
import {
  PersonAdd as PersonAddIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Lock as LockIcon,
  LockOpen as LockOpenIcon,
  AdminPanelSettings as AdminIcon,
  Person as PersonIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material'
import { getUsers, toggleUserActive, deleteUser, bulkUserAction, type User, type UserListParams } from '../../../services/userManagementService'
import EditUserDialog from './EditUserDialog'
import PasswordResetDialog from './PasswordResetDialog'
import BulkActionDialog from './BulkActionDialog'

interface UserListTabProps {
  refreshTrigger: number
  onUserAction: (message: string, severity?: 'success' | 'error' | 'warning' | 'info') => void
  onCreateUser: () => void
}

const UserListTab: React.FC<UserListTabProps> = ({
  refreshTrigger,
  onUserAction,
  onCreateUser,
}) => {
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(false)
  const [totalCount, setTotalCount] = useState(0)
  const [page, setPage] = useState(0)
  const [pageSize, setPageSize] = useState(10)
  const [selectedUsers, setSelectedUsers] = useState<number[]>([])
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [passwordDialogOpen, setPasswordDialogOpen] = useState(false)
  const [bulkDialogOpen, setBulkDialogOpen] = useState(false)
  
  // Filters
  const [filters, setFilters] = useState<UserListParams>({
    search: '',
    userType: '',
    status: undefined,
    isActive: undefined,
    isStaff: undefined,
    ordering: '-date_joined',
  })

  const fetchUsers = async () => {
    setLoading(true)
    try {
      const params: UserListParams = {
        page: page + 1,
        pageSize,
        ...filters,
      }
      
      const response = await getUsers(params)
      setUsers(response.results)
      setTotalCount(response.count)
    } catch (error) {
      onUserAction('Failed to fetch users', 'error')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchUsers()
  }, [page, pageSize, filters, refreshTrigger])

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, user: User) => {
    setAnchorEl(event.currentTarget)
    setSelectedUser(user)
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
    setSelectedUser(null)
  }

  const handleToggleActive = async (user: User) => {
    try {
      const result = await toggleUserActive(user.id)
      onUserAction(result.message, 'success')
      handleMenuClose()
    } catch (error) {
      onUserAction('Failed to toggle user status', 'error')
    }
  }

  const handleDeleteUser = async (user: User) => {
    if (window.confirm(`Are you sure you want to delete ${user.fullName || user.email}?`)) {
      try {
        await deleteUser(user.id)
        onUserAction('User deleted successfully', 'success')
        handleMenuClose()
      } catch (error) {
        onUserAction('Failed to delete user', 'error')
      }
    }
  }

  const handleEditUser = (user: User) => {
    setSelectedUser(user)
    setEditDialogOpen(true)
    handleMenuClose()
  }

  const handleResetPassword = (user: User) => {
    setSelectedUser(user)
    setPasswordDialogOpen(true)
    handleMenuClose()
  }

  const handleSelectUser = (userId: number) => {
    setSelectedUsers(prev => 
      prev.includes(userId) 
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    )
  }

  const handleSelectAll = () => {
    if (selectedUsers.length === users.length) {
      setSelectedUsers([])
    } else {
      setSelectedUsers(users.map(user => user.id))
    }
  }

  const handleBulkAction = () => {
    setBulkDialogOpen(true)
  }

  const handleFilterChange = (field: keyof UserListParams, value: any) => {
    setFilters(prev => ({ ...prev, [field]: value }))
    setPage(0)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success'
      case 'inactive': return 'error'
      case 'idle': return 'warning'
      case 'new': return 'info'
      default: return 'default'
    }
  }

  const getUserTypeIcon = (userType: string, isStaff: boolean, isSuperuser: boolean) => {
    if (isSuperuser) return <AdminIcon color="error" />
    if (isStaff) return <AdminIcon color="warning" />
    return <PersonIcon color="action" />
  }

  return (
    <Box sx={{ maxWidth: '100%', mx: 'auto' }}>
      {/* Header Actions */}
      <Box sx={{ mb: 3, display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center' }}>
        <Button
          variant="contained"
          startIcon={<PersonAddIcon />}
          onClick={onCreateUser}
          sx={{
            flexShrink: 0,
            bgcolor: '#6366f1',
            color: 'white',
            '&:hover': {
              bgcolor: '#4f46e5',
            },
          }}
        >
          Add User
        </Button>

        {selectedUsers.length > 0 && (
          <Button
            variant="outlined"
            onClick={handleBulkAction}
            sx={{
              flexShrink: 0,
              borderColor: '#6366f1',
              color: '#6366f1',
              '&:hover': {
                borderColor: '#4f46e5',
                bgcolor: '#f3f4f6',
              },
            }}
          >
            Bulk Actions ({selectedUsers.length})
          </Button>
        )}

        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={fetchUsers}
          sx={{
            flexShrink: 0,
            borderColor: '#6366f1',
            color: '#6366f1',
            '&:hover': {
              borderColor: '#4f46e5',
              bgcolor: '#f3f4f6',
            },
          }}
        >
          Refresh
        </Button>
      </Box>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                size="small"
                placeholder="Search users..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status || ''}
                  label="Status"
                  onChange={(e) => handleFilterChange('status', e.target.value || undefined)}
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="inactive">Inactive</MenuItem>
                  <MenuItem value="idle">Idle</MenuItem>
                  <MenuItem value="new">New</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>User Type</InputLabel>
                <Select
                  value={filters.userType || ''}
                  label="User Type"
                  onChange={(e) => handleFilterChange('userType', e.target.value || undefined)}
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="admin">Admin</MenuItem>
                  <MenuItem value="teacher">Teacher</MenuItem>
                  <MenuItem value="student">Student</MenuItem>
                  <MenuItem value="parent">Parent</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Role</InputLabel>
                <Select
                  value={filters.isStaff === undefined ? '' : filters.isStaff ? 'staff' : 'regular'}
                  label="Role"
                  onChange={(e) => {
                    const value = e.target.value
                    if (value === 'staff') {
                      handleFilterChange('isStaff', true)
                    } else if (value === 'regular') {
                      handleFilterChange('isStaff', false)
                    } else {
                      handleFilterChange('isStaff', undefined)
                    }
                  }}
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="staff">Staff</MenuItem>
                  <MenuItem value="regular">Regular</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Sort By</InputLabel>
                <Select
                  value={filters.ordering || '-date_joined'}
                  label="Sort By"
                  onChange={(e) => handleFilterChange('ordering', e.target.value)}
                >
                  <MenuItem value="-date_joined">Newest First</MenuItem>
                  <MenuItem value="date_joined">Oldest First</MenuItem>
                  <MenuItem value="email">Email A-Z</MenuItem>
                  <MenuItem value="-email">Email Z-A</MenuItem>
                  <MenuItem value="first_name">Name A-Z</MenuItem>
                  <MenuItem value="-first_name">Name Z-A</MenuItem>
                  <MenuItem value="-last_login">Last Login</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* User List */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <CircularProgress />
        </Box>
      ) : users.length === 0 ? (
        <Alert severity="info">
          No users found. Try adjusting your filters or create a new user.
        </Alert>
      ) : (
        <>
          {/* Select All */}
          <Box sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
            <Checkbox
              checked={selectedUsers.length === users.length && users.length > 0}
              indeterminate={selectedUsers.length > 0 && selectedUsers.length < users.length}
              onChange={handleSelectAll}
            />
            <Typography variant="body2" color="text.secondary">
              Select All ({users.length} users)
            </Typography>
          </Box>

          {/* User Cards */}
          <Grid container spacing={2}>
            {users.map((user) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={user.id}>
                <Card 
                  sx={{ 
                    height: '100%',
                    border: selectedUsers.includes(user.id) ? 2 : 1,
                    borderColor: selectedUsers.includes(user.id) ? 'primary.main' : 'divider',
                    '&:hover': { boxShadow: 3 }
                  }}
                >
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                      <Checkbox
                        checked={selectedUsers.includes(user.id)}
                        onChange={() => handleSelectUser(user.id)}
                        sx={{ p: 0, mr: 1 }}
                      />
                      <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                        {user.fullName ? user.fullName.charAt(0).toUpperCase() : user.email.charAt(0).toUpperCase()}
                      </Avatar>
                      <Box sx={{ flexGrow: 1, minWidth: 0 }}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600 }} noWrap>
                          {user.fullName || user.email}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" noWrap>
                          {user.email}
                        </Typography>
                      </Box>
                      <IconButton
                        size="small"
                        onClick={(e) => handleMenuOpen(e, user)}
                      >
                        <MoreVertIcon />
                      </IconButton>
                    </Box>

                    <Stack spacing={1}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {getUserTypeIcon(user.userType, user.isStaff, user.isSuperuser)}
                        <Typography variant="body2">
                          {user.userType || 'User'}
                        </Typography>
                      </Box>
                      
                      <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                        <Chip
                          label={user.status}
                          color={getStatusColor(user.status) as any}
                          size="small"
                        />
                        {user.isSuperuser && (
                          <Chip label="Superuser" color="error" size="small" />
                        )}
                        {user.isStaff && !user.isSuperuser && (
                          <Chip label="Staff" color="warning" size="small" />
                        )}
                        {!user.isActive && (
                          <Chip label="Inactive" color="error" variant="outlined" size="small" />
                        )}
                      </Box>

                      <Typography variant="caption" color="text.secondary">
                        Last login: {user.lastLoginDisplay}
                      </Typography>
                      
                      <Typography variant="caption" color="text.secondary">
                        Joined: {new Date(user.dateJoined).toLocaleDateString()}
                      </Typography>
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>

          {/* Pagination */}
          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center' }}>
            <TablePagination
              component="div"
              count={totalCount}
              page={page}
              onPageChange={(_, newPage) => setPage(newPage)}
              rowsPerPage={pageSize}
              onRowsPerPageChange={(e) => {
                setPageSize(parseInt(e.target.value, 10))
                setPage(0)
              }}
              rowsPerPageOptions={[5, 10, 25, 50]}
            />
          </Box>
        </>
      )}

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuList>
          <MenuItemComponent onClick={() => selectedUser && handleEditUser(selectedUser)}>
            <ListItemIcon>
              <EditIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Edit User</ListItemText>
          </MenuItemComponent>
          
          <MenuItemComponent onClick={() => selectedUser && handleResetPassword(selectedUser)}>
            <ListItemIcon>
              <LockIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Reset Password</ListItemText>
          </MenuItemComponent>
          
          <MenuItemComponent onClick={() => selectedUser && handleToggleActive(selectedUser)}>
            <ListItemIcon>
              {selectedUser?.isActive ? <LockIcon fontSize="small" /> : <LockOpenIcon fontSize="small" />}
            </ListItemIcon>
            <ListItemText>
              {selectedUser?.isActive ? 'Deactivate' : 'Activate'}
            </ListItemText>
          </MenuItemComponent>
          
          <MenuItemComponent 
            onClick={() => selectedUser && handleDeleteUser(selectedUser)}
            sx={{ color: 'error.main' }}
          >
            <ListItemIcon>
              <DeleteIcon fontSize="small" color="error" />
            </ListItemIcon>
            <ListItemText>Delete User</ListItemText>
          </MenuItemComponent>
        </MenuList>
      </Menu>

      {/* Dialogs */}
      {selectedUser && (
        <>
          <EditUserDialog
            open={editDialogOpen}
            user={selectedUser}
            onClose={() => setEditDialogOpen(false)}
            onUserUpdated={() => {
              setEditDialogOpen(false)
              onUserAction('User updated successfully', 'success')
            }}
            onError={(error) => onUserAction(error, 'error')}
          />
          
          <PasswordResetDialog
            open={passwordDialogOpen}
            user={selectedUser}
            onClose={() => setPasswordDialogOpen(false)}
            onPasswordReset={() => {
              setPasswordDialogOpen(false)
              onUserAction('Password reset successfully', 'success')
            }}
            onError={(error) => onUserAction(error, 'error')}
          />
        </>
      )}

      <BulkActionDialog
        open={bulkDialogOpen}
        userIds={selectedUsers}
        onClose={() => setBulkDialogOpen(false)}
        onActionCompleted={(message) => {
          setBulkDialogOpen(false)
          setSelectedUsers([])
          onUserAction(message, 'success')
        }}
        onError={(error) => onUserAction(error, 'error')}
      />
    </Box>
  )
}

export default UserListTab
