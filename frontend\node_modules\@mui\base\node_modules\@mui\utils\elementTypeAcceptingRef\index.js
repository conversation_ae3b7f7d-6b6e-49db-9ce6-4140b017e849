"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "default", {
  enumerable: true,
  get: function () {
    return _elementTypeAcceptingRef.default;
  }
});
var _elementTypeAcceptingRef = _interopRequireDefault(require("./elementTypeAcceptingRef"));