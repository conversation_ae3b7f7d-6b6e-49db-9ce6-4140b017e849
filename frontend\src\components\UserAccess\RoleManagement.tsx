import React, { useState, useCallback } from 'react'
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Checkbox,
  FormControlLabel,
  Grid,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material'
import {
  Shield as ShieldIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  ExpandMore as ExpandMoreIcon,
  Security as SecurityIcon,
  Group as GroupIcon,
  Warning as WarningIcon,
} from '@mui/icons-material'
import { Role, Permission, getPermissionsByCategory } from '../../services/userAccessService'

interface RoleManagementProps {
  roles: Role[]
  permissions: Permission[]
  onCreateRole: (roleData: any) => void
  onUpdateRole: (id: string, roleData: any) => void
  onDeleteRole: (id: string) => void
}

const RoleManagement: React.FC<RoleManagementProps> = ({
  roles,
  permissions,
  onCreateRole,
  onUpdateRole,
  onDeleteRole,
}) => {
  const [dialogOpen, setDialogOpen] = useState(false)
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    permissions: [] as string[],
  })

  const permissionsByCategory = getPermissionsByCategory(permissions)

  const handleOpenDialog = useCallback((role?: Role) => {
    if (role) {
      setSelectedRole(role)
      setFormData({
        name: role.name,
        description: role.description,
        permissions: role.permissions,
      })
    } else {
      setSelectedRole(null)
      setFormData({
        name: '',
        description: '',
        permissions: [],
      })
    }
    setDialogOpen(true)
  }, [])

  const handleCloseDialog = useCallback(() => {
    setDialogOpen(false)
    setSelectedRole(null)
    setFormData({
      name: '',
      description: '',
      permissions: [],
    })
  }, [])

  const handleFormChange = useCallback((field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value,
    }))
  }, [])

  const handlePermissionChange = useCallback((permissionCode: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      permissions: event.target.checked
        ? [...prev.permissions, permissionCode]
        : prev.permissions.filter(p => p !== permissionCode),
    }))
  }, [])

  const handleSelectAllPermissions = useCallback((category: string, categoryPermissions: Permission[]) => {
    const categoryPermissionCodes = categoryPermissions.map(p => p.codename)
    const allSelected = categoryPermissionCodes.every(code => formData.permissions.includes(code))
    
    setFormData(prev => ({
      ...prev,
      permissions: allSelected
        ? prev.permissions.filter(p => !categoryPermissionCodes.includes(p))
        : [...new Set([...prev.permissions, ...categoryPermissionCodes])],
    }))
  }, [formData.permissions])

  const handleSave = useCallback(() => {
    if (selectedRole) {
      onUpdateRole(selectedRole.id, formData)
    } else {
      onCreateRole(formData)
    }
    handleCloseDialog()
  }, [selectedRole, formData, onCreateRole, onUpdateRole, handleCloseDialog])

  const handleDelete = useCallback((roleId: string) => {
    if (window.confirm('Are you sure you want to delete this role?')) {
      onDeleteRole(roleId)
    }
  }, [onDeleteRole])

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">Role Management</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
          sx={{
            background: 'linear-gradient(45deg, #6366f1 30%, #8b5cf6 90%)',
          }}
        >
          Add Role
        </Button>
      </Box>

      {/* Roles Grid */}
      <Grid container spacing={3}>
        {roles.map((role) => (
          <Grid item xs={12} md={6} lg={4} key={role.id}>
            <Card sx={{ height: '100%', position: 'relative' }}>
              <CardHeader
                title={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <ShieldIcon color="primary" />
                    <Typography variant="h6">{role.name}</Typography>
                    {role.is_system && (
                      <Chip label="System" size="small" color="warning" />
                    )}
                  </Box>
                }
                action={
                  <Box>
                    <IconButton
                      size="small"
                      onClick={() => handleOpenDialog(role)}
                      disabled={role.is_system}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleDelete(role.id)}
                      disabled={role.is_system || role.user_count > 0}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                }
              />
              <CardContent>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {role.description}
                </Typography>
                
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  <GroupIcon fontSize="small" color="action" />
                  <Typography variant="caption" color="text.secondary">
                    {role.user_count} users
                  </Typography>
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                    Permissions ({role.permissions.length}):
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {role.permissions.slice(0, 3).map((permission) => (
                      <Chip
                        key={permission}
                        label={permission.replace('_', ' ')}
                        size="small"
                        variant="outlined"
                      />
                    ))}
                    {role.permissions.length > 3 && (
                      <Chip
                        label={`+${role.permissions.length - 3} more`}
                        size="small"
                        variant="outlined"
                        color="secondary"
                      />
                    )}
                  </Box>
                </Box>

                <Typography variant="caption" color="text.secondary">
                  Created: {new Date(role.created_at).toLocaleDateString()}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Role Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {selectedRole ? 'Edit Role' : 'Create Role'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Role Name"
                  value={formData.name}
                  onChange={handleFormChange('name')}
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  value={formData.description}
                  onChange={handleFormChange('description')}
                  multiline
                  rows={3}
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle2" sx={{ mb: 2 }}>
                  Permissions
                </Typography>
                
                {selectedRole?.is_system ? (
                  <Alert severity="warning" sx={{ mb: 2 }}>
                    <Typography variant="body2">
                      System roles cannot be modified. Permissions are managed by the system.
                    </Typography>
                  </Alert>
                ) : (
                  <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
                    {Object.entries(permissionsByCategory).map(([category, categoryPermissions]) => {
                      const categoryPermissionCodes = categoryPermissions.map(p => p.codename)
                      const selectedCount = categoryPermissionCodes.filter(code => 
                        formData.permissions.includes(code)
                      ).length
                      const allSelected = selectedCount === categoryPermissionCodes.length
                      const someSelected = selectedCount > 0 && selectedCount < categoryPermissionCodes.length

                      return (
                        <Accordion key={category} defaultExpanded>
                          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                              <Checkbox
                                checked={allSelected}
                                indeterminate={someSelected}
                                onChange={() => handleSelectAllPermissions(category, categoryPermissions)}
                              />
                              <SecurityIcon fontSize="small" color="action" />
                              <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                {category}
                              </Typography>
                              <Chip
                                label={`${selectedCount}/${categoryPermissionCodes.length}`}
                                size="small"
                                color={allSelected ? 'success' : someSelected ? 'warning' : 'default'}
                              />
                            </Box>
                          </AccordionSummary>
                          <AccordionDetails>
                            <List dense>
                              {categoryPermissions.map((permission) => (
                                <ListItem key={permission.id} sx={{ pl: 4 }}>
                                  <ListItemIcon>
                                    <Checkbox
                                      checked={formData.permissions.includes(permission.codename)}
                                      onChange={handlePermissionChange(permission.codename)}
                                    />
                                  </ListItemIcon>
                                  <ListItemText
                                    primary={permission.name}
                                    secondary={permission.description}
                                  />
                                </ListItem>
                              ))}
                            </List>
                          </AccordionDetails>
                        </Accordion>
                      )
                    })}
                  </Box>
                )}
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button
            variant="contained"
            onClick={handleSave}
            disabled={!formData.name || !formData.description || selectedRole?.is_system}
            sx={{
              background: 'linear-gradient(45deg, #6366f1 30%, #8b5cf6 90%)',
            }}
          >
            {selectedRole ? 'Update' : 'Create'} Role
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default RoleManagement
