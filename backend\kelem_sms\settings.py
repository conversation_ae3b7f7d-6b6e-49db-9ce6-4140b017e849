"""
Django settings for kelem_sms project.

Generated by 'django-admin startproject' using Django 5.1.7.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.1/ref/settings/
"""

from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-en#!j7e+y!%!q#n5p#vy_k2sg2cq8u&*yvpy+m8(%+o1uje&m7'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['*']


# Application definition

SHARED_APPS = (
    'django_tenants',  # mandatory
    'tenants',  # tenant model
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'corsheaders',  # CORS headers
    'rest_framework',  # REST API framework
    'rest_framework.authtoken',  # Token authentication
    'drf_yasg',  # Swagger/OpenAPI documentation
    'authentication',  # custom user model and authentication
    'school_registration',  # school registration and tenant creation
    'tenant_management',  # tenant management dashboard
    'billing_management',  # billing and subscription management
    'site_settings',  # site settings and branding
    'user_management',  # user management system
)

TENANT_APPS = (
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'rest_framework',  # REST API framework
    'rest_framework.authtoken',  # Token authentication
    'drf_yasg',  # Swagger/OpenAPI documentation
    'students',
    'courses',
    'teachers',
    'academics',
    'assessments',
    'authentication',  # custom user model and authentication
)

INSTALLED_APPS = list(SHARED_APPS) + [app for app in TENANT_APPS if app not in SHARED_APPS]

MIDDLEWARE = [
    'django_tenants.middleware.main.TenantMainMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'corsheaders.middleware.CorsMiddleware',  # CORS middleware (should be placed before CommonMiddleware)
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'authentication.tenant_middleware.SessionBasedTenantMiddleware',  # Session-based tenant selection
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'authentication.middleware.RoleBasedAccessMiddleware',  # Role-based access control
]

ROOT_URLCONF = 'kelem_sms.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'kelem_sms.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.1/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django_tenants.postgresql_backend',
        'NAME': 'kelem_sms',
        'USER': 'postgres',
        'PASSWORD': 'admin',  # Update with your actual PostgreSQL password
        'HOST': 'localhost',
        'PORT': '5432',
    }
}


# Password validation
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.1/howto/static-files/

STATIC_URL = 'static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [BASE_DIR / 'static']

# Media files
MEDIA_URL = 'media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Django Tenants Configuration
TENANT_MODEL = 'tenants.School'  # app.Model
TENANT_DOMAIN_MODEL = 'tenants.Domain'  # app.Model
PUBLIC_SCHEMA_URLCONF = 'kelem_sms.public_urls'
PUBLIC_SCHEMA_NAME = 'public'
TENANT_BASE_SCHEMA = 'public'  # Base schema for tenant creation
TENANT_CREATION_FAKES_MIGRATIONS = False  # Set to False to run migrations for each tenant

# Database Router
DATABASE_ROUTERS = (
    'django_tenants.routers.TenantSyncRouter',
)

# Authentication settings
AUTH_USER_MODEL = 'authentication.User'

AUTHENTICATION_BACKENDS = [
    'authentication.backends.TokenAuthBackend',  # Token-based authentication
    'authentication.backends.TenantAwareAuthBackend',  # Tenant-aware authentication
    'django.contrib.auth.backends.ModelBackend',  # Default Django authentication
]

# Use API endpoints for login/logout
LOGIN_URL = '/auth/api/login/'
LOGIN_REDIRECT_URL = '/'
LOGOUT_REDIRECT_URL = '/'

# Email settings
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'  # For development
DEFAULT_FROM_EMAIL = '<EMAIL>'

# Frontend URL for password reset links
FRONTEND_URL = 'http://localhost:3000'  # Change this to your frontend URL

# Role-based URL patterns
ADMIN_ONLY_URLS = ['admin_dashboard']
TEACHER_ONLY_URLS = ['teacher_dashboard']
STUDENT_ONLY_URLS = ['student_dashboard']
PARENT_ONLY_URLS = ['parent_dashboard']
STAFF_ONLY_URLS = ['staff_dashboard']

# Session settings
SESSION_ENGINE = 'django.contrib.sessions.backends.db'
SESSION_COOKIE_AGE = 86400 * 30  # 30 days
SESSION_COOKIE_SECURE = False  # Set to True in production with HTTPS
SESSION_COOKIE_HTTPONLY = True
SESSION_SAVE_EVERY_REQUEST = True
SESSION_EXPIRE_AT_BROWSER_CLOSE = False

# Prevent SessionInterrupted errors
SESSION_SERIALIZER = 'django.contrib.sessions.serializers.JSONSerializer'

# Increase session security
SESSION_COOKIE_NAME = 'kelem_sessionid'
SESSION_COOKIE_SAMESITE = 'Lax'

# REST Framework settings
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.TokenAuthentication',  # Add token authentication
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.BasicAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 10,
}

# Swagger settings
SWAGGER_SETTINGS = {
    'SECURITY_DEFINITIONS': {
        'Basic': {
            'type': 'basic'
        },
        'Bearer': {
            'type': 'apiKey',
            'name': 'Authorization',
            'in': 'header'
        }
    },
    'USE_SESSION_AUTH': True,
    'PERSIST_AUTH': True,
    'DOC_EXPANSION': 'list',
    'DEFAULT_MODEL_RENDERING': 'model',
}

# CORS settings
CORS_ALLOW_ALL_ORIGINS = True  # Allow all origins during development
# CORS_ALLOWED_ORIGINS = [
#     "http://localhost:3000",  # React app
#     "http://localhost:3001",  # React app alternative port
# ]
CORS_ALLOW_CREDENTIALS = True  # Allow cookies to be sent
CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]
CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
]

# CSRF settings
CSRF_COOKIE_SAMESITE = 'Lax'  # or 'None' with CSRF_COOKIE_SECURE = True
CSRF_COOKIE_HTTPONLY = False  # False to allow JavaScript access
SESSION_COOKIE_SAMESITE = 'Lax'  # or 'None' with SESSION_COOKIE_SECURE = True

# For development only - disable CSRF protection
CSRF_TRUSTED_ORIGINS = ['http://localhost:3000', 'http://localhost:3001']

# Comment this out in production
CSRF_COOKIE_SECURE = False
SESSION_COOKIE_SECURE = False
