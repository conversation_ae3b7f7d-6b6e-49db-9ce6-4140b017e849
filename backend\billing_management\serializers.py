from rest_framework import serializers
from .models import SubscriptionPlan, PlanFeature, Subscription, Invoice, InvoiceItem, Payment
import json

class PlanFeatureSerializer(serializers.ModelSerializer):
    class Meta:
        model = PlanFeature
        fields = ['id', 'name', 'description', 'included']

class SubscriptionPlanSerializer(serializers.ModelSerializer):
    features = serializers.SerializerMethodField()
    storage_gb = serializers.SerializerMethodField()
    yearly_price = serializers.SerializerMethodField()
    
    class Meta:
        model = SubscriptionPlan
        fields = [
            'id', 'name', 'description', 'price', 'billing_cycle', 'max_users', 
            'max_storage', 'storage_gb', 'is_active', 'is_featured', 'trial_days', 
            'setup_fee', 'currency', 'category', 'sort_order', 'features', 
            'yearly_price', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_features(self, obj):
        """Get features as list of objects"""
        try:
            features_data = json.loads(obj.features_json) if obj.features_json else []
            return features_data
        except (json.JSONDecodeError, TypeError):
            return []
    
    def get_storage_gb(self, obj):
        """Get storage in GB"""
        return obj.get_storage_gb()
    
    def get_yearly_price(self, obj):
        """Get yearly price with discount"""
        return float(obj.get_yearly_price())
    
    def create(self, validated_data):
        """Handle features during creation"""
        features_data = self.initial_data.get('features', [])
        plan = SubscriptionPlan.objects.create(**validated_data)
        plan.features_json = json.dumps(features_data)
        plan.save()
        return plan
    
    def update(self, instance, validated_data):
        """Handle features during update"""
        features_data = self.initial_data.get('features', [])
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.features_json = json.dumps(features_data)
        instance.save()
        return instance

class SubscriptionSerializer(serializers.ModelSerializer):
    tenant_name = serializers.CharField(source='tenant.name', read_only=True)
    plan_name = serializers.CharField(source='plan.name', read_only=True)
    plan_price = serializers.DecimalField(source='plan.price', max_digits=10, decimal_places=2, read_only=True)
    is_trial_active = serializers.BooleanField(read_only=True)
    days_until_expiry = serializers.IntegerField(read_only=True)
    
    class Meta:
        model = Subscription
        fields = [
            'id', 'tenant', 'tenant_name', 'plan', 'plan_name', 'plan_price',
            'status', 'start_date', 'end_date', 'trial_end_date', 
            'next_billing_date', 'auto_renew', 'is_trial_active', 
            'days_until_expiry', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

class InvoiceItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = InvoiceItem
        fields = ['id', 'description', 'quantity', 'unit_price', 'amount']
        read_only_fields = ['id', 'amount']

class InvoiceSerializer(serializers.ModelSerializer):
    tenant_name = serializers.CharField(source='tenant.name', read_only=True)
    items = InvoiceItemSerializer(many=True, read_only=True)
    subtotal = serializers.SerializerMethodField()
    tax = serializers.SerializerMethodField()
    total = serializers.SerializerMethodField()
    
    class Meta:
        model = Invoice
        fields = [
            'id', 'invoice_number', 'tenant', 'tenant_name', 'subscription',
            'amount', 'currency', 'status', 'issue_date', 'due_date', 
            'paid_date', 'notes', 'items', 'subtotal', 'tax', 'total',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'invoice_number', 'created_at', 'updated_at']
    
    def get_subtotal(self, obj):
        """Calculate subtotal from items"""
        return sum(item.amount for item in obj.items.all())
    
    def get_tax(self, obj):
        """Calculate tax (placeholder - implement based on requirements)"""
        return 0  # No tax for now
    
    def get_total(self, obj):
        """Calculate total amount"""
        return obj.amount

class PaymentSerializer(serializers.ModelSerializer):
    tenant_name = serializers.CharField(source='tenant.name', read_only=True)
    invoice_number = serializers.CharField(source='invoice.invoice_number', read_only=True)
    
    class Meta:
        model = Payment
        fields = [
            'id', 'invoice', 'invoice_number', 'subscription', 'tenant', 
            'tenant_name', 'amount', 'currency', 'status', 'payment_method',
            'transaction_id', 'processed_at', 'failure_reason', 
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

class BillingMetricsSerializer(serializers.Serializer):
    """Serializer for billing metrics dashboard"""
    total_revenue = serializers.DecimalField(max_digits=15, decimal_places=2)
    monthly_recurring_revenue = serializers.DecimalField(max_digits=15, decimal_places=2)
    annual_recurring_revenue = serializers.DecimalField(max_digits=15, decimal_places=2)
    total_subscriptions = serializers.IntegerField()
    active_subscriptions = serializers.IntegerField()
    trial_subscriptions = serializers.IntegerField()
    cancelled_subscriptions = serializers.IntegerField()
    churn_rate = serializers.FloatField()
    average_revenue_per_user = serializers.DecimalField(max_digits=10, decimal_places=2)
    lifetime_value = serializers.DecimalField(max_digits=10, decimal_places=2)
    conversion_rate = serializers.FloatField()
    total_invoices = serializers.IntegerField()
    paid_invoices = serializers.IntegerField()
    overdue_invoices = serializers.IntegerField()
    total_payments = serializers.IntegerField()
    successful_payments = serializers.IntegerField()
    failed_payments = serializers.IntegerField()
    timestamp = serializers.DateTimeField()
