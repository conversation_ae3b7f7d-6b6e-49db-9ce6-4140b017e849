from django.db import models
from django.conf import settings
from django.core.validators import Min<PERSON><PERSON>ueValida<PERSON>, MaxValueValidator
from django.utils import timezone
from students.models import Student

class Department(models.Model):
    """Model representing an academic department."""
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=10, unique=True)
    description = models.TextField(blank=True, null=True)
    head = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='headed_departments')
    contact_email = models.EmailField(blank=True, null=True)
    contact_phone = models.CharField(max_length=20, blank=True, null=True)
    office_location = models.CharField(max_length=100, blank=True, null=True)
    website = models.URLField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    def __str__(self):
        return f"{self.name} ({self.code})"

    class Meta:
        ordering = ['name']
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'

class Subject(models.Model):
    """Model representing a subject area."""
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=10, unique=True)
    description = models.TextField(blank=True, null=True)
    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name='subjects')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    def __str__(self):
        return f"{self.name} ({self.code})"

    class Meta:
        ordering = ['department', 'name']
        verbose_name = 'Subject'
        verbose_name_plural = 'Subjects'

class Course(models.Model):
    """Model representing a course that can be offered."""
    LEVEL_CHOICES = (
        ('BEGINNER', 'Beginner'),
        ('INTERMEDIATE', 'Intermediate'),
        ('ADVANCED', 'Advanced'),
    )

    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20, unique=True)
    description = models.TextField(blank=True, null=True)
    subject = models.ForeignKey(Subject, on_delete=models.CASCADE, related_name='courses', null=True, blank=True)
    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name='courses')
    credits = models.PositiveSmallIntegerField(default=3)
    hours_per_week = models.PositiveSmallIntegerField(default=3)
    level = models.CharField(max_length=20, choices=LEVEL_CHOICES, default='INTERMEDIATE')
    grade_levels = models.ManyToManyField('academics.GradeLevel', related_name='available_courses', blank=True)
    prerequisites = models.ManyToManyField('self', symmetrical=False, blank=True, related_name='is_prerequisite_for')
    syllabus = models.FileField(upload_to='course_syllabi/', blank=True, null=True)
    learning_outcomes = models.TextField(blank=True, null=True)
    assessment_methods = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='created_courses')
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    def __str__(self):
        return f"{self.name} ({self.code})"

    class Meta:
        ordering = ['department', 'name']
        verbose_name = 'Course'
        verbose_name_plural = 'Courses'

class AcademicTerm(models.Model):
    """Model representing an academic term or semester."""
    TERM_CHOICES = (
        ('FIRST', 'First Term'),
        ('SECOND', 'Second Term'),
        ('THIRD', 'Third Term'),
        ('FALL', 'Fall'),
        ('SPRING', 'Spring'),
        ('SUMMER', 'Summer'),
        ('WINTER', 'Winter'),
    )

    name = models.CharField(max_length=100)
    term = models.CharField(max_length=10, choices=TERM_CHOICES)
    academic_year = models.ForeignKey('academics.AcademicYear', on_delete=models.CASCADE, related_name='terms', null=True, blank=True)
    year = models.PositiveIntegerField()
    start_date = models.DateField()
    end_date = models.DateField()
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=False)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    def __str__(self):
        return f"{self.term} {self.year} ({self.name})"

    class Meta:
        ordering = ['-year', 'term']
        unique_together = ['term', 'year']
        verbose_name = 'Academic Term'
        verbose_name_plural = 'Academic Terms'

class CourseOffering(models.Model):
    """Model representing a specific offering of a course in a term."""
    STATUS_CHOICES = (
        ('SCHEDULED', 'Scheduled'),
        ('IN_PROGRESS', 'In Progress'),
        ('COMPLETED', 'Completed'),
        ('CANCELLED', 'Cancelled'),
    )

    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='offerings')
    term = models.ForeignKey(AcademicTerm, on_delete=models.CASCADE, related_name='course_offerings')
    section = models.CharField(max_length=10, default='A', help_text="Section identifier (A, B, C, etc.)")
    instructor = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='taught_courses')
    assistant_instructors = models.ManyToManyField(settings.AUTH_USER_MODEL, blank=True, related_name='assisted_courses')
    grade_level = models.ForeignKey('academics.GradeLevel', on_delete=models.SET_NULL, null=True, blank=True, related_name='course_offerings')
    classroom = models.CharField(max_length=50, blank=True, null=True)
    schedule_days = models.CharField(max_length=50, blank=True, null=True, help_text="Days of the week (e.g., 'Mon,Wed,Fri')")
    start_time = models.TimeField(null=True, blank=True)
    end_time = models.TimeField(null=True, blank=True)
    max_students = models.PositiveIntegerField(default=30)
    min_students = models.PositiveIntegerField(default=5)
    current_students = models.PositiveIntegerField(default=0)
    start_date = models.DateField()
    end_date = models.DateField()
    syllabus_url = models.URLField(blank=True, null=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='SCHEDULED')
    is_active = models.BooleanField(default=True)
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    def __str__(self):
        return f"{self.course} - {self.term} (Section {self.section})"

    class Meta:
        ordering = ['-term__year', 'term__term', 'course__name', 'section']
        unique_together = ['course', 'term', 'section']
        verbose_name = 'Course Offering'
        verbose_name_plural = 'Course Offerings'

class Enrollment(models.Model):
    """Model representing a student's enrollment in a course offering."""
    STATUS_CHOICES = (
        ('ENROLLED', 'Enrolled'),
        ('COMPLETED', 'Completed'),
        ('WITHDRAWN', 'Withdrawn'),
        ('FAILED', 'Failed'),
        ('INCOMPLETE', 'Incomplete'),
        ('AUDITING', 'Auditing'),
    )

    GRADE_CHOICES = (
        ('A+', 'A+'),
        ('A', 'A'),
        ('A-', 'A-'),
        ('B+', 'B+'),
        ('B', 'B'),
        ('B-', 'B-'),
        ('C+', 'C+'),
        ('C', 'C'),
        ('C-', 'C-'),
        ('D+', 'D+'),
        ('D', 'D'),
        ('D-', 'D-'),
        ('F', 'F'),
        ('I', 'Incomplete'),
        ('W', 'Withdrawn'),
        ('P', 'Pass'),
        ('NP', 'Not Pass'),
    )

    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='enrollments')
    course_offering = models.ForeignKey(CourseOffering, on_delete=models.CASCADE, related_name='enrollments')
    enrollment_date = models.DateField(auto_now_add=True)
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='ENROLLED')
    grade = models.CharField(max_length=2, choices=GRADE_CHOICES, blank=True, null=True)
    numeric_grade = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True,
                                      validators=[MinValueValidator(0.0), MaxValueValidator(100.0)])
    grade_points = models.DecimalField(max_digits=3, decimal_places=2, blank=True, null=True)
    comments = models.TextField(blank=True, null=True)
    last_attendance_date = models.DateField(blank=True, null=True)
    completion_date = models.DateField(blank=True, null=True)
    is_repeat = models.BooleanField(default=False)
    approved_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_enrollments')
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    def save(self, *args, **kwargs):
        # Update the current_students count in the course offering
        if self._state.adding:  # If this is a new enrollment
            self.course_offering.current_students += 1
            self.course_offering.save()
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        # Update the current_students count in the course offering
        self.course_offering.current_students -= 1
        self.course_offering.save()
        super().delete(*args, **kwargs)

    def __str__(self):
        return f"{self.student} - {self.course_offering}"

    class Meta:
        ordering = ['-course_offering__term__year', 'course_offering__term__term']
        unique_together = ['student', 'course_offering']
        verbose_name = 'Enrollment'
        verbose_name_plural = 'Enrollments'

class LessonPlan(models.Model):
    """Model representing a lesson plan for a course offering."""
    course_offering = models.ForeignKey(CourseOffering, on_delete=models.CASCADE, related_name='lesson_plans')
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    lesson_number = models.PositiveIntegerField()
    lesson_date = models.DateField()
    duration_minutes = models.PositiveIntegerField(default=60)
    learning_objectives = models.TextField(blank=True, null=True)
    materials_required = models.TextField(blank=True, null=True)
    teaching_methods = models.TextField(blank=True, null=True)
    assessment_methods = models.TextField(blank=True, null=True)
    homework_assignment = models.TextField(blank=True, null=True)
    attachments = models.FileField(upload_to='lesson_plans/', blank=True, null=True)
    is_published = models.BooleanField(default=False)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='created_lesson_plans')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Lesson {self.lesson_number}: {self.title} - {self.course_offering}"

    class Meta:
        ordering = ['course_offering', 'lesson_number']
        unique_together = ['course_offering', 'lesson_number']
        verbose_name = 'Lesson Plan'
        verbose_name_plural = 'Lesson Plans'

class Resource(models.Model):
    """Model representing a learning resource for a course."""
    RESOURCE_TYPE_CHOICES = (
        ('DOCUMENT', 'Document'),
        ('VIDEO', 'Video'),
        ('AUDIO', 'Audio'),
        ('LINK', 'External Link'),
        ('IMAGE', 'Image'),
        ('PRESENTATION', 'Presentation'),
        ('WORKSHEET', 'Worksheet'),
        ('QUIZ', 'Quiz'),
        ('OTHER', 'Other'),
    )

    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='resources')
    course_offering = models.ForeignKey(CourseOffering, on_delete=models.SET_NULL, null=True, blank=True, related_name='resources')
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    resource_type = models.CharField(max_length=20, choices=RESOURCE_TYPE_CHOICES)
    file = models.FileField(upload_to='course_resources/', blank=True, null=True)
    url = models.URLField(blank=True, null=True)
    is_public = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='created_resources')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.title} ({self.get_resource_type_display()}) - {self.course}"

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Resource'
        verbose_name_plural = 'Resources'

class Assignment(models.Model):
    """Model representing an assignment for a course offering."""
    STATUS_CHOICES = (
        ('DRAFT', 'Draft'),
        ('PUBLISHED', 'Published'),
        ('CLOSED', 'Closed'),
        ('GRADED', 'Graded'),
    )

    course_offering = models.ForeignKey(CourseOffering, on_delete=models.CASCADE, related_name='assignments')
    title = models.CharField(max_length=200)
    description = models.TextField()
    instructions = models.TextField(blank=True, null=True)
    due_date = models.DateTimeField()
    total_marks = models.PositiveIntegerField(default=100)
    weight_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0.0,
                                          validators=[MinValueValidator(0.0), MaxValueValidator(100.0)])
    attachment = models.FileField(upload_to='assignments/', blank=True, null=True)
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='DRAFT')
    is_group_assignment = models.BooleanField(default=False)
    allow_late_submission = models.BooleanField(default=False)
    late_submission_deadline = models.DateTimeField(blank=True, null=True)
    late_submission_penalty = models.DecimalField(max_digits=5, decimal_places=2, default=0.0,
                                               validators=[MinValueValidator(0.0), MaxValueValidator(100.0)])
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='created_assignments')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.title} - {self.course_offering}"

    class Meta:
        ordering = ['course_offering', 'due_date']
        verbose_name = 'Assignment'
        verbose_name_plural = 'Assignments'

class AssignmentSubmission(models.Model):
    """Model representing a student's submission for an assignment."""
    STATUS_CHOICES = (
        ('DRAFT', 'Draft'),
        ('SUBMITTED', 'Submitted'),
        ('LATE', 'Late Submission'),
        ('GRADED', 'Graded'),
        ('RETURNED', 'Returned'),
    )

    assignment = models.ForeignKey(Assignment, on_delete=models.CASCADE, related_name='submissions')
    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='assignment_submissions')
    submission_date = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='DRAFT')
    submission_text = models.TextField(blank=True, null=True)
    attachment = models.FileField(upload_to='assignment_submissions/', blank=True, null=True)
    is_late = models.BooleanField(default=False)
    marks_obtained = models.DecimalField(max_digits=7, decimal_places=2, blank=True, null=True)
    feedback = models.TextField(blank=True, null=True)
    graded_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='graded_submissions')
    graded_at = models.DateTimeField(blank=True, null=True)

    def save(self, *args, **kwargs):
        # Check if submission is late
        if self.submission_date and self.assignment.due_date and self.submission_date > self.assignment.due_date:
            self.is_late = True

            # Apply late submission penalty if applicable
            if self.marks_obtained is not None and self.assignment.late_submission_penalty > 0:
                penalty = (self.assignment.late_submission_penalty / 100) * self.marks_obtained
                self.marks_obtained = max(0, self.marks_obtained - penalty)

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.student} - {self.assignment}"

    class Meta:
        ordering = ['-submission_date']
        unique_together = ['assignment', 'student']
        verbose_name = 'Assignment Submission'
        verbose_name_plural = 'Assignment Submissions'
