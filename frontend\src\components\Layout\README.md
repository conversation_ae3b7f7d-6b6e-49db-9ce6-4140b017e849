# Tenant Management Layout System

This document describes the consistent layout system used across all tenant management pages, based on the main dashboard design at `http://localhost:3001/dashboard`.

## Overview

The layout system provides a consistent visual experience across all tenant management pages with:
- Enhanced gradient headers with background patterns
- Consistent spacing and typography
- Reusable components for common UI patterns
- Responsive design that works on all screen sizes

## Components

### 1. TenantManagementLayout

A reusable layout component that provides the standard header structure for all tenant management pages.

**Usage:**
```tsx
import TenantManagementLayout from '@/components/Layout/TenantManagementLayout';

<TenantManagementLayout
  title="Page Title"
  subtitle="Page description"
  showBackButton={true}
  backButtonText="Back to Tenants"
  backButtonPath="/dashboard/tenant-management/tenants"
  actions={<Button>Action Button</Button>}
  headerStats={[
    { label: 'Total Items', value: 42 },
    { label: 'Active', value: 38 }
  ]}
  variant="dashboard" // 'dashboard' | 'list' | 'detail'
>
  {/* Page content */}
</TenantManagementLayout>
```

### 2. EnhancedCard

A styled card component with gradient backgrounds and consistent styling.

**Usage:**
```tsx
import EnhancedCard from '@/components/Layout/EnhancedCard';

<EnhancedCard
  title="Card Title"
  icon={<SomeIcon />}
  variant="info" // 'default' | 'info' | 'success' | 'warning' | 'error' | 'primary' | 'secondary'
>
  {/* Card content */}
</EnhancedCard>
```

## Design System

### Colors and Gradients

**Header Gradient:**
```css
background: linear-gradient(135deg, #667eea 0%, #764ba2 100%)
```

**Card Variants:**
- **Default:** `linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)`
- **Info:** `linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%)`
- **Success:** `linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%)`
- **Warning:** `linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%)`
- **Error:** `linear-gradient(135deg, #fef2f2 0%, #fecaca 100%)`
- **Primary:** `linear-gradient(135deg, #ede7f6 0%, #d1c4e9 100%)`
- **Secondary:** `linear-gradient(135deg, #fef7ff 0%, #f3e8ff 100%)`

### Typography

**Page Titles:** `variant="h3"` with `fontWeight: 700`
**Subtitles:** `variant="h6"` with `opacity: 0.9, fontWeight: 400`
**Section Headers:** `variant="h5"` with `fontWeight: 600`

### Spacing

**Container Padding:** `p: 3`
**Header Padding:** `p: 4`
**Card Padding:** `p: 4`
**Margin Bottom:** `mb: 4` for major sections

### Shadows and Effects

**Header Shadow:** `boxShadow: '0px 10px 30px rgba(102, 126, 234, 0.3)'`
**Hover Effects:** `transform: 'translateY(-2px)'` with `transition: 'all 0.2s ease-in-out'`

## Button Styles

### Outlined Buttons (Header)
```tsx
sx={{
  borderColor: 'rgba(255, 255, 255, 0.5)',
  color: 'white',
  fontWeight: 600,
  '&:hover': {
    borderColor: 'white',
    bgcolor: 'rgba(255, 255, 255, 0.1)',
    transform: 'translateY(-2px)',
  },
  transition: 'all 0.2s ease-in-out',
}}
```

### Contained Buttons (Header)
```tsx
sx={{
  bgcolor: 'rgba(255, 255, 255, 0.2)',
  backdropFilter: 'blur(10px)',
  border: '1px solid rgba(255, 255, 255, 0.3)',
  color: 'white',
  fontWeight: 600,
  '&:hover': {
    bgcolor: 'rgba(255, 255, 255, 0.3)',
    transform: 'translateY(-2px)',
  },
  transition: 'all 0.2s ease-in-out',
}}
```

## Background Patterns

All headers and cards include a subtle dot pattern overlay:
```css
&::before: {
  content: '""',
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  background: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
  opacity: 0.3,
}
```

## Implementation Examples

### Dashboard Page
- Uses main container with `minHeight: '100vh'` and `bgcolor: 'background.default'`
- Enhanced header with gradient background and stats
- Multiple enhanced cards for different content sections

### List Page
- Same header structure with search and filter controls
- Enhanced card containing the data table
- Consistent button styling throughout

### Detail Page
- Header includes back button and action buttons
- Content organized in tabs with enhanced cards
- Status indicators and metrics cards

## Best Practices

1. **Always use the layout components** for consistency
2. **Follow the established color scheme** and gradients
3. **Use consistent spacing** (multiples of 4px)
4. **Include hover effects** on interactive elements
5. **Maintain responsive design** with proper Grid usage
6. **Use semantic HTML** and proper ARIA labels
7. **Test on different screen sizes** to ensure responsiveness

## Future Development

When creating new tenant management pages:

1. Use `TenantManagementLayout` as the base
2. Organize content in `EnhancedCard` components
3. Follow the established design patterns
4. Maintain consistency with existing pages
5. Test thoroughly across different devices and screen sizes
