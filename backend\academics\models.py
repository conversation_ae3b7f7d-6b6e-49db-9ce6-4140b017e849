from django.db import models
from django.conf import settings
from courses.models import Department

class AcademicYear(models.Model):
    """Model representing an academic year."""
    name = models.CharField(max_length=50)
    start_date = models.DateField()
    end_date = models.DateField()
    is_current = models.BooleanField(default=False)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['-start_date']

class Program(models.Model):
    """Model representing an academic program."""
    LEVEL_CHOICES = (
        ('PRIMARY', 'Primary School'),
        ('SECONDARY', 'Secondary School'),
        ('PREPARATORY', 'Preparatory School'),
        ('VOCATIONAL', 'Vocational Training'),
        ('CERTIFICATE', 'Certificate'),
        ('DIPLOMA', 'Diploma'),
        ('DEGREE', 'Degree'),
        ('MASTERS', 'Masters'),
        ('DOCTORATE', 'Doctorate'),
    )

    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20, unique=True)
    level = models.CharField(max_length=20, choices=LEVEL_CHOICES)
    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name='programs')
    description = models.TextField(blank=True, null=True)
    duration_years = models.PositiveSmallIntegerField(default=4)
    coordinator = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='coordinated_programs')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.code})"

    class Meta:
        ordering = ['department', 'name']

class GradeLevel(models.Model):
    """Model representing a grade level or class."""
    name = models.CharField(max_length=50)
    code = models.CharField(max_length=10, unique=True)
    program = models.ForeignKey(Program, on_delete=models.CASCADE, related_name='grade_levels')
    sequence = models.PositiveSmallIntegerField(help_text="Order in the program sequence")
    description = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"{self.name} ({self.program.code})"

    class Meta:
        ordering = ['program', 'sequence']
        unique_together = ['program', 'sequence']

class Section(models.Model):
    """Model representing a section or division of a grade level."""
    name = models.CharField(max_length=50)
    grade_level = models.ForeignKey(GradeLevel, on_delete=models.CASCADE, related_name='sections')
    academic_year = models.ForeignKey(AcademicYear, on_delete=models.CASCADE, related_name='sections')
    homeroom_teacher = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='homeroom_sections')
    max_students = models.PositiveSmallIntegerField(default=40)
    room = models.CharField(max_length=50, blank=True, null=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.grade_level.name} {self.name} ({self.academic_year.name})"

    class Meta:
        ordering = ['academic_year', 'grade_level', 'name']
        unique_together = ['grade_level', 'name', 'academic_year']

class ClassSchedule(models.Model):
    """Model representing a class schedule."""
    DAY_CHOICES = (
        (0, 'Monday'),
        (1, 'Tuesday'),
        (2, 'Wednesday'),
        (3, 'Thursday'),
        (4, 'Friday'),
        (5, 'Saturday'),
        (6, 'Sunday'),
    )

    section = models.ForeignKey(Section, on_delete=models.CASCADE, related_name='schedules')
    course_offering = models.ForeignKey('courses.CourseOffering', on_delete=models.CASCADE, related_name='schedules')
    teacher = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='teaching_schedules')
    day_of_week = models.PositiveSmallIntegerField(choices=DAY_CHOICES)
    start_time = models.TimeField()
    end_time = models.TimeField()
    room = models.CharField(max_length=50, blank=True, null=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.course_offering.course.name} - {self.section} - {self.get_day_of_week_display()} {self.start_time}-{self.end_time}"

    class Meta:
        ordering = ['day_of_week', 'start_time']

class StudentGradeLevel(models.Model):
    """Model representing a student's assignment to a grade level and section."""
    student = models.ForeignKey('students.Student', on_delete=models.CASCADE, related_name='grade_assignments')
    section = models.ForeignKey(Section, on_delete=models.CASCADE, related_name='student_assignments')
    academic_year = models.ForeignKey(AcademicYear, on_delete=models.CASCADE, related_name='student_assignments')
    enrollment_date = models.DateField(auto_now_add=True)
    status = models.CharField(max_length=20, choices=(
        ('ACTIVE', 'Active'),
        ('TRANSFERRED', 'Transferred'),
        ('WITHDRAWN', 'Withdrawn'),
        ('GRADUATED', 'Graduated'),
        ('SUSPENDED', 'Suspended'),
    ), default='ACTIVE')

    def __str__(self):
        return f"{self.student} - {self.section} ({self.academic_year})"

    class Meta:
        ordering = ['-academic_year__start_date', 'section']
        unique_together = ['student', 'academic_year']

class Calendar(models.Model):
    """Model representing a school calendar event."""
    EVENT_TYPE_CHOICES = (
        ('HOLIDAY', 'Holiday'),
        ('EXAM', 'Examination'),
        ('MEETING', 'Meeting'),
        ('ACTIVITY', 'Activity'),
        ('OTHER', 'Other'),
    )

    title = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    event_type = models.CharField(max_length=20, choices=EVENT_TYPE_CHOICES)
    start_date = models.DateField()
    end_date = models.DateField()
    start_time = models.TimeField(null=True, blank=True)
    end_time = models.TimeField(null=True, blank=True)
    academic_year = models.ForeignKey(AcademicYear, on_delete=models.CASCADE, related_name='calendar_events')
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='created_events')
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.title} ({self.start_date})"

    class Meta:
        ordering = ['start_date', 'start_time']
