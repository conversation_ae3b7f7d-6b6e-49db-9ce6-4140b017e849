import axios from 'axios'

const API_BASE_URL = 'http://localhost:8001/api/site'

// Create axios instance for feature flags API calls
const featureFlagsApi = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Add auth token to requests
featureFlagsApi.interceptors.request.use((config) => {
  const token = localStorage.getItem('token')
  if (token) {
    config.headers.Authorization = `Token ${token}`
  }
  return config
})

// Response interceptor for error handling
featureFlagsApi.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('Feature Flags API Error:', error)
    return Promise.reject(error)
  }
)

// Types
export interface FeatureFlag {
  id: string
  name: string
  key: string
  description: string
  enabled: boolean
  category: string
  environment: 'development' | 'staging' | 'production' | 'all'
  rollout_percentage: number
  dependencies: string[]
  created_at: string
  updated_at: string
  last_modified_by: string
}

export interface CreateFeatureFlagData {
  name: string
  key: string
  description: string
  enabled: boolean
  category: string
  environment: 'development' | 'staging' | 'production' | 'all'
  rollout_percentage: number
  dependencies: string[]
}

export interface UpdateFeatureFlagData extends Partial<CreateFeatureFlagData> {
  id: string
}

export interface FeatureFlagStats {
  total_flags: number
  enabled_flags: number
  disabled_flags: number
  flags_by_category: Record<string, number>
  flags_by_environment: Record<string, number>
  recent_changes: number
}

// API Functions
export const getFeatureFlags = async (): Promise<FeatureFlag[]> => {
  try {
    const response = await featureFlagsApi.get('/feature-flags/')
    return response.data.results || response.data
  } catch (error) {
    console.error('Error fetching feature flags:', error)
    throw error
  }
}

export const getFeatureFlag = async (id: string): Promise<FeatureFlag> => {
  try {
    const response = await featureFlagsApi.get(`/feature-flags/${id}/`)
    return response.data
  } catch (error) {
    console.error('Error fetching feature flag:', error)
    throw error
  }
}

export const createFeatureFlag = async (data: CreateFeatureFlagData): Promise<FeatureFlag> => {
  try {
    const response = await featureFlagsApi.post('/feature-flags/', data)
    return response.data
  } catch (error) {
    console.error('Error creating feature flag:', error)
    throw error
  }
}

export const updateFeatureFlag = async (id: string, data: Partial<CreateFeatureFlagData>): Promise<FeatureFlag> => {
  try {
    const response = await featureFlagsApi.patch(`/feature-flags/${id}/`, data)
    return response.data
  } catch (error) {
    console.error('Error updating feature flag:', error)
    throw error
  }
}

export const deleteFeatureFlag = async (id: string): Promise<void> => {
  try {
    await featureFlagsApi.delete(`/feature-flags/${id}/`)
  } catch (error) {
    console.error('Error deleting feature flag:', error)
    throw error
  }
}

export const toggleFeatureFlag = async (id: string): Promise<FeatureFlag> => {
  try {
    const response = await featureFlagsApi.post(`/feature-flags/${id}/toggle/`)
    return response.data
  } catch (error) {
    console.error('Error toggling feature flag:', error)
    throw error
  }
}

export const bulkUpdateFeatureFlags = async (updates: Array<{ id: string; enabled: boolean }>): Promise<FeatureFlag[]> => {
  try {
    const response = await featureFlagsApi.post('/feature-flags/bulk-update/', { updates })
    return response.data
  } catch (error) {
    console.error('Error bulk updating feature flags:', error)
    throw error
  }
}

export const getFeatureFlagStats = async (): Promise<FeatureFlagStats> => {
  try {
    const response = await featureFlagsApi.get('/feature-flags/stats/')
    return response.data
  } catch (error) {
    console.error('Error fetching feature flag stats:', error)
    throw error
  }
}

export const validateFeatureFlagKey = async (key: string, excludeId?: string): Promise<{ valid: boolean; message?: string }> => {
  try {
    const response = await featureFlagsApi.post('/feature-flags/validate-key/', { 
      key, 
      exclude_id: excludeId 
    })
    return response.data
  } catch (error) {
    console.error('Error validating feature flag key:', error)
    throw error
  }
}

export const getFeatureFlagHistory = async (id: string): Promise<Array<{
  id: string
  action: string
  changes: Record<string, any>
  user: string
  timestamp: string
}>> => {
  try {
    const response = await featureFlagsApi.get(`/feature-flags/${id}/history/`)
    return response.data
  } catch (error) {
    console.error('Error fetching feature flag history:', error)
    throw error
  }
}

export const exportFeatureFlags = async (format: 'json' | 'csv' = 'json'): Promise<Blob> => {
  try {
    const response = await featureFlagsApi.get(`/feature-flags/export/?format=${format}`, {
      responseType: 'blob'
    })
    return response.data
  } catch (error) {
    console.error('Error exporting feature flags:', error)
    throw error
  }
}

export const importFeatureFlags = async (file: File): Promise<{ 
  success: number; 
  errors: Array<{ row: number; message: string }> 
}> => {
  try {
    const formData = new FormData()
    formData.append('file', file)
    
    const response = await featureFlagsApi.post('/feature-flags/import/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    return response.data
  } catch (error) {
    console.error('Error importing feature flags:', error)
    throw error
  }
}

// Utility functions
export const transformFeatureFlagForForm = (flag: FeatureFlag) => ({
  name: flag.name,
  key: flag.key,
  description: flag.description,
  enabled: flag.enabled,
  category: flag.category,
  environment: flag.environment,
  rolloutPercentage: flag.rollout_percentage,
  dependencies: flag.dependencies,
})

export const transformFormDataToFeatureFlag = (formData: any): CreateFeatureFlagData => ({
  name: formData.name,
  key: formData.key,
  description: formData.description,
  enabled: formData.enabled,
  category: formData.category,
  environment: formData.environment,
  rollout_percentage: formData.rolloutPercentage,
  dependencies: formData.dependencies || [],
})

export const generateFeatureFlagKey = (name: string): string => {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, '_')
    .replace(/_{2,}/g, '_')
    .replace(/^_|_$/g, '')
}

export const getFeatureFlagCategories = (): string[] => [
  'UI/UX',
  'Analytics',
  'Integration',
  'AI/ML',
  'Communication',
  'Localization',
  'Security',
  'Performance',
  'Billing',
  'Reporting',
  'Mobile',
  'API',
  'Experimental'
]

export const getEnvironmentOptions = () => [
  { value: 'development', label: 'Development', color: 'info' },
  { value: 'staging', label: 'Staging', color: 'warning' },
  { value: 'production', label: 'Production', color: 'success' },
  { value: 'all', label: 'All Environments', color: 'default' }
]

export const isFeatureFlagEnabled = (key: string, flags: FeatureFlag[]): boolean => {
  const flag = flags.find(f => f.key === key)
  return flag ? flag.enabled : false
}

export const getFeatureFlagValue = (key: string, flags: FeatureFlag[], defaultValue: any = false): any => {
  const flag = flags.find(f => f.key === key)
  return flag ? flag.enabled : defaultValue
}
