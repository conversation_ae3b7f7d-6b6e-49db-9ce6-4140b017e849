// Enhanced RBAC Type Definitions for World-Class Access Control

export interface Resource {
  id: string
  name: string
  type: ResourceType
  description: string
  parent_resource?: string
  attributes: Record<string, any>
  created_at: string
  updated_at: string
}

export enum ResourceType {
  SYSTEM = 'system',
  TENANT = 'tenant',
  SCHOOL = 'school',
  CLASS = 'class',
  STUDENT = 'student',
  TEACHER = 'teacher',
  STAFF = 'staff',
  COURSE = 'course',
  GRADE = 'grade',
  ATTENDANCE = 'attendance',
  BILLING = 'billing',
  REPORT = 'report',
  SETTING = 'setting',
  USER = 'user',
  ROLE = 'role',
  PERMISSION = 'permission',
}

export enum ActionType {
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
  EXECUTE = 'execute',
  APPROVE = 'approve',
  REJECT = 'reject',
  EXPORT = 'export',
  IMPORT = 'import',
  SHARE = 'share',
  ASSIGN = 'assign',
  UNASSIGN = 'unassign',
  ARCHIVE = 'archive',
  RESTORE = 'restore',
}

export interface Permission {
  id: string
  name: string
  codename: string
  description: string
  resource_type: ResourceType
  action: ActionType
  conditions?: PermissionCondition[]
  attributes: Record<string, any>
  is_system: boolean
  created_at: string
  updated_at: string
}

export interface PermissionCondition {
  field: string
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'not_in' | 'contains' | 'starts_with' | 'ends_with'
  value: any
  logical_operator?: 'and' | 'or'
}

export interface Role {
  id: string
  name: string
  display_name: string
  description: string
  level: number
  parent_role?: string
  child_roles: string[]
  permissions: string[]
  inherited_permissions: string[]
  effective_permissions: string[]
  users_count: number
  is_system: boolean
  is_template: boolean
  template_category?: string
  attributes: Record<string, any>
  created_at: string
  updated_at: string
  created_by: string
  last_modified_by: string
}

export interface RoleHierarchy {
  role_id: string
  parent_role_id?: string
  level: number
  path: string[]
  children: RoleHierarchy[]
}

export interface User {
  id: string
  username: string
  email: string
  first_name: string
  last_name: string
  roles: string[]
  direct_permissions: string[]
  effective_permissions: string[]
  groups: string[]
  attributes: Record<string, any>
  is_active: boolean
  is_superuser: boolean
  last_login?: string
  password_changed_at?: string
  mfa_enabled: boolean
  account_locked: boolean
  account_locked_until?: string
  failed_login_attempts: number
  created_at: string
  updated_at: string
  created_by: string
  last_modified_by: string
}

export interface Group {
  id: string
  name: string
  description: string
  permissions: string[]
  users: string[]
  is_system: boolean
  created_at: string
  updated_at: string
}

export interface AccessContext {
  user_id: string
  resource_type: ResourceType
  resource_id?: string
  action: ActionType
  tenant_id?: string
  additional_context?: Record<string, any>
  timestamp: string
}

export interface AccessDecision {
  granted: boolean
  reason: string
  matched_permissions: string[]
  denied_permissions: string[]
  conditions_evaluated: PermissionCondition[]
  context: AccessContext
  evaluation_time_ms: number
}

export interface RoleTemplate {
  id: string
  name: string
  display_name: string
  description: string
  category: string
  permissions: string[]
  attributes: Record<string, any>
  is_builtin: boolean
  usage_count: number
  created_at: string
  updated_at: string
}

export interface PermissionMatrix {
  resource_type: ResourceType
  actions: {
    [key in ActionType]?: {
      roles: string[]
      conditions?: PermissionCondition[]
      description: string
    }
  }
}

export interface AccessPolicy {
  id: string
  name: string
  description: string
  resource_type: ResourceType
  conditions: PermissionCondition[]
  effect: 'allow' | 'deny'
  priority: number
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface RoleAnalytics {
  role_id: string
  users_count: number
  permissions_count: number
  last_used: string
  usage_frequency: number
  risk_score: number
  compliance_status: 'compliant' | 'non_compliant' | 'review_required'
  recommendations: string[]
}

export interface AccessReview {
  id: string
  name: string
  description: string
  type: 'user_access' | 'role_permissions' | 'orphaned_accounts' | 'excessive_permissions'
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  scope: {
    users?: string[]
    roles?: string[]
    resources?: string[]
  }
  findings: AccessReviewFinding[]
  created_by: string
  assigned_to: string[]
  due_date: string
  completed_at?: string
  created_at: string
  updated_at: string
}

export interface AccessReviewFinding {
  id: string
  type: 'excessive_permission' | 'unused_permission' | 'orphaned_account' | 'role_drift' | 'policy_violation'
  severity: 'low' | 'medium' | 'high' | 'critical'
  title: string
  description: string
  affected_user?: string
  affected_role?: string
  affected_permission?: string
  recommendation: string
  status: 'open' | 'acknowledged' | 'resolved' | 'false_positive'
  resolved_by?: string
  resolved_at?: string
  created_at: string
}

export interface ComplianceReport {
  id: string
  name: string
  type: 'sox' | 'gdpr' | 'hipaa' | 'pci_dss' | 'custom'
  period_start: string
  period_end: string
  status: 'generating' | 'completed' | 'failed'
  summary: {
    total_users: number
    total_roles: number
    total_permissions: number
    violations_count: number
    compliance_score: number
  }
  violations: ComplianceViolation[]
  generated_at: string
  generated_by: string
}

export interface ComplianceViolation {
  id: string
  type: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  description: string
  affected_entity: string
  entity_type: 'user' | 'role' | 'permission'
  remediation: string
  status: 'open' | 'in_progress' | 'resolved'
  detected_at: string
}

export interface RBACConfiguration {
  enable_role_hierarchy: boolean
  enable_permission_inheritance: boolean
  enable_dynamic_permissions: boolean
  enable_attribute_based_access: boolean
  enable_time_based_access: boolean
  enable_location_based_access: boolean
  default_session_timeout: number
  max_role_depth: number
  permission_cache_ttl: number
  audit_all_access: boolean
  require_approval_for_role_changes: boolean
  enable_emergency_access: boolean
}

export interface EmergencyAccess {
  id: string
  user_id: string
  requested_permissions: string[]
  justification: string
  approved_by?: string
  approved_at?: string
  expires_at: string
  status: 'pending' | 'approved' | 'denied' | 'expired' | 'revoked'
  created_at: string
}

export interface AccessPattern {
  user_id: string
  resource_type: ResourceType
  action: ActionType
  frequency: number
  last_access: string
  risk_score: number
  anomaly_detected: boolean
}

export interface RiskAssessment {
  user_id: string
  overall_risk_score: number
  factors: {
    excessive_permissions: number
    unused_permissions: number
    privileged_access: number
    anomalous_behavior: number
    compliance_violations: number
  }
  recommendations: string[]
  last_assessed: string
}

// Utility types for API responses
export interface PaginatedResponse<T> {
  results: T[]
  count: number
  next?: string
  previous?: string
  page_size: number
  total_pages: number
}

export interface BulkOperationResult {
  success_count: number
  error_count: number
  errors: Array<{
    item_id: string
    error: string
  }>
  warnings: string[]
}

export interface PermissionCheck {
  user_id: string
  permission: string
  resource_id?: string
  context?: Record<string, any>
}

export interface PermissionCheckResult {
  granted: boolean
  reason: string
  cached: boolean
  evaluation_time_ms: number
}

// Event types for audit logging
export interface RBACEvent {
  id: string
  event_type: 'role_assigned' | 'role_unassigned' | 'permission_granted' | 'permission_revoked' | 'access_granted' | 'access_denied' | 'role_created' | 'role_updated' | 'role_deleted'
  user_id: string
  target_user_id?: string
  role_id?: string
  permission_id?: string
  resource_type?: ResourceType
  resource_id?: string
  details: Record<string, any>
  ip_address: string
  user_agent: string
  timestamp: string
}
