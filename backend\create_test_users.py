#!/usr/bin/env python
"""
Script to create test users for testing group assignment functionality.
"""

import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kelem_sms.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group, Permission

User = get_user_model()

def create_test_users():
    """Create test users for group assignment testing."""
    
    # Test users data
    test_users = [
        {
            'email': '<EMAIL>',
            'first_name': '<PERSON>',
            'last_name': '<PERSON>',
            'user_type': 'teacher',
            'is_staff': True,
            'is_active': True,
        },
        {
            'email': '<EMAIL>',
            'first_name': '<PERSON>',
            'last_name': '<PERSON><PERSON>',
            'user_type': 'teacher',
            'is_staff': True,
            'is_active': True,
        },
        {
            'email': '<EMAIL>',
            'first_name': '<PERSON>',
            'last_name': '<PERSON>',
            'user_type': 'admin',
            'is_staff': True,
            'is_active': True,
        },
        {
            'email': '<EMAIL>',
            'first_name': 'Sarah',
            'last_name': 'Wilson',
            'user_type': 'student',
            'is_staff': False,
            'is_active': True,
        },
        {
            'email': '<EMAIL>',
            'first_name': 'Bob',
            'last_name': 'Brown',
            'user_type': 'student',
            'is_staff': False,
            'is_active': True,
        },
    ]
    
    created_users = []
    
    for user_data in test_users:
        email = user_data['email']
        
        # Check if user already exists
        if User.objects.filter(email=email).exists():
            print(f"User {email} already exists, skipping...")
            continue
        
        # Create user
        user = User.objects.create_user(
            email=email,
            password='testpass123',  # Default password for all test users
            **{k: v for k, v in user_data.items() if k != 'email'}
        )
        
        created_users.append(user)
        print(f"Created user: {user.get_full_name()} ({user.email}) - {user.user_type}")
    
    print(f"\nCreated {len(created_users)} new test users.")
    print(f"Total users in system: {User.objects.count()}")
    
    return created_users

def create_test_groups():
    """Create test groups for testing."""
    
    test_groups = [
        {
            'name': 'Teaching Staff',
            'permissions': ['add_user', 'change_user', 'view_user']
        },
        {
            'name': 'Administrative Staff',
            'permissions': ['add_user', 'change_user', 'delete_user', 'view_user']
        },
        {
            'name': 'Students',
            'permissions': ['view_user']
        }
    ]
    
    created_groups = []
    
    for group_data in test_groups:
        group_name = group_data['name']
        
        # Check if group already exists
        if Group.objects.filter(name=group_name).exists():
            print(f"Group '{group_name}' already exists, skipping...")
            continue
        
        # Create group
        group = Group.objects.create(name=group_name)
        
        # Add permissions
        permission_codenames = group_data.get('permissions', [])
        permissions = Permission.objects.filter(codename__in=permission_codenames)
        group.permissions.set(permissions)
        
        created_groups.append(group)
        print(f"Created group: {group.name} with {permissions.count()} permissions")
    
    print(f"\nCreated {len(created_groups)} new test groups.")
    print(f"Total groups in system: {Group.objects.count()}")
    
    return created_groups

if __name__ == '__main__':
    print("Creating test users and groups for group assignment testing...\n")
    
    # Create test users
    users = create_test_users()
    
    # Create test groups
    groups = create_test_groups()
    
    print("\nTest data creation completed!")
    print("\nYou can now test group assignment functionality in the frontend.")
    print("Default password for all test users: testpass123")
