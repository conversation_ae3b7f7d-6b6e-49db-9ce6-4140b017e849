from django.db import models
from django.conf import settings
from django.core.validators import RegexValidator
from django.utils import timezone
from tenants.models import School

class Student(models.Model):
    """Model representing a student in the system."""
    GENDER_CHOICES = (
        ('M', 'Male'),
        ('F', 'Female'),
        ('O', 'Other'),
    )

    BLOOD_GROUP_CHOICES = (
        ('A+', 'A+'),
        ('A-', 'A-'),
        ('B+', 'B+'),
        ('B-', 'B-'),
        ('AB+', 'AB+'),
        ('AB-', 'AB-'),
        ('O+', 'O+'),
        ('O-', 'O-'),
    )

    STATUS_CHOICES = (
        ('ACTIVE', 'Active'),
        ('INACTIVE', 'Inactive'),
        ('GRADUATED', 'Graduated'),
        ('TRANSFERRED', 'Transferred'),
        ('SUSPENDED', 'Suspended'),
        ('WITHDRAWN', 'Withdrawn'),
    )

    # Basic Information
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, null=True, blank=True, related_name='student_profile')
    first_name = models.CharField(max_length=100)
    middle_name = models.CharField(max_length=100, blank=True, null=True)
    last_name = models.CharField(max_length=100)
    student_id = models.CharField(max_length=20, unique=True)
    date_of_birth = models.DateField(null=True, blank=True)
    place_of_birth = models.CharField(max_length=100, blank=True, null=True)
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES, null=True, blank=True)
    nationality = models.CharField(max_length=50, blank=True, null=True)
    mother_tongue = models.CharField(max_length=50, blank=True, null=True)
    religion = models.CharField(max_length=50, blank=True, null=True)

    # Contact Information
    email = models.EmailField(unique=True, null=True, blank=True)
    phone_regex = RegexValidator(regex=r'^\+?1?\d{9,15}$', message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.")
    phone_number = models.CharField(validators=[phone_regex], max_length=20, null=True, blank=True)
    emergency_contact_name = models.CharField(max_length=100, blank=True, null=True)
    emergency_contact_phone = models.CharField(validators=[phone_regex], max_length=20, null=True, blank=True)
    emergency_contact_relationship = models.CharField(max_length=50, blank=True, null=True)

    # Address Information
    address = models.TextField(null=True, blank=True)
    city = models.CharField(max_length=100, blank=True, null=True)
    state = models.CharField(max_length=100, blank=True, null=True)
    postal_code = models.CharField(max_length=20, blank=True, null=True)
    country = models.CharField(max_length=100, default='Ethiopia')

    # Academic Information
    enrollment_date = models.DateField()
    current_grade_level = models.ForeignKey('academics.GradeLevel', on_delete=models.SET_NULL, null=True, blank=True, related_name='current_students')
    current_section = models.ForeignKey('academics.Section', on_delete=models.SET_NULL, null=True, blank=True, related_name='current_students')
    previous_school = models.CharField(max_length=200, blank=True, null=True)
    admission_type = models.CharField(max_length=50, blank=True, null=True, choices=(
        ('NEW', 'New Admission'),
        ('TRANSFER', 'Transfer'),
        ('READMISSION', 'Re-Admission'),
    ), default='NEW')

    # Health Information
    blood_group = models.CharField(max_length=3, choices=BLOOD_GROUP_CHOICES, blank=True, null=True)
    medical_conditions = models.TextField(blank=True, null=True)
    allergies = models.TextField(blank=True, null=True)
    medications = models.TextField(blank=True, null=True)

    # Status and System Information
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='ACTIVE')
    is_active = models.BooleanField(default=True)
    photo = models.ImageField(upload_to='student_photos/', null=True, blank=True)
    registration_number = models.CharField(max_length=50, blank=True, null=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)
    notes = models.TextField(blank=True, null=True)

    def full_name(self):
        if self.middle_name:
            return f"{self.first_name} {self.middle_name} {self.last_name}"
        return f"{self.first_name} {self.last_name}"

    def __str__(self):
        return f"{self.full_name()} ({self.student_id})"

    class Meta:
        ordering = ['last_name', 'first_name']
        verbose_name = 'Student'
        verbose_name_plural = 'Students'

class Guardian(models.Model):
    """Model representing a parent or guardian of a student."""
    RELATIONSHIP_CHOICES = (
        ('FATHER', 'Father'),
        ('MOTHER', 'Mother'),
        ('GRANDFATHER', 'Grandfather'),
        ('GRANDMOTHER', 'Grandmother'),
        ('UNCLE', 'Uncle'),
        ('AUNT', 'Aunt'),
        ('SIBLING', 'Sibling'),
        ('LEGAL_GUARDIAN', 'Legal Guardian'),
        ('OTHER', 'Other'),
    )

    # Basic Information
    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='guardians')
    name = models.CharField(max_length=200, null=True, blank=True)  # Keep for backward compatibility
    first_name = models.CharField(max_length=100, null=True, blank=True)
    middle_name = models.CharField(max_length=100, blank=True, null=True)
    last_name = models.CharField(max_length=100, null=True, blank=True)
    relationship = models.CharField(max_length=50, choices=RELATIONSHIP_CHOICES)
    occupation = models.CharField(max_length=100, blank=True, null=True)
    employer = models.CharField(max_length=100, blank=True, null=True)

    # Contact Information
    phone_regex = RegexValidator(regex=r'^\+?1?\d{9,15}$', message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.")
    phone_number = models.CharField(validators=[phone_regex], max_length=20, null=True, blank=True)
    alternate_phone = models.CharField(validators=[phone_regex], max_length=20, null=True, blank=True)
    email = models.EmailField(null=True, blank=True)

    # Address Information
    address = models.TextField(null=True, blank=True)
    city = models.CharField(max_length=100, blank=True, null=True)
    state = models.CharField(max_length=100, blank=True, null=True)
    postal_code = models.CharField(max_length=20, blank=True, null=True)
    country = models.CharField(max_length=100, default='Ethiopia')

    # Status and System Information
    is_primary_contact = models.BooleanField(default=False)
    is_emergency_contact = models.BooleanField(default=False)
    can_pickup_student = models.BooleanField(default=False)
    receives_reports = models.BooleanField(default=True)
    photo = models.ImageField(upload_to='guardian_photos/', null=True, blank=True)
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    def full_name(self):
        # If we have the old name field but not the new fields, use the old field
        if self.name and not (self.first_name or self.last_name):
            return self.name

        # If we have the new fields
        if self.first_name and self.last_name:
            if self.middle_name:
                return f"{self.first_name} {self.middle_name} {self.last_name}"
            return f"{self.first_name} {self.last_name}"

        # Fallback
        return "Unknown"

    def __str__(self):
        return f"{self.full_name()} ({self.get_relationship_display()} of {self.student})"

    class Meta:
        ordering = ['student', 'last_name', 'first_name']
        verbose_name = 'Guardian'
        verbose_name_plural = 'Guardians'

class StudentDocument(models.Model):
    """Model representing documents related to a student."""
    DOCUMENT_TYPE_CHOICES = (
        ('BIRTH_CERTIFICATE', 'Birth Certificate'),
        ('ID_CARD', 'ID Card'),
        ('PASSPORT', 'Passport'),
        ('PREVIOUS_SCHOOL_RECORD', 'Previous School Record'),
        ('MEDICAL_RECORD', 'Medical Record'),
        ('VACCINATION_RECORD', 'Vaccination Record'),
        ('PHOTO', 'Photograph'),
        ('OTHER', 'Other'),
    )

    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='documents')
    document_type = models.CharField(max_length=50, choices=DOCUMENT_TYPE_CHOICES)
    title = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    file = models.FileField(upload_to='student_documents/')
    is_verified = models.BooleanField(default=False)
    verified_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='verified_documents')
    verified_date = models.DateTimeField(null=True, blank=True)
    uploaded_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='uploaded_documents')
    uploaded_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    def __str__(self):
        return f"{self.title} - {self.student}"

    class Meta:
        ordering = ['-uploaded_at']
        verbose_name = 'Student Document'
        verbose_name_plural = 'Student Documents'

class FormFieldType(models.TextChoices):
    TEXT = 'text', 'Text'
    NUMBER = 'number', 'Number'
    EMAIL = 'email', 'Email'
    PHONE = 'phone', 'Phone'
    DATE = 'date', 'Date'
    SELECT = 'select', 'Select'
    TEXTAREA = 'textarea', 'Text Area'
    CHECKBOX = 'checkbox', 'Checkbox'
    RADIO = 'radio', 'Radio'
    FILE = 'file', 'File Upload'

class FormFieldStatus(models.TextChoices):
    REQUIRED = 'required', 'Required'
    OPTIONAL = 'optional', 'Optional'
    HIDDEN = 'hidden', 'Hidden'

class FormSection(models.TextChoices):
    PERSONAL = 'personal', 'Personal Information'
    CONTACT = 'contact', 'Contact Information'
    ACADEMIC = 'academic', 'Academic Information'
    GUARDIAN = 'guardian', 'Guardian Information'
    HEALTH = 'health', 'Health Information'
    EMERGENCY = 'emergency', 'Emergency Contact'
    DOCUMENTS = 'documents', 'Documents'
    OTHER = 'other', 'Other Information'

class StudentFormConfig(models.Model):
    """Configuration for student registration form fields per tenant."""
    tenant = models.ForeignKey(School, on_delete=models.CASCADE, related_name='student_form_configs')
    field_name = models.CharField(max_length=100, help_text="Field name in the API")
    display_name = models.CharField(max_length=100, help_text="Display name shown to users")
    field_type = models.CharField(max_length=20, choices=FormFieldType.choices)
    section = models.CharField(max_length=20, choices=FormSection.choices)
    status = models.CharField(max_length=20, choices=FormFieldStatus.choices, default=FormFieldStatus.OPTIONAL)
    order = models.PositiveSmallIntegerField(default=0, help_text="Display order within section")
    options = models.JSONField(null=True, blank=True, help_text="Options for select, radio, etc.")
    help_text = models.CharField(max_length=255, blank=True, null=True)
    default_value = models.CharField(max_length=255, blank=True, null=True)
    validation_regex = models.CharField(max_length=255, blank=True, null=True, help_text="Regex pattern for validation")
    is_custom = models.BooleanField(default=False, help_text="Whether this is a custom field")

    class Meta:
        unique_together = ('tenant', 'field_name')
        ordering = ['section', 'order']

    def __str__(self):
        return f"{self.tenant.name} - {self.display_name}"

class StudentAchievement(models.Model):
    """Model representing achievements and awards earned by a student."""
    ACHIEVEMENT_TYPE_CHOICES = (
        ('ACADEMIC', 'Academic'),
        ('SPORTS', 'Sports'),
        ('ARTS', 'Arts'),
        ('LEADERSHIP', 'Leadership'),
        ('COMMUNITY_SERVICE', 'Community Service'),
        ('OTHER', 'Other'),
    )

    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='achievements')
    title = models.CharField(max_length=100)
    achievement_type = models.CharField(max_length=50, choices=ACHIEVEMENT_TYPE_CHOICES)
    description = models.TextField()
    date_achieved = models.DateField()
    issuing_organization = models.CharField(max_length=100, blank=True, null=True)
    certificate = models.FileField(upload_to='student_achievements/', null=True, blank=True)
    is_verified = models.BooleanField(default=False)
    verified_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='verified_achievements')
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    def __str__(self):
        return f"{self.title} - {self.student}"

    class Meta:
        ordering = ['-date_achieved']
        verbose_name = 'Student Achievement'
        verbose_name_plural = 'Student Achievements'

class StudentFee(models.Model):
    """Model representing fees associated with a student."""
    FEE_TYPE_CHOICES = (
        ('TUITION', 'Tuition Fee'),
        ('REGISTRATION', 'Registration Fee'),
        ('EXAM', 'Examination Fee'),
        ('LIBRARY', 'Library Fee'),
        ('LABORATORY', 'Laboratory Fee'),
        ('TRANSPORTATION', 'Transportation Fee'),
        ('UNIFORM', 'Uniform Fee'),
        ('BOOKS', 'Books Fee'),
        ('ACTIVITY', 'Activity Fee'),
        ('OTHER', 'Other Fee'),
    )

    PAYMENT_STATUS_CHOICES = (
        ('PENDING', 'Pending'),
        ('PARTIAL', 'Partially Paid'),
        ('PAID', 'Paid'),
        ('OVERDUE', 'Overdue'),
        ('WAIVED', 'Waived'),
    )

    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='fees')
    academic_year = models.ForeignKey('academics.AcademicYear', on_delete=models.CASCADE, related_name='student_fees')
    term = models.ForeignKey('courses.AcademicTerm', on_delete=models.CASCADE, related_name='student_fees', null=True, blank=True)
    fee_type = models.CharField(max_length=50, choices=FEE_TYPE_CHOICES)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    due_date = models.DateField()
    payment_status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='PENDING')
    amount_paid = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    balance = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    last_payment_date = models.DateField(null=True, blank=True)
    notes = models.TextField(blank=True, null=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='created_fees')
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    def save(self, *args, **kwargs):
        self.balance = self.amount - self.amount_paid
        if self.balance <= 0:
            self.payment_status = 'PAID'
        elif self.amount_paid > 0:
            self.payment_status = 'PARTIAL'
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.fee_type} - {self.student} ({self.academic_year})"

    class Meta:
        ordering = ['student', 'due_date']
        verbose_name = 'Student Fee'
        verbose_name_plural = 'Student Fees'

class FeePayment(models.Model):
    """Model representing payments made for student fees."""
    PAYMENT_METHOD_CHOICES = (
        ('CASH', 'Cash'),
        ('BANK_TRANSFER', 'Bank Transfer'),
        ('MOBILE_MONEY', 'Mobile Money'),
        ('CHECK', 'Check'),
        ('CREDIT_CARD', 'Credit Card'),
        ('OTHER', 'Other'),
    )

    student_fee = models.ForeignKey(StudentFee, on_delete=models.CASCADE, related_name='payments')
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    payment_date = models.DateField()
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES)
    transaction_id = models.CharField(max_length=100, blank=True, null=True)
    receipt_number = models.CharField(max_length=100, blank=True, null=True)
    received_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='received_payments')
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(default=timezone.now)

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        # Update the parent fee record
        fee = self.student_fee
        fee.amount_paid = sum(payment.amount for payment in fee.payments.all())
        fee.last_payment_date = self.payment_date
        fee.save()

    def __str__(self):
        return f"Payment of {self.amount} for {self.student_fee}"

    class Meta:
        ordering = ['-payment_date']
        verbose_name = 'Fee Payment'
        verbose_name_plural = 'Fee Payments'
