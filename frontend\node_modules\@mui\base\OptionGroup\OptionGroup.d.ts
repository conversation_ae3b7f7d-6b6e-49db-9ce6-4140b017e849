import { PolymorphicComponent } from '../utils/PolymorphicComponent';
import { OptionGroupTypeMap } from './OptionGroup.types';
/**
 * An unstyled option group to be used within a Select.
 *
 * Demos:
 *
 * - [Select](https://mui.com/base-ui/react-select/)
 *
 * API:
 *
 * - [OptionGroup API](https://mui.com/base-ui/react-select/components-api/#option-group)
 */
declare const OptionGroup: PolymorphicComponent<OptionGroupTypeMap<{}, "li">>;
export { OptionGroup };
