import React, { useState, useEffect } from 'react'
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardHeader,
  TextField,
  Button,
  Typography,
  Switch,
  FormControlLabel,
  CircularProgress,
  Paper,
  Alert,
  Chip,
} from '@mui/material'
import {
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Build as BuildIcon,
  Warning as WarningIcon,
  Schedule as ScheduleIcon,
  Security as SecurityIcon,
} from '@mui/icons-material'
import {
  getMaintenanceMode,
  updateMaintenanceMode,
  toggleMaintenanceMode,
  type MaintenanceMode,
} from '../../../services/siteSettingsService'

interface MaintenanceTabProps {
  onSaveSuccess: () => void
  onSaveError: (error: string) => void
}

const MaintenanceTab: React.FC<MaintenanceTabProps> = ({
  onSaveSuccess,
  onSaveError,
}) => {
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [toggling, setToggling] = useState(false)
  const [formData, setFormData] = useState({
    maintenanceMode: false,
    maintenanceTitle: '',
    maintenanceMessage: '',
    maintenanceEndTime: '',
    allowedIps: '',
    showCountdown: false,
  })

  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      setLoading(true)
      const settings = await getMaintenanceMode()
      setFormData({
        maintenanceMode: settings.maintenance_mode,
        maintenanceTitle: settings.maintenance_title,
        maintenanceMessage: settings.maintenance_message,
        maintenanceEndTime: settings.maintenance_end_time || '',
        allowedIps: settings.allowed_ips,
        showCountdown: settings.show_countdown,
      })
    } catch (error) {
      console.error('Error fetching maintenance settings:', error)
      onSaveError('Failed to load maintenance settings')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      const updateData: Partial<MaintenanceMode> = {
        maintenance_mode: formData.maintenanceMode,
        maintenance_title: formData.maintenanceTitle,
        maintenance_message: formData.maintenanceMessage,
        maintenance_end_time: formData.maintenanceEndTime || null,
        allowed_ips: formData.allowedIps,
        show_countdown: formData.showCountdown,
      }
      await updateMaintenanceMode(updateData)
      onSaveSuccess()
    } catch (error) {
      console.error('Error saving maintenance settings:', error)
      onSaveError('Failed to save maintenance settings')
    } finally {
      setSaving(false)
    }
  }

  const handleToggleMaintenance = async () => {
    try {
      setToggling(true)
      const result = await toggleMaintenanceMode()
      setFormData(prev => ({
        ...prev,
        maintenanceMode: result.data.maintenance_mode,
      }))
      onSaveSuccess()
    } catch (error) {
      console.error('Error toggling maintenance mode:', error)
      onSaveError('Failed to toggle maintenance mode')
    } finally {
      setToggling(false)
    }
  }

  const handleReset = () => {
    fetchSettings()
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <CircularProgress />
      </Box>
    )
  }

  return (
    <Box>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
          Maintenance Mode Settings
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Control site availability and maintenance messaging
        </Typography>
      </Box>

      {/* Status Alert */}
      <Alert 
        severity={formData.maintenanceMode ? 'warning' : 'success'} 
        sx={{ mb: 3 }}
        icon={formData.maintenanceMode ? <WarningIcon /> : <BuildIcon />}
        action={
          <Button
            color="inherit"
            size="small"
            onClick={handleToggleMaintenance}
            disabled={toggling}
            startIcon={toggling ? <CircularProgress size={16} /> : null}
          >
            {toggling ? 'Toggling...' : (formData.maintenanceMode ? 'Disable' : 'Enable')}
          </Button>
        }
      >
        <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
          Maintenance Mode is {formData.maintenanceMode ? 'ENABLED' : 'DISABLED'}
        </Typography>
        <Typography variant="body2">
          {formData.maintenanceMode 
            ? 'Your site is currently in maintenance mode. Only allowed IPs can access the site.'
            : 'Your site is accessible to all users.'
          }
        </Typography>
      </Alert>

      <Grid container spacing={3}>
        {/* Basic Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Basic Settings"
              subheader="Main maintenance mode configuration"
              avatar={<BuildIcon color="primary" />}
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.maintenanceMode}
                        onChange={handleInputChange('maintenanceMode')}
                      />
                    }
                    label="Enable Maintenance Mode"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Maintenance Title"
                    value={formData.maintenanceTitle}
                    onChange={handleInputChange('maintenanceTitle')}
                    placeholder="We'll be back soon!"
                    helperText="Title shown on the maintenance page"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Maintenance Message"
                    value={formData.maintenanceMessage}
                    onChange={handleInputChange('maintenanceMessage')}
                    multiline
                    rows={4}
                    placeholder="We're performing scheduled maintenance. Please check back later."
                    helperText="Message shown to users during maintenance"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Schedule Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Schedule Settings"
              subheader="Maintenance timing and countdown"
              avatar={<ScheduleIcon color="primary" />}
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Maintenance End Time"
                    type="datetime-local"
                    value={formData.maintenanceEndTime}
                    onChange={handleInputChange('maintenanceEndTime')}
                    helperText="When maintenance is expected to end (optional)"
                    InputLabelProps={{
                      shrink: true,
                    }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.showCountdown}
                        onChange={handleInputChange('showCountdown')}
                      />
                    }
                    label="Show Countdown Timer"
                  />
                  <Typography variant="caption" color="text.secondary" display="block">
                    Display a countdown timer if end time is set
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Access Control */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Access Control"
              subheader="IP addresses that can bypass maintenance"
              avatar={<SecurityIcon color="primary" />}
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Allowed IP Addresses"
                    value={formData.allowedIps}
                    onChange={handleInputChange('allowedIps')}
                    multiline
                    rows={3}
                    placeholder="***********&#10;********&#10;***********/24"
                    helperText="One IP address or CIDR block per line"
                  />
                </Grid>
                <Grid item xs={12}>
                  <Alert severity="info">
                    <Typography variant="body2">
                      <strong>Tip:</strong> Add your current IP address to avoid being locked out. 
                      You can use individual IPs (***********) or CIDR notation (***********/24).
                    </Typography>
                  </Alert>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Preview */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Preview" subheader="How the maintenance page will look" />
            <CardContent>
              <Paper
                sx={{
                  p: 4,
                  background: 'linear-gradient(45deg, #f59e0b 30%, #f97316 90%)',
                  color: 'white',
                  textAlign: 'center',
                  minHeight: 200,
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  borderRadius: 2,
                }}
              >
                <BuildIcon sx={{ fontSize: 48, mb: 2, mx: 'auto' }} />
                <Typography variant="h4" sx={{ mb: 2, fontWeight: 600 }}>
                  {formData.maintenanceTitle || 'We\'ll be back soon!'}
                </Typography>
                <Typography variant="body1" sx={{ mb: 3, opacity: 0.9 }}>
                  {formData.maintenanceMessage || 'We\'re performing scheduled maintenance. Please check back later.'}
                </Typography>
                {formData.showCountdown && formData.maintenanceEndTime && (
                  <Chip
                    label="Countdown Timer"
                    sx={{
                      bgcolor: 'rgba(255,255,255,0.2)',
                      color: 'white',
                      mx: 'auto',
                    }}
                  />
                )}
              </Paper>
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12}>
          <Card>
            <CardHeader title="Quick Actions" subheader="Common maintenance operations" />
            <CardContent>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Button
                  variant={formData.maintenanceMode ? 'contained' : 'outlined'}
                  color={formData.maintenanceMode ? 'warning' : 'success'}
                  startIcon={formData.maintenanceMode ? <WarningIcon /> : <BuildIcon />}
                  onClick={handleToggleMaintenance}
                  disabled={toggling}
                >
                  {formData.maintenanceMode ? 'Disable Maintenance' : 'Enable Maintenance'}
                </Button>
                <Button
                  variant="outlined"
                  onClick={() => {
                    const now = new Date()
                    now.setHours(now.getHours() + 2)
                    const endTime = now.toISOString().slice(0, 16)
                    setFormData(prev => ({
                      ...prev,
                      maintenanceEndTime: endTime,
                      showCountdown: true,
                    }))
                  }}
                >
                  Set 2 Hour Maintenance
                </Button>
                <Button
                  variant="outlined"
                  onClick={() => {
                    setFormData(prev => ({
                      ...prev,
                      maintenanceTitle: 'Scheduled Maintenance',
                      maintenanceMessage: 'We are performing scheduled maintenance to improve our services. We\'ll be back online shortly. Thank you for your patience.',
                    }))
                  }}
                >
                  Use Default Message
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Action Buttons */}
      <Box sx={{ mt: 4, display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={handleReset}
          disabled={saving}
        >
          Reset
        </Button>
        <Button
          variant="contained"
          startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
          onClick={handleSave}
          disabled={saving}
          sx={{
            background: 'linear-gradient(45deg, #6366f1 30%, #8b5cf6 90%)',
            boxShadow: '0 3px 5px 2px rgba(99, 102, 241, .3)',
          }}
        >
          {saving ? 'Saving...' : 'Save Changes'}
        </Button>
      </Box>
    </Box>
  )
}

export default MaintenanceTab
