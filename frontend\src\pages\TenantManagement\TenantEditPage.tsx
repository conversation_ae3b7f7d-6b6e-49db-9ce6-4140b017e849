import { useState, useEffect } from 'react'
import { usePara<PERSON>, useNavigate } from 'react-router-dom'
import { useToast } from '../../components/ToastProvider'
import {
  Box,
  Paper,
  Typography,
  Grid,
  Button,
  TextField,
  MenuItem,
  Stepper,
  Step,
  StepLabel,
  CircularProgress,
  Alert,
  LinearProgress,
  Fade,
  Card,
  CardContent,
} from '@mui/material'
import {
  Save as SaveIcon,
  Cancel as CancelIcon,
  School as SchoolIcon,
  ContactMail as ContactIcon,
  Business as BusinessIcon,
  AdminPanelSettings as AdminIcon,
  CheckCircle as CheckCircleIcon,
  Preview as ReviewIcon,
  Edit as EditIcon,
  NavigateBefore as BackIcon,
  NavigateNext as NextIcon,
} from '@mui/icons-material'
import { getTenant, updateTenant, Tenant } from '../../services/tenantService'
import TenantManagementLayout from '../../components/Layout/TenantManagementLayout'

const TenantEditPage = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const toast = useToast()
  const [tenant, setTenant] = useState<Tenant | null>(null)
  const [activeStep, setActiveStep] = useState(0)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Form data matching the create form structure
  const [formData, setFormData] = useState({
    name: '',
    address: '',
    city: '',
    state: '',
    country: 'Ethiopia',
    postal_code: '',
    contact_person: '',
    contact_email: '',
    contact_phone: '',
    website: '',
    established_year: new Date().getFullYear(),
    school_type: 'primary',
    student_capacity: 500,
    description: '',
    admin_first_name: '',
    admin_last_name: '',
    admin_email: '',
    admin_username: '',
    admin_password: '',
    admin_confirm_password: '',
  })

  const steps = ['School Information', 'Contact Details', 'School Details', 'Admin Account', 'Review & Submit']

  // Helper functions
  const getSchoolTypes = () => [
    { value: 'primary', label: 'Primary School' },
    { value: 'secondary', label: 'Secondary School' },
    { value: 'high_school', label: 'High School' },
    { value: 'university', label: 'University' },
    { value: 'college', label: 'College' },
    { value: 'vocational', label: 'Vocational School' },
    { value: 'other', label: 'Other' },
  ]

  const getCountries = () => [
    { value: 'Ethiopia', label: 'Ethiopia' },
    { value: 'Kenya', label: 'Kenya' },
    { value: 'Uganda', label: 'Uganda' },
    { value: 'Tanzania', label: 'Tanzania' },
    { value: 'Rwanda', label: 'Rwanda' },
    { value: 'Other', label: 'Other' },
  ]

  useEffect(() => {
    if (id) {
      fetchTenant()
    }
  }, [id])

  const fetchTenant = async () => {
    try {
      setLoading(true)
      setError(null)
      const data = await getTenant(parseInt(id!))
      setTenant(data)



      // Create the complete form data object at once to ensure proper state update
      // Note: Some fields may not exist in the current tenant model, so we provide reasonable defaults
      const completeFormData = {
        name: data.name || '',
        address: data.address || '',
        city: data.city || '', // Leave empty if not available - user can fill
        state: data.state || '', // Leave empty if not available - user can fill
        country: data.country || 'Ethiopia',
        postal_code: data.postal_code || '', // Leave empty if not available - user can fill
        contact_person: data.contact_person || 'School Administrator',
        contact_email: data.contact_email || '',
        contact_phone: data.contact_phone || '',
        website: data.website || '', // Leave empty if not available - user can fill
        established_year: data.established_year || new Date().getFullYear(),
        school_type: data.school_type || 'primary',
        student_capacity: data.student_capacity || 500,
        description: data.description || '', // Leave empty if not available - user can fill
        admin_first_name: data.admin_first_name || 'Admin',
        admin_last_name: data.admin_last_name || 'User',
        admin_email: data.admin_email || data.contact_email || '',
        admin_username: data.admin_username || (data.name ? data.name.toLowerCase().replace(/\s+/g, '_') + '_admin' : 'admin'),
        admin_password: '',
        admin_confirm_password: '',
      }

      // If we have additional data from status or other sources, merge those too
      if (data.status_info) {
        completeFormData.admin_email = data.status_info.admin_email || completeFormData.admin_email
      }

      // Set the complete form data at once
      setFormData(completeFormData)

      console.log('Form data set to:', completeFormData) // Debug log
    } catch (err: any) {
      console.error('Error fetching tenant:', err)
      setError(err.response?.data?.message || 'Failed to fetch tenant details')
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: name === 'established_year' || name === 'student_capacity' ? Number(value) : value
    }))
  }

  const handleNext = () => {
    if (activeStep < steps.length - 1) {
      setActiveStep(activeStep + 1)
    }
  }

  const handleBack = () => {
    if (activeStep > 0) {
      setActiveStep(activeStep - 1)
    }
  }

  const handleSubmit = async () => {
    try {
      setSaving(true)
      setError(null)

      // Map form data to tenant update format
      const updateData = {
        name: formData.name,
        address: formData.address,
        contact_email: formData.contact_email,
        contact_phone: formData.contact_phone,
        // Add other fields as needed
      }

      await updateTenant(parseInt(id!), updateData)

      toast.success('Tenant updated successfully!')

      // Redirect back to tenant details
      navigate(`/dashboard/tenant-management/tenants/${id}`)

    } catch (err: any) {
      console.error('Error updating tenant:', err)
      const errorMessage = err.response?.data?.message || 'Failed to update tenant'
      setError(errorMessage)
      toast.error(errorMessage)
    } finally {
      setSaving(false)
    }
  }

  const isStepValid = () => {
    switch (activeStep) {
      case 0:
        return formData.name // Only require name for school information
      case 1:
        return formData.contact_email // Only require email for contact details
      case 2:
        return formData.established_year && formData.school_type && formData.student_capacity
      case 3:
        return formData.admin_first_name && formData.admin_last_name &&
               formData.admin_email && formData.admin_username
      case 4:
        return true // Review step is always valid if we reached it
      default:
        return false
    }
  }

  const getStepContent = (step: number) => {
    switch (step) {
      case 0: // School Information
        return (
          <Box>
            <Typography variant="h6" gutterBottom sx={{ color: 'primary.main', fontWeight: 600, mb: 3 }}>
              School Information
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  required
                  fullWidth
                  name="name"
                  label="School Name"
                  value={formData.name}
                  onChange={handleChange}
                  disabled={saving}
                  variant="outlined"
                  sx={{ mb: 1 }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  required
                  fullWidth
                  name="address"
                  label="Address"
                  multiline
                  rows={3}
                  value={formData.address}
                  onChange={handleChange}
                  disabled={saving}
                  variant="outlined"
                  sx={{ mb: 1 }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  name="city"
                  label="City"
                  value={formData.city}
                  onChange={handleChange}
                  disabled={saving}
                  variant="outlined"
                  sx={{ mb: 1 }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  name="state"
                  label="State/Region"
                  value={formData.state}
                  onChange={handleChange}
                  disabled={saving}
                  variant="outlined"
                  sx={{ mb: 1 }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  select
                  fullWidth
                  name="country"
                  label="Country"
                  value={formData.country}
                  onChange={handleChange}
                  disabled={saving}
                  variant="outlined"
                  sx={{ mb: 1 }}
                >
                  {getCountries().map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  name="postal_code"
                  label="Postal Code"
                  value={formData.postal_code}
                  onChange={handleChange}
                  disabled={saving}
                  variant="outlined"
                  sx={{ mb: 1 }}
                />
              </Grid>
            </Grid>
          </Box>
        )
      case 1: // Contact Details
        return (
          <Box>
            <Typography variant="h6" gutterBottom sx={{ color: 'primary.main', fontWeight: 600, mb: 3 }}>
              Contact Details
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  name="contact_person"
                  label="Contact Person (Optional)"
                  value={formData.contact_person}
                  onChange={handleChange}
                  disabled={saving}
                  variant="outlined"
                  sx={{ mb: 1 }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  name="contact_email"
                  label="Contact Email"
                  type="email"
                  value={formData.contact_email}
                  onChange={handleChange}
                  disabled={saving}
                  variant="outlined"
                  sx={{ mb: 1 }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  name="contact_phone"
                  label="Contact Phone"
                  value={formData.contact_phone}
                  onChange={handleChange}
                  disabled={saving}
                  variant="outlined"
                  sx={{ mb: 1 }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  name="website"
                  label="Website (Optional)"
                  value={formData.website}
                  onChange={handleChange}
                  disabled={saving}
                  variant="outlined"
                  sx={{ mb: 1 }}
                />
              </Grid>
            </Grid>
          </Box>
        )
      case 2: // School Details
        return (
          <Box>
            <Typography variant="h6" gutterBottom sx={{ color: 'primary.main', fontWeight: 600, mb: 3 }}>
              School Details
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  name="established_year"
                  label="Established Year"
                  type="number"
                  value={formData.established_year}
                  onChange={handleChange}
                  disabled={saving}
                  variant="outlined"
                  sx={{ mb: 1 }}
                  inputProps={{ min: 1800, max: new Date().getFullYear() }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  select
                  required
                  fullWidth
                  name="school_type"
                  label="School Type"
                  value={formData.school_type}
                  onChange={handleChange}
                  disabled={saving}
                  variant="outlined"
                  sx={{ mb: 1 }}
                >
                  {getSchoolTypes().map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  name="student_capacity"
                  label="Student Capacity"
                  type="number"
                  value={formData.student_capacity}
                  onChange={handleChange}
                  disabled={saving}
                  variant="outlined"
                  sx={{ mb: 1 }}
                  inputProps={{ min: 1 }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  name="description"
                  label="Description (Optional)"
                  multiline
                  rows={4}
                  value={formData.description}
                  onChange={handleChange}
                  disabled={saving}
                  variant="outlined"
                  sx={{ mb: 1 }}
                />
              </Grid>
            </Grid>
          </Box>
        )
      case 3: // Admin Account
        return (
          <Box>
            <Typography variant="h6" gutterBottom sx={{ color: 'primary.main', fontWeight: 600, mb: 3 }}>
              Admin Account
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  name="admin_first_name"
                  label="Admin First Name"
                  value={formData.admin_first_name}
                  onChange={handleChange}
                  disabled={saving}
                  variant="outlined"
                  sx={{ mb: 1 }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  name="admin_last_name"
                  label="Admin Last Name"
                  value={formData.admin_last_name}
                  onChange={handleChange}
                  disabled={saving}
                  variant="outlined"
                  sx={{ mb: 1 }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  name="admin_email"
                  label="Admin Email"
                  type="email"
                  value={formData.admin_email}
                  onChange={handleChange}
                  disabled={saving}
                  variant="outlined"
                  sx={{ mb: 1 }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  name="admin_username"
                  label="Admin Username"
                  value={formData.admin_username}
                  onChange={handleChange}
                  disabled={saving}
                  variant="outlined"
                  sx={{ mb: 1 }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  name="admin_password"
                  label="New Password (Optional)"
                  type="password"
                  value={formData.admin_password}
                  onChange={handleChange}
                  disabled={saving}
                  variant="outlined"
                  sx={{ mb: 1 }}
                  helperText="Leave blank to keep current password"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  name="admin_confirm_password"
                  label="Confirm New Password"
                  type="password"
                  value={formData.admin_confirm_password}
                  onChange={handleChange}
                  disabled={saving}
                  variant="outlined"
                  sx={{ mb: 1 }}
                  error={formData.admin_password !== formData.admin_confirm_password && formData.admin_confirm_password !== ''}
                  helperText={
                    formData.admin_password !== formData.admin_confirm_password && formData.admin_confirm_password !== ''
                      ? 'Passwords do not match'
                      : ''
                  }
                />
              </Grid>
            </Grid>
          </Box>
        )
      case 4: // Review & Submit
        return (
          <Box>
            <Typography variant="h6" gutterBottom sx={{ color: 'primary.main', fontWeight: 600, mb: 3 }}>
              Review & Submit
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card variant="outlined" sx={{ mb: 2 }}>
                  <CardContent>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center' }}>
                      <SchoolIcon sx={{ mr: 1, color: 'primary.main' }} />
                      School Information
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>Name:</strong> {formData.name}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>Address:</strong> {formData.address}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>City:</strong> {formData.city || 'Not specified'}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>State:</strong> {formData.state || 'Not specified'}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>Country:</strong> {formData.country}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={6}>
                <Card variant="outlined" sx={{ mb: 2 }}>
                  <CardContent>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center' }}>
                      <ContactIcon sx={{ mr: 1, color: 'primary.main' }} />
                      Contact Details
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>Contact Person:</strong> {formData.contact_person}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>Email:</strong> {formData.contact_email}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>Phone:</strong> {formData.contact_phone}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>Website:</strong> {formData.website || 'Not specified'}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center' }}>
                      <BusinessIcon sx={{ mr: 1, color: 'primary.main' }} />
                      School Details
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={4}>
                        <Typography variant="body2">
                          <strong>Established:</strong> {formData.established_year}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <Typography variant="body2">
                          <strong>Type:</strong> {getSchoolTypes().find(t => t.value === formData.school_type)?.label}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <Typography variant="body2">
                          <strong>Capacity:</strong> {formData.student_capacity} students
                        </Typography>
                      </Grid>
                      {formData.description && (
                        <Grid item xs={12}>
                          <Typography variant="body2">
                            <strong>Description:</strong> {formData.description}
                          </Typography>
                        </Grid>
                      )}
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, display: 'flex', alignItems: 'center' }}>
                      <AdminIcon sx={{ mr: 1, color: 'primary.main' }} />
                      Admin Account
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="body2">
                          <strong>Name:</strong> {formData.admin_first_name} {formData.admin_last_name}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="body2">
                          <strong>Email:</strong> {formData.admin_email}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="body2">
                          <strong>Username:</strong> {formData.admin_username}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="body2">
                          <strong>Password:</strong> {formData.admin_password ? 'Will be updated' : 'No change'}
                        </Typography>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        )
      default:
        return 'Unknown step'
    }
  }

  if (loading) {
    return (
      <TenantManagementLayout
        title="Edit Tenant"
        subtitle="Loading tenant details..."
      >
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ ml: 2 }}>Loading tenant details...</Typography>
        </Box>
      </TenantManagementLayout>
    )
  }

  if (!tenant) {
    return (
      <TenantManagementLayout
        title="Edit Tenant"
        subtitle="Tenant not found"
      >
        <Alert severity="error" sx={{ mt: 2 }}>
          Tenant not found
        </Alert>
      </TenantManagementLayout>
    )
  }

  return (
    <TenantManagementLayout
      title="Edit Tenant"
      subtitle={`Update information for ${tenant.name}`}
    >
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {saving && (
        <Fade in={saving}>
          <LinearProgress sx={{ mb: 3 }} />
        </Fade>
      )}

      {/* Stepper */}
      <Paper sx={{ p: 3, mb: 3, borderRadius: 3, boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)' }}>
        <Stepper activeStep={activeStep} alternativeLabel sx={{ mb: 4 }}>
          {steps.map((label, index) => (
            <Step key={label}>
              <StepLabel
                StepIconProps={{
                  style: {
                    color: index <= activeStep ? '#1976d2' : '#ccc',
                    fontSize: '1.5rem',
                  },
                }}
              >
                <Typography
                  variant="body2"
                  sx={{
                    fontWeight: index === activeStep ? 600 : 400,
                    color: index <= activeStep ? 'primary.main' : 'text.secondary',
                  }}
                >
                  {label}
                </Typography>
              </StepLabel>
            </Step>
          ))}
        </Stepper>

        {/* Step Content */}
        <Box sx={{ minHeight: 400 }}>
          {getStepContent(activeStep)}
        </Box>

        {/* Navigation Buttons */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
          <Button
            disabled={activeStep === 0 || saving}
            onClick={handleBack}
            startIcon={<BackIcon />}
            variant="outlined"
          >
            Back
          </Button>

          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              onClick={() => navigate(`/dashboard/tenant-management/tenants/${id}`)}
              disabled={saving}
            >
              Cancel
            </Button>

            {activeStep === steps.length - 1 ? (
              <Button
                variant="contained"
                onClick={handleSubmit}
                disabled={saving || !isStepValid()}
                startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
                sx={{
                  minWidth: 140,
                  background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                  boxShadow: '0 3px 5px 2px rgba(33, 203, 243, .3)',
                }}
              >
                {saving ? 'Updating...' : 'Update Tenant'}
              </Button>
            ) : (
              <Button
                variant="contained"
                onClick={handleNext}
                disabled={!isStepValid() || saving}
                endIcon={<NextIcon />}
              >
                Next
              </Button>
            )}
          </Box>
        </Box>
      </Paper>
    </TenantManagementLayout>
  )
}

export default TenantEditPage
