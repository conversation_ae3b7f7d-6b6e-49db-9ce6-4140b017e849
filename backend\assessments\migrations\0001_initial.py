# Generated by Django 5.1.7 on 2025-04-06 22:37

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('academics', '0001_initial'),
        ('courses', '0001_initial'),
        ('students', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AssessmentType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=20, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('weight', models.DecimalField(decimal_places=2, default=0.0, help_text='Weight in percentage (0-100)', max_digits=5, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
            ],
            options={
                'verbose_name': 'Assessment Type',
                'verbose_name_plural': 'Assessment Types',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='GradingSystem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=20, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
            ],
            options={
                'verbose_name': 'Grading System',
                'verbose_name_plural': 'Grading Systems',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Assessment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('instructions', models.TextField(blank=True, null=True)),
                ('total_marks', models.DecimalField(decimal_places=2, default=100.0, max_digits=7)),
                ('passing_marks', models.DecimalField(decimal_places=2, default=40.0, max_digits=7)),
                ('weight_percentage', models.DecimalField(decimal_places=2, default=0.0, max_digits=5, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('assessment_date', models.DateField()),
                ('start_time', models.TimeField(blank=True, null=True)),
                ('end_time', models.TimeField(blank=True, null=True)),
                ('duration_minutes', models.PositiveIntegerField(default=60)),
                ('location', models.CharField(blank=True, max_length=100, null=True)),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('SCHEDULED', 'Scheduled'), ('IN_PROGRESS', 'In Progress'), ('COMPLETED', 'Completed'), ('GRADED', 'Graded'), ('CANCELLED', 'Cancelled')], default='DRAFT', max_length=20)),
                ('is_published', models.BooleanField(default=False)),
                ('attachment', models.FileField(blank=True, null=True, upload_to='assessments/')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('course_offering', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assessments', to='courses.courseoffering')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_assessments', to=settings.AUTH_USER_MODEL)),
                ('assessment_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assessments', to='assessments.assessmenttype')),
            ],
            options={
                'verbose_name': 'Assessment',
                'verbose_name_plural': 'Assessments',
                'ordering': ['assessment_date', 'start_time'],
            },
        ),
        migrations.CreateModel(
            name='Feedback',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('feedback_type', models.CharField(choices=[('ACADEMIC', 'Academic'), ('BEHAVIORAL', 'Behavioral'), ('GENERAL', 'General')], max_length=20)),
                ('title', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('is_private', models.BooleanField(default=False)),
                ('parent_notified', models.BooleanField(default=False)),
                ('parent_notification_date', models.DateTimeField(blank=True, null=True)),
                ('course_offering', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='feedback', to='courses.courseoffering')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='given_feedback', to=settings.AUTH_USER_MODEL)),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='feedback', to='students.student')),
            ],
            options={
                'verbose_name': 'Feedback',
                'verbose_name_plural': 'Feedback',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ReportCard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('PUBLISHED', 'Published'), ('ARCHIVED', 'Archived')], default='DRAFT', max_length=10)),
                ('gpa', models.DecimalField(blank=True, decimal_places=2, max_digits=3, null=True)),
                ('total_marks', models.DecimalField(blank=True, decimal_places=2, max_digits=7, null=True)),
                ('average_percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('rank', models.PositiveIntegerField(blank=True, null=True)),
                ('attendance_percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('teacher_remarks', models.TextField(blank=True, null=True)),
                ('principal_remarks', models.TextField(blank=True, null=True)),
                ('parent_signature', models.BooleanField(default=False)),
                ('parent_signature_date', models.DateField(blank=True, null=True)),
                ('generated_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('published_at', models.DateTimeField(blank=True, null=True)),
                ('pdf_file', models.FileField(blank=True, null=True, upload_to='report_cards/')),
                ('academic_year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='report_cards', to='academics.academicyear')),
                ('generated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='generated_report_cards', to=settings.AUTH_USER_MODEL)),
                ('grade_level', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='report_cards', to='academics.gradelevel')),
                ('grading_system', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='report_cards', to='assessments.gradingsystem')),
                ('section', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='report_cards', to='academics.section')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='report_cards', to='students.student')),
                ('term', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='report_cards', to='courses.academicterm')),
            ],
            options={
                'verbose_name': 'Report Card',
                'verbose_name_plural': 'Report Cards',
                'ordering': ['-academic_year__start_date', '-term__start_date'],
                'unique_together': {('student', 'academic_year', 'term')},
            },
        ),
        migrations.CreateModel(
            name='AssessmentResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('marks_obtained', models.DecimalField(decimal_places=2, max_digits=7, validators=[django.core.validators.MinValueValidator(0.0)])),
                ('percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('grade', models.CharField(blank=True, max_length=2, null=True)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('is_absent', models.BooleanField(default=False)),
                ('is_exempted', models.BooleanField(default=False)),
                ('exemption_reason', models.TextField(blank=True, null=True)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('GRADED', 'Graded'), ('REVIEWED', 'Reviewed'), ('DISPUTED', 'Disputed'), ('FINALIZED', 'Finalized')], default='PENDING', max_length=10)),
                ('graded_at', models.DateTimeField(auto_now=True)),
                ('assessment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='results', to='assessments.assessment')),
                ('graded_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='graded_assessments', to=settings.AUTH_USER_MODEL)),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assessment_results', to='students.student')),
            ],
            options={
                'verbose_name': 'Assessment Result',
                'verbose_name_plural': 'Assessment Results',
                'ordering': ['assessment', 'student'],
                'unique_together': {('assessment', 'student')},
            },
        ),
        migrations.CreateModel(
            name='Attendance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('status', models.CharField(choices=[('PRESENT', 'Present'), ('ABSENT', 'Absent'), ('LATE', 'Late'), ('EXCUSED', 'Excused Absence')], default='PRESENT', max_length=10)),
                ('remarks', models.CharField(blank=True, max_length=255, null=True)),
                ('late_minutes', models.PositiveIntegerField(default=0, help_text="Minutes late (if status is 'Late')")),
                ('recorded_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('course_offering', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance_records', to='courses.courseoffering')),
                ('recorded_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='recorded_attendance', to=settings.AUTH_USER_MODEL)),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance_records', to='students.student')),
            ],
            options={
                'verbose_name': 'Attendance',
                'verbose_name_plural': 'Attendance Records',
                'ordering': ['-date'],
                'unique_together': {('student', 'course_offering', 'date')},
            },
        ),
        migrations.CreateModel(
            name='GradeScale',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('grade', models.CharField(max_length=10)),
                ('min_marks', models.DecimalField(decimal_places=2, max_digits=5, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('max_marks', models.DecimalField(decimal_places=2, max_digits=5, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('grade_point', models.DecimalField(decimal_places=1, default=0.0, max_digits=3)),
                ('description', models.CharField(blank=True, max_length=100, null=True)),
                ('grading_system', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='grade_scales', to='assessments.gradingsystem')),
            ],
            options={
                'verbose_name': 'Grade Scale',
                'verbose_name_plural': 'Grade Scales',
                'ordering': ['-min_marks'],
                'unique_together': {('grading_system', 'grade')},
            },
        ),
        migrations.CreateModel(
            name='ReportCardDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('marks_obtained', models.DecimalField(blank=True, decimal_places=2, max_digits=7, null=True)),
                ('total_marks', models.DecimalField(decimal_places=2, default=100.0, max_digits=7)),
                ('percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('grade', models.CharField(blank=True, max_length=10, null=True)),
                ('grade_point', models.DecimalField(blank=True, decimal_places=1, max_digits=3, null=True)),
                ('teacher_remarks', models.CharField(blank=True, max_length=255, null=True)),
                ('attendance_percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='report_card_details', to='courses.course')),
                ('course_offering', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='report_card_details', to='courses.courseoffering')),
                ('report_card', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='details', to='assessments.reportcard')),
            ],
            options={
                'verbose_name': 'Report Card Detail',
                'verbose_name_plural': 'Report Card Details',
                'ordering': ['course__name'],
                'unique_together': {('report_card', 'course')},
            },
        ),
    ]
