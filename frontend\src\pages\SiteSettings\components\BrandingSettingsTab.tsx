import React, { useState } from 'react'
import {
  <PERSON>,
  <PERSON>rid,
  <PERSON>,
  <PERSON><PERSON>ontent,
  CardHeader,
  TextField,
  Button,
  <PERSON>pography,
  Switch,
  FormControlLabel,
  Slider,
  CircularProgress,
  Paper,
} from '@mui/material'
import {
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Palette as PaletteIcon,
} from '@mui/icons-material'

interface BrandingSettingsTabProps {
  onSaveSuccess: () => void
  onSaveError: (error: string) => void
}

const BrandingSettingsTab: React.FC<BrandingSettingsTabProps> = ({
  onSaveSuccess,
  onSaveError,
}) => {
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [formData, setFormData] = useState({
    primaryColor: '#2E7D32',
    primaryLight: '#4CAF50',
    primaryDark: '#1B5E20',
    secondaryColor: '#FFC107',
    secondaryLight: '#FFEB3B',
    secondaryDark: '#FF8F00',
    backgroundDefault: '#FAFAFA',
    backgroundPaper: '#FFFFFF',
    textPrimary: '#212121',
    textSecondary: '#757575',
    successColor: '#4CAF50',
    warningColor: '#FF9800',
    errorColor: '#F44336',
    infoColor: '#2196F3',
    fontFamilyPrimary: 'Roboto',
    fontFamilySecondary: 'Arial',
    borderRadiusSmall: 4,
    borderRadiusMedium: 8,
    borderRadiusLarge: 12,
    enableShadows: true,
    shadowIntensity: 2,
    customCss: '',
  })

  const handleInputChange = (field: string) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSliderChange = (field: string) => (
    event: Event,
    newValue: number | number[]
  ) => {
    setFormData(prev => ({ ...prev, [field]: newValue }))
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      onSaveSuccess()
    } catch (error) {
      onSaveError(error instanceof Error ? error.message : 'Unknown error')
    } finally {
      setSaving(false)
    }
  }

  const handleRefresh = async () => {
    setLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
    } catch (error) {
      onSaveError('Failed to refresh settings')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto' }}>
      <Grid container spacing={3}>
        {/* Color Palette */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Color Palette"
              subheader="Customize your brand colors"
              avatar={<PaletteIcon />}
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Primary Color"
                    type="color"
                    value={formData.primaryColor}
                    onChange={handleInputChange('primaryColor')}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Secondary Color"
                    type="color"
                    value={formData.secondaryColor}
                    onChange={handleInputChange('secondaryColor')}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Success Color"
                    type="color"
                    value={formData.successColor}
                    onChange={handleInputChange('successColor')}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Warning Color"
                    type="color"
                    value={formData.warningColor}
                    onChange={handleInputChange('warningColor')}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Error Color"
                    type="color"
                    value={formData.errorColor}
                    onChange={handleInputChange('errorColor')}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Info Color"
                    type="color"
                    value={formData.infoColor}
                    onChange={handleInputChange('infoColor')}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Typography */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Typography"
              subheader="Font settings and text styling"
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Primary Font Family"
                    value={formData.fontFamilyPrimary}
                    onChange={handleInputChange('fontFamilyPrimary')}
                    helperText="Main font for headings and important text"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Secondary Font Family"
                    value={formData.fontFamilySecondary}
                    onChange={handleInputChange('fontFamilySecondary')}
                    helperText="Font for body text and descriptions"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Primary Text Color"
                    type="color"
                    value={formData.textPrimary}
                    onChange={handleInputChange('textPrimary')}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Secondary Text Color"
                    type="color"
                    value={formData.textSecondary}
                    onChange={handleInputChange('textSecondary')}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Design Elements */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Design Elements"
              subheader="Border radius and shadow settings"
            />
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Typography gutterBottom>Small Border Radius</Typography>
                  <Slider
                    value={formData.borderRadiusSmall}
                    onChange={handleSliderChange('borderRadiusSmall')}
                    min={0}
                    max={20}
                    valueLabelDisplay="auto"
                    marks={[
                      { value: 0, label: '0px' },
                      { value: 10, label: '10px' },
                      { value: 20, label: '20px' },
                    ]}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Typography gutterBottom>Medium Border Radius</Typography>
                  <Slider
                    value={formData.borderRadiusMedium}
                    onChange={handleSliderChange('borderRadiusMedium')}
                    min={0}
                    max={30}
                    valueLabelDisplay="auto"
                    marks={[
                      { value: 0, label: '0px' },
                      { value: 15, label: '15px' },
                      { value: 30, label: '30px' },
                    ]}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Typography gutterBottom>Large Border Radius</Typography>
                  <Slider
                    value={formData.borderRadiusLarge}
                    onChange={handleSliderChange('borderRadiusLarge')}
                    min={0}
                    max={50}
                    valueLabelDisplay="auto"
                    marks={[
                      { value: 0, label: '0px' },
                      { value: 25, label: '25px' },
                      { value: 50, label: '50px' },
                    ]}
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.enableShadows}
                        onChange={handleInputChange('enableShadows')}
                      />
                    }
                    label="Enable Shadows"
                  />
                </Grid>
                {formData.enableShadows && (
                  <Grid item xs={12}>
                    <Typography gutterBottom>Shadow Intensity</Typography>
                    <Slider
                      value={formData.shadowIntensity}
                      onChange={handleSliderChange('shadowIntensity')}
                      min={1}
                      max={5}
                      valueLabelDisplay="auto"
                      marks={[
                        { value: 1, label: 'Light' },
                        { value: 3, label: 'Medium' },
                        { value: 5, label: 'Strong' },
                      ]}
                    />
                  </Grid>
                )}
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Color Preview */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Color Preview"
              subheader="See how your colors look"
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Paper
                    sx={{
                      p: 2,
                      bgcolor: formData.primaryColor,
                      color: 'white',
                      textAlign: 'center',
                      borderRadius: `${formData.borderRadiusMedium}px`,
                    }}
                  >
                    <Typography variant="body2">Primary</Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper
                    sx={{
                      p: 2,
                      bgcolor: formData.secondaryColor,
                      color: 'white',
                      textAlign: 'center',
                      borderRadius: `${formData.borderRadiusMedium}px`,
                    }}
                  >
                    <Typography variant="body2">Secondary</Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper
                    sx={{
                      p: 2,
                      bgcolor: formData.successColor,
                      color: 'white',
                      textAlign: 'center',
                      borderRadius: `${formData.borderRadiusMedium}px`,
                    }}
                  >
                    <Typography variant="body2">Success</Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper
                    sx={{
                      p: 2,
                      bgcolor: formData.errorColor,
                      color: 'white',
                      textAlign: 'center',
                      borderRadius: `${formData.borderRadiusMedium}px`,
                    }}
                  >
                    <Typography variant="body2">Error</Typography>
                  </Paper>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Custom CSS */}
        <Grid item xs={12}>
          <Card>
            <CardHeader
              title="Custom CSS"
              subheader="Add your own custom styles"
            />
            <CardContent>
              <TextField
                fullWidth
                label="Custom CSS"
                multiline
                rows={6}
                value={formData.customCss}
                onChange={handleInputChange('customCss')}
                placeholder="/* Add your custom CSS here */"
                helperText="Advanced users can add custom CSS to override default styles"
              />
            </CardContent>
          </Card>
        </Grid>

        {/* Action Buttons */}
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
            <Button
              variant="outlined"
              startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}
              onClick={handleRefresh}
              disabled={loading || saving}
            >
              Refresh
            </Button>
            <Button
              variant="contained"
              startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
              onClick={handleSave}
              disabled={loading || saving}
            >
              Save Changes
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  )
}

export default BrandingSettingsTab
