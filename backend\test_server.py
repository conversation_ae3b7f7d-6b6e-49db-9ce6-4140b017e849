import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kelem_sms.settings')
django.setup()

# Print some debug information
print("Django version:", django.get_version())
print("Settings module:", os.environ.get('DJANGO_SETTINGS_MODULE'))
print("BASE_DIR:", django.conf.settings.BASE_DIR)
print("INSTALLED_APPS:", django.conf.settings.INSTALLED_APPS)

# Try to connect to the database
from django.db import connections
try:
    connection = connections['default']
    connection.ensure_connection()
    print("Database connection successful!")
except Exception as e:
    print("Database connection error:", e)

# Try to import models
try:
    from tenants.models import School, Domain
    print("Tenant models imported successfully!")
except Exception as e:
    print("Error importing tenant models:", e)

print("Test completed!")
