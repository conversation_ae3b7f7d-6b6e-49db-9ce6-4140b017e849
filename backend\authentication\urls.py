from django.urls import path
from . import views

app_name = 'authentication'

urlpatterns = [
    # API endpoints
    path('api/login/', views.api_login, name='api_login'),
    path('api/logout/', views.api_logout, name='api_logout'),
    path('api/user/', views.api_user, name='api_user'),
    path('api/user-tenants/', views.api_user_tenants, name='api_user_tenants'),
    path('api/set-tenant/', views.api_set_tenant, name='api_set_tenant'),
    path('api/password-reset/', views.api_password_reset_request, name='api_password_reset_request'),
    path('api/password-reset/confirm/', views.api_password_reset_confirm, name='api_password_reset_confirm'),
    path('api/debug/check-user-schemas/', views.api_debug_check_user_schemas, name='api_debug_check_user_schemas'),

    # User profile views
    path('profile/', views.user_profile, name='profile'),
    path('profile/edit/', views.profile_edit, name='profile_edit'),
    path('password/change/', views.password_change, name='password_change'),

    # Dashboard views
    path('admin-dashboard/', views.admin_dashboard, name='admin_dashboard'),
    path('teacher-dashboard/', views.teacher_dashboard, name='teacher_dashboard'),
    path('student-dashboard/', views.student_dashboard, name='student_dashboard'),
    path('parent-dashboard/', views.parent_dashboard, name='parent_dashboard'),
    path('staff-dashboard/', views.staff_dashboard, name='staff_dashboard'),
]
