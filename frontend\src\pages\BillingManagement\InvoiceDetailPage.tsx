import { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom'
import { useToast } from '../../components/ToastProvider'
import {
  Box,
  Paper,
  Typography,
  Button,
  Chip,
  Grid,
  Card,
  CardContent,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress,
  Alert,
} from '@mui/material'
import {
  Download as DownloadIcon,
  Send as SendIcon,
  Edit as EditIcon,
  Print as PrintIcon,
  Receipt as ReceiptIcon,
} from '@mui/icons-material'
import BillingManagementLayout from '../../components/Layout/BillingManagementLayout'

const InvoiceDetailPage = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const toast = useToast()
  const [loading, setLoading] = useState(true)
  const [invoice, setInvoice] = useState<any>(null)

  useEffect(() => {
    fetchInvoice()
  }, [id])

  const fetchInvoice = async () => {
    try {
      setLoading(true)
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock invoice data
      setInvoice({
        id: id,
        invoiceNumber: 'INV-2024-001',
        tenantName: 'Sunrise Academy',
        amount: 299,
        status: 'paid',
        dueDate: '2024-02-15',
        issueDate: '2024-01-15',
        plan: 'Premium',
        description: 'Monthly subscription - Premium Plan',
        items: [
          { description: 'Premium Plan Subscription', quantity: 1, rate: 299, amount: 299 }
        ],
        subtotal: 299,
        tax: 0,
        total: 299,
      })
    } catch (error) {
      console.error('Error fetching invoice:', error)
      toast.error('Failed to load invoice')
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  if (loading) {
    return (
      <BillingManagementLayout
        title="Invoice Details"
        subtitle="Loading invoice..."
        breadcrumbs={[
          { label: 'Invoices', href: '/dashboard/billing-management/invoices' },
          { label: 'Invoice Details' }
        ]}
      >
        <LinearProgress />
      </BillingManagementLayout>
    )
  }

  if (!invoice) {
    return (
      <BillingManagementLayout
        title="Invoice Details"
        breadcrumbs={[
          { label: 'Invoices', href: '/dashboard/billing-management/invoices' },
          { label: 'Invoice Details' }
        ]}
      >
        <Alert severity="error">Invoice not found</Alert>
      </BillingManagementLayout>
    )
  }

  return (
    <BillingManagementLayout
      title={`Invoice ${invoice.invoiceNumber}`}
      subtitle={`${invoice.tenantName} • ${formatCurrency(invoice.amount)}`}
      breadcrumbs={[
        { label: 'Invoices', href: '/dashboard/billing-management/invoices' },
        { label: 'Invoice Details' }
      ]}
    >
      {/* Action Buttons */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
        <Button
          variant="contained"
          startIcon={<DownloadIcon />}
          sx={{
            background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
            boxShadow: '0 3px 5px 2px rgba(33, 203, 243, .3)',
          }}
        >
          Download PDF
        </Button>
        <Button
          variant="outlined"
          startIcon={<SendIcon />}
        >
          Send Invoice
        </Button>
        <Button
          variant="outlined"
          startIcon={<EditIcon />}
          onClick={() => navigate(`/dashboard/billing-management/invoices/${id}/edit`)}
        >
          Edit Invoice
        </Button>
        <Button
          variant="outlined"
          startIcon={<PrintIcon />}
        >
          Print
        </Button>
      </Box>

      {/* Invoice Content */}
      <Paper sx={{ p: 4, mb: 3 }}>
        {/* Invoice Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 4 }}>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
              INVOICE
            </Typography>
            <Typography variant="h6" color="text.secondary">
              {invoice.invoiceNumber}
            </Typography>
          </Box>
          <Box sx={{ textAlign: 'right' }}>
            <Chip
              label={invoice.status}
              color={invoice.status === 'paid' ? 'success' : 'warning'}
              sx={{ textTransform: 'capitalize', mb: 2 }}
            />
            <Typography variant="body2" color="text.secondary">
              Issue Date: {new Date(invoice.issueDate).toLocaleDateString()}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Due Date: {new Date(invoice.dueDate).toLocaleDateString()}
            </Typography>
          </Box>
        </Box>

        <Divider sx={{ mb: 4 }} />

        {/* Billing Information */}
        <Grid container spacing={4} sx={{ mb: 4 }}>
          <Grid item xs={12} md={6}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
              Bill To:
            </Typography>
            <Typography variant="body1" sx={{ fontWeight: 500 }}>
              {invoice.tenantName}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              School Management System
            </Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
              From:
            </Typography>
            <Typography variant="body1" sx={{ fontWeight: 500 }}>
              Kelem SMS
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Student Management Solutions
            </Typography>
          </Grid>
        </Grid>

        {/* Invoice Items */}
        <TableContainer sx={{ mb: 4 }}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 600 }}>Description</TableCell>
                <TableCell align="center" sx={{ fontWeight: 600 }}>Quantity</TableCell>
                <TableCell align="right" sx={{ fontWeight: 600 }}>Rate</TableCell>
                <TableCell align="right" sx={{ fontWeight: 600 }}>Amount</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {invoice.items.map((item: any, index: number) => (
                <TableRow key={index}>
                  <TableCell>{item.description}</TableCell>
                  <TableCell align="center">{item.quantity}</TableCell>
                  <TableCell align="right">{formatCurrency(item.rate)}</TableCell>
                  <TableCell align="right">{formatCurrency(item.amount)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Invoice Totals */}
        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Box sx={{ minWidth: 300 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body1">Subtotal:</Typography>
              <Typography variant="body1">{formatCurrency(invoice.subtotal)}</Typography>
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body1">Tax:</Typography>
              <Typography variant="body1">{formatCurrency(invoice.tax)}</Typography>
            </Box>
            <Divider sx={{ my: 2 }} />
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>Total:</Typography>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>{formatCurrency(invoice.total)}</Typography>
            </Box>
          </Box>
        </Box>
      </Paper>
    </BillingManagementLayout>
  )
}

export default InvoiceDetailPage
