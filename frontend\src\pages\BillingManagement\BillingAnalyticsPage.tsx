import { useState, useEffect } from 'react'
import { useToast } from '../../components/ToastProvider'
import {
  Box,
  Typography,
  Paper,
  LinearProgress,
  Alert,
  Grid,
  Card,
  CardContent,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  IconButton,
  Tooltip,
  useTheme,
} from '@mui/material'
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  AttachMoney as MoneyIcon,
  People as PeopleIcon,
  Receipt as ReceiptIcon,
  Warning as WarningIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  DateRange as DateRangeIcon,
} from '@mui/icons-material'
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts'
import BillingManagementLayout from '../../components/Layout/BillingManagementLayout'
import { getBillingMetrics, getPlans, formatCurrency, BillingMetrics, SubscriptionPlan } from '../../services/billingService'

const BillingAnalyticsPage = () => {
  const toast = useToast()
  const theme = useTheme()
  const [loading, setLoading] = useState(true)
  const [metrics, setMetrics] = useState<BillingMetrics | null>(null)
  const [plans, setPlans] = useState<SubscriptionPlan[]>([])
  const [timeRange, setTimeRange] = useState('30d')
  const [refreshing, setRefreshing] = useState(false)

  // Mock data for charts (in a real app, this would come from API)
  const revenueData = [
    { month: 'Jan', revenue: 45000, subscriptions: 12, mrr: 42000 },
    { month: 'Feb', revenue: 52000, subscriptions: 15, mrr: 48000 },
    { month: 'Mar', revenue: 48000, subscriptions: 14, mrr: 45000 },
    { month: 'Apr', revenue: 61000, subscriptions: 18, mrr: 58000 },
    { month: 'May', revenue: 55000, subscriptions: 16, mrr: 52000 },
    { month: 'Jun', revenue: 67000, subscriptions: 20, mrr: 64000 },
    { month: 'Jul', revenue: 72000, subscriptions: 22, mrr: 69000 },
  ]

  const planDistributionData = [
    { name: 'Basic', value: 45, color: '#8884d8' },
    { name: 'Premium', value: 35, color: '#82ca9d' },
    { name: 'Enterprise', value: 20, color: '#ffc658' },
  ]

  const churnData = [
    { month: 'Jan', churn: 2.1, retention: 97.9 },
    { month: 'Feb', churn: 1.8, retention: 98.2 },
    { month: 'Mar', churn: 2.5, retention: 97.5 },
    { month: 'Apr', churn: 1.9, retention: 98.1 },
    { month: 'May', churn: 2.3, retention: 97.7 },
    { month: 'Jun', churn: 1.6, retention: 98.4 },
    { month: 'Jul', churn: 1.4, retention: 98.6 },
  ]

  const paymentStatusData = [
    { status: 'Paid', count: 156, color: '#4caf50' },
    { status: 'Pending', count: 23, color: '#ff9800' },
    { status: 'Overdue', count: 8, color: '#f44336' },
    { status: 'Failed', count: 4, color: '#9e9e9e' },
  ]

  useEffect(() => {
    fetchAnalyticsData()
  }, [timeRange])

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true)
      const [metricsData, plansData] = await Promise.all([
        getBillingMetrics(),
        getPlans()
      ])
      setMetrics(metricsData)
      setPlans(plansData)
    } catch (error) {
      console.error('Error fetching analytics data:', error)
      toast.error('Failed to load analytics data')
    } finally {
      setLoading(false)
    }
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await fetchAnalyticsData()
    setRefreshing(false)
    toast.success('Analytics data refreshed')
  }

  const handleExport = () => {
    // In a real app, this would generate and download a report
    toast.info('Export functionality coming soon')
  }

  const formatPercentage = (value: number) => `${value.toFixed(1)}%`
  const formatNumber = (value: number) => value.toLocaleString()

  if (loading) {
    return (
      <BillingManagementLayout
        title="Billing Analytics"
        subtitle="Loading analytics..."
        breadcrumbs={[{ label: 'Analytics' }]}
      >
        <LinearProgress />
      </BillingManagementLayout>
    )
  }

  return (
    <BillingManagementLayout
      title="Billing Analytics"
      subtitle="Advanced billing and revenue analytics"
      showBreadcrumbs={false}
      actions={
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Time Range</InputLabel>
            <Select
              value={timeRange}
              label="Time Range"
              onChange={(e) => setTimeRange(e.target.value)}
            >
              <MenuItem value="7d">Last 7 days</MenuItem>
              <MenuItem value="30d">Last 30 days</MenuItem>
              <MenuItem value="90d">Last 90 days</MenuItem>
              <MenuItem value="1y">Last year</MenuItem>
            </Select>
          </FormControl>
          <Tooltip title="Export Report">
            <IconButton onClick={handleExport}>
              <DownloadIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Refresh Data">
            <IconButton onClick={handleRefresh} disabled={refreshing}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      }
    >
      {/* KPI Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Total Revenue
                  </Typography>
                  <Typography variant="h4" component="div">
                    {metrics ? formatCurrency(metrics.totalRevenue, 'ETB') : '---'}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                    <TrendingUpIcon sx={{ color: 'success.main', fontSize: 16, mr: 0.5 }} />
                    <Typography variant="body2" sx={{ color: 'success.main' }}>
                      +12.5% from last month
                    </Typography>
                  </Box>
                </Box>
                <MoneyIcon sx={{ fontSize: 40, color: 'primary.main', opacity: 0.3 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Monthly Recurring Revenue
                  </Typography>
                  <Typography variant="h4" component="div">
                    {metrics ? formatCurrency(metrics.monthlyRecurringRevenue, 'ETB') : '---'}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                    <TrendingUpIcon sx={{ color: 'success.main', fontSize: 16, mr: 0.5 }} />
                    <Typography variant="body2" sx={{ color: 'success.main' }}>
                      +8.2% from last month
                    </Typography>
                  </Box>
                </Box>
                <TrendingUpIcon sx={{ fontSize: 40, color: 'success.main', opacity: 0.3 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Active Subscriptions
                  </Typography>
                  <Typography variant="h4" component="div">
                    {metrics ? formatNumber(metrics.activeSubscriptions) : '---'}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                    <TrendingUpIcon sx={{ color: 'success.main', fontSize: 16, mr: 0.5 }} />
                    <Typography variant="body2" sx={{ color: 'success.main' }}>
                      +15 new this month
                    </Typography>
                  </Box>
                </Box>
                <PeopleIcon sx={{ fontSize: 40, color: 'info.main', opacity: 0.3 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Churn Rate
                  </Typography>
                  <Typography variant="h4" component="div">
                    {metrics ? formatPercentage(metrics.churnRate) : '---'}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                    <TrendingDownIcon sx={{ color: 'success.main', fontSize: 16, mr: 0.5 }} />
                    <Typography variant="body2" sx={{ color: 'success.main' }}>
                      -0.3% from last month
                    </Typography>
                  </Box>
                </Box>
                <WarningIcon sx={{ fontSize: 40, color: 'warning.main', opacity: 0.3 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts Section */}
      <Grid container spacing={3}>
        {/* Revenue Trend Chart */}
        <Grid item xs={12} lg={8}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
              Revenue Trend
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={revenueData}>
                <defs>
                  <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor={theme.palette.primary.main} stopOpacity={0.8}/>
                    <stop offset="95%" stopColor={theme.palette.primary.main} stopOpacity={0}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <RechartsTooltip
                  formatter={(value: number) => [formatCurrency(value, 'ETB'), 'Revenue']}
                />
                <Area
                  type="monotone"
                  dataKey="revenue"
                  stroke={theme.palette.primary.main}
                  fillOpacity={1}
                  fill="url(#colorRevenue)"
                />
              </AreaChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Plan Distribution Chart */}
        <Grid item xs={12} lg={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
              Plan Distribution
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={planDistributionData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {planDistributionData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <RechartsTooltip />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* MRR and Subscriptions Chart */}
        <Grid item xs={12} lg={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
              MRR & Subscriptions Growth
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={revenueData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <RechartsTooltip />
                <Legend />
                <Bar
                  yAxisId="right"
                  dataKey="subscriptions"
                  fill={theme.palette.secondary.main}
                  name="New Subscriptions"
                />
                <Line
                  yAxisId="left"
                  type="monotone"
                  dataKey="mrr"
                  stroke={theme.palette.primary.main}
                  strokeWidth={3}
                  name="MRR (ETB)"
                />
              </LineChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Churn Rate Chart */}
        <Grid item xs={12} lg={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
              Churn & Retention Rate
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={churnData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <RechartsTooltip formatter={(value: number) => `${value}%`} />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="churn"
                  stroke={theme.palette.error.main}
                  strokeWidth={2}
                  name="Churn Rate (%)"
                />
                <Line
                  type="monotone"
                  dataKey="retention"
                  stroke={theme.palette.success.main}
                  strokeWidth={2}
                  name="Retention Rate (%)"
                />
              </LineChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Payment Status Overview */}
        <Grid item xs={12} lg={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
              Payment Status Overview
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={paymentStatusData} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis dataKey="status" type="category" />
                <RechartsTooltip />
                <Bar dataKey="count" fill="#8884d8">
                  {paymentStatusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Key Metrics Summary */}
        <Grid item xs={12} lg={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
              Key Performance Indicators
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'primary.light', borderRadius: 1 }}>
                  <Typography variant="h4" sx={{ color: 'primary.contrastText', fontWeight: 700 }}>
                    {metrics ? formatCurrency(metrics.averageRevenuePerUser, 'ETB') : '---'}
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'primary.contrastText' }}>
                    ARPU
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'secondary.light', borderRadius: 1 }}>
                  <Typography variant="h4" sx={{ color: 'secondary.contrastText', fontWeight: 700 }}>
                    {metrics ? formatPercentage(metrics.conversionRate) : '---'}
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'secondary.contrastText' }}>
                    Conversion Rate
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'warning.light', borderRadius: 1 }}>
                  <Typography variant="h4" sx={{ color: 'warning.contrastText', fontWeight: 700 }}>
                    {metrics ? formatNumber(metrics.pendingPayments) : '---'}
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'warning.contrastText' }}>
                    Pending Payments
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'error.light', borderRadius: 1 }}>
                  <Typography variant="h4" sx={{ color: 'error.contrastText', fontWeight: 700 }}>
                    {metrics ? formatNumber(metrics.overdueInvoices) : '---'}
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'error.contrastText' }}>
                    Overdue Invoices
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Analytics Insights */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
              Analytics Insights
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <Alert severity="success" sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                    Revenue Growth
                  </Typography>
                  <Typography variant="body2">
                    Monthly revenue increased by 12.5% compared to last month, driven by new enterprise subscriptions.
                  </Typography>
                </Alert>
              </Grid>
              <Grid item xs={12} md={4}>
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                    Churn Improvement
                  </Typography>
                  <Typography variant="body2">
                    Customer churn rate decreased to 1.4%, the lowest in 6 months, indicating improved customer satisfaction.
                  </Typography>
                </Alert>
              </Grid>
              <Grid item xs={12} md={4}>
                <Alert severity="warning" sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                    Payment Attention
                  </Typography>
                  <Typography variant="body2">
                    {metrics?.overdueInvoices || 0} invoices are overdue. Consider following up with these customers.
                  </Typography>
                </Alert>
              </Grid>
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </BillingManagementLayout>
  )
}

export default BillingAnalyticsPage
