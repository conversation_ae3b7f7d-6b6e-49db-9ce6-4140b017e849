# Django Performance Optimization Guide

## Database Optimizations

### 1. Database Indexing

Add these indexes to your models for better query performance:

```python
# In your models.py files

class Student(models.Model):
    # ... existing fields ...
    
    class Meta:
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['grade']),
            models.Index(fields=['gender']),
            models.Index(fields=['created_at']),
            models.Index(fields=['tenant', 'status']),  # Composite index
            models.Index(fields=['tenant', 'grade']),   # Composite index
        ]

class Teacher(models.Model):
    # ... existing fields ...
    
    class Meta:
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['department']),
            models.Index(fields=['tenant', 'status']),
        ]

class Class(models.Model):
    # ... existing fields ...
    
    class Meta:
        indexes = [
            models.Index(fields=['grade']),
            models.Index(fields=['academic_year']),
            models.Index(fields=['tenant', 'grade']),
        ]
```

### 2. Query Optimization

Optimize your ViewSets with select_related and prefetch_related:

```python
# In your views.py or viewsets.py

class StudentViewSet(viewsets.ModelViewSet):
    def get_queryset(self):
        queryset = Student.objects.select_related(
            'tenant',
            'class_assigned'
        ).prefetch_related(
            'subjects',
            'payments'
        )
        
        # Add filtering
        status = self.request.query_params.get('status')
        if status:
            queryset = queryset.filter(status=status)
            
        grade = self.request.query_params.get('grade')
        if grade:
            queryset = queryset.filter(grade=grade)
            
        return queryset

class TeacherViewSet(viewsets.ModelViewSet):
    def get_queryset(self):
        return Teacher.objects.select_related(
            'tenant',
            'department'
        ).prefetch_related(
            'classes_teaching',
            'subjects'
        )
```

### 3. Database Connection Optimization

Add these settings to your Django settings.py:

```python
# Database connection pooling
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'kelem_sms',
        'USER': 'your_user',
        'PASSWORD': 'your_password',
        'HOST': 'localhost',
        'PORT': '5432',
        'OPTIONS': {
            'MAX_CONNS': 20,
            'MIN_CONNS': 5,
        },
        'CONN_MAX_AGE': 600,  # 10 minutes
    }
}

# Query optimization
DATABASE_ROUTERS = ['path.to.your.DatabaseRouter']
```

### 4. Caching Configuration

```python
# Redis caching
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'COMPRESSOR': 'django_redis.compressors.zlib.ZlibCompressor',
            'IGNORE_EXCEPTIONS': True,
        },
        'TIMEOUT': 300,  # 5 minutes
        'KEY_PREFIX': 'kelem_sms',
    }
}

# Session caching
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'default'
```

## API Optimizations

### 1. Pagination

```python
# In your settings.py
REST_FRAMEWORK = {
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20,
    'MAX_PAGE_SIZE': 100,
}

# Custom pagination class
class OptimizedPagination(PageNumberPagination):
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100
    
    def get_paginated_response(self, data):
        return Response({
            'count': self.page.paginator.count,
            'next': self.get_next_link(),
            'previous': self.get_previous_link(),
            'results': data,
            'page_size': self.page_size,
            'total_pages': self.page.paginator.num_pages,
        })
```

### 2. API Response Optimization

```python
# Optimized serializers
class OptimizedStudentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Student
        fields = ['id', 'first_name', 'last_name', 'status', 'grade']
        
class DetailedStudentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Student
        fields = '__all__'

# Use different serializers for list vs detail views
class StudentViewSet(viewsets.ModelViewSet):
    def get_serializer_class(self):
        if self.action == 'list':
            return OptimizedStudentSerializer
        return DetailedStudentSerializer
```

### 3. Background Tasks

```python
# Using Celery for background tasks
from celery import shared_task

@shared_task
def send_bulk_notifications(student_ids, message):
    """Send notifications in background"""
    students = Student.objects.filter(id__in=student_ids)
    for student in students:
        # Send notification logic
        pass

@shared_task
def generate_report(report_type, filters):
    """Generate reports in background"""
    # Report generation logic
    pass
```

## Middleware Optimizations

### 1. Custom Middleware

```python
# middleware.py
import time
from django.utils.deprecation import MiddlewareMixin

class PerformanceMiddleware(MiddlewareMixin):
    def process_request(self, request):
        request.start_time = time.time()
        
    def process_response(self, request, response):
        if hasattr(request, 'start_time'):
            duration = time.time() - request.start_time
            response['X-Response-Time'] = f'{duration:.3f}s'
            
            # Log slow requests
            if duration > 1.0:  # Log requests taking more than 1 second
                logger.warning(f'Slow request: {request.path} took {duration:.3f}s')
                
        return response

class CacheMiddleware(MiddlewareMixin):
    def process_request(self, request):
        # Add cache headers for static content
        if request.path.startswith('/static/') or request.path.startswith('/media/'):
            response = HttpResponse()
            response['Cache-Control'] = 'public, max-age=31536000'  # 1 year
            return response
```

## Static Files Optimization

### 1. Static Files Configuration

```python
# settings.py
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')

# Use WhiteNoise for static files in production
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',  # Add this
    # ... other middleware
]

# Static files compression
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# Media files optimization
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')
```

## Monitoring and Profiling

### 1. Django Debug Toolbar (Development)

```python
# settings.py (development)
if DEBUG:
    INSTALLED_APPS += ['debug_toolbar']
    MIDDLEWARE += ['debug_toolbar.middleware.DebugToolbarMiddleware']
    
    DEBUG_TOOLBAR_CONFIG = {
        'SHOW_TOOLBAR_CALLBACK': lambda request: True,
    }
```

### 2. Performance Monitoring

```python
# Custom management command for performance monitoring
from django.core.management.base import BaseCommand
from django.db import connection

class Command(BaseCommand):
    help = 'Monitor database performance'
    
    def handle(self, *args, **options):
        # Monitor slow queries
        slow_queries = []
        for query in connection.queries:
            if float(query['time']) > 0.1:  # Queries taking more than 100ms
                slow_queries.append(query)
                
        self.stdout.write(f'Found {len(slow_queries)} slow queries')
        for query in slow_queries:
            self.stdout.write(f"Time: {query['time']}s - SQL: {query['sql'][:100]}...")
```

## Production Optimizations

### 1. Gunicorn Configuration

```python
# gunicorn.conf.py
bind = "0.0.0.0:8000"
workers = 4  # 2 * CPU cores
worker_class = "gevent"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 5
preload_app = True
```

### 2. Nginx Configuration

```nginx
# nginx.conf
upstream django {
    server 127.0.0.1:8000;
}

server {
    listen 80;
    server_name your-domain.com;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # Static files
    location /static/ {
        alias /path/to/staticfiles/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Media files
    location /media/ {
        alias /path/to/media/;
        expires 1M;
        add_header Cache-Control "public";
    }
    
    # Django app
    location / {
        proxy_pass http://django;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Performance Testing

### 1. Load Testing Script

```python
# load_test.py
import requests
import time
import concurrent.futures
from statistics import mean, median

def test_endpoint(url, num_requests=100):
    response_times = []
    
    def make_request():
        start_time = time.time()
        response = requests.get(url)
        end_time = time.time()
        return end_time - start_time, response.status_code
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(make_request) for _ in range(num_requests)]
        
        for future in concurrent.futures.as_completed(futures):
            response_time, status_code = future.result()
            response_times.append(response_time)
    
    print(f"Average response time: {mean(response_times):.3f}s")
    print(f"Median response time: {median(response_times):.3f}s")
    print(f"Max response time: {max(response_times):.3f}s")
    print(f"Min response time: {min(response_times):.3f}s")

if __name__ == "__main__":
    test_endpoint("http://localhost:8000/api/students/")
```

## Implementation Checklist

- [ ] Add database indexes to frequently queried fields
- [ ] Implement select_related and prefetch_related in ViewSets
- [ ] Configure Redis caching
- [ ] Set up pagination for all list endpoints
- [ ] Implement background tasks with Celery
- [ ] Add performance monitoring middleware
- [ ] Configure static files optimization
- [ ] Set up Gunicorn with optimal worker configuration
- [ ] Configure Nginx for static file serving and compression
- [ ] Implement load testing and monitoring
