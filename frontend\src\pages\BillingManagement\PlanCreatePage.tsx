import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useToast } from '../../components/ToastProvider'
import { createPlan, CreatePlanData, PlanFeature } from '../../services/billingService'
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  Grid,
  MenuItem,
  Card,
  CardContent,
  Divider,
  Switch,
  FormControlLabel,
  Chip,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Alert,
  InputAdornment,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormControl,
  InputLabel,
  Select,
  Slider,
  Stack,
} from '@mui/material'
import {
  Save as SaveIcon,
  Cancel as CancelIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
  AttachMoney as MoneyIcon,
  People as PeopleIcon,
  Storage as StorageIcon,
  Star as StarIcon,
  Check as CheckIcon,
} from '@mui/icons-material'
import BillingManagementLayout from '../../components/Layout/BillingManagementLayout'

interface PlanFormData extends CreatePlanData {}

const PlanCreatePage = () => {
  const navigate = useNavigate()
  const toast = useToast()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<PlanFormData>({
    name: '',
    description: '',
    price: 0,
    billingCycle: 'monthly',
    maxUsers: 100,
    maxStorage: 5, // GB
    isActive: true,
    isFeatured: false,
    features: [],
    trialDays: 14,
    setupFee: 0,
    currency: 'ETB',
    category: 'standard',
    sortOrder: 1,
  })
  const [newFeature, setNewFeature] = useState({ name: '', description: '' })

  const predefinedFeatures = [
    { name: 'Student Management', description: 'Manage student records and profiles' },
    { name: 'Grade Management', description: 'Track and manage student grades' },
    { name: 'Attendance Tracking', description: 'Monitor student attendance' },
    { name: 'Parent Portal', description: 'Parent access to student information' },
    { name: 'Teacher Dashboard', description: 'Comprehensive teacher tools' },
    { name: 'Reports & Analytics', description: 'Detailed reporting and analytics' },
    { name: 'Mobile App Access', description: 'Mobile application support' },
    { name: 'API Access', description: 'REST API for integrations' },
    { name: 'Custom Branding', description: 'White-label customization' },
    { name: 'Priority Support', description: '24/7 priority customer support' },
    { name: 'Data Export', description: 'Export data in various formats' },
    { name: 'Advanced Security', description: 'Enhanced security features' },
  ]

  const handleInputChange = (field: keyof PlanFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const addFeature = (feature: { name: string; description: string }) => {
    const newFeatureObj: PlanFeature = {
      id: Date.now().toString(),
      name: feature.name,
      description: feature.description,
      included: true,
    }
    setFormData(prev => ({
      ...prev,
      features: [...prev.features, newFeatureObj]
    }))
  }

  const addCustomFeature = () => {
    if (newFeature.name.trim()) {
      addFeature(newFeature)
      setNewFeature({ name: '', description: '' })
    }
  }

  const removeFeature = (featureId: string) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter(f => f.id !== featureId)
    }))
  }

  const toggleFeature = (featureId: string) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.map(f => 
        f.id === featureId ? { ...f, included: !f.included } : f
      )
    }))
  }

  const calculateYearlyPrice = () => {
    return formData.price * 12 * 0.8 // 20% discount for yearly
  }

  const handleSubmit = async () => {
    try {
      setLoading(true)
      
      // Validation
      if (!formData.name.trim()) {
        toast.error('Plan name is required')
        return
      }
      
      if (formData.price <= 0) {
        toast.error('Price must be greater than 0')
        return
      }

      // Prepare plan data
      const planData: CreatePlanData = {
        ...formData,
        maxStorage: formData.maxStorage * 1024 * 1024 * 1024, // Convert GB to bytes
        features: formData.features.filter(f => f.included),
      }

      // Create plan via API
      await createPlan(planData)

      toast.success('Subscription plan created successfully')
      navigate('/dashboard/billing-management/plans')
    } catch (error) {
      toast.error('Failed to create subscription plan')
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: formData.currency,
    }).format(amount)
  }

  return (
    <BillingManagementLayout
      title="Create Subscription Plan"
      subtitle="Design a new subscription plan for tenants"
      breadcrumbs={[
        { label: 'Plans', href: '/dashboard/billing-management/plans' },
        { label: 'Create Plan' }
      ]}
    >
      {/* Action Buttons */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
        <Button
          variant="contained"
          startIcon={<SaveIcon />}
          onClick={handleSubmit}
          disabled={loading}
          sx={{
            background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
            boxShadow: '0 3px 5px 2px rgba(33, 203, 243, .3)',
          }}
        >
          {loading ? 'Creating...' : 'Create Plan'}
        </Button>
        <Button
          variant="outlined"
          startIcon={<CancelIcon />}
          onClick={() => navigate('/dashboard/billing-management/plans')}
        >
          Cancel
        </Button>
      </Box>

      <Grid container spacing={3}>
        {/* Plan Details */}
        <Grid item xs={12} lg={8}>
          <Paper sx={{ p: 4, mb: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
              Plan Details
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Plan Name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  required
                  placeholder="e.g., Premium Plan"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Category</InputLabel>
                  <Select
                    value={formData.category}
                    onChange={(e) => handleInputChange('category', e.target.value)}
                    label="Category"
                  >
                    <MenuItem value="basic">Basic</MenuItem>
                    <MenuItem value="standard">Standard</MenuItem>
                    <MenuItem value="premium">Premium</MenuItem>
                    <MenuItem value="enterprise">Enterprise</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Describe what this plan offers..."
                />
              </Grid>
            </Grid>
          </Paper>

          {/* Pricing */}
          <Paper sx={{ p: 4, mb: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
              Pricing & Billing
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  type="number"
                  label="Price"
                  value={formData.price}
                  onChange={(e) => handleInputChange('price', Number(e.target.value))}
                  InputProps={{
                    startAdornment: <InputAdornment position="start"><MoneyIcon /></InputAdornment>,
                  }}
                  required
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <FormControl fullWidth>
                  <InputLabel>Billing Cycle</InputLabel>
                  <Select
                    value={formData.billingCycle}
                    onChange={(e) => handleInputChange('billingCycle', e.target.value)}
                    label="Billing Cycle"
                  >
                    <MenuItem value="monthly">Monthly</MenuItem>
                    <MenuItem value="yearly">Yearly</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={4}>
                <FormControl fullWidth>
                  <InputLabel>Currency</InputLabel>
                  <Select
                    value={formData.currency}
                    onChange={(e) => handleInputChange('currency', e.target.value)}
                    label="Currency"
                  >
                    <MenuItem value="ETB">ETB (Br)</MenuItem>
                    <MenuItem value="USD">USD ($)</MenuItem>
                    <MenuItem value="EUR">EUR (€)</MenuItem>
                    <MenuItem value="GBP">GBP (£)</MenuItem>
                    <MenuItem value="CAD">CAD (C$)</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  type="number"
                  label="Setup Fee"
                  value={formData.setupFee}
                  onChange={(e) => handleInputChange('setupFee', Number(e.target.value))}
                  InputProps={{
                    startAdornment: <InputAdornment position="start"><MoneyIcon /></InputAdornment>,
                  }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  type="number"
                  label="Trial Days"
                  value={formData.trialDays}
                  onChange={(e) => handleInputChange('trialDays', Number(e.target.value))}
                  helperText="Number of free trial days"
                />
              </Grid>
            </Grid>

            {formData.billingCycle === 'monthly' && (
              <Alert severity="info" sx={{ mt: 2 }}>
                Yearly price would be: {formatCurrency(calculateYearlyPrice())} (20% discount)
              </Alert>
            )}
          </Paper>

          {/* Limits & Quotas */}
          <Paper sx={{ p: 4, mb: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
              Limits & Quotas
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Typography gutterBottom>Max Users: {formData.maxUsers}</Typography>
                <Slider
                  value={formData.maxUsers}
                  onChange={(e, value) => handleInputChange('maxUsers', value)}
                  min={1}
                  max={10000}
                  step={10}
                  marks={[
                    { value: 100, label: '100' },
                    { value: 1000, label: '1K' },
                    { value: 5000, label: '5K' },
                    { value: 10000, label: '10K' },
                  ]}
                  valueLabelDisplay="auto"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography gutterBottom>Max Storage: {formData.maxStorage} GB</Typography>
                <Slider
                  value={formData.maxStorage}
                  onChange={(e, value) => handleInputChange('maxStorage', value)}
                  min={1}
                  max={1000}
                  step={1}
                  marks={[
                    { value: 5, label: '5GB' },
                    { value: 50, label: '50GB' },
                    { value: 500, label: '500GB' },
                    { value: 1000, label: '1TB' },
                  ]}
                  valueLabelDisplay="auto"
                />
              </Grid>
            </Grid>
          </Paper>

          {/* Features */}
          <Paper sx={{ p: 4, mb: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
              Plan Features
            </Typography>

            {/* Predefined Features */}
            <Accordion defaultExpanded>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                  Standard Features
                </Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={2}>
                  {predefinedFeatures.map((feature, index) => (
                    <Grid item xs={12} sm={6} key={index}>
                      <Card
                        variant="outlined"
                        sx={{
                          cursor: 'pointer',
                          transition: 'all 0.2s',
                          '&:hover': { boxShadow: 2 }
                        }}
                        onClick={() => addFeature(feature)}
                      >
                        <CardContent sx={{ p: 2 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <AddIcon color="primary" fontSize="small" />
                            <Typography variant="body2" sx={{ fontWeight: 600 }}>
                              {feature.name}
                            </Typography>
                          </Box>
                          <Typography variant="caption" color="text.secondary">
                            {feature.description}
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </AccordionDetails>
            </Accordion>

            {/* Custom Feature */}
            <Accordion sx={{ mt: 2 }}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                  Add Custom Feature
                </Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={2} alignItems="end">
                  <Grid item xs={12} md={5}>
                    <TextField
                      fullWidth
                      label="Feature Name"
                      value={newFeature.name}
                      onChange={(e) => setNewFeature(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="e.g., Advanced Reporting"
                    />
                  </Grid>
                  <Grid item xs={12} md={5}>
                    <TextField
                      fullWidth
                      label="Description"
                      value={newFeature.description}
                      onChange={(e) => setNewFeature(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Brief description of the feature"
                    />
                  </Grid>
                  <Grid item xs={12} md={2}>
                    <Button
                      fullWidth
                      variant="contained"
                      onClick={addCustomFeature}
                      disabled={!newFeature.name.trim()}
                    >
                      Add
                    </Button>
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>

            {/* Selected Features */}
            {formData.features.length > 0 && (
              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                  Selected Features ({formData.features.filter(f => f.included).length})
                </Typography>
                <List>
                  {formData.features.map((feature) => (
                    <ListItem key={feature.id} divider>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={feature.included}
                            onChange={() => toggleFeature(feature.id)}
                            color="primary"
                          />
                        }
                        label={
                          <Box>
                            <Typography variant="body2" sx={{ fontWeight: 600 }}>
                              {feature.name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {feature.description}
                            </Typography>
                          </Box>
                        }
                      />
                      <ListItemSecondaryAction>
                        <IconButton
                          edge="end"
                          onClick={() => removeFeature(feature.id)}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>
              </Box>
            )}
          </Paper>

          {/* Plan Settings */}
          <Paper sx={{ p: 4, mb: 3 }}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
              Plan Settings
            </Typography>

            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.isActive}
                      onChange={(e) => handleInputChange('isActive', e.target.checked)}
                      color="primary"
                    />
                  }
                  label={
                    <Box>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        Active Plan
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Make this plan available for new subscriptions
                      </Typography>
                    </Box>
                  }
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.isFeatured}
                      onChange={(e) => handleInputChange('isFeatured', e.target.checked)}
                      color="secondary"
                    />
                  }
                  label={
                    <Box>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        Featured Plan
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Highlight this plan as recommended
                      </Typography>
                    </Box>
                  }
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  type="number"
                  label="Sort Order"
                  value={formData.sortOrder}
                  onChange={(e) => handleInputChange('sortOrder', Number(e.target.value))}
                  helperText="Lower numbers appear first"
                  inputProps={{ min: 1 }}
                />
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Plan Preview */}
        <Grid item xs={12} lg={4}>
          <Card sx={{ position: 'sticky', top: 20 }}>
            <CardContent>
              <Box sx={{ textAlign: 'center', mb: 3 }}>
                <Typography variant="h5" sx={{ fontWeight: 700, mb: 1 }}>
                  {formData.name || 'Plan Name'}
                </Typography>
                <Typography variant="h3" sx={{ fontWeight: 700, color: 'primary.main' }}>
                  {formatCurrency(formData.price)}
                  <Typography component="span" variant="body2" sx={{ ml: 1 }}>
                    /{formData.billingCycle === 'monthly' ? 'month' : 'year'}
                  </Typography>
                </Typography>
                {formData.setupFee > 0 && (
                  <Typography variant="body2" color="text.secondary">
                    + {formatCurrency(formData.setupFee)} setup fee
                  </Typography>
                )}
              </Box>

              <Divider sx={{ mb: 2 }} />

              <Stack spacing={1} sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <PeopleIcon fontSize="small" color="primary" />
                  <Typography variant="body2">
                    Up to {formData.maxUsers.toLocaleString()} users
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <StorageIcon fontSize="small" color="primary" />
                  <Typography variant="body2">
                    {formData.maxStorage} GB storage
                  </Typography>
                </Box>
                {formData.trialDays > 0 && (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <StarIcon fontSize="small" color="primary" />
                    <Typography variant="body2">
                      {formData.trialDays} days free trial
                    </Typography>
                  </Box>
                )}
              </Stack>

              <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                Features:
              </Typography>
              <Stack spacing={0.5}>
                {formData.features.filter(f => f.included).map((feature) => (
                  <Box key={feature.id} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <CheckIcon fontSize="small" color="success" />
                    <Typography variant="body2">{feature.name}</Typography>
                  </Box>
                ))}
                {formData.features.filter(f => f.included).length === 0 && (
                  <Typography variant="body2" color="text.secondary">
                    No features selected
                  </Typography>
                )}
              </Stack>

              <Box sx={{ mt: 3, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                {formData.isFeatured && (
                  <Chip label="Featured" color="secondary" size="small" />
                )}
                {formData.isActive && (
                  <Chip label="Active" color="success" size="small" />
                )}
                <Chip label={formData.category} variant="outlined" size="small" />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </BillingManagementLayout>
  )
}

export default PlanCreatePage
