import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useToast } from '../../components/ToastProvider'
import {
  Box,
  Typography,
  Button,
  Paper,
  LinearProgress,
  Alert,
} from '@mui/material'
import {
  Add as AddIcon,
} from '@mui/icons-material'
import BillingManagementLayout from '../../components/Layout/BillingManagementLayout'

const SubscriptionListPage = () => {
  const navigate = useNavigate()
  const toast = useToast()
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Simulate loading
    setTimeout(() => setLoading(false), 1000)
  }, [])

  if (loading) {
    return (
      <BillingManagementLayout
        title="Subscriptions"
        subtitle="Loading subscriptions..."
        breadcrumbs={[{ label: 'Subscriptions' }]}
      >
        <LinearProgress />
      </BillingManagementLayout>
    )
  }

  return (
    <BillingManagementLayout
      title="Subscriptions"
      subtitle="Manage tenant subscriptions and plans"
      breadcrumbs={[{ label: 'Subscriptions' }]}
    >
      <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          sx={{
            background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
            boxShadow: '0 3px 5px 2px rgba(33, 203, 243, .3)',
          }}
        >
          Create Subscription
        </Button>
      </Box>

      <Paper sx={{ p: 4, textAlign: 'center' }}>
        <Alert severity="info" sx={{ mb: 3 }}>
          Subscription management functionality will be implemented here.
        </Alert>
        <Typography variant="h6" color="text.secondary">
          Coming Soon: Subscription Management
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          This page will contain subscription plans, billing cycles, and tenant subscription management.
        </Typography>
      </Paper>
    </BillingManagementLayout>
  )
}

export default SubscriptionListPage
