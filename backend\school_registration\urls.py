from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'school_registration'

# Create a router for API views
router = DefaultRouter()
router.register(r'api/schools', views.SchoolRegistrationViewSet, basename='school-registration')
router.register(r'api/admin/schools', views.SchoolRegistrationAdminViewSet, basename='school-registration-admin')

urlpatterns = [
    # API endpoints
    path('', include(router.urls)),
    
    # Template views
    path('register/', views.school_registration_form, name='registration_form'),
    path('register/success/', views.school_registration_success, name='registration_success'),
    path('admin/registrations/', views.school_registration_list, name='registration_list'),
    path('admin/registrations/<int:pk>/', views.school_registration_detail, name='registration_detail'),
]
