from django.core.management.base import BaseCommand
from django.contrib.admin.models import LogEntry
from django.db import connection

class Command(BaseCommand):
    help = 'Fixes admin log entries by removing entries with invalid user references'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting admin log cleanup...'))
        
        # Get count before deletion
        count_before = LogEntry.objects.count()
        self.stdout.write(f'Admin log entries before cleanup: {count_before}')
        
        # Delete log entries with invalid user references
        with connection.cursor() as cursor:
            cursor.execute("""
                DELETE FROM django_admin_log 
                WHERE user_id NOT IN (SELECT id FROM authentication_user)
            """)
        
        # Get count after deletion
        count_after = LogEntry.objects.count()
        deleted_count = count_before - count_after
        
        self.stdout.write(self.style.SUCCESS(f'Admin log cleanup complete. Deleted {deleted_count} entries.'))
        self.stdout.write(f'Admin log entries after cleanup: {count_after}')
