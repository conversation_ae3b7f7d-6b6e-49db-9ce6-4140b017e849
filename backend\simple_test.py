#!/usr/bin/env python
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kelem_sms.settings')
django.setup()

try:
    from billing_management.models import SubscriptionPlan
    print("Successfully imported SubscriptionPlan model")
    
    # Try to create a simple plan
    plan = SubscriptionPlan.objects.create(
        name="Test ETB Plan",
        description="A test plan with ETB currency",
        price=1500.00,  # ETB pricing
        billing_cycle="monthly",
        max_users=100,
        max_storage=5368709120,  # 5GB
        currency="ETB",
        category="basic"
    )
    print(f"Created plan: {plan}")

    # List all plans
    plans = SubscriptionPlan.objects.all()
    print(f"Total plans: {plans.count()}")
    for p in plans:
        print(f"- {p.name}: Br {p.price} {p.currency}/{p.billing_cycle}")
        
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
