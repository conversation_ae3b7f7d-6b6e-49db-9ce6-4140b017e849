"use client";

import createSvgIcon from './utils/createSvgIcon';
import { jsx as _jsx } from "react/jsx-runtime";
export default createSvgIcon([/*#__PURE__*/_jsx("circle", {
  cx: "8",
  cy: "17",
  r: "1"
}, "0"), /*#__PURE__*/_jsx("circle", {
  cx: "12",
  cy: "17",
  r: "1"
}, "1"), /*#__PURE__*/_jsx("circle", {
  cx: "16",
  cy: "17",
  r: "1"
}, "2"), /*#__PURE__*/_jsx("path", {
  d: "M13 5.08V3h-2v2.08C7.61 5.57 5 8.47 5 12v2h14v-2c0-3.53-2.61-6.43-6-6.92"
}, "3"), /*#__PURE__*/_jsx("circle", {
  cx: "8",
  cy: "20",
  r: "1"
}, "4"), /*#__PURE__*/_jsx("circle", {
  cx: "12",
  cy: "20",
  r: "1"
}, "5"), /*#__PURE__*/_jsx("circle", {
  cx: "16",
  cy: "20",
  r: "1"
}, "6")], 'ShowerSharp');