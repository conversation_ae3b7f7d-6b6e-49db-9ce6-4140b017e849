from django.db import models
from django.core.validators import URLValidator
from django.contrib.auth import get_user_model

User = get_user_model()


class SiteSettings(models.Model):
    """Model for general site settings"""
    site_name = models.CharField(max_length=200, default='Kelem SMS')
    site_tagline = models.CharField(max_length=500, blank=True, default='')
    site_description = models.TextField(blank=True, default='')
    contact_email = models.EmailField(blank=True, default='')
    contact_phone = models.CharField(max_length=20, blank=True, default='')
    contact_address = models.TextField(blank=True, default='')
    
    # Social media links
    facebook_url = models.URLField(blank=True, default='', validators=[URLValidator()])
    twitter_url = models.URLField(blank=True, default='', validators=[URLValidator()])
    linkedin_url = models.URLField(blank=True, default='', validators=[URLValidator()])
    youtube_url = models.URLField(blank=True, default='', validators=[URLValidator()])
    
    # SEO settings
    meta_title = models.CharField(max_length=200, blank=True, default='')
    meta_description = models.TextField(max_length=500, blank=True, default='')
    meta_keywords = models.TextField(blank=True, default='')
    
    # Feature toggles
    enable_registration = models.BooleanField(default=True)
    enable_trial = models.BooleanField(default=True)
    enable_contact_form = models.BooleanField(default=True)
    enable_blog = models.BooleanField(default=False)
    enable_testimonials = models.BooleanField(default=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    
    class Meta:
        verbose_name = 'Site Settings'
        verbose_name_plural = 'Site Settings'
    
    def __str__(self):
        return f"Site Settings - {self.site_name}"


class BrandingSettings(models.Model):
    """Model for branding and theme settings"""
    # Color palette
    primary_color = models.CharField(max_length=7, default='#2E7D32')
    primary_light = models.CharField(max_length=7, default='#4CAF50')
    primary_dark = models.CharField(max_length=7, default='#1B5E20')
    secondary_color = models.CharField(max_length=7, default='#FFC107')
    secondary_light = models.CharField(max_length=7, default='#FFEB3B')
    secondary_dark = models.CharField(max_length=7, default='#FF8F00')
    
    # Background colors
    background_default = models.CharField(max_length=7, default='#FAFAFA')
    background_paper = models.CharField(max_length=7, default='#FFFFFF')
    
    # Text colors
    text_primary = models.CharField(max_length=7, default='#212121')
    text_secondary = models.CharField(max_length=7, default='#757575')
    
    # Status colors
    success_color = models.CharField(max_length=7, default='#4CAF50')
    warning_color = models.CharField(max_length=7, default='#FF9800')
    error_color = models.CharField(max_length=7, default='#F44336')
    info_color = models.CharField(max_length=7, default='#2196F3')
    
    # Typography
    font_family_primary = models.CharField(max_length=100, default='Roboto')
    font_family_secondary = models.CharField(max_length=100, default='Arial')
    
    # Design elements
    border_radius_small = models.IntegerField(default=4)
    border_radius_medium = models.IntegerField(default=8)
    border_radius_large = models.IntegerField(default=12)
    enable_shadows = models.BooleanField(default=True)
    shadow_intensity = models.IntegerField(default=2)
    
    # Custom CSS
    custom_css = models.TextField(blank=True, default='')
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    
    class Meta:
        verbose_name = 'Branding Settings'
        verbose_name_plural = 'Branding Settings'
    
    def __str__(self):
        return "Branding Settings"


class HeroSection(models.Model):
    """Model for homepage hero section settings"""
    title = models.CharField(max_length=200, default='Welcome to Kelem SMS')
    subtitle = models.CharField(max_length=300, default='Student Management System')
    description = models.TextField(default='A comprehensive solution for managing students, courses, and academic activities.')
    
    # Call-to-action buttons
    primary_button_text = models.CharField(max_length=50, default='Get Started')
    primary_button_url = models.CharField(max_length=200, default='/register')
    secondary_button_text = models.CharField(max_length=50, default='Learn More')
    secondary_button_url = models.CharField(max_length=200, default='/about')
    
    # Background settings
    BACKGROUND_CHOICES = [
        ('gradient', 'Gradient'),
        ('image', 'Image'),
        ('video', 'Video'),
        ('solid', 'Solid Color'),
    ]
    background_type = models.CharField(max_length=20, choices=BACKGROUND_CHOICES, default='gradient')
    
    # Display options
    show_stats = models.BooleanField(default=True)
    show_features = models.BooleanField(default=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    
    class Meta:
        verbose_name = 'Hero Section'
        verbose_name_plural = 'Hero Section'
    
    def __str__(self):
        return f"Hero Section - {self.title}"


class MaintenanceMode(models.Model):
    """Model for maintenance mode settings"""
    maintenance_mode = models.BooleanField(default=False)
    maintenance_message = models.TextField(
        default='We are currently performing maintenance to improve your experience. Please check back soon.'
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    
    class Meta:
        verbose_name = 'Maintenance Mode'
        verbose_name_plural = 'Maintenance Mode'
    
    def __str__(self):
        status = "Active" if self.maintenance_mode else "Inactive"
        return f"Maintenance Mode - {status}"
