from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'students'

router = DefaultRouter()
router.register(r'students', views.StudentViewSet)
router.register(r'guardians', views.GuardianViewSet)
router.register(r'documents', views.StudentDocumentViewSet)
router.register(r'achievements', views.StudentAchievementViewSet)
router.register(r'fees', views.StudentFeeViewSet)
router.register(r'payments', views.FeePaymentViewSet)
router.register(r'form-configs', views.StudentFormConfigViewSet)

urlpatterns = [
    path('api/', include(router.urls)),
    path('api/register-student/', views.StudentRegistrationView.as_view(), name='register-student'),
    path('api/dashboard-data/', views.DashboardDataView.as_view(), name='dashboard-data'),
]
