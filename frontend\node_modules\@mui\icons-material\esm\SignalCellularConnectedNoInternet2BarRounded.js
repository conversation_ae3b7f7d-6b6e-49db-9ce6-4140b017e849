import * as React from 'react';
import createSvgIcon from './utils/createSvgIcon';
import { jsx as _jsx } from "react/jsx-runtime";
import { jsxs as _jsxs } from "react/jsx-runtime";
export default createSvgIcon( /*#__PURE__*/_jsxs(React.Fragment, {
  children: [/*#__PURE__*/_jsx("path", {
    fillOpacity: ".3",
    d: "M22 8V4.41c0-.89-1.08-1.34-1.71-.71L3.71 20.29c-.63.63-.19 1.71.7 1.71H18V11c0-1.66 1.34-3 3-3h1z"
  }), /*#__PURE__*/_jsx("path", {
    d: "M14 22V10L3.71 20.29c-.63.63-.19 1.71.7 1.71H14zm6-11v6c0 .55.45 1 1 1s1-.45 1-1v-6c0-.55-.45-1-1-1s-1 .45-1 1zm0 11h2v-2h-2v2z"
  })]
}), 'SignalCellularConnectedNoInternet2BarRounded');