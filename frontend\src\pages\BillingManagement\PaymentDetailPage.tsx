import { useState, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import { useToast } from '../../components/ToastProvider'
import {
  Box,
  Typography,
  Paper,
  LinearProgress,
  Alert,
} from '@mui/material'
import BillingManagementLayout from '../../components/Layout/BillingManagementLayout'

const PaymentDetailPage = () => {
  const { id } = useParams<{ id: string }>()
  const toast = useToast()
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    setTimeout(() => setLoading(false), 1000)
  }, [])

  if (loading) {
    return (
      <BillingManagementLayout
        title="Payment Details"
        subtitle="Loading payment..."
        breadcrumbs={[
          { label: 'Payments', href: '/dashboard/billing-management/payments' },
          { label: 'Payment Details' }
        ]}
      >
        <LinearProgress />
      </BillingManagementLayout>
    )
  }

  return (
    <BillingManagementLayout
      title="Payment Details"
      subtitle={`Payment ID: ${id}`}
      breadcrumbs={[
        { label: 'Payments', href: '/dashboard/billing-management/payments' },
        { label: 'Payment Details' }
      ]}
    >
      <Paper sx={{ p: 4, textAlign: 'center' }}>
        <Alert severity="info" sx={{ mb: 3 }}>
          Payment detail functionality will be implemented here.
        </Alert>
        <Typography variant="h6" color="text.secondary">
          Coming Soon: Payment Details
        </Typography>
      </Paper>
    </BillingManagementLayout>
  )
}

export default PaymentDetailPage
