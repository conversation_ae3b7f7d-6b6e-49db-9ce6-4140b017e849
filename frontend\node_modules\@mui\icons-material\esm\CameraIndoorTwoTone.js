"use client";

import createSvgIcon from './utils/createSvgIcon';
import { jsx as _jsx } from "react/jsx-runtime";
export default createSvgIcon([/*#__PURE__*/_jsx("path", {
  d: "M6 10v9h12v-9l-6-4.5zm8 2v1l2-1.06v4.12L14 15v1c0 .55-.45 1-1 1H9c-.55 0-1-.45-1-1v-4c0-.55.45-1 1-1h4c.55 0 1 .45 1 1",
  opacity: ".3"
}, "0"), /*#__PURE__*/_jsx("path", {
  d: "M8 12v4c0 .55.45 1 1 1h4c.55 0 1-.45 1-1v-1l2 1.06v-4.12L14 13v-1c0-.55-.45-1-1-1H9c-.55 0-1 .45-1 1"
}, "1"), /*#__PURE__*/_jsx("path", {
  d: "M12 3 4 9v12h16V9zm6 16H6v-9l6-4.5 6 4.5z"
}, "2")], 'CameraIndoorTwoTone');