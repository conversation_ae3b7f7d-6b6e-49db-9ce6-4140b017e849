import React, { useState, useEffect } from 'react'
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardHeader,
  TextField,
  Button,
  Typography,
  Switch,
  FormControlLabel,
  CircularProgress,
  Paper,
  Alert,
} from '@mui/material'
import {
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Home as HomeIcon,
  Image as ImageIcon,
  VideoLibrary as VideoIcon,
} from '@mui/icons-material'
import {
  getHeroSection,
  updateHeroSection,
  type HeroSection,
} from '../../../services/siteSettingsService'

interface HeroSectionTabProps {
  onSaveSuccess: () => void
  onSaveError: (error: string) => void
}

const HeroSectionTab: React.FC<HeroSectionTabProps> = ({
  onSaveSuccess,
  onSaveError,
}) => {
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    subtitle: '',
    description: '',
    backgroundImage: '',
    backgroundVideo: '',
    ctaText: '',
    ctaUrl: '',
    secondaryCtaText: '',
    secondaryCtaUrl: '',
    showHero: true,
  })

  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      setLoading(true)
      const settings = await getHeroSection()
      setFormData({
        title: settings.title,
        subtitle: settings.subtitle,
        description: settings.description,
        backgroundImage: settings.background_image,
        backgroundVideo: settings.background_video,
        ctaText: settings.cta_text,
        ctaUrl: settings.cta_url,
        secondaryCtaText: settings.secondary_cta_text,
        secondaryCtaUrl: settings.secondary_cta_url,
        showHero: settings.show_hero,
      })
    } catch (error) {
      console.error('Error fetching hero section settings:', error)
      onSaveError('Failed to load hero section settings')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      const updateData: Partial<HeroSection> = {
        title: formData.title,
        subtitle: formData.subtitle,
        description: formData.description,
        background_image: formData.backgroundImage,
        background_video: formData.backgroundVideo,
        cta_text: formData.ctaText,
        cta_url: formData.ctaUrl,
        secondary_cta_text: formData.secondaryCtaText,
        secondary_cta_url: formData.secondaryCtaUrl,
        show_hero: formData.showHero,
      }
      await updateHeroSection(updateData)
      onSaveSuccess()
    } catch (error) {
      console.error('Error saving hero section settings:', error)
      onSaveError('Failed to save hero section settings')
    } finally {
      setSaving(false)
    }
  }

  const handleReset = () => {
    fetchSettings()
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <CircularProgress />
      </Box>
    )
  }

  return (
    <Box>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
          Hero Section Settings
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Configure the main hero section that appears on your homepage
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Content Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Content"
              subheader="Hero section text content"
              avatar={<HomeIcon color="primary" />}
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.showHero}
                        onChange={handleInputChange('showHero')}
                      />
                    }
                    label="Show Hero Section"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Title"
                    value={formData.title}
                    onChange={handleInputChange('title')}
                    helperText="Main headline for the hero section"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Subtitle"
                    value={formData.subtitle}
                    onChange={handleInputChange('subtitle')}
                    helperText="Secondary headline"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Description"
                    value={formData.description}
                    onChange={handleInputChange('description')}
                    multiline
                    rows={4}
                    helperText="Detailed description text"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Call-to-Action Buttons */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Call-to-Action Buttons"
              subheader="Primary and secondary action buttons"
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Primary CTA Text"
                    value={formData.ctaText}
                    onChange={handleInputChange('ctaText')}
                    placeholder="Get Started"
                    helperText="Text for the primary button"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Primary CTA URL"
                    value={formData.ctaUrl}
                    onChange={handleInputChange('ctaUrl')}
                    placeholder="/register"
                    helperText="Link for the primary button"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Secondary CTA Text"
                    value={formData.secondaryCtaText}
                    onChange={handleInputChange('secondaryCtaText')}
                    placeholder="Learn More"
                    helperText="Text for the secondary button"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Secondary CTA URL"
                    value={formData.secondaryCtaUrl}
                    onChange={handleInputChange('secondaryCtaUrl')}
                    placeholder="/about"
                    helperText="Link for the secondary button"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Background Media */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Background Media"
              subheader="Background image or video"
              avatar={<ImageIcon color="primary" />}
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Background Image URL"
                    value={formData.backgroundImage}
                    onChange={handleInputChange('backgroundImage')}
                    placeholder="https://example.com/hero-bg.jpg"
                    helperText="URL to background image"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Background Video URL"
                    value={formData.backgroundVideo}
                    onChange={handleInputChange('backgroundVideo')}
                    placeholder="https://example.com/hero-video.mp4"
                    helperText="URL to background video (optional)"
                  />
                </Grid>
                <Grid item xs={12}>
                  <Alert severity="info" sx={{ mt: 2 }}>
                    <Typography variant="body2">
                      <strong>Note:</strong> If both image and video are provided, video will take precedence. 
                      For best performance, use optimized images (WebP format recommended) and compressed videos.
                    </Typography>
                  </Alert>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Preview */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Preview" subheader="How your hero section will look" />
            <CardContent>
              <Paper
                sx={{
                  p: 4,
                  background: formData.backgroundImage 
                    ? `linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), url(${formData.backgroundImage})`
                    : 'linear-gradient(45deg, #6366f1 30%, #8b5cf6 90%)',
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  color: 'white',
                  textAlign: 'center',
                  minHeight: 300,
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  borderRadius: 2,
                }}
              >
                {formData.showHero ? (
                  <>
                    {formData.title && (
                      <Typography variant="h3" sx={{ mb: 2, fontWeight: 700 }}>
                        {formData.title}
                      </Typography>
                    )}
                    {formData.subtitle && (
                      <Typography variant="h5" sx={{ mb: 2, opacity: 0.9 }}>
                        {formData.subtitle}
                      </Typography>
                    )}
                    {formData.description && (
                      <Typography variant="body1" sx={{ mb: 3, opacity: 0.8, maxWidth: 600, mx: 'auto' }}>
                        {formData.description}
                      </Typography>
                    )}
                    <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                      {formData.ctaText && (
                        <Button
                          variant="contained"
                          size="large"
                          sx={{
                            bgcolor: 'white',
                            color: '#6366f1',
                            '&:hover': {
                              bgcolor: 'rgba(255,255,255,0.9)',
                            },
                          }}
                        >
                          {formData.ctaText}
                        </Button>
                      )}
                      {formData.secondaryCtaText && (
                        <Button
                          variant="outlined"
                          size="large"
                          sx={{
                            borderColor: 'white',
                            color: 'white',
                            '&:hover': {
                              borderColor: 'white',
                              bgcolor: 'rgba(255,255,255,0.1)',
                            },
                          }}
                        >
                          {formData.secondaryCtaText}
                        </Button>
                      )}
                    </Box>
                  </>
                ) : (
                  <Typography variant="h6" sx={{ opacity: 0.7 }}>
                    Hero section is disabled
                  </Typography>
                )}
              </Paper>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Action Buttons */}
      <Box sx={{ mt: 4, display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={handleReset}
          disabled={saving}
        >
          Reset
        </Button>
        <Button
          variant="contained"
          startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
          onClick={handleSave}
          disabled={saving}
          sx={{
            background: 'linear-gradient(45deg, #6366f1 30%, #8b5cf6 90%)',
            boxShadow: '0 3px 5px 2px rgba(99, 102, 241, .3)',
          }}
        >
          {saving ? 'Saving...' : 'Save Changes'}
        </Button>
      </Box>
    </Box>
  )
}

export default HeroSectionTab
