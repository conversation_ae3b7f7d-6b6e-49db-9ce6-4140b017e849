import os
import django
import sys
import traceback

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kelem_sms.settings')
django.setup()

from school_registration.models import SchoolRegistrationRequest
from django.utils.text import slugify
from django.utils import timezone
from tenants.models import School, Domain
from django_tenants.utils import schema_context
from django.contrib.auth import get_user_model
from django.db import connection

def create_test_school():
    # Create a test school registration
    test_school = SchoolRegistrationRequest(
        name="Test School Fix",
        slug=slugify("Test School Fix"),
        address="123 Test Street",
        city="Test City",
        state="Test State",
        country="Ethiopia",
        postal_code="12345",
        contact_person="Test Admin",
        contact_email="<EMAIL>",
        contact_phone="************",
        website="https://testschool.com",
        established_year=2020,
        school_type="Primary",
        student_capacity=500,
        description="A test school for testing admin user creation",
        admin_email="<EMAIL>",
        admin_password="testpassword123",
        schema_name="testschoolfix",
        domain="testschoolfix.kelemsms.com"
    )
    test_school.save()
    print(f"Created test school registration with ID: {test_school.id}")

    try:
        # Create the tenant (school) without creating schema
        # Temporarily set the class attribute to False
        original_auto_create_schema = School.auto_create_schema
        School.auto_create_schema = False

        try:
            # Create the tenant (school)
            school = School(
                schema_name=test_school.schema_name,
                name=test_school.name,
                address=test_school.address,
                contact_email=test_school.contact_email,
                contact_phone=test_school.contact_phone,
            )
            school.save()  # Save without creating schema

            # Create the domain for the tenant
            domain = Domain.objects.create(
                domain=test_school.domain,
                tenant=school,
                is_primary=True
            )

            print(f"Created tenant: {school.name} with schema: {school.schema_name}")

            # Create the schema manually
            create_schema_for_tenant(school.schema_name)

            # Create an admin user for the tenant
            admin_user = create_admin_user_for_tenant(school, test_school)

            print(f"Created admin user: {admin_user}")
        finally:
            # Restore the original value
            School.auto_create_schema = original_auto_create_schema

        # Check if admin user was created
        schema_name = test_school.schema_name
        with schema_context(schema_name):
            User = get_user_model()
            admin_users = User.objects.filter(user_type='admin', is_superuser=True)
            print(f"Found {admin_users.count()} admin users in schema {schema_name}")

            for user in admin_users:
                print(f"Admin user: {user.email}, ID: {user.id}")
    except Exception as e:
        print(f"Error: {str(e)}")
        traceback.print_exc()

def create_schema_for_tenant(schema_name):
    """Create a schema for a tenant and run migrations."""
    # Check if the schema already exists
    with connection.cursor() as cursor:
        cursor.execute("SELECT schema_name FROM information_schema.schemata WHERE schema_name = %s", [schema_name])
        if cursor.fetchone():
            print(f"Schema {schema_name} already exists")
            return

    # Create the schema
    with connection.cursor() as cursor:
        cursor.execute(f"CREATE SCHEMA {schema_name}")
        # Set the search path to the new schema
        cursor.execute(f"SET search_path TO {schema_name}, public")

    # Use schema_context to ensure migrations run in the correct schema
    with schema_context(schema_name):
        # Run migrations for the tenant using subprocess instead of os.system
        # This provides better error handling and is more secure
        try:
            # Get the absolute path to manage.py
            import subprocess
            manage_py_path = os.path.abspath(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'manage.py'))

            # Run migrations for the specific schema
            subprocess.run(
                ["python", manage_py_path, "migrate_schemas", "--schema", schema_name],
                check=True,
                capture_output=True,
                text=True
            )

            print(f"Successfully migrated schema: {schema_name}")
        except subprocess.CalledProcessError as e:
            print(f"Error migrating schema {schema_name}: {e.stdout}\n{e.stderr}")
            raise Exception(f"Failed to migrate schema {schema_name}: {e.stderr}")

def create_admin_user_for_tenant(school, registration):
    """Create an admin user for the tenant."""
    print(f"Creating admin user for tenant {school.name} with schema {school.schema_name}")

    # Use admin_password if provided, otherwise generate a random password
    import secrets
    import string
    if registration.admin_password:
        password = registration.admin_password
        print(f"Using provided admin password")
    else:
        password = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(12))
        print(f"Generated random password")

    try:
        # Use schema_context instead of manually setting the schema
        with schema_context(school.schema_name):
            # Get the User model inside the schema context
            User = get_user_model()
            print(f"User model class: {User.__name__}")
            print(f"User model fields: {[f.name for f in User._meta.get_fields()]}")

            # Use admin_email if provided, otherwise use contact_email
            email = registration.admin_email if registration.admin_email else registration.contact_email
            print(f"Using email {email} for admin user")

            # Parse the contact person name
            if ' ' in registration.contact_person:
                first_name = registration.contact_person.split()[0]
                last_name = registration.contact_person.split()[-1]
            else:
                first_name = registration.contact_person
                last_name = ''

            print(f"Creating user with first_name={first_name}, last_name={last_name}")

            # Check if any users exist in this schema
            print(f"Total users in schema: {User.objects.count()}")

            # Create the admin user
            try:
                # Check if the User model has a create_user method
                print(f"User model has create_user method: {hasattr(User, 'create_user')}")
                print(f"User model has objects manager: {hasattr(User, 'objects')}")
                print(f"User objects manager has create_user method: {hasattr(User.objects, 'create_user')}")

                # Our custom User model uses email as the username field and has username=None
                # So we should never try to create a user with a username field
                admin_user = User.objects.create_user(
                    email=email,
                    password=password,
                    first_name=first_name,
                    last_name=last_name,
                    user_type='admin',
                    is_staff=True,
                    is_active=True,
                    is_superuser=True  # Make them a superuser for this tenant
                )
                print(f"Admin user created successfully with ID {admin_user.id}")

                # Return the admin user and password
                return {
                    'email': admin_user.email,
                    'password': password,
                    'first_name': admin_user.first_name,
                    'last_name': admin_user.last_name
                }
            except Exception as inner_e:
                print(f"Error creating user object: {str(inner_e)}")
                traceback.print_exc()
                raise
    except Exception as e:
        print(f"Error creating admin user: {str(e)}")
        traceback.print_exc()
        # Return a default response so the tenant creation can still proceed
        return {
            'email': registration.contact_email,
            'password': password,
            'error': str(e)
        }

if __name__ == "__main__":
    # Delete existing test school if it exists
    try:
        existing_school = School.objects.filter(schema_name="testschoolfix").first()
        if existing_school:
            print(f"Deleting existing school with schema: {existing_school.schema_name}")
            existing_school.delete()

        existing_registration = SchoolRegistrationRequest.objects.filter(schema_name="testschoolfix").first()
        if existing_registration:
            print(f"Deleting existing registration with schema: {existing_registration.schema_name}")
            existing_registration.delete()
    except Exception as e:
        print(f"Error cleaning up existing data: {str(e)}")

    create_test_school()
