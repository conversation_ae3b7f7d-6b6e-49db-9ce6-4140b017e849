import React, { useState, useEffect } from 'react'
import {
  Box,
  Container,
  Typography,
  Paper,
  Card,
  CardContent,
  CardHeader,
  Switch,
  FormControlLabel,
  Grid,
  Chip,
  Alert,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Divider,

  IconButton,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material'
import {
  Flag as FlagIcon,
  Settings as SettingsIcon,
  ArrowBack as ArrowBackIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import {
  getFeatureFlags,
  toggleFeatureFlag,
  deleteFeatureFlag,
  createFeatureFlag,
  updateFeatureFlag,
  type FeatureFlag,
  type CreateFeatureFlagData,
  transformFeatureFlagForForm,
  transformFormDataToFeatureFlag,
  generateFeatureFlagKey,
  getFeatureFlagCategories,
  getEnvironmentOptions,
} from '../../services/featureFlagsService'

const FeatureFlagsPage: React.FC = () => {
  const navigate = useNavigate()
  const [features, setFeatures] = useState<FeatureFlag[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingFeature, setEditingFeature] = useState<FeatureFlag | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    key: '',
    description: '',
    enabled: true,
    category: '',
    environment: 'development' as const,
    rolloutPercentage: 100,
    dependencies: [] as string[],
  })

  useEffect(() => {
    fetchFeatureFlags()
  }, [])

  const fetchFeatureFlags = async () => {
    try {
      setLoading(true)
      const flags = await getFeatureFlags()
      setFeatures(flags)
    } catch (error) {
      console.error('Error fetching feature flags:', error)
      // Fallback to mock data if API fails
      setFeatures(mockFeatures)
    } finally {
      setLoading(false)
    }
  }

  // Mock data for fallback
  const mockFeatures: FeatureFlag[] = [
    {
      id: '1',
      name: 'New Dashboard UI',
      key: 'new_dashboard_ui',
      description: 'Enable the new dashboard user interface with improved navigation and modern design',
      enabled: true,
      category: 'UI/UX',
      environment: 'production',
      rollout_percentage: 100,
      dependencies: [],
      created_at: '2024-01-15T10:00:00Z',
      updated_at: '2024-01-20T14:30:00Z',
      last_modified_by: '<EMAIL>'
    },
    {
      id: '2',
      name: 'Advanced Analytics',
      key: 'advanced_analytics',
      description: 'Enable advanced analytics features including custom reports and data visualization',
      enabled: false,
      category: 'Analytics',
      environment: 'staging',
      rollout_percentage: 50,
      dependencies: ['new_dashboard_ui'],
      created_at: '2024-01-10T09:00:00Z',
      updated_at: '2024-01-18T16:45:00Z',
      last_modified_by: '<EMAIL>'
    },
    {
      id: '3',
      name: 'Mobile App Integration',
      key: 'mobile_app_integration',
      description: 'Enable mobile app integration features and API endpoints',
      enabled: true,
      category: 'Integration',
      environment: 'development',
      rollout_percentage: 25,
      dependencies: [],
      created_at: '2024-01-12T11:30:00Z',
      updated_at: '2024-01-19T13:15:00Z',
      last_modified_by: '<EMAIL>'
    },
    {
      id: '4',
      name: 'AI-Powered Recommendations',
      key: 'ai_recommendations',
      description: 'Enable AI-powered student performance recommendations and insights',
      enabled: false,
      category: 'AI/ML',
      environment: 'development',
      rollout_percentage: 10,
      dependencies: ['advanced_analytics'],
      created_at: '2024-01-08T08:00:00Z',
      updated_at: '2024-01-17T10:20:00Z',
      last_modified_by: '<EMAIL>'
    },
    {
      id: '5',
      name: 'Real-time Notifications',
      key: 'realtime_notifications',
      description: 'Enable real-time push notifications for important events and updates',
      enabled: true,
      category: 'Communication',
      environment: 'production',
      rollout_percentage: 80,
      dependencies: ['mobile_app_integration'],
      created_at: '2024-01-14T15:20:00Z',
      updated_at: '2024-01-21T09:10:00Z',
      last_modified_by: '<EMAIL>'
    },
    {
      id: '6',
      name: 'Multi-language Support',
      key: 'multi_language_support',
      description: 'Enable multi-language support for the platform interface',
      enabled: false,
      category: 'Localization',
      environment: 'staging',
      rollout_percentage: 0,
      dependencies: ['new_dashboard_ui'],
      created_at: '2024-01-11T12:45:00Z',
      updated_at: '2024-01-16T14:55:00Z',
      last_modified_by: '<EMAIL>'
    }
  ]

  const categories = ['all', ...Array.from(new Set(features.map(f => f.category)))]

  const filteredFeatures = features.filter(feature => {
    const matchesCategory = selectedCategory === 'all' || feature.category === selectedCategory
    const matchesSearch = feature.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         feature.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         feature.key.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesCategory && matchesSearch
  })

  const handleToggleFeature = async (featureId: string) => {
    try {
      const updatedFeature = await toggleFeatureFlag(featureId)
      setFeatures(prev => prev.map(feature =>
        feature.id === featureId ? updatedFeature : feature
      ))
    } catch (error) {
      console.error('Error toggling feature flag:', error)
      // Fallback to local update
      setFeatures(prev => prev.map(feature =>
        feature.id === featureId
          ? { ...feature, enabled: !feature.enabled, updated_at: new Date().toISOString() }
          : feature
      ))
    }
  }

  const handleEditFeature = (feature: FeatureFlag) => {
    setEditingFeature(feature)
    const formData = transformFeatureFlagForForm(feature)
    setFormData(formData)
    setDialogOpen(true)
  }

  const handleDeleteFeature = async (featureId: string) => {
    if (window.confirm('Are you sure you want to delete this feature flag?')) {
      try {
        await deleteFeatureFlag(featureId)
        setFeatures(prev => prev.filter(f => f.id !== featureId))
      } catch (error) {
        console.error('Error deleting feature flag:', error)
        // Fallback to local removal
        setFeatures(prev => prev.filter(f => f.id !== featureId))
      }
    }
  }

  const handleSaveFeature = async () => {
    try {
      const featureFlagData = transformFormDataToFeatureFlag(formData)

      if (editingFeature) {
        const updatedFeature = await updateFeatureFlag(editingFeature.id, featureFlagData)
        setFeatures(prev => prev.map(f => f.id === editingFeature.id ? updatedFeature : f))
      } else {
        const newFeature = await createFeatureFlag(featureFlagData)
        setFeatures(prev => [...prev, newFeature])
      }

      setDialogOpen(false)
      setEditingFeature(null)
      setFormData({
        name: '',
        key: '',
        description: '',
        enabled: true,
        category: '',
        environment: 'development',
        rolloutPercentage: 100,
        dependencies: [],
      })
    } catch (error) {
      console.error('Error saving feature flag:', error)
    }
  }

  const handleFormChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))

    // Auto-generate key from name
    if (field === 'name') {
      setFormData(prev => ({
        ...prev,
        key: generateFeatureFlagKey(value as string)
      }))
    }
  }

  const getEnvironmentColor = (env: string) => {
    switch (env) {
      case 'production': return 'success'
      case 'staging': return 'warning'
      case 'development': return 'info'
      default: return 'default'
    }
  }

  const getStatusIcon = (enabled: boolean) => {
    return enabled ? <CheckCircleIcon color="success" /> : <CancelIcon color="error" />
  }

  return (
    <Box sx={{ bgcolor: '#f5f5f5', minHeight: '100vh' }}>
      {/* Header Section */}
      <Paper
        elevation={0}
        sx={{
          bgcolor: 'white',
          color: '#1f2937',
          py: 4,
          px: 3,
          mb: 3,
          borderRadius: 0,
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        }}
      >
        <Container maxWidth="xl">
          {/* Header Content */}
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Tooltip title="Go Back">
                <IconButton
                  onClick={() => navigate(-1)}
                  sx={{
                    color: '#6366f1',
                    mr: 2,
                    bgcolor: '#f3f4f6',
                    '&:hover': {
                      bgcolor: '#e5e7eb',
                    },
                  }}
                >
                  <ArrowBackIcon />
                </IconButton>
              </Tooltip>
              <Box>
                <Typography
                  variant="h4"
                  component="h1"
                  sx={{
                    fontWeight: 700,
                    mb: 1,
                    color: '#1f2937',
                  }}
                >
                  Feature Flags
                </Typography>
                <Typography
                  variant="body1"
                  sx={{
                    color: '#6b7280',
                    fontWeight: 400,
                  }}
                >
                  Manage feature toggles and gradual rollouts across environments
                </Typography>
              </Box>
            </Box>
            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => {
                  setEditingFeature(null)
                  setDialogOpen(true)
                }}
                sx={{
                  background: 'linear-gradient(45deg, #6366f1 30%, #8b5cf6 90%)',
                  boxShadow: '0 3px 5px 2px rgba(99, 102, 241, .3)',
                }}
              >
                Add Feature Flag
              </Button>
              <Tooltip title="Refresh">
                <IconButton
                  onClick={() => window.location.reload()}
                  sx={{
                    color: '#6366f1',
                    bgcolor: '#f3f4f6',
                    '&:hover': {
                      bgcolor: '#e5e7eb',
                    },
                  }}
                >
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
        </Container>
      </Paper>

      {/* Main Content */}
      <Container maxWidth="xl">
        {/* Filters and Search */}
        <Paper sx={{ p: 3, mb: 3, borderRadius: 2 }}>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="Search feature flags..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                size="small"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                {categories.map((category) => (
                  <Chip
                    key={category}
                    label={category === 'all' ? 'All Categories' : category}
                    onClick={() => setSelectedCategory(category)}
                    color={selectedCategory === category ? 'primary' : 'default'}
                    variant={selectedCategory === category ? 'filled' : 'outlined'}
                  />
                ))}
              </Box>
            </Grid>
          </Grid>
        </Paper>

        {/* Feature Flags List */}
        <Grid container spacing={3}>
          {filteredFeatures.map((feature) => (
            <Grid item xs={12} key={feature.id}>
              <Card sx={{ borderRadius: 2 }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between' }}>
                    <Box sx={{ flex: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        {getStatusIcon(feature.enabled)}
                        <Typography variant="h6" sx={{ ml: 1, fontWeight: 600 }}>
                          {feature.name}
                        </Typography>
                        <Chip
                          label={feature.category}
                          size="small"
                          sx={{ ml: 2 }}
                          color="primary"
                          variant="outlined"
                        />
                        <Chip
                          label={feature.environment}
                          size="small"
                          sx={{ ml: 1 }}
                          color={getEnvironmentColor(feature.environment) as any}
                        />
                      </Box>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        {feature.description}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                        <Typography variant="caption" color="text.secondary">
                          Key: <code>{feature.key}</code>
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Rollout: {feature.rollout_percentage}%
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Updated: {new Date(feature.updated_at).toLocaleDateString()}
                        </Typography>
                      </Box>
                      {feature.dependencies.length > 0 && (
                        <Box sx={{ mb: 2 }}>
                          <Typography variant="caption" color="text.secondary">
                            Dependencies:
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
                            {feature.dependencies.map((dep) => (
                              <Chip key={dep} label={dep} size="small" variant="outlined" />
                            ))}
                          </Box>
                        </Box>
                      )}
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={feature.enabled}
                            onChange={() => handleToggleFeature(feature.id)}
                            color="primary"
                          />
                        }
                        label={feature.enabled ? 'Enabled' : 'Disabled'}
                      />
                      <IconButton
                        size="small"
                        onClick={() => handleEditFeature(feature)}
                        sx={{ color: '#6366f1' }}
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleDeleteFeature(feature.id)}
                        sx={{ color: '#ef4444' }}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        {filteredFeatures.length === 0 && !loading && (
          <Paper sx={{ p: 4, textAlign: 'center', borderRadius: 2 }}>
            <FlagIcon sx={{ fontSize: 64, color: '#9ca3af', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" sx={{ mb: 1 }}>
              No feature flags found
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {searchTerm || selectedCategory !== 'all' 
                ? 'Try adjusting your search or filter criteria'
                : 'Create your first feature flag to get started'
              }
            </Typography>
          </Paper>
        )}

        {/* Feature Flag Dialog */}
        <Dialog
          open={dialogOpen}
          onClose={() => setDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            {editingFeature ? 'Edit Feature Flag' : 'Create Feature Flag'}
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Feature Name"
                  value={formData.name}
                  onChange={handleFormChange('name')}
                  placeholder="New Dashboard UI"
                  helperText="Human-readable name for the feature"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Feature Key"
                  value={formData.key}
                  onChange={handleFormChange('key')}
                  placeholder="new_dashboard_ui"
                  helperText="Unique identifier (lowercase, underscores)"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  value={formData.description}
                  onChange={handleFormChange('description')}
                  multiline
                  rows={3}
                  placeholder="Describe what this feature does and its impact"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  select
                  label="Category"
                  value={formData.category}
                  onChange={handleFormChange('category')}
                  SelectProps={{ native: true }}
                >
                  <option value="">Select Category</option>
                  {getFeatureFlagCategories().map((cat) => (
                    <option key={cat} value={cat}>{cat}</option>
                  ))}
                </TextField>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  select
                  label="Environment"
                  value={formData.environment}
                  onChange={handleFormChange('environment')}
                  SelectProps={{ native: true }}
                >
                  {getEnvironmentOptions().map((env) => (
                    <option key={env.value} value={env.value}>{env.label}</option>
                  ))}
                </TextField>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Rollout Percentage"
                  type="number"
                  value={formData.rolloutPercentage}
                  onChange={handleFormChange('rolloutPercentage')}
                  inputProps={{ min: 0, max: 100 }}
                  helperText="Percentage of users who see this feature"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.enabled}
                      onChange={handleFormChange('enabled')}
                    />
                  }
                  label="Enable Feature"
                />
              </Grid>
              <Grid item xs={12}>
                <Alert severity="info">
                  <Typography variant="body2">
                    <strong>Note:</strong> Feature flags allow you to safely deploy code and control feature rollouts.
                    Use gradual rollouts to minimize risk and gather feedback before full deployment.
                  </Typography>
                </Alert>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="contained"
              onClick={handleSaveFeature}
              sx={{
                background: 'linear-gradient(45deg, #6366f1 30%, #8b5cf6 90%)',
              }}
            >
              {editingFeature ? 'Update' : 'Create'} Feature Flag
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Box>
  )
}

export default FeatureFlagsPage
