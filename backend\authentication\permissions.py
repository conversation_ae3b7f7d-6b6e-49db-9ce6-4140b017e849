from rest_framework import permissions
from django.conf import settings

class IsAdmin(permissions.BasePermission):
    """
    Permission to only allow admin users to access the view.
    """
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.user_type == 'admin'

class IsTeacher(permissions.BasePermission):
    """
    Permission to only allow teacher users to access the view.
    """
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.user_type == 'teacher'

class IsStudent(permissions.BasePermission):
    """
    Permission to only allow student users to access the view.
    """
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.user_type == 'student'

class IsParent(permissions.BasePermission):
    """
    Permission to only allow parent users to access the view.
    """
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.user_type == 'parent'

class IsStaff(permissions.BasePermission):
    """
    Permission to only allow staff users to access the view.
    """
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.user_type == 'staff'

class IsAdminOrTeacher(permissions.BasePermission):
    """
    Permission to allow admin or teacher users to access the view.
    """
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        return request.user.user_type in ['admin', 'teacher']

class IsAdminOrStaff(permissions.BasePermission):
    """
    Permission to allow admin or staff users to access the view.
    """
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        return request.user.user_type in ['admin', 'staff']

class IsOwnerOrAdmin(permissions.BasePermission):
    """
    Object-level permission to only allow owners of an object or admins to edit it.
    Assumes the model instance has an `owner` attribute.
    """
    def has_object_permission(self, request, view, obj):
        if not request.user.is_authenticated:
            return False
            
        if request.user.user_type == 'admin':
            return True
            
        # Check if the object has a user field
        if hasattr(obj, 'user'):
            return obj.user == request.user
            
        # Check if the object has an owner field
        if hasattr(obj, 'owner'):
            return obj.owner == request.user
            
        return False

class IsTeacherOfCourse(permissions.BasePermission):
    """
    Object-level permission to only allow teachers assigned to a course to edit it.
    """
    def has_object_permission(self, request, view, obj):
        if not request.user.is_authenticated:
            return False
            
        if request.user.user_type == 'admin':
            return True
            
        if request.user.user_type != 'teacher':
            return False
            
        # For CourseOffering objects
        if hasattr(obj, 'instructor'):
            return obj.instructor == request.user
            
        # For Course objects, check if user is instructor in any offering
        if hasattr(obj, 'offerings'):
            return obj.offerings.filter(instructor=request.user).exists()
            
        # For other related objects like assignments
        if hasattr(obj, 'course_offering'):
            return obj.course_offering.instructor == request.user
            
        return False

class IsStudentEnrolled(permissions.BasePermission):
    """
    Object-level permission to only allow students enrolled in a course to access it.
    """
    def has_object_permission(self, request, view, obj):
        if not request.user.is_authenticated:
            return False
            
        if request.user.user_type == 'admin':
            return True
            
        if request.user.user_type != 'student':
            return False
            
        # Get the student profile
        try:
            student = request.user.student_profile
        except:
            return False
            
        # For CourseOffering objects
        if hasattr(obj, 'enrollments'):
            return obj.enrollments.filter(student__user=request.user).exists()
            
        # For Course objects
        if hasattr(obj, 'offerings'):
            course_offerings = obj.offerings.all()
            for offering in course_offerings:
                if offering.enrollments.filter(student__user=request.user).exists():
                    return True
            return False
            
        # For other related objects like assignments
        if hasattr(obj, 'course_offering'):
            return obj.course_offering.enrollments.filter(student__user=request.user).exists()
            
        return False

class IsParentOfStudent(permissions.BasePermission):
    """
    Object-level permission to only allow parents to access their children's data.
    """
    def has_object_permission(self, request, view, obj):
        if not request.user.is_authenticated:
            return False
            
        if request.user.user_type == 'admin':
            return True
            
        if request.user.user_type != 'parent':
            return False
            
        # For Student objects
        if hasattr(obj, 'guardians'):
            return obj.guardians.filter(user=request.user).exists()
            
        # For objects related to a student
        if hasattr(obj, 'student'):
            return obj.student.guardians.filter(user=request.user).exists()
            
        return False

class ReadOnly(permissions.BasePermission):
    """
    Permission to only allow read-only access.
    """
    def has_permission(self, request, view):
        return request.method in permissions.SAFE_METHODS
