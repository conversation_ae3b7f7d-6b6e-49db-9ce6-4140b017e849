import os
import django
import traceback
import json

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kelem_sms.settings')
django.setup()

from school_registration.models import SchoolRegistrationRequest
from django.utils.text import slugify
from tenants.models import School, Domain
from django_tenants.utils import schema_context
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from django.contrib.auth.models import AnonymousUser
from rest_framework.test import force_authenticate

def test_school_registration_api():
    """Test the school registration API."""
    print("=== Testing School Registration API ===")

    # Create a test client
    client = APIClient()
    # Set the HTTP_HOST header to a valid hostname for the public tenant
    client.defaults['HTTP_HOST'] = 'localhost'

    # Create test data
    test_data = {
        "name": "Test School API",
        "address": "123 Test Street",
        "city": "Test City",
        "state": "Test State",
        "country": "Ethiopia",
        "postal_code": "12345",
        "contact_person": "Test Admin",
        "contact_email": "<EMAIL>",
        "contact_phone": "+251123456789",
        "website": "https://testschool.com",
        "established_year": 2020,
        "school_type": "Primary",
        "student_capacity": 500,
        "description": "A test school for testing admin user creation",
        "admin_email": "<EMAIL>",
        "admin_password": "testpassword123"
    }

    # Delete existing test school if it exists
    try:
        schema_name = slugify(test_data["name"]).replace('-', '_')
        existing_school = School.objects.filter(schema_name=schema_name).first()
        if existing_school:
            print(f"Deleting existing school with schema: {existing_school.schema_name}")
            existing_school.delete()

        existing_registration = SchoolRegistrationRequest.objects.filter(name=test_data["name"]).first()
        if existing_registration:
            print(f"Deleting existing registration with name: {existing_registration.name}")
            existing_registration.delete()
    except Exception as e:
        print(f"Error cleaning up existing data: {str(e)}")

    # Make the API request
    try:
        response = client.post('/schools/api/schools/', test_data, format='json')
        print(f"API Response Status: {response.status_code}")

        # Check if the response has data
        if hasattr(response, 'data'):
            print(f"API Response Data: {json.dumps(response.data, indent=2)}")
        else:
            print(f"API Response Content: {response.content.decode('utf-8')}")

        # Check if the tenant was created
        schema_name = slugify(test_data["name"]).replace('-', '_')
        school = School.objects.filter(schema_name=schema_name).first()
        if school:
            print(f"School created with ID: {school.id}, Schema: {school.schema_name}")

            # Check if admin user was created
            with schema_context(school.schema_name):
                User = get_user_model()
                admin_users = User.objects.filter(user_type='admin', is_superuser=True)
                print(f"Found {admin_users.count()} admin users in schema {school.schema_name}")

                for user in admin_users:
                    print(f"Admin user: {user.email}, ID: {user.id}")
        else:
            print("School was not created")
    except Exception as e:
        print(f"Error testing API: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    test_school_registration_api()
