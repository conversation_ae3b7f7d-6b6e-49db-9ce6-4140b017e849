import React, { useState, useMemo } from 'react'
import {
  <PERSON>,
  Card,
  Card<PERSON>ontent,
  CardHeader,
  <PERSON><PERSON><PERSON>,
  IconButton,
  Tooltip,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  <PERSON>ert,
  Collapse,
} from '@mui/material'
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  AccountTree as AccountTreeIcon,
  Security as SecurityIcon,
  Group as GroupIcon,
  Person as PersonIcon,
  Info as InfoIcon,
  Edit as EditIcon,
  Add as AddIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  AdminPanelSettings as AdminIcon,
  SupervisorAccount as SupervisorIcon,
  AccountCircle as UserIcon,
} from '@mui/icons-material'
import { Role, RoleHierarchy as RoleHierarchyType } from '../../types/rbac'

interface RoleHierarchyProps {
  roles: Role[]
  hierarchy: RoleHierarchyType[]
  onRoleSelect?: (role: Role) => void
  onEditRole?: (role: Role) => void
  onCreateSubRole?: (parentRole: Role) => void
  showUserCounts?: boolean
  showPermissionCounts?: boolean
  expandAll?: boolean
}

interface RoleNodeProps {
  role: Role
  hierarchy: RoleHierarchyType
  level: number
  onRoleSelect?: (role: Role) => void
  onEditRole?: (role: Role) => void
  onCreateSubRole?: (parentRole: Role) => void
  showUserCounts?: boolean
  showPermissionCounts?: boolean
  isExpanded: boolean
  onToggleExpand: (roleId: string) => void
}

const RoleNode: React.FC<RoleNodeProps> = ({
  role,
  hierarchy,
  level,
  onRoleSelect,
  onEditRole,
  onCreateSubRole,
  showUserCounts = true,
  showPermissionCounts = true,
  isExpanded,
  onToggleExpand,
}) => {
  const [detailsOpen, setDetailsOpen] = useState(false)

  const getRoleIcon = (role: Role) => {
    if (role.is_system) {
      return <AdminIcon color="error" />
    }
    
    switch (role.level) {
      case 0:
        return <AdminIcon color="primary" />
      case 1:
        return <SupervisorIcon color="secondary" />
      case 2:
        return <GroupIcon color="info" />
      default:
        return <UserIcon color="action" />
    }
  }

  const getRoleColor = (role: Role) => {
    if (role.is_system) return 'error'
    
    switch (role.level) {
      case 0:
        return 'error'
      case 1:
        return 'warning'
      case 2:
        return 'info'
      default:
        return 'default'
    }
  }

  const hasChildren = hierarchy.children && hierarchy.children.length > 0

  return (
    <Box>
      <Card 
        sx={{ 
          ml: level * 3,
          mb: 1,
          border: '1px solid',
          borderColor: 'divider',
          '&:hover': {
            borderColor: 'primary.main',
            boxShadow: 1,
          },
        }}
      >
        <CardContent sx={{ py: 1.5, '&:last-child': { pb: 1.5 } }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flex: 1 }}>
              {hasChildren && (
                <IconButton
                  size="small"
                  onClick={() => onToggleExpand(role.id)}
                >
                  {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
              )}
              
              {!hasChildren && <Box sx={{ width: 32 }} />}
              
              {getRoleIcon(role)}
              
              <Box sx={{ flex: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography 
                    variant="body2" 
                    sx={{ 
                      fontWeight: 600,
                      cursor: onRoleSelect ? 'pointer' : 'default',
                    }}
                    onClick={() => onRoleSelect?.(role)}
                  >
                    {role.display_name || role.name}
                  </Typography>
                  
                  <Chip
                    label={`Level ${role.level}`}
                    size="small"
                    color={getRoleColor(role) as any}
                    variant="outlined"
                  />
                  
                  {role.is_system && (
                    <Chip label="System" size="small" color="warning" />
                  )}
                  
                  {role.is_template && (
                    <Chip label="Template" size="small" color="info" />
                  )}
                </Box>
                
                <Typography variant="caption" color="text.secondary">
                  {role.description}
                </Typography>
                
                <Box sx={{ display: 'flex', gap: 2, mt: 0.5 }}>
                  {showUserCounts && (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      <PersonIcon fontSize="small" color="action" />
                      <Typography variant="caption" color="text.secondary">
                        {role.users_count} users
                      </Typography>
                    </Box>
                  )}
                  
                  {showPermissionCounts && (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      <SecurityIcon fontSize="small" color="action" />
                      <Typography variant="caption" color="text.secondary">
                        {role.effective_permissions.length} permissions
                      </Typography>
                    </Box>
                  )}
                </Box>
              </Box>
            </Box>
            
            <Box sx={{ display: 'flex', gap: 0.5 }}>
              <Tooltip title="View Details">
                <IconButton size="small" onClick={() => setDetailsOpen(true)}>
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              
              {onEditRole && (
                <Tooltip title="Edit Role">
                  <IconButton 
                    size="small" 
                    onClick={() => onEditRole(role)}
                    disabled={role.is_system}
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
              
              {onCreateSubRole && (
                <Tooltip title="Create Sub-Role">
                  <IconButton 
                    size="small" 
                    onClick={() => onCreateSubRole(role)}
                  >
                    <AddIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* Children */}
      <Collapse in={isExpanded}>
        <Box>
          {hierarchy.children?.map((childHierarchy) => {
            const childRole = childHierarchy.role_id // This would need to be resolved to actual role
            // For now, we'll skip rendering children as we need the actual role objects
            return null
          })}
        </Box>
      </Collapse>

      {/* Role Details Dialog */}
      <Dialog
        open={detailsOpen}
        onClose={() => setDetailsOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {getRoleIcon(role)}
            <Typography variant="h6">{role.display_name || role.name}</Typography>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {role.description}
            </Typography>

            <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
              <Chip
                label={`Level ${role.level}`}
                color={getRoleColor(role) as any}
                variant="outlined"
              />
              {role.is_system && <Chip label="System Role" color="warning" />}
              {role.is_template && <Chip label="Template" color="info" />}
            </Box>

            <Typography variant="subtitle2" sx={{ mb: 1 }}>
              Statistics
            </Typography>
            <Box sx={{ display: 'flex', gap: 4, mb: 3 }}>
              <Box>
                <Typography variant="h6">{role.users_count}</Typography>
                <Typography variant="caption" color="text.secondary">Users</Typography>
              </Box>
              <Box>
                <Typography variant="h6">{role.permissions.length}</Typography>
                <Typography variant="caption" color="text.secondary">Direct Permissions</Typography>
              </Box>
              <Box>
                <Typography variant="h6">{role.inherited_permissions.length}</Typography>
                <Typography variant="caption" color="text.secondary">Inherited Permissions</Typography>
              </Box>
              <Box>
                <Typography variant="h6">{role.effective_permissions.length}</Typography>
                <Typography variant="caption" color="text.secondary">Total Permissions</Typography>
              </Box>
            </Box>

            {role.parent_role && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" sx={{ mb: 1 }}>
                  Parent Role
                </Typography>
                <Chip label={role.parent_role} variant="outlined" />
              </Box>
            )}

            {role.child_roles.length > 0 && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" sx={{ mb: 1 }}>
                  Child Roles ({role.child_roles.length})
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {role.child_roles.map((childRoleId) => (
                    <Chip key={childRoleId} label={childRoleId} variant="outlined" size="small" />
                  ))}
                </Box>
              </Box>
            )}

            <Typography variant="subtitle2" sx={{ mb: 1 }}>
              Metadata
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Typography variant="body2">
                <strong>Created:</strong> {new Date(role.created_at).toLocaleString()}
              </Typography>
              <Typography variant="body2">
                <strong>Last Modified:</strong> {new Date(role.updated_at).toLocaleString()}
              </Typography>
              <Typography variant="body2">
                <strong>Created By:</strong> {role.created_by}
              </Typography>
              <Typography variant="body2">
                <strong>Last Modified By:</strong> {role.last_modified_by}
              </Typography>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsOpen(false)}>Close</Button>
          {onEditRole && !role.is_system && (
            <Button
              variant="contained"
              onClick={() => {
                setDetailsOpen(false)
                onEditRole(role)
              }}
            >
              Edit Role
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  )
}

const RoleHierarchy: React.FC<RoleHierarchyProps> = ({
  roles,
  hierarchy,
  onRoleSelect,
  onEditRole,
  onCreateSubRole,
  showUserCounts = true,
  showPermissionCounts = true,
  expandAll = false,
}) => {
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(
    expandAll ? new Set(roles.map(r => r.id)) : new Set()
  )

  const handleToggleExpand = (roleId: string) => {
    setExpandedNodes(prev => {
      const newSet = new Set(prev)
      if (newSet.has(roleId)) {
        newSet.delete(roleId)
      } else {
        newSet.add(roleId)
      }
      return newSet
    })
  }

  const handleExpandAll = () => {
    setExpandedNodes(new Set(roles.map(r => r.id)))
  }

  const handleCollapseAll = () => {
    setExpandedNodes(new Set())
  }

  const rootRoles = useMemo(() => {
    return roles.filter(role => !role.parent_role).sort((a, b) => a.level - b.level)
  }, [roles])

  const getHierarchyStats = () => {
    const maxLevel = Math.max(...roles.map(r => r.level))
    const systemRoles = roles.filter(r => r.is_system).length
    const customRoles = roles.filter(r => !r.is_system).length
    
    return { maxLevel, systemRoles, customRoles }
  }

  const stats = getHierarchyStats()

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h6">Role Hierarchy</Typography>
          <Typography variant="body2" color="text.secondary">
            {roles.length} roles across {stats.maxLevel + 1} levels
          </Typography>
        </Box>
        
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button size="small" onClick={handleExpandAll}>
            Expand All
          </Button>
          <Button size="small" onClick={handleCollapseAll}>
            Collapse All
          </Button>
        </Box>
      </Box>

      {/* Statistics */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', gap: 4 }}>
          <Box>
            <Typography variant="body2">
              <strong>{stats.systemRoles}</strong> System Roles
            </Typography>
          </Box>
          <Box>
            <Typography variant="body2">
              <strong>{stats.customRoles}</strong> Custom Roles
            </Typography>
          </Box>
          <Box>
            <Typography variant="body2">
              <strong>{stats.maxLevel + 1}</strong> Hierarchy Levels
            </Typography>
          </Box>
        </Box>
      </Alert>

      {/* Hierarchy Tree */}
      <Box>
        {rootRoles.map((role) => {
          const roleHierarchy = hierarchy.find(h => h.role_id === role.id)
          if (!roleHierarchy) return null

          return (
            <RoleNode
              key={role.id}
              role={role}
              hierarchy={roleHierarchy}
              level={0}
              onRoleSelect={onRoleSelect}
              onEditRole={onEditRole}
              onCreateSubRole={onCreateSubRole}
              showUserCounts={showUserCounts}
              showPermissionCounts={showPermissionCounts}
              isExpanded={expandedNodes.has(role.id)}
              onToggleExpand={handleToggleExpand}
            />
          )
        })}
      </Box>

      {rootRoles.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <AccountTreeIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
          <Typography variant="body2" color="text.secondary">
            No role hierarchy found
          </Typography>
        </Box>
      )}
    </Box>
  )
}

export default RoleHierarchy
