from django.db import models
from django.conf import settings
from courses.models import Department, Course, CourseOffering

class Teacher(models.Model):
    """Model representing a teacher in the system."""
    EMPLOYMENT_STATUS_CHOICES = (
        ('FULL_TIME', 'Full Time'),
        ('PART_TIME', 'Part Time'),
        ('CONTRACT', 'Contract'),
        ('VISITING', 'Visiting'),
        ('RETIRED', 'Retired'),
    )

    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='teacher_profile')
    employee_id = models.CharField(max_length=20, unique=True)
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, related_name='teachers')
    date_of_birth = models.DateField(null=True, blank=True)
    date_joined = models.DateField()
    employment_status = models.CharField(max_length=20, choices=EMPLOYMENT_STATUS_CHOICES, default='FULL_TIME')
    qualification = models.CharField(max_length=100, blank=True, null=True)
    specialization = models.CharField(max_length=100, blank=True, null=True)
    bio = models.TextField(blank=True, null=True)
    photo = models.ImageField(upload_to='teacher_photos/', null=True, blank=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.user.get_full_name()} ({self.employee_id})"

    class Meta:
        ordering = ['user__last_name', 'user__first_name']

class TeacherQualification(models.Model):
    """Model representing a teacher's qualifications."""
    QUALIFICATION_TYPE_CHOICES = (
        ('DEGREE', 'Degree'),
        ('CERTIFICATE', 'Certificate'),
        ('LICENSE', 'License'),
        ('AWARD', 'Award'),
        ('OTHER', 'Other'),
    )

    teacher = models.ForeignKey(Teacher, on_delete=models.CASCADE, related_name='qualifications')
    qualification_type = models.CharField(max_length=20, choices=QUALIFICATION_TYPE_CHOICES)
    title = models.CharField(max_length=100)
    institution = models.CharField(max_length=100)
    year_obtained = models.PositiveIntegerField()
    description = models.TextField(blank=True, null=True)
    document = models.FileField(upload_to='teacher_qualifications/', null=True, blank=True)

    def __str__(self):
        return f"{self.title} - {self.teacher}"

    class Meta:
        ordering = ['-year_obtained']

class TeacherAssignment(models.Model):
    """Model representing a teacher's assignment to a course offering."""
    ROLE_CHOICES = (
        ('PRIMARY', 'Primary Instructor'),
        ('ASSISTANT', 'Assistant Instructor'),
        ('LAB', 'Lab Instructor'),
        ('GUEST', 'Guest Lecturer'),
    )

    teacher = models.ForeignKey(Teacher, on_delete=models.CASCADE, related_name='assignments')
    course_offering = models.ForeignKey(CourseOffering, on_delete=models.CASCADE, related_name='teacher_assignments')
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default='PRIMARY')
    assigned_date = models.DateField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    hours_per_week = models.PositiveSmallIntegerField(default=0)
    notes = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"{self.teacher} - {self.course_offering} ({self.get_role_display()})"

    class Meta:
        ordering = ['-course_offering__term__year', 'course_offering__term__term']
        unique_together = ['teacher', 'course_offering', 'role']

class TeacherAvailability(models.Model):
    """Model representing a teacher's availability schedule."""
    DAY_CHOICES = (
        (0, 'Monday'),
        (1, 'Tuesday'),
        (2, 'Wednesday'),
        (3, 'Thursday'),
        (4, 'Friday'),
        (5, 'Saturday'),
        (6, 'Sunday'),
    )

    teacher = models.ForeignKey(Teacher, on_delete=models.CASCADE, related_name='availability')
    day_of_week = models.PositiveSmallIntegerField(choices=DAY_CHOICES)
    start_time = models.TimeField()
    end_time = models.TimeField()
    is_available = models.BooleanField(default=True)
    notes = models.CharField(max_length=255, blank=True, null=True)

    def __str__(self):
        return f"{self.teacher} - {self.get_day_of_week_display()} ({self.start_time} to {self.end_time})"

    class Meta:
        ordering = ['day_of_week', 'start_time']
        unique_together = ['teacher', 'day_of_week', 'start_time', 'end_time']
