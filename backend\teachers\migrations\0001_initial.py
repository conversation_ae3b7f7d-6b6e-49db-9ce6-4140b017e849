# Generated by Django 5.1.7 on 2025-04-06 22:37

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('courses', '0002_alter_academicterm_options_alter_course_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Teacher',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('employee_id', models.CharField(max_length=20, unique=True)),
                ('date_of_birth', models.DateField(blank=True, null=True)),
                ('date_joined', models.DateField()),
                ('employment_status', models.CharField(choices=[('FULL_TIME', 'Full Time'), ('PART_TIME', 'Part Time'), ('CONTRACT', 'Contract'), ('VISITING', 'Visiting'), ('RETIRED', 'Retired')], default='FULL_TIME', max_length=20)),
                ('qualification', models.Char<PERSON><PERSON>(blank=True, max_length=100, null=True)),
                ('specialization', models.CharField(blank=True, max_length=100, null=True)),
                ('bio', models.TextField(blank=True, null=True)),
                ('photo', models.ImageField(blank=True, null=True, upload_to='teacher_photos/')),
                ('is_active', models.BooleanField(default=True)),
                ('department', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='teachers', to='courses.department')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='teacher_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['user__last_name', 'user__first_name'],
            },
        ),
        migrations.CreateModel(
            name='TeacherQualification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('qualification_type', models.CharField(choices=[('DEGREE', 'Degree'), ('CERTIFICATE', 'Certificate'), ('LICENSE', 'License'), ('AWARD', 'Award'), ('OTHER', 'Other')], max_length=20)),
                ('title', models.CharField(max_length=100)),
                ('institution', models.CharField(max_length=100)),
                ('year_obtained', models.PositiveIntegerField()),
                ('description', models.TextField(blank=True, null=True)),
                ('document', models.FileField(blank=True, null=True, upload_to='teacher_qualifications/')),
                ('teacher', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='qualifications', to='teachers.teacher')),
            ],
            options={
                'ordering': ['-year_obtained'],
            },
        ),
        migrations.CreateModel(
            name='TeacherAssignment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('PRIMARY', 'Primary Instructor'), ('ASSISTANT', 'Assistant Instructor'), ('LAB', 'Lab Instructor'), ('GUEST', 'Guest Lecturer')], default='PRIMARY', max_length=20)),
                ('assigned_date', models.DateField(auto_now_add=True)),
                ('is_active', models.BooleanField(default=True)),
                ('hours_per_week', models.PositiveSmallIntegerField(default=0)),
                ('notes', models.TextField(blank=True, null=True)),
                ('course_offering', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='teacher_assignments', to='courses.courseoffering')),
                ('teacher', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assignments', to='teachers.teacher')),
            ],
            options={
                'ordering': ['-course_offering__term__year', 'course_offering__term__term'],
                'unique_together': {('teacher', 'course_offering', 'role')},
            },
        ),
        migrations.CreateModel(
            name='TeacherAvailability',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('day_of_week', models.PositiveSmallIntegerField(choices=[(0, 'Monday'), (1, 'Tuesday'), (2, 'Wednesday'), (3, 'Thursday'), (4, 'Friday'), (5, 'Saturday'), (6, 'Sunday')])),
                ('start_time', models.TimeField()),
                ('end_time', models.TimeField()),
                ('is_available', models.BooleanField(default=True)),
                ('notes', models.CharField(blank=True, max_length=255, null=True)),
                ('teacher', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='availability', to='teachers.teacher')),
            ],
            options={
                'ordering': ['day_of_week', 'start_time'],
                'unique_together': {('teacher', 'day_of_week', 'start_time', 'end_time')},
            },
        ),
    ]
