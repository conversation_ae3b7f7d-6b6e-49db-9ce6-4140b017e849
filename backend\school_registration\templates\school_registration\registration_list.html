{% extends "base.html" %}

{% block title %}School Registration Requests{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h3 class="mb-0">School Registration Requests</h3>
                </div>
                <div class="card-body">
                    <ul class="nav nav-tabs mb-4">
                        <li class="nav-item">
                            <a class="nav-link active" id="pending-tab" data-bs-toggle="tab" href="#pending" role="tab">Pending</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="approved-tab" data-bs-toggle="tab" href="#approved" role="tab">Approved</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="rejected-tab" data-bs-toggle="tab" href="#rejected" role="tab">Rejected</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="all-tab" data-bs-toggle="tab" href="#all" role="tab">All</a>
                        </li>
                    </ul>
                    
                    <div class="tab-content">
                        <div class="tab-pane fade show active" id="pending" role="tabpanel">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>School Name</th>
                                            <th>Type</th>
                                            <th>Contact Person</th>
                                            <th>Contact Email</th>
                                            <th>Requested On</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for registration in registrations %}
                                            {% if registration.status == 'pending' %}
                                            <tr>
                                                <td>{{ registration.name }}</td>
                                                <td>{{ registration.school_type }}</td>
                                                <td>{{ registration.contact_person }}</td>
                                                <td>{{ registration.contact_email }}</td>
                                                <td>{{ registration.requested_on|date:"M d, Y" }}</td>
                                                <td>
                                                    <a href="{% url 'school_registration:registration_detail' registration.id %}" class="btn btn-sm btn-primary">View</a>
                                                </td>
                                            </tr>
                                            {% endif %}
                                        {% empty %}
                                            <tr>
                                                <td colspan="6" class="text-center">No pending registration requests.</td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <div class="tab-pane fade" id="approved" role="tabpanel">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>School Name</th>
                                            <th>Type</th>
                                            <th>Domain</th>
                                            <th>Approved On</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for registration in registrations %}
                                            {% if registration.status == 'approved' %}
                                            <tr>
                                                <td>{{ registration.name }}</td>
                                                <td>{{ registration.school_type }}</td>
                                                <td>{{ registration.domain }}</td>
                                                <td>{{ registration.processed_on|date:"M d, Y" }}</td>
                                                <td>
                                                    <a href="{% url 'school_registration:registration_detail' registration.id %}" class="btn btn-sm btn-primary">View</a>
                                                </td>
                                            </tr>
                                            {% endif %}
                                        {% empty %}
                                            <tr>
                                                <td colspan="5" class="text-center">No approved registration requests.</td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <div class="tab-pane fade" id="rejected" role="tabpanel">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>School Name</th>
                                            <th>Type</th>
                                            <th>Contact Email</th>
                                            <th>Rejected On</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for registration in registrations %}
                                            {% if registration.status == 'rejected' %}
                                            <tr>
                                                <td>{{ registration.name }}</td>
                                                <td>{{ registration.school_type }}</td>
                                                <td>{{ registration.contact_email }}</td>
                                                <td>{{ registration.processed_on|date:"M d, Y" }}</td>
                                                <td>
                                                    <a href="{% url 'school_registration:registration_detail' registration.id %}" class="btn btn-sm btn-primary">View</a>
                                                </td>
                                            </tr>
                                            {% endif %}
                                        {% empty %}
                                            <tr>
                                                <td colspan="5" class="text-center">No rejected registration requests.</td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <div class="tab-pane fade" id="all" role="tabpanel">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>School Name</th>
                                            <th>Type</th>
                                            <th>Status</th>
                                            <th>Requested On</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for registration in registrations %}
                                        <tr>
                                            <td>{{ registration.name }}</td>
                                            <td>{{ registration.school_type }}</td>
                                            <td>
                                                {% if registration.status == 'pending' %}
                                                <span class="badge bg-warning">Pending</span>
                                                {% elif registration.status == 'approved' %}
                                                <span class="badge bg-success">Approved</span>
                                                {% elif registration.status == 'rejected' %}
                                                <span class="badge bg-danger">Rejected</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ registration.requested_on|date:"M d, Y" }}</td>
                                            <td>
                                                <a href="{% url 'school_registration:registration_detail' registration.id %}" class="btn btn-sm btn-primary">View</a>
                                            </td>
                                        </tr>
                                        {% empty %}
                                            <tr>
                                                <td colspan="5" class="text-center">No registration requests.</td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
