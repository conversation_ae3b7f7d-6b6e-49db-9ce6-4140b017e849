from django.contrib import admin
from django.utils import timezone
from django.db import transaction
from django_tenants.utils import schema_context
from tenants.models import School, Domain
from .models import SchoolRegistrationRequest

@admin.register(SchoolRegistrationRequest)
class SchoolRegistrationRequestAdmin(admin.ModelAdmin):
    list_display = ('name', 'school_type', 'contact_person', 'contact_email', 'status', 'requested_on')
    list_filter = ('status', 'school_type', 'requested_on')
    search_fields = ('name', 'contact_person', 'contact_email')
    readonly_fields = ('requested_on', 'processed_on')
    fieldsets = (
        ('School Information', {
            'fields': ('name', 'slug', 'school_type', 'established_year', 'student_capacity', 'description')
        }),
        ('Address Information', {
            'fields': ('address', 'city', 'state', 'country', 'postal_code')
        }),
        ('Contact Information', {
            'fields': ('contact_person', 'contact_email', 'contact_phone', 'website')
        }),
        ('Registration Status', {
            'fields': ('status', 'requested_on', 'processed_on', 'rejection_reason')
        }),
        ('Tenant Information', {
            'fields': ('schema_name', 'domain')
        }),
    )

    actions = ['approve_registrations', 'reject_registrations']

    def approve_registrations(self, request, queryset):
        """Approve selected registration requests and create tenants."""
        for registration in queryset.filter(status='pending'):
            try:
                with transaction.atomic():
                    # Update the registration request
                    registration.status = 'approved'
                    registration.processed_on = timezone.now()
                    registration.save()

                    # Create the tenant (school)
                    school = School.objects.create(
                        schema_name=registration.schema_name,
                        name=registration.name,
                        address=registration.address,
                        contact_email=registration.contact_email,
                        contact_phone=registration.contact_phone,
                    )

                    # Create the domain for the tenant
                    Domain.objects.create(
                        domain=registration.domain,
                        tenant=school,
                        is_primary=True
                    )

                    # Initialize the tenant schema with necessary data
                    with schema_context(school.schema_name):
                        # Here you can add code to initialize the tenant schema
                        pass
            except Exception as e:
                self.message_user(request, f"Error approving {registration.name}: {str(e)}", level='error')

        self.message_user(request, f"{queryset.filter(status='pending').count()} registration requests approved successfully.")

    approve_registrations.short_description = "Approve selected registration requests"

    def reject_registrations(self, request, queryset):
        """Reject selected registration requests."""
        queryset.filter(status='pending').update(
            status='rejected',
            processed_on=timezone.now(),
            rejection_reason='Rejected by administrator.'
        )

        self.message_user(request, f"{queryset.filter(status='pending').count()} registration requests rejected.")

    reject_registrations.short_description = "Reject selected registration requests"
