import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kelem_sms.settings')
django.setup()

from django.db import connection

# Create the authentication_user table
with connection.cursor() as cursor:
    # Drop the existing table if it exists
    cursor.execute("DROP TABLE IF EXISTS authentication_userrole_permissions CASCADE")
    cursor.execute("DROP TABLE IF EXISTS authentication_userpermission CASCADE")
    cursor.execute("DROP TABLE IF EXISTS authentication_userrole CASCADE")
    cursor.execute("DROP TABLE IF EXISTS authentication_user CASCADE")

    cursor.execute("""
    CREATE TABLE IF NOT EXISTS authentication_user (
        id SERIAL PRIMARY KEY,
        password VARCHAR(128) NOT NULL,
        last_login TIMESTAMP WITH TIME ZONE NULL,
        is_superuser BOOLEAN NOT NULL,
        first_name VARCHAR(150) NOT NULL,
        last_name VA<PERSON>HA<PERSON>(150) NOT NULL,
        is_staff BOOLEAN NOT NULL,
        is_active BOOLEAN NOT NULL,
        date_joined TIMESTAMP WITH TIME ZONE NOT NULL,
        email VARCHAR(254) UNIQUE NOT NULL,
        user_type VARCHAR(20) NOT NULL,
        phone_number VARCHAR(20) NULL
    )
    """)
    print("Created authentication_user table.")

    # Create the authentication_userpermission table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS authentication_userpermission (
        id SERIAL PRIMARY KEY,
        permission_name VARCHAR(100) NOT NULL,
        permission_code VARCHAR(100) UNIQUE NOT NULL,
        description TEXT NULL,
        user_id INTEGER NOT NULL REFERENCES authentication_user(id) ON DELETE CASCADE
    )
    """)
    print("Created authentication_userpermission table.")

    # Create the authentication_userrole table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS authentication_userrole (
        id SERIAL PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT NULL
    )
    """)
    print("Created authentication_userrole table.")

    # Create the authentication_userrole_permissions table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS authentication_userrole_permissions (
        id SERIAL PRIMARY KEY,
        userrole_id INTEGER NOT NULL REFERENCES authentication_userrole(id) ON DELETE CASCADE,
        userpermission_id INTEGER NOT NULL REFERENCES authentication_userpermission(id) ON DELETE CASCADE,
        UNIQUE (userrole_id, userpermission_id)
    )
    """)
    print("Created authentication_userrole_permissions table.")

print("Table creation completed.")
