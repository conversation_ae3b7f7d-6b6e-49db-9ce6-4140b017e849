# Generated by Django 5.1.7 on 2025-04-06 22:37

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('academics', '0001_initial'),
        ('courses', '0001_initial'),
        ('students', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='academicterm',
            options={'ordering': ['-year', 'term'], 'verbose_name': 'Academic Term', 'verbose_name_plural': 'Academic Terms'},
        ),
        migrations.AlterModelOptions(
            name='course',
            options={'ordering': ['department', 'name'], 'verbose_name': 'Course', 'verbose_name_plural': 'Courses'},
        ),
        migrations.AlterModelOptions(
            name='courseoffering',
            options={'ordering': ['-term__year', 'term__term', 'course__name', 'section'], 'verbose_name': 'Course Offering', 'verbose_name_plural': 'Course Offerings'},
        ),
        migrations.AlterModelOptions(
            name='department',
            options={'ordering': ['name'], 'verbose_name': 'Department', 'verbose_name_plural': 'Departments'},
        ),
        migrations.AlterModelOptions(
            name='enrollment',
            options={'ordering': ['-course_offering__term__year', 'course_offering__term__term'], 'verbose_name': 'Enrollment', 'verbose_name_plural': 'Enrollments'},
        ),
        migrations.AlterUniqueTogether(
            name='courseoffering',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='academicterm',
            name='academic_year',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='terms', to='academics.academicyear'),
        ),
        migrations.AddField(
            model_name='academicterm',
            name='created_at',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name='academicterm',
            name='description',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='academicterm',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, null=True),
        ),
        migrations.AddField(
            model_name='course',
            name='assessment_methods',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='course',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_courses', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='course',
            name='grade_levels',
            field=models.ManyToManyField(blank=True, related_name='available_courses', to='academics.gradelevel'),
        ),
        migrations.AddField(
            model_name='course',
            name='hours_per_week',
            field=models.PositiveSmallIntegerField(default=3),
        ),
        migrations.AddField(
            model_name='course',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='course',
            name='learning_outcomes',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='course',
            name='level',
            field=models.CharField(choices=[('BEGINNER', 'Beginner'), ('INTERMEDIATE', 'Intermediate'), ('ADVANCED', 'Advanced')], default='INTERMEDIATE', max_length=20),
        ),
        migrations.AddField(
            model_name='course',
            name='syllabus',
            field=models.FileField(blank=True, null=True, upload_to='course_syllabi/'),
        ),
        migrations.AddField(
            model_name='courseoffering',
            name='assistant_instructors',
            field=models.ManyToManyField(blank=True, related_name='assisted_courses', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='courseoffering',
            name='classroom',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='courseoffering',
            name='created_at',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name='courseoffering',
            name='current_students',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='courseoffering',
            name='end_time',
            field=models.TimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='courseoffering',
            name='grade_level',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='course_offerings', to='academics.gradelevel'),
        ),
        migrations.AddField(
            model_name='courseoffering',
            name='min_students',
            field=models.PositiveIntegerField(default=5),
        ),
        migrations.AddField(
            model_name='courseoffering',
            name='notes',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='courseoffering',
            name='schedule_days',
            field=models.CharField(blank=True, help_text="Days of the week (e.g., 'Mon,Wed,Fri')", max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='courseoffering',
            name='section',
            field=models.CharField(default='A', help_text='Section identifier (A, B, C, etc.)', max_length=10),
        ),
        migrations.AddField(
            model_name='courseoffering',
            name='start_time',
            field=models.TimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='courseoffering',
            name='status',
            field=models.CharField(choices=[('SCHEDULED', 'Scheduled'), ('IN_PROGRESS', 'In Progress'), ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled')], default='SCHEDULED', max_length=20),
        ),
        migrations.AddField(
            model_name='courseoffering',
            name='syllabus_url',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='courseoffering',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, null=True),
        ),
        migrations.AddField(
            model_name='department',
            name='contact_email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='department',
            name='contact_phone',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='department',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='department',
            name='office_location',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='department',
            name='website',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='enrollment',
            name='approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_enrollments', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='enrollment',
            name='comments',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='enrollment',
            name='completion_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='enrollment',
            name='created_at',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name='enrollment',
            name='grade_points',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=3, null=True),
        ),
        migrations.AddField(
            model_name='enrollment',
            name='is_repeat',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='enrollment',
            name='last_attendance_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='enrollment',
            name='numeric_grade',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)]),
        ),
        migrations.AddField(
            model_name='enrollment',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, null=True),
        ),
        migrations.AlterField(
            model_name='academicterm',
            name='term',
            field=models.CharField(choices=[('FIRST', 'First Term'), ('SECOND', 'Second Term'), ('THIRD', 'Third Term'), ('FALL', 'Fall'), ('SPRING', 'Spring'), ('SUMMER', 'Summer'), ('WINTER', 'Winter')], max_length=10),
        ),
        migrations.AlterField(
            model_name='course',
            name='created_at',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name='course',
            name='prerequisites',
            field=models.ManyToManyField(blank=True, related_name='is_prerequisite_for', to='courses.course'),
        ),
        migrations.AlterField(
            model_name='course',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, null=True),
        ),
        migrations.AlterField(
            model_name='department',
            name='created_at',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name='department',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, null=True),
        ),
        migrations.AlterField(
            model_name='enrollment',
            name='grade',
            field=models.CharField(blank=True, choices=[('A+', 'A+'), ('A', 'A'), ('A-', 'A-'), ('B+', 'B+'), ('B', 'B'), ('B-', 'B-'), ('C+', 'C+'), ('C', 'C'), ('C-', 'C-'), ('D+', 'D+'), ('D', 'D'), ('D-', 'D-'), ('F', 'F'), ('I', 'Incomplete'), ('W', 'Withdrawn'), ('P', 'Pass'), ('NP', 'Not Pass')], max_length=2, null=True),
        ),
        migrations.AlterField(
            model_name='enrollment',
            name='status',
            field=models.CharField(choices=[('ENROLLED', 'Enrolled'), ('COMPLETED', 'Completed'), ('WITHDRAWN', 'Withdrawn'), ('FAILED', 'Failed'), ('INCOMPLETE', 'Incomplete'), ('AUDITING', 'Auditing')], default='ENROLLED', max_length=10),
        ),
        migrations.AlterUniqueTogether(
            name='courseoffering',
            unique_together={('course', 'term', 'section')},
        ),
        migrations.CreateModel(
            name='Assignment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('instructions', models.TextField(blank=True, null=True)),
                ('due_date', models.DateTimeField()),
                ('total_marks', models.PositiveIntegerField(default=100)),
                ('weight_percentage', models.DecimalField(decimal_places=2, default=0.0, max_digits=5, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('attachment', models.FileField(blank=True, null=True, upload_to='assignments/')),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('PUBLISHED', 'Published'), ('CLOSED', 'Closed'), ('GRADED', 'Graded')], default='DRAFT', max_length=10)),
                ('is_group_assignment', models.BooleanField(default=False)),
                ('allow_late_submission', models.BooleanField(default=False)),
                ('late_submission_deadline', models.DateTimeField(blank=True, null=True)),
                ('late_submission_penalty', models.DecimalField(decimal_places=2, default=0.0, max_digits=5, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('course_offering', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assignments', to='courses.courseoffering')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_assignments', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Assignment',
                'verbose_name_plural': 'Assignments',
                'ordering': ['course_offering', 'due_date'],
            },
        ),
        migrations.CreateModel(
            name='Resource',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True, null=True)),
                ('resource_type', models.CharField(choices=[('DOCUMENT', 'Document'), ('VIDEO', 'Video'), ('AUDIO', 'Audio'), ('LINK', 'External Link'), ('IMAGE', 'Image'), ('PRESENTATION', 'Presentation'), ('WORKSHEET', 'Worksheet'), ('QUIZ', 'Quiz'), ('OTHER', 'Other')], max_length=20)),
                ('file', models.FileField(blank=True, null=True, upload_to='course_resources/')),
                ('url', models.URLField(blank=True, null=True)),
                ('is_public', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='resources', to='courses.course')),
                ('course_offering', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resources', to='courses.courseoffering')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_resources', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Resource',
                'verbose_name_plural': 'Resources',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Subject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=10, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subjects', to='courses.department')),
            ],
            options={
                'verbose_name': 'Subject',
                'verbose_name_plural': 'Subjects',
                'ordering': ['department', 'name'],
            },
        ),
        migrations.AddField(
            model_name='course',
            name='subject',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='courses', to='courses.subject'),
        ),
        migrations.CreateModel(
            name='AssignmentSubmission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('submission_date', models.DateTimeField(auto_now_add=True)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('SUBMITTED', 'Submitted'), ('LATE', 'Late Submission'), ('GRADED', 'Graded'), ('RETURNED', 'Returned')], default='DRAFT', max_length=10)),
                ('submission_text', models.TextField(blank=True, null=True)),
                ('attachment', models.FileField(blank=True, null=True, upload_to='assignment_submissions/')),
                ('is_late', models.BooleanField(default=False)),
                ('marks_obtained', models.DecimalField(blank=True, decimal_places=2, max_digits=7, null=True)),
                ('feedback', models.TextField(blank=True, null=True)),
                ('graded_at', models.DateTimeField(blank=True, null=True)),
                ('assignment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='submissions', to='courses.assignment')),
                ('graded_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='graded_submissions', to=settings.AUTH_USER_MODEL)),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assignment_submissions', to='students.student')),
            ],
            options={
                'verbose_name': 'Assignment Submission',
                'verbose_name_plural': 'Assignment Submissions',
                'ordering': ['-submission_date'],
                'unique_together': {('assignment', 'student')},
            },
        ),
        migrations.CreateModel(
            name='LessonPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True, null=True)),
                ('lesson_number', models.PositiveIntegerField()),
                ('lesson_date', models.DateField()),
                ('duration_minutes', models.PositiveIntegerField(default=60)),
                ('learning_objectives', models.TextField(blank=True, null=True)),
                ('materials_required', models.TextField(blank=True, null=True)),
                ('teaching_methods', models.TextField(blank=True, null=True)),
                ('assessment_methods', models.TextField(blank=True, null=True)),
                ('homework_assignment', models.TextField(blank=True, null=True)),
                ('attachments', models.FileField(blank=True, null=True, upload_to='lesson_plans/')),
                ('is_published', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('course_offering', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lesson_plans', to='courses.courseoffering')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_lesson_plans', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Lesson Plan',
                'verbose_name_plural': 'Lesson Plans',
                'ordering': ['course_offering', 'lesson_number'],
                'unique_together': {('course_offering', 'lesson_number')},
            },
        ),
    ]
