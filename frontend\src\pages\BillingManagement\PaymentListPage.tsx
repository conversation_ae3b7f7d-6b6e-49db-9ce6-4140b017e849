import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useToast } from '../../components/ToastProvider'
import {
  Box,
  Typography,
  Paper,
  LinearProgress,
  Alert,
} from '@mui/material'
import BillingManagementLayout from '../../components/Layout/BillingManagementLayout'

const PaymentListPage = () => {
  const navigate = useNavigate()
  const toast = useToast()
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    setTimeout(() => setLoading(false), 1000)
  }, [])

  if (loading) {
    return (
      <BillingManagementLayout
        title="Payments"
        subtitle="Loading payments..."
        breadcrumbs={[{ label: 'Payments' }]}
      >
        <LinearProgress />
      </BillingManagementLayout>
    )
  }

  return (
    <BillingManagementLayout
      title="Payments"
      subtitle="Track and manage all payments"
      breadcrumbs={[{ label: 'Payments' }]}
    >
      <Paper sx={{ p: 4, textAlign: 'center' }}>
        <Alert severity="info" sx={{ mb: 3 }}>
          Payment management functionality will be implemented here.
        </Alert>
        <Typography variant="h6" color="text.secondary">
          Coming Soon: Payment Management
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          This page will contain payment history, transaction details, and payment processing.
        </Typography>
      </Paper>
    </BillingManagementLayout>
  )
}

export default PaymentListPage
