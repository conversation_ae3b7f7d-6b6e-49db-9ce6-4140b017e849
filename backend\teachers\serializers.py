from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import Teacher, TeacherQualification, TeacherAssignment
from courses.models import Department, CourseOffering

User = get_user_model()

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name']
        read_only_fields = ['id', 'username']

class DepartmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Department
        fields = ['id', 'name', 'code']
        read_only_fields = ['id']

class TeacherSerializer(serializers.ModelSerializer):
    user_details = serializers.SerializerMethodField()
    department_name = serializers.SerializerMethodField()
    full_name = serializers.SerializerMethodField()

    class Meta:
        model = Teacher
        fields = [
            'id', 'user', 'user_details', 'full_name', 'employee_id',
            'department', 'department_name', 'employment_status',
            'date_joined', 'qualification', 'specialization', 'is_active'
        ]
        read_only_fields = ['id']

    def get_user_details(self, obj):
        return {
            'first_name': obj.user.first_name,
            'last_name': obj.user.last_name,
            'email': obj.user.email
        }

    def get_department_name(self, obj):
        return obj.department.name if obj.department else None

    def get_full_name(self, obj):
        return f"{obj.user.first_name} {obj.user.last_name}"

class TeacherDetailSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    department = DepartmentSerializer(read_only=True)
    qualifications = serializers.SerializerMethodField()
    assignments = serializers.SerializerMethodField()

    class Meta:
        model = Teacher
        fields = [
            'id', 'user', 'employee_id', 'department', 'date_of_birth',
            'date_joined', 'employment_status', 'qualification', 'specialization',
            'bio', 'photo', 'is_active', 'qualifications', 'assignments'
        ]
        read_only_fields = ['id', 'employee_id']

    def get_qualifications(self, obj):
        qualifications = TeacherQualification.objects.filter(teacher=obj)
        return QualificationSerializer(qualifications, many=True).data

    def get_assignments(self, obj):
        assignments = TeacherAssignment.objects.filter(teacher=obj)
        return TeacherAssignmentListSerializer(assignments, many=True).data

class QualificationSerializer(serializers.ModelSerializer):
    teacher_name = serializers.SerializerMethodField()

    class Meta:
        model = TeacherQualification
        fields = [
            'id', 'teacher', 'teacher_name', 'qualification_type', 'title',
            'institution', 'year_obtained', 'description', 'document'
        ]
        read_only_fields = ['id']

    def get_teacher_name(self, obj):
        return f"{obj.teacher.user.first_name} {obj.teacher.user.last_name}"

class CourseOfferingSerializer(serializers.ModelSerializer):
    course_name = serializers.SerializerMethodField()
    term_name = serializers.SerializerMethodField()

    class Meta:
        model = CourseOffering
        fields = ['id', 'course', 'course_name', 'term', 'term_name', 'section', 'status']
        read_only_fields = ['id']

    def get_course_name(self, obj):
        return obj.course.name if obj.course else None

    def get_term_name(self, obj):
        return obj.term.name if obj.term else None

class TeacherAssignmentListSerializer(serializers.ModelSerializer):
    teacher_name = serializers.SerializerMethodField()
    course_offering_details = serializers.SerializerMethodField()
    role_display = serializers.SerializerMethodField()

    class Meta:
        model = TeacherAssignment
        fields = [
            'id', 'teacher', 'teacher_name', 'course_offering',
            'course_offering_details', 'role', 'role_display',
            'assigned_date', 'is_active'
        ]
        read_only_fields = ['id']

    def get_teacher_name(self, obj):
        return f"{obj.teacher.user.first_name} {obj.teacher.user.last_name}"

    def get_course_offering_details(self, obj):
        return {
            'course_name': obj.course_offering.course.name,
            'course_code': obj.course_offering.course.code,
            'term': obj.course_offering.term.name,
            'section': obj.course_offering.section
        }

    def get_role_display(self, obj):
        return obj.get_role_display()

class TeacherAssignmentSerializer(serializers.ModelSerializer):
    teacher_details = serializers.SerializerMethodField()
    course_offering_details = CourseOfferingSerializer(source='course_offering', read_only=True)
    role_display = serializers.SerializerMethodField()

    class Meta:
        model = TeacherAssignment
        fields = [
            'id', 'teacher', 'teacher_details', 'course_offering',
            'course_offering_details', 'role', 'role_display',
            'assigned_date', 'is_active', 'hours_per_week', 'notes'
        ]
        read_only_fields = ['id']

    def get_teacher_details(self, obj):
        return {
            'name': f"{obj.teacher.user.first_name} {obj.teacher.user.last_name}",
            'employee_id': obj.teacher.employee_id,
            'department': obj.teacher.department.name if obj.teacher.department else None
        }

    def get_role_display(self, obj):
        return obj.get_role_display()
