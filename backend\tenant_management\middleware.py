from django.http import HttpResponseForbidden
from django.shortcuts import redirect
from django.urls import reverse

class SuperAdminAccessMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        if request.path.startswith('/tenant-management/'):
            if not request.user.is_authenticated:
                return redirect(reverse('login'))
            if not request.user.is_superuser:
                return HttpResponseForbidden("Access denied. Super admin privileges required.")
        return self.get_response(request)