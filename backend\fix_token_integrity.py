import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kelem_sms.settings')
django.setup()

from django.db import connection, transaction
from django_tenants.utils import schema_context, get_tenant_model
from rest_framework.authtoken.models import Token
from django.contrib.auth import get_user_model

User = get_user_model()
TenantModel = get_tenant_model()

def fix_token_integrity():
    """Fix integrity issues with authentication tokens."""
    print("Starting token integrity fix...")
    
    # Check for the specific user with the integrity error
    email = "<EMAIL>"
    print(f"Checking tokens for user: {email}")
    
    # Check in public schema
    try:
        user = User.objects.filter(email=email).first()
        if user:
            print(f"Found user in public schema: {user.email} (ID: {user.id})")
            
            # Check for tokens
            tokens = Token.objects.filter(user=user)
            print(f"Found {tokens.count()} tokens for user in public schema")
            
            if tokens.count() > 1:
                print("Deleting duplicate tokens...")
                # Keep only the newest token
                newest_token = tokens.order_by('-created').first()
                for token in tokens:
                    if token != newest_token:
                        print(f"Deleting token: {token.key[:10]}...")
                        token.delete()
            
            # Verify the token
            try:
                token = Token.objects.get(user=user)
                print(f"User now has token: {token.key[:10]}...")
            except Token.DoesNotExist:
                token = Token.objects.create(user=user)
                print(f"Created new token: {token.key[:10]}...")
            except Exception as e:
                print(f"Error verifying token: {str(e)}")
    except Exception as e:
        print(f"Error checking public schema: {str(e)}")
    
    # Check in all tenant schemas
    for tenant in TenantModel.objects.all():
        print(f"Checking schema {tenant.schema_name}...")
        try:
            with schema_context(tenant.schema_name):
                user = User.objects.filter(email=email).first()
                if user:
                    print(f"Found user in schema {tenant.schema_name}: {user.email} (ID: {user.id})")
                    
                    # Check for tokens
                    tokens = Token.objects.filter(user=user)
                    print(f"Found {tokens.count()} tokens for user in schema {tenant.schema_name}")
                    
                    if tokens.count() > 1:
                        print("Deleting duplicate tokens...")
                        # Keep only the newest token
                        newest_token = tokens.order_by('-created').first()
                        for token in tokens:
                            if token != newest_token:
                                print(f"Deleting token: {token.key[:10]}...")
                                token.delete()
                    
                    # Verify the token
                    try:
                        token = Token.objects.get(user=user)
                        print(f"User now has token: {token.key[:10]}...")
                    except Token.DoesNotExist:
                        token = Token.objects.create(user=user)
                        print(f"Created new token: {token.key[:10]}...")
                    except Exception as e:
                        print(f"Error verifying token: {str(e)}")
        except Exception as e:
            print(f"Error checking schema {tenant.schema_name}: {str(e)}")
    
    print("Token integrity fix completed.")

if __name__ == '__main__':
    fix_token_integrity()
