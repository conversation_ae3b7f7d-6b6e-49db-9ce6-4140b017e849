from django.db import migrations

class Migration(migrations.Migration):
    """
    This migration fixes the foreign key constraint issue with django_admin_log.
    It updates the user_id references to point to the correct user table.
    """

    dependencies = [
        ('tenant_management', '0001_initial'),
        ('admin', '0003_logentry_add_action_flag_choices'),
        ('authentication', '0001_initial'),
    ]

    operations = [
        migrations.RunSQL(
            # Forward SQL - Update the foreign key constraint
            """
            -- First, delete any admin log entries that reference non-existent users
            DELETE FROM django_admin_log WHERE user_id NOT IN (SELECT id FROM authentication_user);

            -- Then drop the existing constraint if it exists
            ALTER TABLE django_admin_log
            DROP CONSTRAINT IF EXISTS django_admin_log_user_id_c564eba6_fk_auth_user_id;

            -- Add the new constraint
            ALTER TABLE django_admin_log
            ADD CONSTRAINT django_admin_log_user_id_fk
            FOREIGN KEY (user_id) REFERENCES authentication_user(id)
            ON DELETE CASCADE;
            """,

            # Reverse SQL - Restore the original constraint
            """
            ALTER TABLE django_admin_log
            DROP CONSTRAINT IF EXISTS django_admin_log_user_id_fk;

            ALTER TABLE django_admin_log
            ADD CONSTRAINT django_admin_log_user_id_c564eba6_fk_auth_user_id
            FOREIGN KEY (user_id) REFERENCES auth_user(id)
            ON DELETE CASCADE;
            """
        ),
    ]
