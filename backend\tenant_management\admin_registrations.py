"""
Register models with the custom admin site.
This file should be imported after all admin classes are defined.
"""
from .admin import TenantMetricsAdmin, TenantActivityAdmin, TenantStatusAdmin
from .models import TenantMetrics, TenantActivity, TenantStatus
from .admin_site import tenant_admin_site

# Register models with the custom admin site
tenant_admin_site.register(TenantMetrics, TenantMetricsAdmin)
tenant_admin_site.register(TenantActivity, TenantActivityAdmin)
tenant_admin_site.register(TenantStatus, TenantStatusAdmin)
