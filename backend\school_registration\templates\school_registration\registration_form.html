{% extends "base.html" %}

{% block title %}School Registration{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row">
        <div class="col-md-10 offset-md-1">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3>School Registration</h3>
                </div>
                <div class="card-body">
                    <p class="lead mb-4">Register your school/institution to use the Kelem Student Management System.</p>

                    <form id="school-registration-form" method="post">
                        {% csrf_token %}

                        <h4 class="mb-3">School Information</h4>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">School Name *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            <div class="col-md-6">
                                <label for="established_year" class="form-label">Established Year *</label>
                                <input type="number" class="form-control" id="established_year" name="established_year" min="1800" max="2025" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="school_type" class="form-label">School Type *</label>
                                <select class="form-select" id="school_type" name="school_type" required>
                                    <option value="">Select School Type</option>
                                    <option value="Primary School">Primary School</option>
                                    <option value="Secondary School">Secondary School</option>
                                    <option value="High School">High School</option>
                                    <option value="College">College</option>
                                    <option value="University">University</option>
                                    <option value="Technical Institute">Technical Institute</option>
                                    <option value="Vocational School">Vocational School</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="student_capacity" class="form-label">Student Capacity *</label>
                                <input type="number" class="form-control" id="student_capacity" name="student_capacity" min="1" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">School Description *</label>
                            <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                        </div>

                        <h4 class="mb-3 mt-4">Address Information</h4>
                        <div class="mb-3">
                            <label for="address" class="form-label">Address *</label>
                            <textarea class="form-control" id="address" name="address" rows="2" required></textarea>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="city" class="form-label">City *</label>
                                <input type="text" class="form-control" id="city" name="city" required>
                            </div>
                            <div class="col-md-4">
                                <label for="state" class="form-label">State/Region *</label>
                                <input type="text" class="form-control" id="state" name="state" required>
                            </div>
                            <div class="col-md-4">
                                <label for="postal_code" class="form-label">Postal Code</label>
                                <input type="text" class="form-control" id="postal_code" name="postal_code">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="country" class="form-label">Country *</label>
                            <input type="text" class="form-control" id="country" name="country" value="Ethiopia" required>
                        </div>

                        <h4 class="mb-3 mt-4">Contact Information</h4>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="contact_person" class="form-label">Contact Person *</label>
                                <input type="text" class="form-control" id="contact_person" name="contact_person" required>
                            </div>
                            <div class="col-md-6">
                                <label for="contact_email" class="form-label">Contact Email *</label>
                                <input type="email" class="form-control" id="contact_email" name="contact_email" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="contact_phone" class="form-label">Contact Phone *</label>
                                <input type="tel" class="form-control" id="contact_phone" name="contact_phone" placeholder="+251..." required>
                            </div>
                            <div class="col-md-6">
                                <label for="website" class="form-label">Website</label>
                                <input type="url" class="form-control" id="website" name="website" placeholder="https://...">
                            </div>
                        </div>

                        <div class="alert alert-info mt-4">
                            <p class="mb-0"><strong>Note:</strong> Your school will be automatically registered and a tenant will be created for you immediately.</p>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <button type="reset" class="btn btn-secondary me-md-2">Reset</button>
                            <button type="submit" class="btn btn-primary">Submit Registration</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('school-registration-form');

        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(form);
            const data = {};

            for (const [key, value] of formData.entries()) {
                data[key] = value;
            }

            // Remove CSRF token from data
            delete data['csrfmiddlewaretoken'];

            fetch('/schools/api/schools/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': formData.get('csrfmiddlewaretoken')
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.message) {
                    // Store tenant information in localStorage to display on success page
                    if (data.tenant) {
                        localStorage.setItem('schoolName', data.data.name);
                        localStorage.setItem('schoolDomain', data.tenant.domain);
                        localStorage.setItem('schoolSchema', data.tenant.schema_name);
                    }
                    window.location.href = '/schools/register/success/';
                } else {
                    alert('There was an error with your submission. Please check the form and try again.');
                    console.error(data);
                }
            })
            .catch(error => {
                alert('There was an error with your submission. Please try again later.');
                console.error('Error:', error);
            });
        });
    });
</script>
{% endblock %}
{% endblock %}
