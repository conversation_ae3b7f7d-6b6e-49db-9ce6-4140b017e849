from django.contrib import admin
from .models import Department, Course, AcademicTerm, CourseOffering, Enrollment

@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'head')
    search_fields = ('name', 'code')

class PrerequisiteInline(admin.TabularInline):
    model = Course.prerequisites.through
    fk_name = 'from_course'
    verbose_name = 'Prerequisite'
    verbose_name_plural = 'Prerequisites'
    extra = 1

@admin.register(Course)
class CourseAdmin(admin.ModelAdmin):
    list_display = ('code', 'name', 'department', 'credits')
    list_filter = ('department', 'credits')
    search_fields = ('name', 'code', 'description')
    inlines = [PrerequisiteInline]
    exclude = ('prerequisites',)

@admin.register(AcademicTerm)
class AcademicTermAdmin(admin.ModelAdmin):
    list_display = ('name', 'term', 'year', 'start_date', 'end_date', 'is_active')
    list_filter = ('term', 'year', 'is_active')
    search_fields = ('name',)

@admin.register(CourseOffering)
class CourseOfferingAdmin(admin.ModelAdmin):
    list_display = ('course', 'term', 'instructor', 'start_date', 'end_date', 'is_active')
    list_filter = ('term', 'is_active')
    search_fields = ('course__name', 'course__code', 'instructor__username')

class EnrollmentInline(admin.TabularInline):
    model = Enrollment
    extra = 1

@admin.register(Enrollment)
class EnrollmentAdmin(admin.ModelAdmin):
    list_display = ('student', 'course_offering', 'enrollment_date', 'status', 'grade')
    list_filter = ('status', 'enrollment_date', 'course_offering__term')
    search_fields = ('student__first_name', 'student__last_name', 'student__student_id', 'course_offering__course__name')
