{% extends "base.html" %}

{% block title %}Tenant Management Dashboard{% endblock %}

{% block extra_css %}
<style>
    .dashboard-card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s;
    }
    .dashboard-card:hover {
        transform: translateY(-5px);
    }
    .stat-card {
        text-align: center;
        padding: 20px;
    }
    .stat-value {
        font-size: 2.5rem;
        font-weight: bold;
    }
    .stat-label {
        font-size: 1rem;
        color: #6c757d;
    }
    .chart-container {
        height: 300px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row mb-4">
        <div class="col-md-12">
            <h1 class="mb-3">Tenant Management Dashboard</h1>
            <p class="lead">Monitor and manage all tenants in the system.</p>
            
            <div class="d-flex justify-content-end mb-3">
                <button id="refreshDashboard" class="btn btn-primary me-2">
                    <i class="bi bi-arrow-clockwise"></i> Refresh Dashboard
                </button>
                <button id="collectAllMetrics" class="btn btn-success">
                    <i class="bi bi-graph-up"></i> Collect All Metrics
                </button>
            </div>
        </div>
    </div>
    
    <!-- Tenant Summary Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card dashboard-card stat-card bg-primary text-white">
                <div class="card-body">
                    <div class="stat-value" id="totalTenants">--</div>
                    <div class="stat-label">Total Tenants</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card dashboard-card stat-card bg-success text-white">
                <div class="card-body">
                    <div class="stat-value" id="activeTenants">--</div>
                    <div class="stat-label">Active Tenants</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card dashboard-card stat-card bg-warning text-white">
                <div class="card-body">
                    <div class="stat-value" id="totalUsers">--</div>
                    <div class="stat-label">Total Users</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card dashboard-card stat-card bg-info text-white">
                <div class="card-body">
                    <div class="stat-value" id="totalStorage">--</div>
                    <div class="stat-label">Total Storage</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Tenant Status -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Tenant Status</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="tenantStatusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">System Resources</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>CPU Usage</h6>
                            <div class="progress mb-3" style="height: 25px;">
                                <div id="cpuProgress" class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>Memory Usage</h6>
                            <div class="progress mb-3" style="height: 25px;">
                                <div id="memoryProgress" class="progress-bar bg-success" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Disk Usage</h6>
                            <div class="progress mb-3" style="height: 25px;">
                                <div id="diskProgress" class="progress-bar bg-warning" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>Database Connections</h6>
                            <div id="dbConnections" class="h4">--</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Top Tenants -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Top Tenants by Users</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="topTenantsByUsersChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card dashboard-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Top Tenants by Storage</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="topTenantsByStorageChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Activities -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card dashboard-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Recent Activities</h5>
                    <a href="{% url 'tenant_management:tenant_list' %}" class="btn btn-sm btn-primary">View All Tenants</a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Tenant</th>
                                    <th>Activity</th>
                                    <th>Description</th>
                                    <th>Timestamp</th>
                                </tr>
                            </thead>
                            <tbody id="recentActivitiesTable">
                                <tr>
                                    <td colspan="4" class="text-center">Loading activities...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Charts
    let tenantStatusChart;
    let topTenantsByUsersChart;
    let topTenantsByStorageChart;
    
    // Function to format bytes to human-readable format
    function formatBytes(bytes, decimals = 2) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const dm = decimals < 0 ? 0 : decimals;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
        
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
    }
    
    // Function to format date
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString();
    }
    
    // Function to load dashboard data
    function loadDashboardData() {
        fetch('/tenant-management/api/dashboard/')
            .then(response => response.json())
            .then(data => {
                // Update summary stats
                document.getElementById('totalTenants').textContent = data.tenant_summary.total_tenants;
                document.getElementById('activeTenants').textContent = data.tenant_summary.active_tenants;
                document.getElementById('totalUsers').textContent = data.tenant_summary.total_users;
                document.getElementById('totalStorage').textContent = formatBytes(data.tenant_summary.total_storage);
                
                // Update system metrics
                if (data.system_metrics) {
                    const cpuPercent = data.system_metrics.cpu_percent || 0;
                    const memoryPercent = data.system_metrics.memory_percent || 0;
                    const diskPercent = data.system_metrics.disk_percent || 0;
                    const dbConnections = data.system_metrics.db_connections || 0;
                    
                    const cpuProgress = document.getElementById('cpuProgress');
                    cpuProgress.style.width = cpuPercent + '%';
                    cpuProgress.textContent = cpuPercent + '%';
                    cpuProgress.setAttribute('aria-valuenow', cpuPercent);
                    
                    const memoryProgress = document.getElementById('memoryProgress');
                    memoryProgress.style.width = memoryPercent + '%';
                    memoryProgress.textContent = memoryPercent + '%';
                    memoryProgress.setAttribute('aria-valuenow', memoryPercent);
                    
                    const diskProgress = document.getElementById('diskProgress');
                    diskProgress.style.width = diskPercent + '%';
                    diskProgress.textContent = diskPercent + '%';
                    diskProgress.setAttribute('aria-valuenow', diskPercent);
                    
                    document.getElementById('dbConnections').textContent = dbConnections;
                }
                
                // Update tenant status chart
                const statusLabels = ['Active', 'Suspended', 'Trial', 'Expired'];
                const statusData = [
                    data.tenant_summary.active_tenants,
                    data.tenant_summary.suspended_tenants,
                    data.tenant_summary.trial_tenants,
                    data.tenant_summary.expired_tenants
                ];
                
                if (tenantStatusChart) {
                    tenantStatusChart.data.datasets[0].data = statusData;
                    tenantStatusChart.update();
                } else {
                    const statusCtx = document.getElementById('tenantStatusChart').getContext('2d');
                    tenantStatusChart = new Chart(statusCtx, {
                        type: 'pie',
                        data: {
                            labels: statusLabels,
                            datasets: [{
                                data: statusData,
                                backgroundColor: [
                                    '#28a745', // Active - Green
                                    '#dc3545', // Suspended - Red
                                    '#ffc107', // Trial - Yellow
                                    '#6c757d'  // Expired - Gray
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false
                        }
                    });
                }
                
                // Update top tenants by users chart
                if (data.tenant_summary.top_tenants_by_users && data.tenant_summary.top_tenants_by_users.length > 0) {
                    // Fetch tenant names for the IDs
                    const tenantIds = data.tenant_summary.top_tenants_by_users.map(item => item[0]);
                    
                    // For simplicity, we'll use the IDs as labels for now
                    // In a real implementation, you would fetch the tenant names
                    const userLabels = tenantIds.map(id => `Tenant ${id}`);
                    const userData = data.tenant_summary.top_tenants_by_users.map(item => item[1]);
                    
                    if (topTenantsByUsersChart) {
                        topTenantsByUsersChart.data.labels = userLabels;
                        topTenantsByUsersChart.data.datasets[0].data = userData;
                        topTenantsByUsersChart.update();
                    } else {
                        const usersCtx = document.getElementById('topTenantsByUsersChart').getContext('2d');
                        topTenantsByUsersChart = new Chart(usersCtx, {
                            type: 'bar',
                            data: {
                                labels: userLabels,
                                datasets: [{
                                    label: 'Number of Users',
                                    data: userData,
                                    backgroundColor: '#007bff',
                                    borderWidth: 1
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                scales: {
                                    y: {
                                        beginAtZero: true
                                    }
                                }
                            }
                        });
                    }
                }
                
                // Update top tenants by storage chart
                if (data.tenant_summary.top_tenants_by_storage && data.tenant_summary.top_tenants_by_storage.length > 0) {
                    // Fetch tenant names for the IDs
                    const tenantIds = data.tenant_summary.top_tenants_by_storage.map(item => item[0]);
                    
                    // For simplicity, we'll use the IDs as labels for now
                    const storageLabels = tenantIds.map(id => `Tenant ${id}`);
                    const storageData = data.tenant_summary.top_tenants_by_storage.map(item => item[1] / (1024 * 1024)); // Convert to MB
                    
                    if (topTenantsByStorageChart) {
                        topTenantsByStorageChart.data.labels = storageLabels;
                        topTenantsByStorageChart.data.datasets[0].data = storageData;
                        topTenantsByStorageChart.update();
                    } else {
                        const storageCtx = document.getElementById('topTenantsByStorageChart').getContext('2d');
                        topTenantsByStorageChart = new Chart(storageCtx, {
                            type: 'bar',
                            data: {
                                labels: storageLabels,
                                datasets: [{
                                    label: 'Storage Used (MB)',
                                    data: storageData,
                                    backgroundColor: '#17a2b8',
                                    borderWidth: 1
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                scales: {
                                    y: {
                                        beginAtZero: true
                                    }
                                }
                            }
                        });
                    }
                }
                
                // Update recent activities table
                if (data.recent_activities && data.recent_activities.length > 0) {
                    const activitiesTable = document.getElementById('recentActivitiesTable');
                    activitiesTable.innerHTML = '';
                    
                    data.recent_activities.forEach(activity => {
                        const row = document.createElement('tr');
                        
                        const tenantCell = document.createElement('td');
                        tenantCell.innerHTML = `<a href="/tenant-management/tenants/${activity.tenant}/">${activity.tenant_name}</a>`;
                        
                        const activityTypeCell = document.createElement('td');
                        activityTypeCell.textContent = activity.activity_type;
                        
                        const descriptionCell = document.createElement('td');
                        descriptionCell.textContent = activity.description;
                        
                        const timestampCell = document.createElement('td');
                        timestampCell.textContent = formatDate(activity.timestamp);
                        
                        row.appendChild(tenantCell);
                        row.appendChild(activityTypeCell);
                        row.appendChild(descriptionCell);
                        row.appendChild(timestampCell);
                        
                        activitiesTable.appendChild(row);
                    });
                } else {
                    document.getElementById('recentActivitiesTable').innerHTML = '<tr><td colspan="4" class="text-center">No recent activities</td></tr>';
                }
            })
            .catch(error => {
                console.error('Error loading dashboard data:', error);
            });
    }
    
    // Function to collect metrics for all tenants
    function collectAllMetrics() {
        const button = document.getElementById('collectAllMetrics');
        button.disabled = true;
        button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Collecting...';
        
        fetch('/tenant-management/api/collect-metrics/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);
            loadDashboardData();
        })
        .catch(error => {
            console.error('Error collecting metrics:', error);
            alert('Error collecting metrics. Please try again.');
        })
        .finally(() => {
            button.disabled = false;
            button.innerHTML = '<i class="bi bi-graph-up"></i> Collect All Metrics';
        });
    }
    
    // Function to get CSRF token from cookies
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
    
    // Event listeners
    document.addEventListener('DOMContentLoaded', function() {
        // Load dashboard data on page load
        loadDashboardData();
        
        // Refresh dashboard button
        document.getElementById('refreshDashboard').addEventListener('click', loadDashboardData);
        
        // Collect all metrics button
        document.getElementById('collectAllMetrics').addEventListener('click', collectAllMetrics);
    });
</script>
{% endblock %}
