# Generated by Django 5.1.7 on 2025-04-09 14:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tenant_management', '0002_fix_admin_log_fk'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='tenantstatus',
            options={'ordering': ['-last_updated'], 'verbose_name': 'Tenant Status', 'verbose_name_plural': 'Tenant Statuses'},
        ),
        migrations.AlterField(
            model_name='tenantstatus',
            name='status',
            field=models.CharField(choices=[('active', 'Active'), ('suspended', 'Suspended'), ('maintenance', 'Maintenance'), ('trial', 'Trial'), ('expired', 'Expired'), ('archived', 'Archived')], default='active', max_length=20),
        ),
    ]
