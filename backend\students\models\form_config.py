from django.db import models
from tenants.models import School

class FormFieldType(models.TextChoices):
    TEXT = 'text', 'Text'
    NUMBER = 'number', 'Number'
    EMAIL = 'email', 'Email'
    PHONE = 'phone', 'Phone'
    DATE = 'date', 'Date'
    SELECT = 'select', 'Select'
    TEXTAREA = 'textarea', 'Text Area'
    CHECKBOX = 'checkbox', 'Checkbox'
    RADIO = 'radio', 'Radio'
    FILE = 'file', 'File Upload'

class FormFieldStatus(models.TextChoices):
    REQUIRED = 'required', 'Required'
    OPTIONAL = 'optional', 'Optional'
    HIDDEN = 'hidden', 'Hidden'

class FormSection(models.TextChoices):
    PERSONAL = 'personal', 'Personal Information'
    CONTACT = 'contact', 'Contact Information'
    ACADEMIC = 'academic', 'Academic Information'
    GUARDIAN = 'guardian', 'Guardian Information'
    HEALTH = 'health', 'Health Information'
    EMERGENCY = 'emergency', 'Emergency Contact'
    DOCUMENTS = 'documents', 'Documents'
    OTHER = 'other', 'Other Information'

class StudentFormConfig(models.Model):
    """Configuration for student registration form fields per tenant."""
    tenant = models.ForeignKey(School, on_delete=models.CASCADE, related_name='student_form_configs')
    field_name = models.CharField(max_length=100, help_text="Field name in the API")
    display_name = models.CharField(max_length=100, help_text="Display name shown to users")
    field_type = models.CharField(max_length=20, choices=FormFieldType.choices)
    section = models.CharField(max_length=20, choices=FormSection.choices)
    status = models.CharField(max_length=20, choices=FormFieldStatus.choices, default=FormFieldStatus.OPTIONAL)
    order = models.PositiveSmallIntegerField(default=0, help_text="Display order within section")
    options = models.JSONField(null=True, blank=True, help_text="Options for select, radio, etc.")
    help_text = models.CharField(max_length=255, blank=True, null=True)
    default_value = models.CharField(max_length=255, blank=True, null=True)
    validation_regex = models.CharField(max_length=255, blank=True, null=True, help_text="Regex pattern for validation")
    is_custom = models.BooleanField(default=False, help_text="Whether this is a custom field")
    
    class Meta:
        unique_together = ('tenant', 'field_name')
        ordering = ['section', 'order']
    
    def __str__(self):
        return f"{self.tenant.name} - {self.display_name}"
