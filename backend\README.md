# Kelem Student Management System - Backend

This is the backend for the Kelem Student Management System, a tenant-based student management system for schools.

## Setup

1. Create a virtual environment and install the dependencies:

```bash
python -m venv venv
venv\Scripts\activate
pip install -r requirements.txt
```

2. Create a PostgreSQL database:

```bash
createdb kelem_sms
```

3. Apply migrations to the public schema:

```bash
python manage.py migrate_schemas --shared
```

4. Create the public tenant:

```bash
python setup_tenant.py create_public
```

5. Create a superuser:

```bash
python manage.py createsuperuser
```

6. Run the development server:

```bash
python manage.py runserver
```

## Tenant Management

### Creating a New Tenant

You can create a new tenant (school) through the admin interface or the API.

### Setting Up a Tenant Schema

After creating a tenant, you need to set up the tenant schema and apply migrations:

```bash
python setup_tenant.py <tenant_schema_name>
```

For example:

```bash
python setup_tenant.py school1
```

To set up all tenant schemas:

```bash
python setup_tenant.py
```

### Troubleshooting

If you encounter issues with migrations, you can try the following:

1. Run the fix_circular_dependencies.py script to fix circular dependencies:

```bash
python fix_circular_dependencies.py
```

This script will:
- Check if the academics_gradelevel table exists in each tenant schema
- Create the necessary tables to resolve circular dependencies
- Mark the relevant migrations as applied

2. Run the fix_migrations.py script to fix common migration issues:

```bash
python fix_migrations.py
```

This script will:
- Check if the public schema exists and create it if needed
- Apply migrations to the public schema
- Check if tenant schemas exist and create them if needed
- Apply migrations to each tenant schema
- Fix specific issues with missing tables

3. Apply migrations to the public schema:

```bash
python manage.py migrate_schemas --shared
```

4. Apply migrations to a specific tenant:

```bash
python manage.py migrate_schemas --tenant=<tenant_schema_name>
```

5. Apply migrations for a specific app to a specific tenant:

```bash
python manage.py migrate <app_name> --schema=<tenant_schema_name>
```

6. Set up all tenant schemas:

```bash
python setup_tenant.py
```

## API Documentation

The API documentation is available at `/api/docs/` when the server is running.
