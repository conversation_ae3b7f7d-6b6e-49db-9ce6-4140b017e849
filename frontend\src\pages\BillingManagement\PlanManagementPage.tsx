import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useToast } from '../../components/ToastProvider'
import {
  Box,
  Paper,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
  Stack,
  Divider,
  Tooltip,
  Alert,
  CircularProgress,
  TextField,
  InputAdornment,
} from '@mui/material'
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Visibility as ViewIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  People as PeopleIcon,
  Storage as StorageIcon,
  Search as SearchIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  ContentCopy,
} from '@mui/icons-material'
import BillingManagementLayout from '../../components/Layout/BillingManagementLayout'
import {
  getPlans,
  deletePlan,
  updatePlan,
  SubscriptionPlan,
  formatCurrency
} from '../../services/billingService'

const PlanManagementPage = () => {
  const navigate = useNavigate()
  const toast = useToast()
  const [plans, setPlans] = useState<SubscriptionPlan[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterCategory, setFilterCategory] = useState('all')
  const [sortBy, setSortBy] = useState('sort_order')
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null)
  const [actionLoading, setActionLoading] = useState<string | null>(null)

  useEffect(() => {
    fetchPlans()
  }, [])

  const fetchPlans = async () => {
    try {
      setLoading(true)
      const plansData = await getPlans()
      // Ensure plansData is always an array
      setPlans(Array.isArray(plansData) ? plansData : [])
    } catch (error) {
      console.error('Error fetching plans:', error)
      toast.error('Failed to fetch subscription plans')
      // Set empty array on error to prevent filter issues
      setPlans([])
    } finally {
      setLoading(false)
    }
  }

  const handleDeletePlan = async () => {
    if (!selectedPlan) return

    try {
      setActionLoading('delete')
      await deletePlan(selectedPlan.id)
      toast.success('Plan deleted successfully')
      setDeleteDialogOpen(false)
      setSelectedPlan(null)
      fetchPlans()
    } catch (error) {
      toast.error('Failed to delete plan')
    } finally {
      setActionLoading(null)
    }
  }

  const handleToggleStatus = async (plan: SubscriptionPlan) => {
    try {
      setActionLoading(`status-${plan.id}`)
      await updatePlan(plan.id, { isActive: !plan.isActive })
      toast.success(`Plan ${plan.isActive ? 'deactivated' : 'activated'} successfully`)
      fetchPlans()
    } catch (error) {
      toast.error('Failed to update plan status')
    } finally {
      setActionLoading(null)
    }
  }

  const handleToggleFeatured = async (plan: SubscriptionPlan) => {
    try {
      setActionLoading(`featured-${plan.id}`)
      await updatePlan(plan.id, { isFeatured: !plan.isFeatured })
      toast.success(`Plan ${plan.isFeatured ? 'unfeatured' : 'featured'} successfully`)
      fetchPlans()
    } catch (error) {
      toast.error('Failed to update featured status')
    } finally {
      setActionLoading(null)
    }
  }

  const handleDuplicatePlan = (plan: SubscriptionPlan) => {
    navigate('/dashboard/billing-management/plans/new', {
      state: { duplicateFrom: plan }
    })
  }

  const openMenu = (event: React.MouseEvent<HTMLElement>, plan: SubscriptionPlan) => {
    setMenuAnchor(event.currentTarget)
    setSelectedPlan(plan)
  }

  const closeMenu = () => {
    setMenuAnchor(null)
    setSelectedPlan(null)
  }

  const openDeleteDialog = (plan: SubscriptionPlan) => {
    setSelectedPlan(plan)
    setDeleteDialogOpen(true)
    closeMenu()
  }

  const filteredAndSortedPlans = (Array.isArray(plans) ? plans : [])
    .filter(plan => {
      const matchesSearch = plan.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           plan.description.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesCategory = filterCategory === 'all' || plan.category === filterCategory
      return matchesSearch && matchesCategory
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name)
        case 'price':
          return a.price - b.price
        case 'category':
          return a.category.localeCompare(b.category)
        case 'created':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        default: // sort_order
          return a.sortOrder - b.sortOrder
      }
    })

  const getStorageDisplay = (bytes: number) => {
    const gb = bytes / (1024 * 1024 * 1024)
    return gb >= 1 ? `${gb.toFixed(0)} GB` : `${(bytes / (1024 * 1024)).toFixed(0)} MB`
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'basic': return 'default'
      case 'standard': return 'primary'
      case 'premium': return 'secondary'
      case 'enterprise': return 'warning'
      default: return 'default'
    }
  }

  return (
    <BillingManagementLayout
      title="Subscription Plans"
      subtitle="Manage subscription plans and pricing"
      showBreadcrumbs={false}
    >
      {/* Header Actions */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap', alignItems: 'center' }}>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => navigate('/dashboard/billing-management/plans/new')}
          sx={{
            background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
            boxShadow: '0 3px 5px 2px rgba(33, 203, 243, .3)',
          }}
        >
          Create Plan
        </Button>

        <TextField
          size="small"
          placeholder="Search plans..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ minWidth: 250 }}
        />

        <TextField
          select
          size="small"
          label="Category"
          value={filterCategory}
          onChange={(e) => setFilterCategory(e.target.value)}
          sx={{ minWidth: 120 }}
        >
          <MenuItem value="all">All Categories</MenuItem>
          <MenuItem value="basic">Basic</MenuItem>
          <MenuItem value="standard">Standard</MenuItem>
          <MenuItem value="premium">Premium</MenuItem>
          <MenuItem value="enterprise">Enterprise</MenuItem>
        </TextField>

        <TextField
          select
          size="small"
          label="Sort by"
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value)}
          sx={{ minWidth: 120 }}
        >
          <MenuItem value="sort_order">Sort Order</MenuItem>
          <MenuItem value="name">Name</MenuItem>
          <MenuItem value="price">Price</MenuItem>
          <MenuItem value="category">Category</MenuItem>
          <MenuItem value="created">Created Date</MenuItem>
        </TextField>
      </Box>

      {/* Plans Grid */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
          <CircularProgress />
        </Box>
      ) : filteredAndSortedPlans.length === 0 ? (
        <Paper sx={{ p: 8, textAlign: 'center' }}>
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No subscription plans found
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            {searchTerm || filterCategory !== 'all'
              ? 'Try adjusting your search or filter criteria'
              : 'Create your first subscription plan to get started'
            }
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => navigate('/dashboard/billing-management/plans/new')}
          >
            Create First Plan
          </Button>
        </Paper>
      ) : (
        <Grid container spacing={3}>
          {filteredAndSortedPlans.map((plan) => (
            <Grid item xs={12} sm={6} lg={4} key={plan.id}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  position: 'relative',
                  transition: 'all 0.2s',
                  '&:hover': {
                    boxShadow: 4,
                    transform: 'translateY(-2px)'
                  },
                  ...(plan.isFeatured && {
                    border: '2px solid',
                    borderColor: 'secondary.main',
                    boxShadow: 3
                  })
                }}
              >
                {/* Featured Badge */}
                {plan.isFeatured && (
                  <Chip
                    label="Featured"
                    color="secondary"
                    size="small"
                    sx={{
                      position: 'absolute',
                      top: 12,
                      right: 12,
                      zIndex: 1
                    }}
                  />
                )}

                <CardContent sx={{ flexGrow: 1, pb: 1 }}>
                  {/* Plan Header */}
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Box>
                      <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
                        {plan.name}
                      </Typography>
                      <Chip
                        label={plan.category}
                        color={getCategoryColor(plan.category) as any}
                        size="small"
                        variant="outlined"
                      />
                    </Box>
                    <IconButton
                      size="small"
                      onClick={(e) => openMenu(e, plan)}
                    >
                      <MoreVertIcon />
                    </IconButton>
                  </Box>

                  {/* Pricing */}
                  <Box sx={{ textAlign: 'center', mb: 3 }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main' }}>
                      {formatCurrency(plan.price, plan.currency)}
                      <Typography component="span" variant="body2" sx={{ ml: 1 }}>
                        /{plan.billingCycle === 'monthly' ? 'month' : 'year'}
                      </Typography>
                    </Typography>
                    {plan.setupFee > 0 && (
                      <Typography variant="body2" color="text.secondary">
                        + {formatCurrency(plan.setupFee, plan.currency)} setup fee
                      </Typography>
                    )}
                  </Box>

                  {/* Description */}
                  {plan.description && (
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {plan.description}
                    </Typography>
                  )}

                  {/* Plan Details */}
                  <Stack spacing={1} sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <PeopleIcon fontSize="small" color="primary" />
                      <Typography variant="body2">
                        Up to {plan.maxUsers.toLocaleString()} users
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <StorageIcon fontSize="small" color="primary" />
                      <Typography variant="body2">
                        {getStorageDisplay(plan.maxStorage)} storage
                      </Typography>
                    </Box>
                    {plan.trialDays > 0 && (
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <StarIcon fontSize="small" color="primary" />
                        <Typography variant="body2">
                          {plan.trialDays} days free trial
                        </Typography>
                      </Box>
                    )}
                  </Stack>

                  {/* Features */}
                  {plan.features && plan.features.length > 0 && (
                    <Box>
                      <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                        Features:
                      </Typography>
                      <Stack spacing={0.5}>
                        {plan.features.slice(0, 3).map((feature, index) => (
                          <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <CheckIcon fontSize="small" color="success" />
                            <Typography variant="body2">{feature.name}</Typography>
                          </Box>
                        ))}
                        {plan.features.length > 3 && (
                          <Typography variant="body2" color="text.secondary">
                            +{plan.features.length - 3} more features
                          </Typography>
                        )}
                      </Stack>
                    </Box>
                  )}
                </CardContent>

                <Divider />

                <CardActions sx={{ p: 2, justifyContent: 'space-between' }}>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Chip
                      label={plan.isActive ? 'Active' : 'Inactive'}
                      color={plan.isActive ? 'success' : 'default'}
                      size="small"
                    />
                    {plan.isFeatured && (
                      <Chip
                        label="Featured"
                        color="secondary"
                        size="small"
                      />
                    )}
                  </Box>

                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Tooltip title="Edit Plan">
                      <IconButton
                        size="small"
                        onClick={() => navigate(`/dashboard/billing-management/plans/${plan.id}/edit`)}
                      >
                        <EditIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="View Details">
                      <IconButton
                        size="small"
                        onClick={() => navigate(`/dashboard/billing-management/plans/${plan.id}`)}
                      >
                        <ViewIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Action Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={closeMenu}
      >
        <MenuItem onClick={() => {
          if (selectedPlan) navigate(`/dashboard/billing-management/plans/${selectedPlan.id}`)
          closeMenu()
        }}>
          <ViewIcon fontSize="small" sx={{ mr: 1 }} />
          View Details
        </MenuItem>
        <MenuItem onClick={() => {
          if (selectedPlan) navigate(`/dashboard/billing-management/plans/${selectedPlan.id}/edit`)
          closeMenu()
        }}>
          <EditIcon fontSize="small" sx={{ mr: 1 }} />
          Edit Plan
        </MenuItem>
        <MenuItem onClick={() => {
          if (selectedPlan) handleDuplicatePlan(selectedPlan)
          closeMenu()
        }}>
          <ContentCopy fontSize="small" sx={{ mr: 1 }} />
          Duplicate Plan
        </MenuItem>
        <Divider />
        <MenuItem
          onClick={() => {
            if (selectedPlan) handleToggleStatus(selectedPlan)
            closeMenu()
          }}
          disabled={actionLoading === `status-${selectedPlan?.id}`}
        >
          {selectedPlan?.isActive ? <Close fontSize="small" sx={{ mr: 1 }} /> : <CheckIcon fontSize="small" sx={{ mr: 1 }} />}
          {selectedPlan?.isActive ? 'Deactivate' : 'Activate'}
        </MenuItem>
        <MenuItem
          onClick={() => {
            if (selectedPlan) handleToggleFeatured(selectedPlan)
            closeMenu()
          }}
          disabled={actionLoading === `featured-${selectedPlan?.id}`}
        >
          {selectedPlan?.isFeatured ? <StarBorderIcon fontSize="small" sx={{ mr: 1 }} /> : <StarIcon fontSize="small" sx={{ mr: 1 }} />}
          {selectedPlan?.isFeatured ? 'Unfeature' : 'Feature'}
        </MenuItem>
        <Divider />
        <MenuItem
          onClick={() => openDeleteDialog(selectedPlan!)}
          sx={{ color: 'error.main' }}
        >
          <DeleteIcon fontSize="small" sx={{ mr: 1 }} />
          Delete Plan
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Delete Subscription Plan</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete "{selectedPlan?.name}"? This action cannot be undone.
            Any active subscriptions using this plan will need to be migrated to another plan.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleDeletePlan}
            color="error"
            disabled={actionLoading === 'delete'}
          >
            {actionLoading === 'delete' ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Summary Stats */}
      {!loading && plans.length > 0 && (
        <Paper sx={{ p: 3, mt: 4 }}>
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
            Plan Summary
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={6} sm={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main' }}>
                  {plans.length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Plans
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" sx={{ fontWeight: 700, color: 'success.main' }}>
                  {plans.filter(p => p.isActive).length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Active Plans
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" sx={{ fontWeight: 700, color: 'secondary.main' }}>
                  {plans.filter(p => p.isFeatured).length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Featured Plans
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" sx={{ fontWeight: 700, color: 'warning.main' }}>
                  {plans.length > 0 ? formatCurrency(Math.min(...plans.map(p => p.price)), plans[0]?.currency || 'ETB') : 'N/A'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Starting From
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Paper>
      )}
    </BillingManagementLayout>
  )
}

export default PlanManagementPage
