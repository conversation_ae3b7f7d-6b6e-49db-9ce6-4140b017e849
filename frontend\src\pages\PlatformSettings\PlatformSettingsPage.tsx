import React, { useState, useEffect } from 'react'
import {
  Box,
  Container,
  Typography,
  Paper,
  Tabs,
  Tab,
  Alert,
  Snackbar,
  CircularProgress,
  Backdrop,

  IconButton,
  Tooltip,
  Button,
} from '@mui/material'
import {
  Settings as SettingsIcon,
  <PERSON>lette as PaletteIcon,
  Build as BuildIcon,
  Security as SecurityIcon,
  Storage as StorageIcon,
  ArrowBack as ArrowBackIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import GeneralSettingsTab from './components/GeneralSettingsTab'
import BrandingSettingsTab from './components/BrandingSettingsTab'
import HeroSectionTab from './components/HeroSectionTab'
import MaintenanceTab from './components/MaintenanceTab'
import SecuritySettingsTab from './components/SecuritySettingsTab'
import SystemConfigTab from './components/SystemConfigTab'

interface TabPanelProps {
  children?: React.ReactNode
  index: number
  value: number
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`platform-settings-tabpanel-${index}`}
      aria-labelledby={`platform-settings-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  )
}

function a11yProps(index: number) {
  return {
    id: `platform-settings-tab-${index}`,
    'aria-controls': `platform-settings-tabpanel-${index}`,
  }
}

const PlatformSettingsPage: React.FC = () => {
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState(0)
  const [loading, setLoading] = useState(false)
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error' | 'warning' | 'info',
  })

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue)
  }

  const showSnackbar = (message: string, severity: 'success' | 'error' | 'warning' | 'info' = 'success') => {
    setSnackbar({ open: true, message, severity })
  }

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false })
  }

  const handleSaveSuccess = () => {
    showSnackbar('Settings saved successfully!', 'success')
  }

  const handleSaveError = (error: string) => {
    showSnackbar(error, 'error')
  }

  const handleRefresh = () => {
    window.location.reload()
  }

  const tabs = [
    {
      label: 'General',
      icon: <SettingsIcon />,
      component: (
        <GeneralSettingsTab
          onSaveSuccess={handleSaveSuccess}
          onSaveError={handleSaveError}
        />
      ),
    },
    {
      label: 'Branding',
      icon: <PaletteIcon />,
      component: (
        <BrandingSettingsTab
          onSaveSuccess={handleSaveSuccess}
          onSaveError={handleSaveError}
        />
      ),
    },
    {
      label: 'Hero Section',
      icon: <HomeIcon />,
      component: (
        <HeroSectionTab
          onSaveSuccess={handleSaveSuccess}
          onSaveError={handleSaveError}
        />
      ),
    },
    {
      label: 'Maintenance',
      icon: <BuildIcon />,
      component: (
        <MaintenanceTab
          onSaveSuccess={handleSaveSuccess}
          onSaveError={handleSaveError}
        />
      ),
    },
    {
      label: 'Security',
      icon: <SecurityIcon />,
      component: (
        <SecuritySettingsTab
          onSaveSuccess={handleSaveSuccess}
          onSaveError={handleSaveError}
        />
      ),
    },
    {
      label: 'System Config',
      icon: <StorageIcon />,
      component: (
        <SystemConfigTab
          onSaveSuccess={handleSaveSuccess}
          onSaveError={handleSaveError}
        />
      ),
    },
  ]

  return (
    <Box sx={{ bgcolor: '#f5f5f5', minHeight: '100vh' }}>
      {/* Header Section */}
      <Paper
        elevation={0}
        sx={{
          bgcolor: 'white',
          color: '#1f2937',
          py: 4,
          px: 3,
          mb: 3,
          borderRadius: 0,
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        }}
      >
        <Container maxWidth="xl">
          {/* Header Content */}
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Tooltip title="Go Back">
                <IconButton
                  onClick={() => navigate(-1)}
                  sx={{
                    color: '#6366f1',
                    mr: 2,
                    bgcolor: '#f3f4f6',
                    '&:hover': {
                      bgcolor: '#e5e7eb',
                    },
                  }}
                >
                  <ArrowBackIcon />
                </IconButton>
              </Tooltip>
              <Box>
                <Typography
                  variant="h4"
                  component="h1"
                  sx={{
                    fontWeight: 700,
                    mb: 1,
                    color: '#1f2937',
                  }}
                >
                  Platform Settings
                </Typography>
                <Typography
                  variant="body1"
                  sx={{
                    color: '#6b7280',
                    fontWeight: 400,
                  }}
                >
                  Configure global platform settings, branding, and system preferences
                </Typography>
              </Box>
            </Box>
            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              <Button
                variant="outlined"
                onClick={() => navigate('/dashboard/platform-settings/features')}
                sx={{
                  color: '#6366f1',
                  borderColor: '#6366f1',
                  '&:hover': {
                    borderColor: '#6366f1',
                    bgcolor: 'rgba(99, 102, 241, 0.04)',
                  },
                }}
              >
                Feature Flags
              </Button>
              <Button
                variant="outlined"
                onClick={() => navigate('/dashboard/platform-settings/config')}
                sx={{
                  color: '#6366f1',
                  borderColor: '#6366f1',
                  '&:hover': {
                    borderColor: '#6366f1',
                    bgcolor: 'rgba(99, 102, 241, 0.04)',
                  },
                }}
              >
                System Config
              </Button>
              <Tooltip title="Refresh Settings">
                <IconButton
                  onClick={handleRefresh}
                  sx={{
                    color: '#6366f1',
                    bgcolor: '#f3f4f6',
                    '&:hover': {
                      bgcolor: '#e5e7eb',
                    },
                  }}
                >
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
        </Container>
      </Paper>

      {/* Main Content */}
      <Container maxWidth="xl">
        <Paper
          elevation={0}
          sx={{
            borderRadius: 2,
            overflow: 'hidden',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          }}
        >
          {/* Tabs */}
          <Box sx={{ borderBottom: 1, borderColor: 'divider', bgcolor: 'white' }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              aria-label="platform settings tabs"
              variant="scrollable"
              scrollButtons="auto"
              sx={{
                '& .MuiTab-root': {
                  minHeight: 64,
                  textTransform: 'none',
                  fontWeight: 600,
                  color: '#6b7280',
                  '&.Mui-selected': {
                    color: '#6366f1',
                  },
                },
                '& .MuiTabs-indicator': {
                  backgroundColor: '#6366f1',
                  height: 3,
                },
              }}
            >
              {tabs.map((tab, index) => (
                <Tab
                  key={index}
                  icon={tab.icon}
                  label={tab.label}
                  iconPosition="start"
                  {...a11yProps(index)}
                />
              ))}
            </Tabs>
          </Box>

          {/* Tab Panels */}
          {tabs.map((tab, index) => (
            <TabPanel key={index} value={activeTab} index={index}>
              <Box sx={{ bgcolor: 'white', p: 3 }}>
                {tab.component}
              </Box>
            </TabPanel>
          ))}
        </Paper>
      </Container>

      {/* Loading Backdrop */}
      <Backdrop
        sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1 }}
        open={loading}
      >
        <CircularProgress color="inherit" />
      </Backdrop>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  )
}

export default PlatformSettingsPage
