from django.urls import path
from . import views

app_name = 'accounts'

urlpatterns = [
    # API endpoints
    path('api/login/', views.api_login, name='api_login'),
    path('api/logout/', views.api_logout, name='api_logout'),
    path('api/password-reset/', views.api_password_reset_request, name='api_password_reset_request'),
    path('api/password-reset/confirm/', views.api_password_reset_confirm, name='api_password_reset_confirm'),
    
    # Dashboard views
    path('profile/', views.user_profile, name='profile'),
    path('admin-dashboard/', views.admin_dashboard, name='admin_dashboard'),
    path('teacher-dashboard/', views.teacher_dashboard, name='teacher_dashboard'),
    path('student-dashboard/', views.student_dashboard, name='student_dashboard'),
    path('parent-dashboard/', views.parent_dashboard, name='parent_dashboard'),
    path('staff-dashboard/', views.staff_dashboard, name='staff_dashboard'),
]
