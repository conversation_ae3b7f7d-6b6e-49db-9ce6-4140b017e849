import os
import django
import datetime

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kelem_sms.settings')
django.setup()

from django.db import connection
from django.contrib.auth.hashers import make_password

# Create a superuser directly in the database
with connection.cursor() as cursor:
    # Check if superuser already exists
    cursor.execute("SELECT COUNT(*) FROM authentication_user WHERE email = %s", ['<EMAIL>'])
    count = cursor.fetchone()[0]

    if count == 0:
        # Create a superuser
        cursor.execute("""
        INSERT INTO authentication_user (
            password, last_login, is_superuser, first_name, last_name, is_staff, is_active, date_joined, email, user_type, phone_number
        ) VALUES (%s, NULL, TRUE, %s, %s, TRUE, TRUE, %s, %s, %s, NULL)
        """, [
            make_password('admin123'),  # password
            'Admin',  # first_name
            'User',  # last_name
            datetime.datetime.now(),  # date_joined
            '<EMAIL>',  # email
            'admin'  # user_type
        ])
        print("Superuser created successfully.")
    else:
        print("Superuser already exists.")
