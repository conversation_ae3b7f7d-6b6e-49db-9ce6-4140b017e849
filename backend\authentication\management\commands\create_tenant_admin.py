from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django_tenants.utils import schema_context
from tenants.models import School

class Command(BaseCommand):
    help = 'Create an admin user in a tenant schema'

    def add_arguments(self, parser):
        parser.add_argument('schema_name', type=str, help='The schema name to create the admin user in')
        parser.add_argument('email', type=str, help='Email for the admin user')
        parser.add_argument('password', type=str, help='Password for the admin user')
        parser.add_argument('--first-name', type=str, help='First name for the admin user')
        parser.add_argument('--last-name', type=str, help='Last name for the admin user')

    def handle(self, *args, **options):
        schema_name = options['schema_name']
        email = options['email']
        password = options['password']
        first_name = options.get('first_name', '')
        last_name = options.get('last_name', '')
        
        # Check if the schema exists
        try:
            school = School.objects.get(schema_name=schema_name)
            self.stdout.write(self.style.SUCCESS(f'Found school: {school.name} with schema: {schema_name}'))
        except School.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'No school found with schema: {schema_name}'))
            return
        
        # Create admin user in the schema
        with schema_context(schema_name):
            User = get_user_model()
            
            # Check if user already exists
            if User.objects.filter(email=email).exists():
                self.stdout.write(self.style.WARNING(f'User with email {email} already exists in schema {schema_name}'))
                return
            
            # Create the admin user
            try:
                admin_user = User.objects.create_user(
                    email=email,
                    password=password,
                    first_name=first_name,
                    last_name=last_name,
                    user_type='admin',
                    is_staff=True,
                    is_active=True,
                    is_superuser=True
                )
                self.stdout.write(self.style.SUCCESS(f'Created admin user with ID {admin_user.id} in schema {schema_name}'))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'Error creating admin user: {str(e)}'))
                import traceback
                self.stdout.write(traceback.format_exc())
