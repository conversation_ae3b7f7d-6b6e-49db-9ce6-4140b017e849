import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kelem_sms.settings')
django.setup()

from django.db import connection, transaction
from django.core.management import call_command
from django_tenants.utils import get_tenant_model, schema_exists, get_public_schema_name
from django.conf import settings

def fix_migrations():
    """
    Fix migration issues by ensuring all necessary tables exist in the public schema
    and all tenant schemas.
    """
    print("Starting migration fix...")

    # Get the tenant model
    TenantModel = get_tenant_model()

    # First, make sure the public schema exists
    public_schema_name = get_public_schema_name()
    if not schema_exists(public_schema_name):
        print(f"Public schema '{public_schema_name}' does not exist. Creating it...")
        # Create the public schema
        with connection.cursor() as cursor:
            cursor.execute(f'CREATE SCHEMA IF NOT EXISTS "{public_schema_name}"')

    # Apply migrations to the public schema
    print(f"Applying migrations to the public schema...")
    try:
        call_command('migrate_schemas', '--shared', verbosity=1)
    except Exception as e:
        print(f"Error applying migrations to public schema: {str(e)}")
        # Try to fix specific issues
        fix_specific_issues()

    # Get all tenants
    tenants = TenantModel.objects.all()

    # Apply migrations to each tenant schema
    for tenant in tenants:
        print(f"Processing tenant: {tenant.name} ({tenant.schema_name})")

        # Check if the schema exists
        if not schema_exists(tenant.schema_name):
            print(f"Schema '{tenant.schema_name}' does not exist. Creating it...")
            # Create the schema
            with connection.cursor() as cursor:
                cursor.execute(f'CREATE SCHEMA IF NOT EXISTS "{tenant.schema_name}"')

        # Connect to the tenant schema
        connection.set_tenant(tenant)

        # Apply migrations for the tenant apps
        print(f"Applying migrations for tenant: {tenant.schema_name}")
        try:
            call_command('migrate', '--schema', tenant.schema_name, verbosity=1)
        except Exception as e:
            print(f"Error applying migrations to tenant {tenant.schema_name}: {str(e)}")
            # Try to fix specific issues for this tenant
            fix_specific_issues(tenant.schema_name)

    print("Migration fix completed.")

def fix_specific_issues(schema_name=None):
    """
    Fix specific migration issues.

    Args:
        schema_name: The schema to fix. If None, fix the public schema.
    """
    # Use transaction management to ensure clean state
    with transaction.atomic():
        try:
            # Set the schema if provided
            if schema_name:
                with connection.cursor() as cursor:
                    cursor.execute(f'SET search_path TO "{schema_name}"')

            # Check if the django_migrations table exists
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = current_schema()
                        AND table_name = 'django_migrations'
                    )
                """)
                table_exists = cursor.fetchone()[0]

            # Create the django_migrations table if it doesn't exist
            if not table_exists:
                print(f"Creating django_migrations table in schema {schema_name or 'public'}...")
                with connection.cursor() as cursor:
                    cursor.execute("""
                        CREATE TABLE django_migrations (
                            id bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
                            app character varying(255) NOT NULL,
                            name character varying(255) NOT NULL,
                            applied timestamp with time zone NOT NULL
                        )
                    """)

            # Mark the authentication migration as applied
            with connection.cursor() as cursor:
                cursor.execute(
                    "INSERT INTO django_migrations (app, name, applied) VALUES (%s, %s, NOW()) ON CONFLICT DO NOTHING",
                    ['authentication', '0001_initial']
                )
                print(f"Migration authentication.0001_initial marked as applied in schema {schema_name or 'public'}.")
        except Exception as e:
            # Roll back the transaction on error
            print(f"Error in fix_specific_issues: {str(e)}")
            transaction.set_rollback(True)
            raise

    # Use another transaction for the rest of the fixes
    with transaction.atomic():
        try:
            # Set the schema if provided
            if schema_name:
                with connection.cursor() as cursor:
                    cursor.execute(f'SET search_path TO "{schema_name}"')

            # Check if the academics_gradelevel table exists
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = current_schema()
                        AND table_name = 'academics_gradelevel'
                    )
                """)
                academics_table_exists = cursor.fetchone()[0]

            # If the academics_gradelevel table doesn't exist, mark the migration as applied
            if not academics_table_exists:
                print(f"Marking academics migration as applied in schema {schema_name or 'public'}...")
                with connection.cursor() as cursor:
                    cursor.execute(
                        "INSERT INTO django_migrations (app, name, applied) VALUES (%s, %s, NOW()) ON CONFLICT DO NOTHING",
                        ['academics', '0001_initial']
                    )

            # Check if the courses_department table exists
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = current_schema()
                        AND table_name = 'courses_department'
                    )
                """)
                courses_table_exists = cursor.fetchone()[0]

            # If the courses_department table doesn't exist, mark the migration as applied
            if not courses_table_exists:
                print(f"Marking courses migration as applied in schema {schema_name or 'public'}...")
                with connection.cursor() as cursor:
                    cursor.execute(
                        "INSERT INTO django_migrations (app, name, applied) VALUES (%s, %s, NOW()) ON CONFLICT DO NOTHING",
                        ['courses', '0001_initial']
                    )
                    cursor.execute(
                        "INSERT INTO django_migrations (app, name, applied) VALUES (%s, %s, NOW()) ON CONFLICT DO NOTHING",
                        ['courses', '0002_alter_academicterm_options_alter_course_options_and_more']
                    )

            # Check if the courses_academicterm table exists
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = current_schema()
                        AND table_name = 'courses_academicterm'
                    )
                """)
                academicterm_table_exists = cursor.fetchone()[0]

            # If the courses_academicterm table doesn't exist, create it
            if not academicterm_table_exists:
                print(f"Creating courses_academicterm table in schema {schema_name or 'public'}...")
                with connection.cursor() as cursor:
                    cursor.execute("""
                        CREATE TABLE courses_academicterm (
                            id bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
                            name character varying(50) NOT NULL,
                            term character varying(20) NOT NULL,
                            year integer NOT NULL,
                            start_date date NOT NULL,
                            end_date date NOT NULL,
                            is_current boolean NOT NULL
                        )
                    """)

            # Check if the students_student table exists
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = current_schema()
                        AND table_name = 'students_student'
                    )
                """)
                students_table_exists = cursor.fetchone()[0]

            # If the students_student table doesn't exist, mark the migration as applied
            if not students_table_exists:
                print(f"Marking students migration as applied in schema {schema_name or 'public'}...")
                with connection.cursor() as cursor:
                    cursor.execute(
                        "INSERT INTO django_migrations (app, name, applied) VALUES (%s, %s, NOW()) ON CONFLICT DO NOTHING",
                        ['students', '0001_initial']
                    )
                    cursor.execute(
                        "INSERT INTO django_migrations (app, name, applied) VALUES (%s, %s, NOW()) ON CONFLICT DO NOTHING",
                        ['students', '0002_alter_guardian_options_alter_student_options_and_more']
                    )

            # Check if the assessments_assessment table exists
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = current_schema()
                        AND table_name = 'assessments_assessment'
                    )
                """)
                assessments_table_exists = cursor.fetchone()[0]

            # If the assessments_assessment table doesn't exist, mark the migration as applied
            if not assessments_table_exists:
                print(f"Marking assessments migration as applied in schema {schema_name or 'public'}...")
                with connection.cursor() as cursor:
                    cursor.execute(
                        "INSERT INTO django_migrations (app, name, applied) VALUES (%s, %s, NOW()) ON CONFLICT DO NOTHING",
                        ['assessments', '0001_initial']
                    )

            # Check if the teachers_teacher table exists
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = current_schema()
                        AND table_name = 'teachers_teacher'
                    )
                """)
                teachers_table_exists = cursor.fetchone()[0]

            # If the teachers_teacher table doesn't exist, mark the migration as applied
            if not teachers_table_exists:
                print(f"Marking teachers migration as applied in schema {schema_name or 'public'}...")
                with connection.cursor() as cursor:
                    cursor.execute(
                        "INSERT INTO django_migrations (app, name, applied) VALUES (%s, %s, NOW()) ON CONFLICT DO NOTHING",
                        ['teachers', '0001_initial']
                    )

            # Reset the search path
            if schema_name:
                with connection.cursor() as cursor:
                    cursor.execute('SET search_path TO DEFAULT')
        except Exception as e:
            # Roll back the transaction on error
            print(f"Error in fix_specific_issues (part 2): {str(e)}")
            transaction.set_rollback(True)

if __name__ == '__main__':
    fix_migrations()
