from rest_framework import serializers
from .models import Student, Guardian, StudentDocument, StudentAchievement, StudentFormConfig, FormFieldType, FormFieldStatus, FormSection, StudentFee, FeePayment
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'user_type']
        read_only_fields = ['id', 'user_type']

class GuardianSerializer(serializers.ModelSerializer):
    class Meta:
        model = Guardian
        fields = [
            'id', 'student', 'first_name', 'middle_name', 'last_name', 'relationship',
            'occupation', 'employer', 'phone_number', 'alternate_phone', 'email',
            'address', 'city', 'state', 'postal_code', 'country',
            'is_primary_contact', 'is_emergency_contact', 'can_pickup_student',
            'receives_reports', 'photo', 'notes'
        ]
        read_only_fields = ['id']

class StudentDocumentSerializer(serializers.ModelSerializer):
    uploaded_by_name = serializers.SerializerMethodField()
    verified_by_name = serializers.SerializerMethodField()

    class Meta:
        model = StudentDocument
        fields = [
            'id', 'student', 'document_type', 'title', 'description', 'file',
            'is_verified', 'verified_by', 'verified_by_name', 'verified_date',
            'uploaded_by', 'uploaded_by_name', 'uploaded_at', 'updated_at'
        ]
        read_only_fields = ['id', 'uploaded_by', 'uploaded_at', 'updated_at']

    def get_uploaded_by_name(self, obj):
        if obj.uploaded_by:
            return f"{obj.uploaded_by.first_name} {obj.uploaded_by.last_name}"
        return None

    def get_verified_by_name(self, obj):
        if obj.verified_by:
            return f"{obj.verified_by.first_name} {obj.verified_by.last_name}"
        return None

    def create(self, validated_data):
        # Set the uploaded_by field to the current user
        validated_data['uploaded_by'] = self.context['request'].user
        return super().create(validated_data)

class StudentFormConfigSerializer(serializers.ModelSerializer):
    field_type_display = serializers.CharField(source='get_field_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    section_display = serializers.CharField(source='get_section_display', read_only=True)

    class Meta:
        model = StudentFormConfig
        fields = [
            'id', 'tenant', 'field_name', 'display_name', 'field_type', 'field_type_display',
            'section', 'section_display', 'status', 'status_display', 'order', 'options',
            'help_text', 'default_value', 'validation_regex', 'is_custom'
        ]
        read_only_fields = ['id']

class StudentAchievementSerializer(serializers.ModelSerializer):
    class Meta:
        model = StudentAchievement
        fields = [
            'id', 'student', 'title', 'achievement_type', 'description',
            'date_achieved', 'issuing_organization', 'certificate',
            'is_verified', 'verified_by', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

class StudentFeeSerializer(serializers.ModelSerializer):
    class Meta:
        model = StudentFee
        fields = [
            'id', 'student', 'academic_year', 'term', 'fee_type',
            'amount', 'due_date', 'payment_status', 'amount_paid',
            'balance', 'last_payment_date', 'notes', 'created_by',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'balance', 'amount_paid', 'last_payment_date', 'created_at', 'updated_at']

    def create(self, validated_data):
        # Set the created_by field to the current user
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)

class FeePaymentSerializer(serializers.ModelSerializer):
    class Meta:
        model = FeePayment
        fields = [
            'id', 'student_fee', 'amount', 'payment_date', 'payment_method',
            'transaction_id', 'receipt_number', 'received_by', 'notes', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']

    def create(self, validated_data):
        # Set the received_by field to the current user
        validated_data['received_by'] = self.context['request'].user
        return super().create(validated_data)

class StudentListSerializer(serializers.ModelSerializer):
    """Serializer for listing students with minimal information."""
    current_grade = serializers.SerializerMethodField()

    class Meta:
        model = Student
        fields = [
            'id', 'student_id', 'first_name', 'middle_name', 'last_name',
            'gender', 'current_grade', 'enrollment_date', 'status', 'photo'
        ]
        read_only_fields = ['id']

    def get_current_grade(self, obj):
        if obj.current_grade_level:
            return obj.current_grade_level.name
        return None

class StudentDetailSerializer(serializers.ModelSerializer):
    """Serializer for detailed student information."""
    user = UserSerializer(read_only=True)
    guardians = GuardianSerializer(many=True, read_only=True)
    documents = StudentDocumentSerializer(many=True, read_only=True)
    achievements = StudentAchievementSerializer(many=True, read_only=True)
    fees = StudentFeeSerializer(many=True, read_only=True)
    current_grade_level_name = serializers.SerializerMethodField()
    current_section_name = serializers.SerializerMethodField()

    class Meta:
        model = Student
        fields = [
            'id', 'user', 'student_id', 'first_name', 'middle_name', 'last_name',
            'date_of_birth', 'place_of_birth', 'gender', 'nationality', 'mother_tongue',
            'religion', 'email', 'phone_number', 'emergency_contact_name',
            'emergency_contact_phone', 'emergency_contact_relationship',
            'address', 'city', 'state', 'postal_code', 'country',
            'enrollment_date', 'current_grade_level', 'current_grade_level_name',
            'current_section', 'current_section_name', 'previous_school', 'admission_type',
            'blood_group', 'medical_conditions', 'allergies', 'medications',
            'status', 'is_active', 'photo', 'registration_number', 'notes',
            'guardians', 'documents', 'achievements', 'fees',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_current_grade_level_name(self, obj):
        if obj.current_grade_level:
            return obj.current_grade_level.name
        return None

    def get_current_section_name(self, obj):
        if obj.current_section:
            return obj.current_section.name
        return None

class StudentRegistrationSerializer(serializers.ModelSerializer):
    """Serializer for student registration."""
    # Guardian information fields
    guardian_first_name = serializers.CharField(write_only=True, required=True)
    guardian_last_name = serializers.CharField(write_only=True, required=True)
    guardian_relationship = serializers.CharField(write_only=True, required=True)
    guardian_phone = serializers.CharField(write_only=True, required=True)
    guardian_email = serializers.EmailField(write_only=True, required=False, allow_blank=True, allow_null=True)
    guardian_address = serializers.CharField(write_only=True, required=False, allow_blank=True, allow_null=True)
    guardian_is_primary = serializers.BooleanField(write_only=True, default=True)

    # Optional second guardian
    guardian2_first_name = serializers.CharField(write_only=True, required=False, allow_blank=True, allow_null=True)
    guardian2_last_name = serializers.CharField(write_only=True, required=False, allow_blank=True, allow_null=True)
    guardian2_relationship = serializers.CharField(write_only=True, required=False, allow_blank=True, allow_null=True)
    guardian2_phone = serializers.CharField(write_only=True, required=False, allow_blank=True, allow_null=True)
    guardian2_email = serializers.EmailField(write_only=True, required=False, allow_blank=True, allow_null=True)

    class Meta:
        model = Student
        fields = [
            'first_name', 'middle_name', 'last_name', 'date_of_birth', 'gender',
            'nationality', 'mother_tongue', 'religion', 'email', 'phone_number',
            'address', 'city', 'state', 'postal_code', 'country',
            'enrollment_date', 'current_grade_level', 'current_section',
            'previous_school', 'admission_type', 'blood_group',
            'medical_conditions', 'allergies', 'medications',
            'emergency_contact_name', 'emergency_contact_phone', 'emergency_contact_relationship',
            'photo', 'notes',
            # Guardian fields
            'guardian_first_name', 'guardian_last_name', 'guardian_relationship',
            'guardian_phone', 'guardian_email', 'guardian_address', 'guardian_is_primary',
            # Second guardian fields
            'guardian2_first_name', 'guardian2_last_name', 'guardian2_relationship',
            'guardian2_phone', 'guardian2_email'
        ]

    def validate(self, data):
        # Ensure enrollment_date is provided
        if 'enrollment_date' not in data:
            data['enrollment_date'] = timezone.now().date()

        # Generate a unique student ID if not provided
        if 'student_id' not in data:
            # Format: Year + Grade + Random 4-digit number
            year = timezone.now().year
            grade_code = 'NEW'
            if data.get('current_grade_level'):
                try:
                    grade_code = data['current_grade_level'].code
                except:
                    pass

            # Find the highest student ID with the same prefix and increment
            prefix = f"{year}{grade_code}"
            highest_id = 0

            # Get all students with the same prefix
            students_with_prefix = Student.objects.filter(student_id__startswith=prefix)

            # Find the highest ID
            for student in students_with_prefix:
                try:
                    # Extract the numeric part
                    numeric_part = student.student_id[len(prefix):]
                    if numeric_part.isdigit():
                        student_id_num = int(numeric_part)
                        if student_id_num > highest_id:
                            highest_id = student_id_num
                except (ValueError, IndexError):
                    pass

            # Increment the highest ID
            new_id = highest_id + 1

            # Generate a candidate student ID
            candidate_id = f"{prefix}{new_id:04d}"

            # Make sure it's unique
            while Student.objects.filter(student_id=candidate_id).exists():
                new_id += 1
                candidate_id = f"{prefix}{new_id:04d}"

            data['student_id'] = candidate_id
            print(f"Generated unique student ID: {candidate_id}")

        return data

    def create(self, validated_data):
        # Extract guardian data
        guardian_data = {
            'first_name': validated_data.pop('guardian_first_name'),
            'last_name': validated_data.pop('guardian_last_name'),
            'relationship': validated_data.pop('guardian_relationship'),
            'phone_number': validated_data.pop('guardian_phone'),
            'is_primary_contact': validated_data.pop('guardian_is_primary', True)
        }

        # Optional guardian fields
        if 'guardian_email' in validated_data:
            guardian_data['email'] = validated_data.pop('guardian_email')
        if 'guardian_address' in validated_data:
            guardian_data['address'] = validated_data.pop('guardian_address')

        # Extract second guardian data if provided
        guardian2_data = None
        if 'guardian2_first_name' in validated_data and validated_data['guardian2_first_name']:
            guardian2_data = {
                'first_name': validated_data.pop('guardian2_first_name'),
                'last_name': validated_data.pop('guardian2_last_name'),
                'relationship': validated_data.pop('guardian2_relationship'),
                'phone_number': validated_data.pop('guardian2_phone'),
                'is_primary_contact': False
            }

            if 'guardian2_email' in validated_data:
                guardian2_data['email'] = validated_data.pop('guardian2_email')
        else:
            # Remove second guardian fields if not used
            validated_data.pop('guardian2_first_name', None)
            validated_data.pop('guardian2_last_name', None)
            validated_data.pop('guardian2_relationship', None)
            validated_data.pop('guardian2_phone', None)
            validated_data.pop('guardian2_email', None)

        # Set status to ACTIVE
        validated_data['status'] = 'ACTIVE'

        # Create the student
        student = Student.objects.create(**validated_data)

        # Create the primary guardian
        Guardian.objects.create(student=student, **guardian_data)

        # Create the second guardian if provided
        if guardian2_data:
            Guardian.objects.create(student=student, **guardian2_data)

        return student
