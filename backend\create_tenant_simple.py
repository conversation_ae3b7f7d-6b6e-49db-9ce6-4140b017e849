import os
import django
import traceback

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kelem_sms.settings')
django.setup()

from django.db import transaction
from django.utils import timezone
from school_registration.models import SchoolRegistrationRequest
from tenants.models import School, Domain

def create_tenant_simple(registration_id):
    """Create a tenant from an existing registration request without schema creation."""
    try:
        # Get the registration request
        registration = SchoolRegistrationRequest.objects.get(id=registration_id)
        print(f"Found registration: {registration.name} (Status: {registration.status})")

        # Check if tenant already exists
        existing_school = School.objects.filter(schema_name=registration.schema_name).first()
        if existing_school:
            print(f"Error: A tenant with schema name '{registration.schema_name}' already exists.")
            return

        # Check if domain already exists
        existing_domain = Domain.objects.filter(domain=registration.domain).first()
        if existing_domain:
            print(f"Error: A domain with name '{registration.domain}' already exists.")
            return

        # Create the tenant (school) without creating schema
        # Temporarily set the class attribute to False
        original_auto_create_schema = School.auto_create_schema
        School.auto_create_schema = False

        try:
            school = School(
                schema_name=registration.schema_name,
                name=registration.name,
                address=registration.address,
                contact_email=registration.contact_email,
                contact_phone=registration.contact_phone,
            )
            school.save()  # Save without creating schema
        finally:
            # Restore the original value
            School.auto_create_schema = original_auto_create_schema

        # Create the domain for the tenant
        domain = Domain.objects.create(
            domain=registration.domain,
            tenant=school,
            is_primary=True
        )

        # Update the registration request
        registration.status = 'approved'
        registration.processed_on = timezone.now()
        registration.save()

        print(f"Tenant created successfully!")
        print(f"School ID: {school.id}")
        print(f"Domain ID: {domain.id}")

        return school, domain
    except Exception as e:
        print(f"Error creating tenant: {str(e)}")
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    # Create tenant for Fasiledes school (registration ID 1)
    create_tenant_simple(1)
