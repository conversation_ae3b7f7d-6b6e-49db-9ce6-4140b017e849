from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tenants', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='school',
            name='is_archived',
            field=models.BooleanField(default=False, help_text='Whether this tenant is archived'),
        ),
        migrations.AddField(
            model_name='school',
            name='archived_on',
            field=models.DateTimeField(blank=True, help_text='When this tenant was archived', null=True),
        ),
        migrations.AddField(
            model_name='school',
            name='archive_reason',
            field=models.TextField(blank=True, help_text='Reason for archiving this tenant', null=True),
        ),
    ]
