import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import {
  Box,
  Paper,
  Typography,
  Grid,
  Button,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  Divider,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
} from '@mui/material'
import {
  ArrowBack as ArrowBackIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  School as SchoolIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
  Person as PersonIcon,
  CalendarToday as CalendarIcon,
} from '@mui/icons-material'
import { approveRegistration, rejectRegistration } from '../../services/tenantService'

interface PendingRegistration {
  id: string
  name: string
  address: string
  contact_email: string
  contact_phone: string
  contact_person: string
  created_on: string
  registration_id: number
  status: string
  city?: string
  state?: string
  country?: string
  established_year?: number
  school_type?: string
  student_capacity?: number
  description?: string
}

const PendingRegistrationDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const [registration, setRegistration] = useState<PendingRegistration | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [actionLoading, setActionLoading] = useState(false)
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false)
  const [rejectionReason, setRejectionReason] = useState('')

  useEffect(() => {
    fetchRegistrationData()
  }, [id])

  const fetchRegistrationData = async () => {
    if (!id) return

    setLoading(true)
    setError(null)

    try {
      // For now, we'll get the registration data from the tenant list API
      // In a real implementation, you might want a dedicated endpoint for registration details
      const response = await fetch(`http://localhost:8001/tenant-management/api/tenants/`, {
        headers: {
          'Authorization': `Token ${localStorage.getItem('token')}`,
        },
      })
      
      if (!response.ok) {
        throw new Error('Failed to fetch registration data')
      }
      
      const data = await response.json()
      const pendingRegistration = data.results.find((item: any) => 
        item.registration_id && item.registration_id.toString() === id
      )
      
      if (!pendingRegistration) {
        throw new Error('Pending registration not found')
      }
      
      setRegistration(pendingRegistration)
    } catch (err: any) {
      console.error('Error fetching registration data:', err)
      setError(err.message || 'Failed to fetch registration data')
    } finally {
      setLoading(false)
    }
  }

  const handleApprove = async () => {
    if (!registration) return

    setActionLoading(true)
    try {
      await approveRegistration(registration.registration_id.toString())
      navigate('/dashboard/tenant-management/tenants', {
        state: { message: `Registration for "${registration.name}" approved successfully!` }
      })
    } catch (err: any) {
      console.error('Error approving registration:', err)
      setError(err.response?.data?.message || 'Failed to approve registration')
    } finally {
      setActionLoading(false)
    }
  }

  const handleReject = async () => {
    if (!registration) return

    setActionLoading(true)
    try {
      await rejectRegistration(registration.registration_id.toString(), rejectionReason)
      navigate('/dashboard/tenant-management/tenants', {
        state: { message: `Registration for "${registration.name}" rejected.` }
      })
    } catch (err: any) {
      console.error('Error rejecting registration:', err)
      setError(err.response?.data?.message || 'Failed to reject registration')
    } finally {
      setActionLoading(false)
      setRejectDialogOpen(false)
      setRejectionReason('')
    }
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <CircularProgress />
      </Box>
    )
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/dashboard/tenant-management/tenants')}
        >
          Back to Tenant List
        </Button>
      </Box>
    )
  }

  if (!registration) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="warning" sx={{ mb: 2 }}>
          Registration not found
        </Alert>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/dashboard/tenant-management/tenants')}
        >
          Back to Tenant List
        </Button>
      </Box>
    )
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/dashboard/tenant-management/tenants')}
          >
            Back
          </Button>
          <Box>
            <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
              {registration.name}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
              <Chip
                label="Pending Approval"
                color="info"
                size="small"
                icon={<SchoolIcon />}
              />
              <Typography variant="body2" color="text.secondary">
                Registration ID: {registration.registration_id}
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Action Buttons */}
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="contained"
            color="success"
            startIcon={<ApproveIcon />}
            onClick={handleApprove}
            disabled={actionLoading}
          >
            {actionLoading ? 'Approving...' : 'Approve'}
          </Button>
          <Button
            variant="outlined"
            color="error"
            startIcon={<RejectIcon />}
            onClick={() => setRejectDialogOpen(true)}
            disabled={actionLoading}
          >
            Reject
          </Button>
        </Box>
      </Box>

      {/* Registration Details */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                School Information
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <SchoolIcon color="primary" />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        School Name
                      </Typography>
                      <Typography variant="body1" fontWeight={500}>
                        {registration.name}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <LocationIcon color="primary" />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Address
                      </Typography>
                      <Typography variant="body1" fontWeight={500}>
                        {registration.address}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <PersonIcon color="primary" />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Contact Person
                      </Typography>
                      <Typography variant="body1" fontWeight={500}>
                        {registration.contact_person}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <EmailIcon color="primary" />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Email
                      </Typography>
                      <Typography variant="body1" fontWeight={500}>
                        {registration.contact_email}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <PhoneIcon color="primary" />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Phone
                      </Typography>
                      <Typography variant="body1" fontWeight={500}>
                        {registration.contact_phone}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <CalendarIcon color="primary" />
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Registration Date
                      </Typography>
                      <Typography variant="body1" fontWeight={500}>
                        {new Date(registration.created_on).toLocaleDateString()}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Actions Required
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <Alert severity="info" sx={{ mb: 2 }}>
                This school registration is pending approval. Review the information and choose to approve or reject.
              </Alert>

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Button
                  variant="contained"
                  color="success"
                  startIcon={<ApproveIcon />}
                  onClick={handleApprove}
                  disabled={actionLoading}
                  fullWidth
                >
                  {actionLoading ? 'Approving...' : 'Approve Registration'}
                </Button>
                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<RejectIcon />}
                  onClick={() => setRejectDialogOpen(true)}
                  disabled={actionLoading}
                  fullWidth
                >
                  Reject Registration
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Reject Dialog */}
      <Dialog open={rejectDialogOpen} onClose={() => setRejectDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Reject Registration</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Are you sure you want to reject the registration for "{registration.name}"?
          </Typography>
          <TextField
            label="Rejection Reason (Optional)"
            multiline
            rows={3}
            fullWidth
            value={rejectionReason}
            onChange={(e) => setRejectionReason(e.target.value)}
            placeholder="Provide a reason for rejection..."
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRejectDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleReject}
            color="error"
            variant="contained"
            disabled={actionLoading}
          >
            {actionLoading ? 'Rejecting...' : 'Reject'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default PendingRegistrationDetailPage
