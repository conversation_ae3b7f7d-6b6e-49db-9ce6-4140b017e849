import React, { useState, useEffect, useMemo } from 'react'
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Grid,
  LinearProgress,
  Chip,
  Button,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material'
import {
  Security as SecurityIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Group as GroupIcon,
  Person as PersonIcon,
  Key as KeyIcon,
  Shield as ShieldIcon,
  Analytics as AnalyticsIcon,
  Visibility as VisibilityIcon,
  Assignment as AssignmentIcon,
  Schedule as ScheduleIcon,
  LocationOn as LocationIcon,
  Computer as ComputerIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material'
import {
  Role,
  User,
  Permission,
  RiskAssessment,
  AccessPattern,
  ComplianceReport,
} from '../../types/rbac'

interface AccessControlDashboardProps {
  roles: Role[]
  users: User[]
  permissions: Permission[]
  onRefreshData: () => void
  onViewDetails: (type: 'users' | 'roles' | 'permissions', id?: string) => void
  loading?: boolean
}

interface DashboardMetrics {
  totalUsers: number
  activeUsers: number
  totalRoles: number
  customRoles: number
  totalPermissions: number
  dynamicPermissions: number
  riskScore: number
  complianceScore: number
  recentActivity: number
  privilegedUsers: number
  orphanedPermissions: number
  roleConflicts: number
}

interface SecurityAlert {
  id: string
  type: 'high_risk_user' | 'orphaned_permission' | 'role_conflict' | 'excessive_permissions' | 'inactive_user'
  severity: 'low' | 'medium' | 'high' | 'critical'
  title: string
  description: string
  affectedEntity: string
  recommendation: string
  createdAt: string
}

const AccessControlDashboard: React.FC<AccessControlDashboardProps> = ({
  roles,
  users,
  permissions,
  onRefreshData,
  onViewDetails,
  loading = false,
}) => {
  const [selectedAlert, setSelectedAlert] = useState<SecurityAlert | null>(null)
  const [alertDialogOpen, setAlertDialogOpen] = useState(false)

  const metrics = useMemo((): DashboardMetrics => {
    const activeUsers = users.filter(u => u.is_active).length
    const customRoles = roles.filter(r => !r.is_system).length
    const dynamicPermissions = permissions.filter(p => p.conditions && p.conditions.length > 0).length
    const privilegedUsers = users.filter(u => u.is_superuser || u.roles.some(roleId => {
      const role = roles.find(r => r.id === roleId)
      return role && role.level <= 1
    })).length

    // Calculate risk score based on various factors
    let riskScore = 0
    if (privilegedUsers / users.length > 0.1) riskScore += 25 // Too many privileged users
    if (customRoles / roles.length > 0.8) riskScore += 15 // Too many custom roles
    if (users.filter(u => !u.mfa_enabled).length / users.length > 0.5) riskScore += 30 // Too many users without MFA
    if (users.filter(u => u.failed_login_attempts > 0).length > 0) riskScore += 10 // Failed login attempts

    // Calculate compliance score
    let complianceScore = 100
    if (users.filter(u => !u.mfa_enabled).length / users.length > 0.2) complianceScore -= 20
    if (roles.filter(r => r.effective_permissions.length > 50).length > 0) complianceScore -= 15
    if (users.filter(u => !u.last_login || new Date(u.last_login) < new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)).length > 0) complianceScore -= 10

    return {
      totalUsers: users.length,
      activeUsers,
      totalRoles: roles.length,
      customRoles,
      totalPermissions: permissions.length,
      dynamicPermissions,
      riskScore: Math.min(100, riskScore),
      complianceScore: Math.max(0, complianceScore),
      recentActivity: users.filter(u => u.last_login && new Date(u.last_login) > new Date(Date.now() - 24 * 60 * 60 * 1000)).length,
      privilegedUsers,
      orphanedPermissions: permissions.filter(p => !roles.some(r => r.effective_permissions.includes(p.id))).length,
      roleConflicts: 0, // Would be calculated based on actual conflict detection logic
    }
  }, [users, roles, permissions])

  const securityAlerts = useMemo((): SecurityAlert[] => {
    const alerts: SecurityAlert[] = []

    // High-risk users
    users.forEach(user => {
      if (user.is_superuser && !user.mfa_enabled) {
        alerts.push({
          id: `high_risk_${user.id}`,
          type: 'high_risk_user',
          severity: 'critical',
          title: 'Superuser without MFA',
          description: `User ${user.username} has superuser privileges but no MFA enabled`,
          affectedEntity: user.username,
          recommendation: 'Enable MFA for this user immediately',
          createdAt: new Date().toISOString(),
        })
      }

      if (user.failed_login_attempts > 5) {
        alerts.push({
          id: `failed_login_${user.id}`,
          type: 'high_risk_user',
          severity: 'high',
          title: 'Multiple failed login attempts',
          description: `User ${user.username} has ${user.failed_login_attempts} failed login attempts`,
          affectedEntity: user.username,
          recommendation: 'Review user account and consider temporary lockout',
          createdAt: new Date().toISOString(),
        })
      }
    })

    // Orphaned permissions
    permissions.forEach(permission => {
      const isUsed = roles.some(role => role.effective_permissions.includes(permission.id))
      if (!isUsed && !permission.is_system) {
        alerts.push({
          id: `orphaned_${permission.id}`,
          type: 'orphaned_permission',
          severity: 'medium',
          title: 'Orphaned permission',
          description: `Permission "${permission.name}" is not assigned to any role`,
          affectedEntity: permission.name,
          recommendation: 'Review and remove unused permission or assign to appropriate role',
          createdAt: new Date().toISOString(),
        })
      }
    })

    // Excessive permissions
    roles.forEach(role => {
      if (role.effective_permissions.length > 50) {
        alerts.push({
          id: `excessive_${role.id}`,
          type: 'excessive_permissions',
          severity: 'medium',
          title: 'Role with excessive permissions',
          description: `Role "${role.name}" has ${role.effective_permissions.length} permissions`,
          affectedEntity: role.name,
          recommendation: 'Review role permissions and consider splitting into multiple roles',
          createdAt: new Date().toISOString(),
        })
      }
    })

    return alerts.sort((a, b) => {
      const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 }
      return severityOrder[b.severity] - severityOrder[a.severity]
    })
  }, [users, roles, permissions])

  const getMetricColor = (value: number, type: 'risk' | 'compliance') => {
    if (type === 'risk') {
      if (value >= 70) return 'error'
      if (value >= 40) return 'warning'
      return 'success'
    } else {
      if (value >= 80) return 'success'
      if (value >= 60) return 'warning'
      return 'error'
    }
  }

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <ErrorIcon color="error" />
      case 'high':
        return <WarningIcon color="error" />
      case 'medium':
        return <WarningIcon color="warning" />
      case 'low':
        return <CheckCircleIcon color="info" />
      default:
        return <CheckCircleIcon />
    }
  }

  const MetricCard: React.FC<{
    title: string
    value: number | string
    subtitle?: string
    icon: React.ReactNode
    color?: string
    trend?: { direction: 'up' | 'down'; percentage: number }
    onClick?: () => void
  }> = ({ title, value, subtitle, icon, color = 'primary', trend, onClick }) => (
    <Card 
      sx={{ 
        cursor: onClick ? 'pointer' : 'default',
        '&:hover': onClick ? { boxShadow: 3 } : {},
      }}
      onClick={onClick}
    >
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 700, color: `${color}.main` }}>
              {value}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {title}
            </Typography>
            {subtitle && (
              <Typography variant="caption" color="text.secondary">
                {subtitle}
              </Typography>
            )}
          </Box>
          <Box sx={{ color: `${color}.main`, fontSize: 40 }}>
            {icon}
          </Box>
        </Box>
        
        {trend && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
            {trend.direction === 'up' ? (
              <TrendingUpIcon sx={{ fontSize: 16, color: 'error.main' }} />
            ) : (
              <TrendingDownIcon sx={{ fontSize: 16, color: 'success.main' }} />
            )}
            <Typography variant="caption" color={trend.direction === 'up' ? 'error.main' : 'success.main'}>
              {trend.percentage}% vs last month
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  )

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h5" sx={{ fontWeight: 700 }}>
            Access Control Dashboard
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Real-time monitoring and analytics for your RBAC system
          </Typography>
        </Box>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={onRefreshData}
          disabled={loading}
        >
          Refresh Data
        </Button>
      </Box>

      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Total Users"
            value={metrics.totalUsers}
            subtitle={`${metrics.activeUsers} active`}
            icon={<PersonIcon />}
            onClick={() => onViewDetails('users')}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Total Roles"
            value={metrics.totalRoles}
            subtitle={`${metrics.customRoles} custom`}
            icon={<GroupIcon />}
            onClick={() => onViewDetails('roles')}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Permissions"
            value={metrics.totalPermissions}
            subtitle={`${metrics.dynamicPermissions} dynamic`}
            icon={<KeyIcon />}
            onClick={() => onViewDetails('permissions')}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Risk Score"
            value={`${metrics.riskScore}%`}
            icon={<ShieldIcon />}
            color={getMetricColor(metrics.riskScore, 'risk')}
          />
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Security Alerts */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <WarningIcon color="warning" />
                  <Typography variant="h6">Security Alerts</Typography>
                  {securityAlerts.length > 0 && (
                    <Chip label={securityAlerts.length} size="small" color="warning" />
                  )}
                </Box>
              }
            />
            <CardContent sx={{ maxHeight: 400, overflow: 'auto' }}>
              {securityAlerts.length > 0 ? (
                <List>
                  {securityAlerts.slice(0, 5).map((alert, index) => (
                    <React.Fragment key={alert.id}>
                      <ListItem
                        button
                        onClick={() => {
                          setSelectedAlert(alert)
                          setAlertDialogOpen(true)
                        }}
                      >
                        <ListItemIcon>
                          {getSeverityIcon(alert.severity)}
                        </ListItemIcon>
                        <ListItemText
                          primary={alert.title}
                          secondary={alert.description}
                        />
                        <ListItemSecondaryAction>
                          <Chip
                            label={alert.severity.toUpperCase()}
                            size="small"
                            color={alert.severity === 'critical' ? 'error' : alert.severity === 'high' ? 'error' : 'warning'}
                          />
                        </ListItemSecondaryAction>
                      </ListItem>
                      {index < securityAlerts.slice(0, 5).length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              ) : (
                <Alert severity="success">
                  No security alerts. Your system is secure!
                </Alert>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* System Health */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <AnalyticsIcon color="primary" />
                  <Typography variant="h6">System Health</Typography>
                </Box>
              }
            />
            <CardContent>
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Compliance Score</Typography>
                  <Typography variant="body2" color={`${getMetricColor(metrics.complianceScore, 'compliance')}.main`}>
                    {metrics.complianceScore}%
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={metrics.complianceScore}
                  color={getMetricColor(metrics.complianceScore, 'compliance') as any}
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>

              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" color="warning.main">
                      {metrics.privilegedUsers}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Privileged Users
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" color="info.main">
                      {metrics.recentActivity}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Recent Activity (24h)
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12}>
          <Card>
            <CardHeader
              title={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <AssignmentIcon color="primary" />
                  <Typography variant="h6">Quick Actions</Typography>
                </Box>
              }
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<PersonIcon />}
                    onClick={() => onViewDetails('users')}
                  >
                    Manage Users
                  </Button>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<GroupIcon />}
                    onClick={() => onViewDetails('roles')}
                  >
                    Manage Roles
                  </Button>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<KeyIcon />}
                    onClick={() => onViewDetails('permissions')}
                  >
                    Manage Permissions
                  </Button>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<AnalyticsIcon />}
                    onClick={() => onViewDetails('roles')}
                  >
                    View Analytics
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Alert Details Dialog */}
      <Dialog
        open={alertDialogOpen}
        onClose={() => setAlertDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {selectedAlert && getSeverityIcon(selectedAlert.severity)}
            <Typography variant="h6">Security Alert Details</Typography>
          </Box>
        </DialogTitle>
        <DialogContent>
          {selectedAlert && (
            <Box sx={{ pt: 2 }}>
              <Typography variant="h6" sx={{ mb: 2 }}>
                {selectedAlert.title}
              </Typography>
              
              <Typography variant="body1" sx={{ mb: 2 }}>
                {selectedAlert.description}
              </Typography>

              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Affected Entity:
                </Typography>
                <Typography variant="body2">
                  {selectedAlert.affectedEntity}
                </Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Recommendation:
                </Typography>
                <Typography variant="body2">
                  {selectedAlert.recommendation}
                </Typography>
              </Box>

              <Box>
                <Typography variant="subtitle2" color="text.secondary">
                  Detected:
                </Typography>
                <Typography variant="body2">
                  {new Date(selectedAlert.createdAt).toLocaleString()}
                </Typography>
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAlertDialogOpen(false)}>Close</Button>
          <Button variant="contained" onClick={() => setAlertDialogOpen(false)}>
            Mark as Resolved
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default AccessControlDashboard
