from django.db import models
from django.conf import settings
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from courses.models import CourseOffering, Course
from students.models import Student

class AssessmentType(models.Model):
    """Model representing a type of assessment."""
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20, unique=True)
    description = models.TextField(blank=True, null=True)
    weight = models.DecimalField(max_digits=5, decimal_places=2, default=0.0,
                               validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
                               help_text="Weight in percentage (0-100)")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    def __str__(self):
        return f"{self.name} ({self.code})"

    class Meta:
        ordering = ['name']
        verbose_name = 'Assessment Type'
        verbose_name_plural = 'Assessment Types'

class Assessment(models.Model):
    """Model representing an assessment for a course offering."""
    STATUS_CHOICES = (
        ('DRAFT', 'Draft'),
        ('SCHEDULED', 'Scheduled'),
        ('IN_PROGRESS', 'In Progress'),
        ('COMPLETED', 'Completed'),
        ('GRADED', 'Graded'),
        ('CANCELLED', 'Cancelled'),
    )

    title = models.CharField(max_length=100)
    assessment_type = models.ForeignKey(AssessmentType, on_delete=models.CASCADE, related_name='assessments')
    course_offering = models.ForeignKey(CourseOffering, on_delete=models.CASCADE, related_name='assessments')
    description = models.TextField(blank=True, null=True)
    instructions = models.TextField(blank=True, null=True)
    total_marks = models.DecimalField(max_digits=7, decimal_places=2, default=100.0)
    passing_marks = models.DecimalField(max_digits=7, decimal_places=2, default=40.0)
    weight_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0.0,
                                          validators=[MinValueValidator(0.0), MaxValueValidator(100.0)])
    assessment_date = models.DateField()
    start_time = models.TimeField(null=True, blank=True)
    end_time = models.TimeField(null=True, blank=True)
    duration_minutes = models.PositiveIntegerField(default=60)
    location = models.CharField(max_length=100, blank=True, null=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='DRAFT')
    is_published = models.BooleanField(default=False)
    attachment = models.FileField(upload_to='assessments/', blank=True, null=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='created_assessments')
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    def __str__(self):
        return f"{self.title} - {self.course_offering}"

    class Meta:
        ordering = ['assessment_date', 'start_time']
        verbose_name = 'Assessment'
        verbose_name_plural = 'Assessments'

class AssessmentResult(models.Model):
    """Model representing a student's result for an assessment."""
    STATUS_CHOICES = (
        ('PENDING', 'Pending'),
        ('GRADED', 'Graded'),
        ('REVIEWED', 'Reviewed'),
        ('DISPUTED', 'Disputed'),
        ('FINALIZED', 'Finalized'),
    )

    assessment = models.ForeignKey(Assessment, on_delete=models.CASCADE, related_name='results')
    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='assessment_results')
    marks_obtained = models.DecimalField(max_digits=7, decimal_places=2, validators=[MinValueValidator(0.0)])
    percentage = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True,
                                   validators=[MinValueValidator(0.0), MaxValueValidator(100.0)])
    grade = models.CharField(max_length=2, blank=True, null=True)
    remarks = models.TextField(blank=True, null=True)
    is_absent = models.BooleanField(default=False)
    is_exempted = models.BooleanField(default=False)
    exemption_reason = models.TextField(blank=True, null=True)
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='PENDING')
    graded_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='graded_assessments')
    graded_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        # Calculate percentage if not provided
        if self.marks_obtained is not None and self.assessment.total_marks > 0:
            self.percentage = (self.marks_obtained / self.assessment.total_marks) * 100

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.student} - {self.assessment} - {self.marks_obtained}"

    class Meta:
        ordering = ['assessment', 'student']
        unique_together = ['assessment', 'student']
        verbose_name = 'Assessment Result'
        verbose_name_plural = 'Assessment Results'

class GradingSystem(models.Model):
    """Model representing a grading system."""
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20, unique=True)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['name']
        verbose_name = 'Grading System'
        verbose_name_plural = 'Grading Systems'

class GradeScale(models.Model):
    """Model representing a grade scale within a grading system."""
    grading_system = models.ForeignKey(GradingSystem, on_delete=models.CASCADE, related_name='grade_scales')
    grade = models.CharField(max_length=10)
    min_marks = models.DecimalField(max_digits=5, decimal_places=2, validators=[MinValueValidator(0.0), MaxValueValidator(100.0)])
    max_marks = models.DecimalField(max_digits=5, decimal_places=2, validators=[MinValueValidator(0.0), MaxValueValidator(100.0)])
    grade_point = models.DecimalField(max_digits=3, decimal_places=1, default=0.0)
    description = models.CharField(max_length=100, blank=True, null=True)

    def __str__(self):
        return f"{self.grade} ({self.min_marks}-{self.max_marks})"

    class Meta:
        ordering = ['-min_marks']
        unique_together = ['grading_system', 'grade']
        verbose_name = 'Grade Scale'
        verbose_name_plural = 'Grade Scales'

class Attendance(models.Model):
    """Model representing student attendance for a class session."""
    STATUS_CHOICES = (
        ('PRESENT', 'Present'),
        ('ABSENT', 'Absent'),
        ('LATE', 'Late'),
        ('EXCUSED', 'Excused Absence'),
    )

    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='attendance_records')
    course_offering = models.ForeignKey(CourseOffering, on_delete=models.CASCADE, related_name='attendance_records')
    date = models.DateField()
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='PRESENT')
    remarks = models.CharField(max_length=255, blank=True, null=True)
    late_minutes = models.PositiveIntegerField(default=0, help_text="Minutes late (if status is 'Late')")
    recorded_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='recorded_attendance')
    recorded_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.student} - {self.course_offering} - {self.date} - {self.get_status_display()}"

    class Meta:
        ordering = ['-date']
        unique_together = ['student', 'course_offering', 'date']
        verbose_name = 'Attendance'
        verbose_name_plural = 'Attendance Records'

class Feedback(models.Model):
    """Model representing feedback for a student."""
    FEEDBACK_TYPE_CHOICES = (
        ('ACADEMIC', 'Academic'),
        ('BEHAVIORAL', 'Behavioral'),
        ('GENERAL', 'General'),
    )

    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='feedback')
    course_offering = models.ForeignKey(CourseOffering, on_delete=models.SET_NULL, null=True, blank=True, related_name='feedback')
    feedback_type = models.CharField(max_length=20, choices=FEEDBACK_TYPE_CHOICES)
    title = models.CharField(max_length=100)
    description = models.TextField()
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='given_feedback')
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)
    is_private = models.BooleanField(default=False)
    parent_notified = models.BooleanField(default=False)
    parent_notification_date = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.title} - {self.student}"

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Feedback'
        verbose_name_plural = 'Feedback'

class ReportCard(models.Model):
    """Model representing a student's report card for an academic term."""
    STATUS_CHOICES = (
        ('DRAFT', 'Draft'),
        ('PUBLISHED', 'Published'),
        ('ARCHIVED', 'Archived'),
    )

    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='report_cards')
    academic_year = models.ForeignKey('academics.AcademicYear', on_delete=models.CASCADE, related_name='report_cards')
    term = models.ForeignKey('courses.AcademicTerm', on_delete=models.CASCADE, related_name='report_cards')
    grade_level = models.ForeignKey('academics.GradeLevel', on_delete=models.CASCADE, related_name='report_cards')
    section = models.ForeignKey('academics.Section', on_delete=models.CASCADE, related_name='report_cards')
    grading_system = models.ForeignKey(GradingSystem, on_delete=models.CASCADE, related_name='report_cards')
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='DRAFT')
    gpa = models.DecimalField(max_digits=3, decimal_places=2, null=True, blank=True)
    total_marks = models.DecimalField(max_digits=7, decimal_places=2, null=True, blank=True)
    average_percentage = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    rank = models.PositiveIntegerField(null=True, blank=True)
    attendance_percentage = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    teacher_remarks = models.TextField(blank=True, null=True)
    principal_remarks = models.TextField(blank=True, null=True)
    parent_signature = models.BooleanField(default=False)
    parent_signature_date = models.DateField(null=True, blank=True)
    generated_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='generated_report_cards')
    generated_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)
    published_at = models.DateTimeField(null=True, blank=True)
    pdf_file = models.FileField(upload_to='report_cards/', null=True, blank=True)

    def __str__(self):
        return f"{self.student} - {self.term} ({self.academic_year})"

    class Meta:
        ordering = ['-academic_year__start_date', '-term__start_date']
        unique_together = ['student', 'academic_year', 'term']
        verbose_name = 'Report Card'
        verbose_name_plural = 'Report Cards'

class ReportCardDetail(models.Model):
    """Model representing details of a course in a report card."""
    report_card = models.ForeignKey(ReportCard, on_delete=models.CASCADE, related_name='details')
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='report_card_details')
    course_offering = models.ForeignKey(CourseOffering, on_delete=models.CASCADE, related_name='report_card_details')
    marks_obtained = models.DecimalField(max_digits=7, decimal_places=2, null=True, blank=True)
    total_marks = models.DecimalField(max_digits=7, decimal_places=2, default=100.0)
    percentage = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    grade = models.CharField(max_length=10, blank=True, null=True)
    grade_point = models.DecimalField(max_digits=3, decimal_places=1, null=True, blank=True)
    teacher_remarks = models.CharField(max_length=255, blank=True, null=True)
    attendance_percentage = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)

    def save(self, *args, **kwargs):
        # Calculate percentage if not provided
        if self.marks_obtained is not None and self.total_marks > 0:
            self.percentage = (self.marks_obtained / self.total_marks) * 100

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.course} - {self.report_card}"

    class Meta:
        ordering = ['course__name']
        unique_together = ['report_card', 'course']
        verbose_name = 'Report Card Detail'
        verbose_name_plural = 'Report Card Details'
