{% extends "base.html" %}

{% block title %}Registration Successful{% endblock %}

{% block content %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get tenant information from localStorage
        const schoolName = localStorage.getItem('schoolName');
        const schoolDomain = localStorage.getItem('schoolDomain');

        // Display tenant information
        if (schoolName) {
            document.getElementById('school-name').textContent = schoolName;
        }

        if (schoolDomain) {
            document.getElementById('school-domain').textContent = schoolDomain;

            // Create a link to the tenant domain
            const domainSpan = document.getElementById('school-domain');
            const domainLink = document.createElement('a');
            domainLink.href = 'http://' + schoolDomain;
            domainLink.textContent = schoolDomain;
            domainLink.target = '_blank';
            domainSpan.innerHTML = '';
            domainSpan.appendChild(domainLink);
        }
    });
</script>
<div class="container mt-5">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3>Registration Successful</h3>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <i class="bi bi-check-circle-fill text-success" style="font-size: 5rem;"></i>
                    </div>

                    <h4 class="text-center mb-4">Thank you for registering your school!</h4>

                    <p class="lead text-center">Your school has been registered and your tenant has been automatically created!</p>

                    <div class="alert alert-success mt-4">
                        <h5>Your School Tenant Information</h5>
                        <ul>
                            <li><strong>School Name:</strong> <span id="school-name"></span></li>
                            <li><strong>Domain:</strong> <span id="school-domain"></span></li>
                            <li><strong>Status:</strong> <span class="badge bg-success">Active</span></li>
                        </ul>
                    </div>

                    <div class="alert alert-info mt-4">
                        <h5>What happens next?</h5>
                        <ol>
                            <li>You can now access your school's management system using the domain above.</li>
                            <li>Use the admin credentials to log in and start setting up your school.</li>
                            <li>Add teachers, students, courses, and other data to your school's system.</li>
                        </ol>
                    </div>

                    <p class="text-center mt-4">If you have any questions, please contact our support team at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>

                    <div class="d-grid gap-2 col-6 mx-auto mt-4">
                        <a href="/" class="btn btn-primary">Return to Home</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
