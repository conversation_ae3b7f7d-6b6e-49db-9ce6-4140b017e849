import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kelem_sms.settings')
django.setup()

from django.db import connection
from django_tenants.utils import get_tenant_model, get_tenant_domain_model
from django.core.management import call_command

def setup_tenant(tenant_name=None):
    """
    Set up a tenant schema and apply migrations.

    Args:
        tenant_name: The schema name of the tenant to set up. If None, set up all tenants.
    """
    # Get the tenant model
    TenantModel = get_tenant_model()

    if tenant_name:
        # Set up a specific tenant
        try:
            tenant = TenantModel.objects.get(schema_name=tenant_name)
            setup_single_tenant(tenant)
        except TenantModel.DoesNotExist:
            print(f'Tenant {tenant_name} does not exist')
    else:
        # Set up all tenants
        tenants = TenantModel.objects.all()
        for tenant in tenants:
            setup_single_tenant(tenant)

def setup_single_tenant(tenant):
    """
    Set up a single tenant schema and apply migrations.

    Args:
        tenant: The tenant object to set up.
    """
    print(f'Setting up tenant: {tenant.name} ({tenant.schema_name})')

    # Connect to the tenant schema
    connection.set_tenant(tenant)

    # Apply migrations for the tenant apps
    print(f'Applying migrations for tenant: {tenant.schema_name}')

    # First, check if the academics_gradelevel table exists
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = current_schema()
                AND table_name = 'academics_gradelevel'
            )
        """)
        academics_table_exists = cursor.fetchone()[0]

    # If the academics_gradelevel table doesn't exist, we need to handle it specially
    if not academics_table_exists:
        print(f"academics_gradelevel table does not exist in schema {tenant.schema_name}")
        print(f"Handling circular dependencies...")

        # First, make sure the django_migrations table exists
        with connection.cursor() as cursor:
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS django_migrations (
                    id bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
                    app character varying(255) NOT NULL,
                    name character varying(255) NOT NULL,
                    applied timestamp with time zone NOT NULL
                )
            """)

        # Mark the academics migration as applied
        with connection.cursor() as cursor:
            cursor.execute(
                "INSERT INTO django_migrations (app, name, applied) VALUES (%s, %s, NOW()) ON CONFLICT DO NOTHING",
                ['academics', '0001_initial']
            )

        # Mark the courses migration as applied
        with connection.cursor() as cursor:
            cursor.execute(
                "INSERT INTO django_migrations (app, name, applied) VALUES (%s, %s, NOW()) ON CONFLICT DO NOTHING",
                ['courses', '0001_initial']
            )
            cursor.execute(
                "INSERT INTO django_migrations (app, name, applied) VALUES (%s, %s, NOW()) ON CONFLICT DO NOTHING",
                ['courses', '0002_alter_academicterm_options_alter_course_options_and_more']
            )

        # Create the academics_gradelevel table
        with connection.cursor() as cursor:
            cursor.execute("""
                CREATE TABLE academics_gradelevel (
                    id bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
                    name character varying(50) NOT NULL,
                    code character varying(10) NOT NULL UNIQUE,
                    sequence smallint NOT NULL,
                    description text,
                    program_id bigint NOT NULL REFERENCES academics_program(id) DEFERRABLE INITIALLY DEFERRED
                )
            """)

        # Create the academics_program table
        with connection.cursor() as cursor:
            cursor.execute("""
                CREATE TABLE academics_program (
                    id bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
                    name character varying(100) NOT NULL,
                    code character varying(20) NOT NULL UNIQUE,
                    level character varying(20) NOT NULL,
                    description text,
                    duration_years smallint NOT NULL,
                    is_active boolean NOT NULL,
                    created_at timestamp with time zone NOT NULL,
                    updated_at timestamp with time zone NOT NULL,
                    coordinator_id bigint REFERENCES auth_user(id) DEFERRABLE INITIALLY DEFERRED,
                    department_id bigint NOT NULL REFERENCES courses_department(id) DEFERRABLE INITIALLY DEFERRED
                )
            """)

    # Use the Django migrate command with fake-initial to avoid issues with existing tables
    try:
        call_command('migrate', '--schema', tenant.schema_name, '--fake-initial', verbosity=1)
    except Exception as e:
        print(f"Error applying migrations to tenant {tenant.schema_name}: {str(e)}")
        print(f"Trying to apply migrations one by one...")

        # Try to apply migrations for each app individually
        for app in ['authentication', 'students', 'courses', 'teachers', 'academics', 'assessments']:
            try:
                call_command('migrate', app, '--schema', tenant.schema_name, '--fake-initial', verbosity=1)
            except Exception as app_e:
                print(f"Error applying migrations for {app}: {str(app_e)}")

    print(f'Successfully set up tenant: {tenant.name} ({tenant.schema_name})')

def create_public_tenant():
    """
    Create the public tenant if it doesn't exist.
    """
    from tenants.models import School, Domain

    # Check if the public tenant already exists
    if School.objects.filter(schema_name='public').exists():
        print("Public tenant already exists.")
        return

    # Create the public tenant
    public_tenant = School.objects.create(
        schema_name='public',
        name='Public',
        address='Main Office',
        contact_email='<EMAIL>',
        contact_phone='************'
    )

    # Create a domain for the public tenant
    Domain.objects.create(
        domain='localhost',
        tenant=public_tenant,
        is_primary=True
    )

    print("Public tenant created successfully!")

if __name__ == '__main__':
    # Check if we should create the public tenant
    if len(sys.argv) > 1 and sys.argv[1] == 'create_public':
        create_public_tenant()
    else:
        # Get the tenant name from the command line arguments
        tenant_name = sys.argv[1] if len(sys.argv) > 1 else None

        # Set up the tenant(s)
        setup_tenant(tenant_name)
