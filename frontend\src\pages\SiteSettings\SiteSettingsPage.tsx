import React, { useState } from 'react'
import {
  Box,
  Container,
  Typography,
  Paper,
  Tabs,
  Tab,
  Alert,
  Snackbar,
  CircularProgress,
  Backdrop,
} from '@mui/material'
import {
  Settings as SettingsIcon,
  <PERSON>lette as PaletteIcon,
  Home as HomeIcon,
  Build as BuildIcon,
} from '@mui/icons-material'
import GeneralSettingsTab from './components/GeneralSettingsTab'
import BrandingSettingsTab from './components/BrandingSettingsTab'
import HeroSectionTab from './components/HeroSectionTab'
import MaintenanceTab from './components/MaintenanceTab'

interface TabPanelProps {
  children?: React.ReactNode
  index: number
  value: number
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`site-settings-tabpanel-${index}`}
      aria-labelledby={`site-settings-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  )
}

function a11yProps(index: number) {
  return {
    id: `site-settings-tab-${index}`,
    'aria-controls': `site-settings-tabpanel-${index}`,
  }
}

const SiteSettingsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0)
  const [loading, setLoading] = useState(false)
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error' | 'warning' | 'info',
  })

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue)
  }

  const showSnackbar = (message: string, severity: 'success' | 'error' | 'warning' | 'info' = 'success') => {
    setSnackbar({ open: true, message, severity })
  }

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false })
  }

  const handleSaveSuccess = () => {
    showSnackbar('Settings saved successfully!', 'success')
  }

  const handleSaveError = (error: string) => {
    showSnackbar(error, 'error')
  }

  const tabs = [
    {
      label: 'General',
      icon: <SettingsIcon />,
      component: (
        <GeneralSettingsTab
          onSaveSuccess={handleSaveSuccess}
          onSaveError={handleSaveError}
        />
      ),
    },
    {
      label: 'Branding',
      icon: <PaletteIcon />,
      component: (
        <BrandingSettingsTab
          onSaveSuccess={handleSaveSuccess}
          onSaveError={handleSaveError}
        />
      ),
    },
    {
      label: 'Hero Section',
      icon: <HomeIcon />,
      component: (
        <HeroSectionTab
          onSaveSuccess={handleSaveSuccess}
          onSaveError={handleSaveError}
        />
      ),
    },
    {
      label: 'Maintenance',
      icon: <BuildIcon />,
      component: (
        <MaintenanceTab
          onSaveSuccess={handleSaveSuccess}
          onSaveError={handleSaveError}
        />
      ),
    },
  ]

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: '#f5f5f5' }}>
      {/* White Header Section */}
      <Box
        sx={{
          bgcolor: 'white',
          color: '#1f2937',
          py: 4,
          px: 3,
          mb: 3,
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          borderRadius: 0,
        }}
      >
        <Container maxWidth="xl">
          <Typography variant="h4" component="h1" sx={{ fontWeight: 700, mb: 1 }}>
            Site Settings
          </Typography>
          <Typography variant="body1" sx={{ color: '#6b7280' }}>
            Configure your site's appearance, content, and functionality
          </Typography>
        </Container>
      </Box>

      <Container maxWidth="lg" sx={{ pb: 4 }}>
        {/* Main Content - White Card */}
        <Paper
          sx={{
            width: '100%',
            bgcolor: 'white',
            borderRadius: 0,
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
            overflow: 'hidden'
          }}
        >
          {/* Tabs */}
          <Box sx={{ borderBottom: 1, borderColor: 'divider', bgcolor: 'white' }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              aria-label="site settings tabs"
              variant="scrollable"
              scrollButtons="auto"
              sx={{
                '& .MuiTab-root': {
                  minHeight: 64,
                  textTransform: 'none',
                  fontWeight: 600,
                  color: '#6b7280',
                  '&.Mui-selected': {
                    color: '#6366f1',
                  },
                },
                '& .MuiTabs-indicator': {
                  backgroundColor: '#6366f1',
                  height: 3,
                },
              }}
            >
              {tabs.map((tab, index) => (
                <Tab
                  key={index}
                  icon={tab.icon}
                  label={tab.label}
                  iconPosition="start"
                  {...a11yProps(index)}
                />
              ))}
            </Tabs>
          </Box>

          {/* Tab Panels */}
          {tabs.map((tab, index) => (
            <TabPanel key={index} value={activeTab} index={index}>
              <Box sx={{ bgcolor: 'white', p: 3 }}>
                {tab.component}
              </Box>
            </TabPanel>
          ))}
        </Paper>
      </Container>

      {/* Loading Backdrop */}
      <Backdrop
        sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1 }}
        open={loading}
      >
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <CircularProgress color="inherit" />
          <Typography sx={{ mt: 2 }}>Saving settings...</Typography>
        </Box>
      </Backdrop>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  )
}

export default SiteSettingsPage
