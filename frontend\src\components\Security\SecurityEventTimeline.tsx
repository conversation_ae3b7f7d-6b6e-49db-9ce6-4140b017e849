import React from 'react'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Chip,
  Avatar,
  IconButton,
  Tooltip,
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
} from '@mui/material'
import {
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Security as SecurityIcon,
  Visibility as VisibilityIcon,
  VpnKey as VpnKeyIcon,
  AdminPanelSettings as AdminIcon,
  Settings as SettingsIcon,
  Login as LoginIcon,
  Logout as LogoutIcon,
  MoreVert as MoreVertIcon,
} from '@mui/icons-material'
import { SecurityEvent } from '../../services/securityService'

interface SecurityEventTimelineProps {
  events: SecurityEvent[]
  onEventClick?: (event: SecurityEvent) => void
  maxEvents?: number
  showUserAvatars?: boolean
}

const SecurityEventTimeline: React.FC<SecurityEventTimelineProps> = ({
  events,
  onEventClick,
  maxEvents = 10,
  showUserAvatars = true,
}) => {
  const getEventIcon = (type: string, severity: string) => {
    const iconProps = { fontSize: 'small' as const }
    
    switch (type) {
      case 'login':
        return <LoginIcon {...iconProps} />
      case 'logout':
        return <LogoutIcon {...iconProps} />
      case 'failed_login':
        return <ErrorIcon {...iconProps} />
      case 'password_change':
        return <VpnKeyIcon {...iconProps} />
      case 'permission_change':
        return <AdminIcon {...iconProps} />
      case 'data_access':
        return <VisibilityIcon {...iconProps} />
      case 'system_change':
        return <SettingsIcon {...iconProps} />
      default:
        return <SecurityIcon {...iconProps} />
    }
  }

  const getEventColor = (severity: string, status: string) => {
    if (status === 'failed' || status === 'blocked') {
      return 'error'
    }
    
    switch (severity) {
      case 'low':
        return 'success'
      case 'medium':
        return 'warning'
      case 'high':
        return 'error'
      case 'critical':
        return 'error'
      default:
        return 'primary'
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low':
        return 'success'
      case 'medium':
        return 'warning'
      case 'high':
        return 'error'
      case 'critical':
        return 'error'
      default:
        return 'default'
    }
  }

  const formatEventType = (type: string) => {
    return type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  const getTimeAgo = (timestamp: string) => {
    const now = new Date()
    const eventTime = new Date(timestamp)
    const diffInMinutes = Math.floor((now.getTime() - eventTime.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    
    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `${diffInHours}h ago`
    
    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays}d ago`
  }

  const displayEvents = events.slice(0, maxEvents)

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
          <Typography variant="h6">Recent Security Events</Typography>
          <Typography variant="caption" color="text.secondary">
            Showing {displayEvents.length} of {events.length} events
          </Typography>
        </Box>

        <Timeline sx={{ p: 0 }}>
          {displayEvents.map((event, index) => (
            <TimelineItem key={event.id}>
              <TimelineSeparator>
                <TimelineDot 
                  color={getEventColor(event.severity, event.status) as any}
                  variant={event.severity === 'critical' ? 'filled' : 'outlined'}
                >
                  {getEventIcon(event.type, event.severity)}
                </TimelineDot>
                {index < displayEvents.length - 1 && <TimelineConnector />}
              </TimelineSeparator>
              
              <TimelineContent sx={{ py: 1.5 }}>
                <Box 
                  sx={{ 
                    cursor: onEventClick ? 'pointer' : 'default',
                    p: 2,
                    borderRadius: 1,
                    bgcolor: 'background.paper',
                    border: '1px solid',
                    borderColor: 'divider',
                    transition: 'all 0.2s ease-in-out',
                    '&:hover': onEventClick ? {
                      borderColor: 'primary.main',
                      boxShadow: 1,
                    } : {},
                  }}
                  onClick={() => onEventClick?.(event)}
                >
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 1 }}>
                    <Box sx={{ flex: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          {event.description}
                        </Typography>
                        <Chip
                          label={event.severity.toUpperCase()}
                          size="small"
                          color={getSeverityColor(event.severity) as any}
                          variant="outlined"
                        />
                      </Box>
                      
                      <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
                        {formatEventType(event.type)} • {getTimeAgo(event.timestamp)}
                      </Typography>

                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
                        {showUserAvatars && (
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Avatar sx={{ width: 20, height: 20, fontSize: '0.75rem', bgcolor: 'primary.main' }}>
                              {event.user.charAt(0).toUpperCase()}
                            </Avatar>
                            <Typography variant="caption" color="text.secondary">
                              {event.user}
                            </Typography>
                          </Box>
                        )}
                        
                        <Typography variant="caption" color="text.secondary" sx={{ fontFamily: 'monospace' }}>
                          {event.ip_address}
                        </Typography>
                        
                        {event.location && (
                          <Typography variant="caption" color="text.secondary">
                            {event.location}
                          </Typography>
                        )}
                      </Box>
                    </Box>

                    {onEventClick && (
                      <Tooltip title="View Details">
                        <IconButton size="small" sx={{ ml: 1 }}>
                          <MoreVertIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}
                  </Box>

                  {/* Status Indicator */}
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {event.status === 'success' && (
                      <Chip label="Success" size="small" color="success" variant="filled" />
                    )}
                    {event.status === 'failed' && (
                      <Chip label="Failed" size="small" color="error" variant="filled" />
                    )}
                    {event.status === 'blocked' && (
                      <Chip label="Blocked" size="small" color="error" variant="filled" />
                    )}
                  </Box>
                </Box>
              </TimelineContent>
            </TimelineItem>
          ))}
        </Timeline>

        {events.length === 0 && (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <SecurityIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="body2" color="text.secondary">
              No security events found
            </Typography>
          </Box>
        )}

        {events.length > maxEvents && (
          <Box sx={{ textAlign: 'center', mt: 2 }}>
            <Typography variant="caption" color="text.secondary">
              {events.length - maxEvents} more events available
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  )
}

export default SecurityEventTimeline
