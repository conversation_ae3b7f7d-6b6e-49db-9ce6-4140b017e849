# Generated by Django 5.2.4 on 2025-07-26 13:54

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('site_settings', '0003_fix_existing_data'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveField(
            model_name='brandingsettings',
            name='favicon',
        ),
        migrations.RemoveField(
            model_name='brandingsettings',
            name='hero_background',
        ),
        migrations.RemoveField(
            model_name='brandingsettings',
            name='logo',
        ),
        migrations.RemoveField(
            model_name='brandingsettings',
            name='logo_dark',
        ),
        migrations.RemoveField(
            model_name='herosection',
            name='background_image',
        ),
        migrations.RemoveField(
            model_name='herosection',
            name='background_video',
        ),
        migrations.RemoveField(
            model_name='sitesettings',
            name='facebook_pixel_id',
        ),
        migrations.RemoveField(
            model_name='sitesettings',
            name='google_analytics_id',
        ),
        migrations.RemoveField(
            model_name='sitesettings',
            name='maintenance_message',
        ),
        migrations.RemoveField(
            model_name='sitesettings',
            name='maintenance_mode',
        ),
        migrations.AddField(
            model_name='brandingsettings',
            name='updated_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='herosection',
            name='updated_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='sitesettings',
            name='updated_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='brandingsettings',
            name='background_default',
            field=models.CharField(default='#FAFAFA', max_length=7),
        ),
        migrations.AlterField(
            model_name='brandingsettings',
            name='background_paper',
            field=models.CharField(default='#FFFFFF', max_length=7),
        ),
        migrations.AlterField(
            model_name='brandingsettings',
            name='custom_css',
            field=models.TextField(blank=True, default=''),
        ),
        migrations.AlterField(
            model_name='brandingsettings',
            name='error_color',
            field=models.CharField(default='#F44336', max_length=7),
        ),
        migrations.AlterField(
            model_name='brandingsettings',
            name='font_family_primary',
            field=models.CharField(default='Roboto', max_length=100),
        ),
        migrations.AlterField(
            model_name='brandingsettings',
            name='font_family_secondary',
            field=models.CharField(default='Arial', max_length=100),
        ),
        migrations.AlterField(
            model_name='brandingsettings',
            name='info_color',
            field=models.CharField(default='#2196F3', max_length=7),
        ),
        migrations.AlterField(
            model_name='brandingsettings',
            name='primary_color',
            field=models.CharField(default='#2E7D32', max_length=7),
        ),
        migrations.AlterField(
            model_name='brandingsettings',
            name='primary_dark',
            field=models.CharField(default='#1B5E20', max_length=7),
        ),
        migrations.AlterField(
            model_name='brandingsettings',
            name='primary_light',
            field=models.CharField(default='#4CAF50', max_length=7),
        ),
        migrations.AlterField(
            model_name='brandingsettings',
            name='secondary_color',
            field=models.CharField(default='#FFC107', max_length=7),
        ),
        migrations.AlterField(
            model_name='brandingsettings',
            name='secondary_dark',
            field=models.CharField(default='#FF8F00', max_length=7),
        ),
        migrations.AlterField(
            model_name='brandingsettings',
            name='secondary_light',
            field=models.CharField(default='#FFEB3B', max_length=7),
        ),
        migrations.AlterField(
            model_name='brandingsettings',
            name='shadow_intensity',
            field=models.IntegerField(default=2),
        ),
        migrations.AlterField(
            model_name='brandingsettings',
            name='success_color',
            field=models.CharField(default='#4CAF50', max_length=7),
        ),
        migrations.AlterField(
            model_name='brandingsettings',
            name='text_primary',
            field=models.CharField(default='#212121', max_length=7),
        ),
        migrations.AlterField(
            model_name='brandingsettings',
            name='text_secondary',
            field=models.CharField(default='#757575', max_length=7),
        ),
        migrations.AlterField(
            model_name='brandingsettings',
            name='warning_color',
            field=models.CharField(default='#FF9800', max_length=7),
        ),
        migrations.AlterField(
            model_name='herosection',
            name='description',
            field=models.TextField(default='A comprehensive solution for managing students, courses, and academic activities.'),
        ),
        migrations.AlterField(
            model_name='herosection',
            name='primary_button_url',
            field=models.CharField(default='/register', max_length=200),
        ),
        migrations.AlterField(
            model_name='herosection',
            name='secondary_button_text',
            field=models.CharField(default='Learn More', max_length=50),
        ),
        migrations.AlterField(
            model_name='herosection',
            name='secondary_button_url',
            field=models.CharField(default='/about', max_length=200),
        ),
        migrations.AlterField(
            model_name='herosection',
            name='subtitle',
            field=models.CharField(default='Student Management System', max_length=300),
        ),
        migrations.AlterField(
            model_name='herosection',
            name='title',
            field=models.CharField(default='Welcome to Kelem SMS', max_length=200),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='contact_address',
            field=models.TextField(blank=True, default=''),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='contact_email',
            field=models.EmailField(blank=True, default='', max_length=254),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='contact_phone',
            field=models.CharField(blank=True, default='', max_length=20),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='facebook_url',
            field=models.URLField(blank=True, default='', validators=[django.core.validators.URLValidator()]),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='linkedin_url',
            field=models.URLField(blank=True, default='', validators=[django.core.validators.URLValidator()]),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='meta_description',
            field=models.TextField(blank=True, default='', max_length=500),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='meta_keywords',
            field=models.TextField(blank=True, default=''),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='meta_title',
            field=models.CharField(blank=True, default='', max_length=200),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='site_description',
            field=models.TextField(blank=True, default=''),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='site_name',
            field=models.CharField(default='Kelem SMS', max_length=200),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='site_tagline',
            field=models.CharField(blank=True, default='', max_length=500),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='twitter_url',
            field=models.URLField(blank=True, default='', validators=[django.core.validators.URLValidator()]),
        ),
        migrations.AlterField(
            model_name='sitesettings',
            name='youtube_url',
            field=models.URLField(blank=True, default='', validators=[django.core.validators.URLValidator()]),
        ),
        migrations.CreateModel(
            name='MaintenanceMode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('maintenance_mode', models.BooleanField(default=False)),
                ('maintenance_message', models.TextField(default='We are currently performing maintenance to improve your experience. Please check back soon.')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Maintenance Mode',
                'verbose_name_plural': 'Maintenance Mode',
            },
        ),
    ]
