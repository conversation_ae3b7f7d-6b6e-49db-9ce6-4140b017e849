import axios from 'axios'
import { createCachedApiCall } from './cacheService'

const API_BASE_URL = 'http://localhost:8001/api/auth'

// Create axios instance for user access API calls
const userAccessApi = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Add auth token to requests
userAccessApi.interceptors.request.use((config) => {
  const token = localStorage.getItem('token')
  if (token) {
    config.headers.Authorization = `Token ${token}`
  }
  return config
})

// Response interceptor for error handling
userAccessApi.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('User Access API Error:', error)
    return Promise.reject(error)
  }
)

// Types
export interface User {
  id: string
  username: string
  email: string
  first_name: string
  last_name: string
  role: string
  permissions: string[]
  is_active: boolean
  last_login: string
  date_joined: string
  avatar?: string
  groups: string[]
}

export interface Role {
  id: string
  name: string
  description: string
  permissions: string[]
  user_count: number
  is_system: boolean
  created_at: string
  updated_at: string
}

export interface Permission {
  id: string
  name: string
  codename: string
  content_type: string
  description?: string
  category: string
}

export interface UserCreateData {
  username: string
  email: string
  first_name: string
  last_name: string
  password: string
  role: string
  is_active: boolean
  groups?: string[]
}

export interface UserUpdateData {
  username?: string
  email?: string
  first_name?: string
  last_name?: string
  role?: string
  is_active?: boolean
  groups?: string[]
}

export interface RoleCreateData {
  name: string
  description: string
  permissions: string[]
}

export interface RoleUpdateData {
  name?: string
  description?: string
  permissions?: string[]
}

export interface UserAccessStats {
  total_users: number
  active_users: number
  inactive_users: number
  total_roles: number
  total_permissions: number
  users_by_role: Record<string, number>
  recent_logins: number
}

// API Functions

// Users
const getUsersUncached = async (params?: any): Promise<{ results: User[]; count: number }> => {
  try {
    const response = await userAccessApi.get('/users/', { params })
    return response.data
  } catch (error) {
    console.error('Error fetching users:', error)
    throw error
  }
}

export const getUsers = createCachedApiCall(
  getUsersUncached,
  (params?: any) => {
    const currentTenant = JSON.parse(localStorage.getItem('currentTenant') || '{}')
    const tenantId = currentTenant?.id || 'default'
    const paramsStr = params ? JSON.stringify(params) : 'no-params'
    return `users.list.${tenantId}.${paramsStr}`
  },
  2 * 60 * 1000 // 2 minutes cache
)

const getUserUncached = async (id: string): Promise<User> => {
  try {
    const response = await userAccessApi.get(`/users/${id}/`)
    return response.data
  } catch (error) {
    console.error('Error fetching user:', error)
    throw error
  }
}

export const getUser = createCachedApiCall(
  getUserUncached,
  (id: string) => {
    const currentTenant = JSON.parse(localStorage.getItem('currentTenant') || '{}')
    const tenantId = currentTenant?.id || 'default'
    return `users.detail.${tenantId}.${id}`
  },
  5 * 60 * 1000 // 5 minutes cache
)

export const createUser = async (data: UserCreateData): Promise<User> => {
  try {
    const response = await userAccessApi.post('/users/', data)
    // Invalidate users cache
    cacheService.invalidate('users.list')
    return response.data
  } catch (error) {
    console.error('Error creating user:', error)
    throw error
  }
}

export const updateUser = async (id: string, data: UserUpdateData): Promise<User> => {
  try {
    const response = await userAccessApi.patch(`/users/${id}/`, data)
    // Invalidate user cache
    return response.data
  } catch (error) {
    console.error('Error updating user:', error)
    throw error
  }
}

export const deleteUser = async (id: string): Promise<void> => {
  try {
    await userAccessApi.delete(`/users/${id}/`)
    // Invalidate users cache
  } catch (error) {
    console.error('Error deleting user:', error)
    throw error
  }
}

export const toggleUserStatus = async (id: string): Promise<User> => {
  try {
    const response = await userAccessApi.post(`/users/${id}/toggle-status/`)
    return response.data
  } catch (error) {
    console.error('Error toggling user status:', error)
    throw error
  }
}

// Roles
const getRolesUncached = async (): Promise<Role[]> => {
  try {
    const response = await userAccessApi.get('/roles/')
    return response.data.results || response.data
  } catch (error) {
    console.error('Error fetching roles:', error)
    throw error
  }
}

export const getRoles = createCachedApiCall(
  getRolesUncached,
  () => {
    const currentTenant = JSON.parse(localStorage.getItem('currentTenant') || '{}')
    const tenantId = currentTenant?.id || 'default'
    return `roles.list.${tenantId}`
  },
  10 * 60 * 1000 // 10 minutes cache
)

export const getRole = async (id: string): Promise<Role> => {
  try {
    const response = await userAccessApi.get(`/roles/${id}/`)
    return response.data
  } catch (error) {
    console.error('Error fetching role:', error)
    throw error
  }
}

export const createRole = async (data: RoleCreateData): Promise<Role> => {
  try {
    const response = await userAccessApi.post('/roles/', data)
    // Invalidate roles cache
    cacheService.invalidate('roles.list')
    return response.data
  } catch (error) {
    console.error('Error creating role:', error)
    throw error
  }
}

export const updateRole = async (id: string, data: RoleUpdateData): Promise<Role> => {
  try {
    const response = await userAccessApi.patch(`/roles/${id}/`, data)
    return response.data
  } catch (error) {
    console.error('Error updating role:', error)
    throw error
  }
}

export const deleteRole = async (id: string): Promise<void> => {
  try {
    await userAccessApi.delete(`/roles/${id}/`)
  } catch (error) {
    console.error('Error deleting role:', error)
    throw error
  }
}

// Permissions
const getPermissionsUncached = async (): Promise<Permission[]> => {
  try {
    const response = await userAccessApi.get('/permissions/')
    return response.data.results || response.data
  } catch (error) {
    console.error('Error fetching permissions:', error)
    throw error
  }
}

export const getPermissions = createCachedApiCall(
  getPermissionsUncached,
  () => 'permissions.list',
  30 * 60 * 1000 // 30 minutes cache (permissions change rarely)
)

export const getUserPermissions = async (userId: string): Promise<string[]> => {
  try {
    const response = await userAccessApi.get(`/users/${userId}/permissions/`)
    return response.data.permissions
  } catch (error) {
    console.error('Error fetching user permissions:', error)
    throw error
  }
}

export const updateUserPermissions = async (userId: string, permissions: string[]): Promise<void> => {
  try {
    await userAccessApi.post(`/users/${userId}/permissions/`, { permissions })
  } catch (error) {
    console.error('Error updating user permissions:', error)
    throw error
  }
}

// Groups
export const getGroups = async (): Promise<Array<{ id: string; name: string; permissions: string[] }>> => {
  try {
    const response = await userAccessApi.get('/groups/')
    return response.data.results || response.data
  } catch (error) {
    console.error('Error fetching groups:', error)
    throw error
  }
}

// Statistics
export const getUserAccessStats = async (): Promise<UserAccessStats> => {
  try {
    const response = await userAccessApi.get('/stats/')
    return response.data
  } catch (error) {
    console.error('Error fetching user access stats:', error)
    throw error
  }
}

// Bulk operations
export const bulkUpdateUsers = async (updates: Array<{ id: string; data: UserUpdateData }>): Promise<User[]> => {
  try {
    const response = await userAccessApi.post('/users/bulk-update/', { updates })
    return response.data
  } catch (error) {
    console.error('Error bulk updating users:', error)
    throw error
  }
}

export const bulkDeleteUsers = async (userIds: string[]): Promise<void> => {
  try {
    await userAccessApi.post('/users/bulk-delete/', { user_ids: userIds })
  } catch (error) {
    console.error('Error bulk deleting users:', error)
    throw error
  }
}

// Password management
export const resetUserPassword = async (userId: string): Promise<{ temporary_password: string }> => {
  try {
    const response = await userAccessApi.post(`/users/${userId}/reset-password/`)
    return response.data
  } catch (error) {
    console.error('Error resetting user password:', error)
    throw error
  }
}

export const changeUserPassword = async (userId: string, newPassword: string): Promise<void> => {
  try {
    await userAccessApi.post(`/users/${userId}/change-password/`, { password: newPassword })
  } catch (error) {
    console.error('Error changing user password:', error)
    throw error
  }
}

// Utility functions
export const transformUserForForm = (user: User) => ({
  username: user.username,
  email: user.email,
  firstName: user.first_name,
  lastName: user.last_name,
  role: user.role,
  isActive: user.is_active,
  groups: user.groups,
})

export const transformFormDataToUser = (formData: any): UserCreateData | UserUpdateData => ({
  username: formData.username,
  email: formData.email,
  first_name: formData.firstName,
  last_name: formData.lastName,
  role: formData.role,
  is_active: formData.isActive,
  groups: formData.groups || [],
  ...(formData.password && { password: formData.password }),
})

export const transformRoleForForm = (role: Role) => ({
  name: role.name,
  description: role.description,
  permissions: role.permissions,
})

export const transformFormDataToRole = (formData: any): RoleCreateData | RoleUpdateData => ({
  name: formData.name,
  description: formData.description,
  permissions: formData.permissions || [],
})

export const hasPermission = (user: User, permission: string): boolean => {
  return user.permissions.includes(permission) || user.permissions.includes('all')
}

export const getUserRoleColor = (role: string): string => {
  switch (role.toLowerCase()) {
    case 'super admin':
      return '#ef4444'
    case 'admin':
      return '#f59e0b'
    case 'teacher':
      return '#3b82f6'
    case 'staff':
      return '#8b5cf6'
    case 'student':
      return '#10b981'
    default:
      return '#6b7280'
  }
}

export const getPermissionsByCategory = (permissions: Permission[]): Record<string, Permission[]> => {
  return permissions.reduce((acc, permission) => {
    const category = permission.category || 'Other'
    if (!acc[category]) {
      acc[category] = []
    }
    acc[category].push(permission)
    return acc
  }, {} as Record<string, Permission[]>)
}
