import os
import psutil
import datetime
from django.db import connection
from django.utils import timezone
from django.db.models import Count, Sum, Avg, Max
from django_tenants.utils import schema_context
from tenants.models import School, Domain
from authentication.models import User
from students.models import Student
from courses.models import Department, Course, Enrollment
from .models import TenantMetrics, TenantActivity, TenantStatus

def collect_all_tenant_metrics():
    """
    Collect metrics for all tenants.

    Returns:
        List of created TenantMetrics objects
    """
    return collect_tenant_metrics()

def collect_tenant_metrics(tenant_id=None):
    """
    Collect metrics for a specific tenant or all tenants.

    Args:
        tenant_id: ID of the tenant to collect metrics for. If None, collect for all tenants.

    Returns:
        List of created TenantMetrics objects
    """
    tenants = School.objects.all()
    if tenant_id:
        tenants = tenants.filter(id=tenant_id)

    created_metrics = []

    for tenant in tenants:
        try:
            # Create metrics object
            metrics = TenantMetrics(tenant=tenant)

            # Collect metrics from the tenant's schema
            with schema_context(tenant.schema_name):
                # User metrics
                metrics.total_users = User.objects.count()
                metrics.active_users = User.objects.filter(last_login__gte=timezone.now() - datetime.timedelta(days=30)).count()
                metrics.admin_users = User.objects.filter(user_type='admin').count()
                metrics.teacher_users = User.objects.filter(user_type='teacher').count()
                metrics.student_users = User.objects.filter(user_type='student').count()
                metrics.parent_users = User.objects.filter(user_type='parent').count()
                metrics.staff_users = User.objects.filter(user_type='staff').count()

                # Data metrics
                metrics.total_students = Student.objects.count()
                metrics.total_departments = Department.objects.count()
                metrics.total_courses = Course.objects.count()
                metrics.total_enrollments = Enrollment.objects.count()

            # Database size metrics
            metrics.database_size = get_schema_size(tenant.schema_name)

            # File storage metrics
            metrics.file_storage_size = get_tenant_file_storage_size(tenant.schema_name)

            # Save the metrics
            metrics.save()
            created_metrics.append(metrics)

            # Log activity
            TenantActivity.objects.create(
                tenant=tenant,
                activity_type='metrics_collection',
                description=f'Collected metrics for {tenant.name}',
                user_count=metrics.total_users
            )

            # Update or create tenant status
            update_tenant_status(tenant)

        except Exception as e:
            print(f"Error collecting metrics for tenant {tenant.name}: {str(e)}")

    return created_metrics

def get_schema_size(schema_name):
    """
    Get the size of a schema in bytes.

    Args:
        schema_name: Name of the schema

    Returns:
        Size of the schema in bytes
    """
    try:
        with connection.cursor() as cursor:
            cursor.execute(f"""
                SELECT pg_total_relation_size(quote_ident(table_name))
                FROM information_schema.tables
                WHERE table_schema = %s
            """, [schema_name])

            sizes = cursor.fetchall()
            total_size = sum(size[0] for size in sizes)
            return total_size
    except Exception as e:
        print(f"Error getting schema size for {schema_name}: {str(e)}")
        return 0

def get_tenant_file_storage_size(schema_name):
    """
    Get the size of a tenant's file storage in bytes.

    Args:
        schema_name: Name of the schema

    Returns:
        Size of the tenant's file storage in bytes
    """
    try:
        # Assuming media files are stored in a directory named after the schema
        media_path = os.path.join('media', schema_name)

        if not os.path.exists(media_path):
            return 0

        total_size = 0
        for dirpath, dirnames, filenames in os.walk(media_path):
            for f in filenames:
                fp = os.path.join(dirpath, f)
                total_size += os.path.getsize(fp)

        return total_size
    except Exception as e:
        print(f"Error getting file storage size for {schema_name}: {str(e)}")
        return 0

def update_tenant_status(tenant):
    """
    Update or create status for a tenant.

    Args:
        tenant: School object

    Returns:
        TenantStatus object
    """
    try:
        status, created = TenantStatus.objects.get_or_create(
            tenant=tenant,
            defaults={
                'status': 'active',
                'subscription_plan': 'free',
                'admin_email': tenant.contact_email
            }
        )

        # Update status based on metrics
        metrics = TenantMetrics.objects.filter(tenant=tenant).order_by('-timestamp').first()

        if metrics:
            # Check if tenant is over limits
            if metrics.total_users > status.max_users:
                status.notes = f"Tenant has exceeded the maximum number of users ({metrics.total_users}/{status.max_users})"

            if metrics.file_storage_size + metrics.database_size > status.max_storage:
                status.notes = f"Tenant has exceeded the maximum storage ({(metrics.file_storage_size + metrics.database_size) / 1024 / 1024:.2f}MB/{status.max_storage / 1024 / 1024:.2f}MB)"

        # Check subscription validity
        if status.subscription_end_date and status.subscription_end_date < timezone.now().date():
            status.status = 'expired'
            status.notes = f"Subscription expired on {status.subscription_end_date}"

        status.save()
        return status
    except Exception as e:
        print(f"Error updating tenant status for {tenant.name}: {str(e)}")
        return None

def get_system_metrics():
    """
    Get system-wide metrics.

    Returns:
        Dictionary of system metrics
    """
    try:
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)

        # Memory usage
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_used = memory.used
        memory_total = memory.total

        # Disk usage
        disk = psutil.disk_usage('/')
        disk_percent = disk.percent
        disk_used = disk.used
        disk_total = disk.total

        # Network stats
        net_io = psutil.net_io_counters()
        bytes_sent = net_io.bytes_sent
        bytes_recv = net_io.bytes_recv

        # Database connections
        with connection.cursor() as cursor:
            cursor.execute("SELECT count(*) FROM pg_stat_activity")
            db_connections = cursor.fetchone()[0]

        return {
            'timestamp': timezone.now(),
            'cpu_percent': cpu_percent,
            'memory_percent': memory_percent,
            'memory_used': memory_used,
            'memory_total': memory_total,
            'disk_percent': disk_percent,
            'disk_used': disk_used,
            'disk_total': disk_total,
            'bytes_sent': bytes_sent,
            'bytes_recv': bytes_recv,
            'db_connections': db_connections,
            'tenant_count': School.objects.count(),
            'domain_count': Domain.objects.count(),
        }
    except Exception as e:
        print(f"Error getting system metrics: {str(e)}")
        return {
            'timestamp': timezone.now(),
            'error': str(e)
        }

def get_tenant_summary():
    """
    Get a summary of all tenants.

    Returns:
        Dictionary with tenant summary information
    """
    try:
        total_tenants = School.objects.count()
        active_tenants = TenantStatus.objects.filter(status='active').count()
        suspended_tenants = TenantStatus.objects.filter(status='suspended').count()
        trial_tenants = TenantStatus.objects.filter(status='trial').count()
        expired_tenants = TenantStatus.objects.filter(status='expired').count()

        # Get the latest metrics for each tenant
        latest_metrics = {}
        for tenant in School.objects.all():
            metrics = TenantMetrics.objects.filter(tenant=tenant).order_by('-timestamp').first()
            if metrics:
                latest_metrics[tenant.id] = metrics

        # Calculate totals
        total_users = sum(m.total_users for m in latest_metrics.values())
        total_students = sum(m.total_students for m in latest_metrics.values())
        total_courses = sum(m.total_courses for m in latest_metrics.values())
        total_storage = sum(m.database_size + m.file_storage_size for m in latest_metrics.values())

        # Get top tenants by users
        top_tenants_by_users = sorted(
            [(tenant_id, metrics.total_users) for tenant_id, metrics in latest_metrics.items()],
            key=lambda x: x[1],
            reverse=True
        )[:5]

        # Get top tenants by storage
        top_tenants_by_storage = sorted(
            [(tenant_id, metrics.database_size + metrics.file_storage_size) for tenant_id, metrics in latest_metrics.items()],
            key=lambda x: x[1],
            reverse=True
        )[:5]

        return {
            'total_tenants': total_tenants,
            'active_tenants': active_tenants,
            'suspended_tenants': suspended_tenants,
            'trial_tenants': trial_tenants,
            'expired_tenants': expired_tenants,
            'total_users': total_users,
            'total_students': total_students,
            'total_courses': total_courses,
            'total_storage': total_storage,
            'top_tenants_by_users': top_tenants_by_users,
            'top_tenants_by_storage': top_tenants_by_storage,
        }
    except Exception as e:
        print(f"Error getting tenant summary: {str(e)}")
        return {
            'error': str(e)
        }
