import { useState, useEffect } from 'react'
import {
  Box,
  Container,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  Chip,
  Snackbar,
} from '@mui/material'
import {
  Refresh as RefreshIcon,
  Storage as StorageIcon,
  People as PeopleIcon,
  School as SchoolIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Speed as SpeedIcon,
  Memory as MemoryIcon,
  BarChart as BarChartIcon,
  Error as ErrorIcon,
} from '@mui/icons-material'
import {
  getDashboardData,
  formatBytes,
  DashboardData,
  getTenants,
  Tenant
} from '../../services/tenantService'
import TenantStatusChart from '../../components/TenantManagement/TenantStatusChart'
import TenantStorageChart from '../../components/TenantManagement/TenantStorageChart'
import TenantUsersChart from '../../components/TenantManagement/TenantUsersChart'
import SystemMetricsCard from '../../components/TenantManagement/SystemMetricsCard'
import TenantListTable from '../../components/TenantManagement/TenantListTable'
import TenantHealthMonitor from '../../components/TenantManagement/TenantHealthMonitor'

const TenantDashboardPage = () => {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null)
  const [recentTenants, setRecentTenants] = useState<Tenant[]>([])
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'warning' | 'info';
  }>({
    open: false,
    message: '',
    severity: 'info'
  })

  // Extract data from dashboardData with fallbacks
  const system_metrics = dashboardData?.system_metrics || {
    timestamp: new Date().toISOString(),
    cpu_percent: 0,
    memory_percent: 0,
    memory_used: 0,
    memory_total: 0,
    disk_percent: 0,
    disk_used: 0,
    disk_total: 0,
    db_connections: 0,
    bytes_sent: 0,
    bytes_recv: 0,
  }

  const tenant_summary = dashboardData?.tenant_summary || {
    total_tenants: 0,
    active_tenants: 0,
    suspended_tenants: 0,
    trial_tenants: 0,
    expired_tenants: 0,
    total_users: 0,
    total_students: 0,
    total_courses: 0,
    total_storage: 0,
    top_tenants_by_users: [],
    top_tenants_by_storage: [],
  }

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      setError(null)

      console.log('Starting to fetch dashboard data...')

      // Fetch dashboard data and recent tenants in parallel
      const [data, tenantsResponse] = await Promise.all([
        getDashboardData(),
        getTenants({ ordering: '-created_on', limit: 5 })
      ])

      console.log('Dashboard data received:', data)
      console.log('Tenants response received:', tenantsResponse)

      setDashboardData(data)
      setRecentTenants(tenantsResponse.results || [])
    } catch (err: any) {
      console.error('Error fetching dashboard data:', err)
      setError(err.message || 'Failed to load dashboard data')

      // Set default data to prevent crashes
      setDashboardData({
        system_metrics: {
          timestamp: new Date().toISOString(),
          cpu_percent: 0,
          memory_percent: 0,
          memory_used: 0,
          memory_total: 0,
          disk_percent: 0,
          disk_used: 0,
          disk_total: 0,
          db_connections: 0,
          bytes_sent: 0,
          bytes_recv: 0,
          tenant_count: 0,
          domain_count: 0,
        },
        tenant_summary: {
          total_tenants: 0,
          active_tenants: 0,
          suspended_tenants: 0,
          trial_tenants: 0,
          expired_tenants: 0,
          total_users: 0,
          total_students: 0,
          total_courses: 0,
          total_storage: 0,
          top_tenants_by_users: [],
          top_tenants_by_storage: [],
        },
        tenant_health: {
          healthy_count: 0,
          warning_count: 0,
          critical_count: 0,
          tenants: []
        }
      })
      setRecentTenants([])
    } finally {
      setLoading(false)
    }
  }



  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress size={60} />
      </Box>
    )
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        {error}
      </Alert>
    )
  }

  return (
    <Container maxWidth="lg" sx={{ py: 1 }}>
      {/* Header Section */}
      <Box sx={{ mb: 2 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 700, mb: 0.5 }}>
          Tenant Management
        </Typography>
        <Typography variant="body1" sx={{ color: '#6b7280' }}>
          Monitor and manage all tenants across the platform
        </Typography>
      </Box>

      {/* Quick Stats Cards */}
      <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={6} sm={3}>
            <Card sx={{ bgcolor: 'white', borderRadius: 0, boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)' }}>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" sx={{ fontWeight: 700, mb: 0.5, color: '#1f2937' }}>
                  {tenant_summary.total_tenants}
                </Typography>
                <Typography variant="body2" sx={{ color: '#6b7280' }}>
                  Total Tenants
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Card sx={{ bgcolor: 'white', borderRadius: 0, boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)' }}>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" sx={{ fontWeight: 700, mb: 0.5, color: '#1f2937' }}>
                  {tenant_summary.active_tenants}
                </Typography>
                <Typography variant="body2" sx={{ color: '#6b7280' }}>
                  Active
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Card sx={{ bgcolor: 'white', borderRadius: 0, boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)' }}>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" sx={{ fontWeight: 700, mb: 0.5, color: '#1f2937' }}>
                  {tenant_summary.total_users}
                </Typography>
                <Typography variant="body2" sx={{ color: '#6b7280' }}>
                  Total Users
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Card sx={{ bgcolor: 'white', borderRadius: 0, boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)' }}>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" sx={{ fontWeight: 700, mb: 0.5, color: '#1f2937' }}>
                  {tenant_summary.total_students}
                </Typography>
                <Typography variant="body2" sx={{ color: '#6b7280' }}>
                  Total Students
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Enhanced Information Cards */}
        <Card sx={{ p: 2, mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Box
                sx={{
                  width: 80,
                  height: 80,
                  borderRadius: '50%',
                  background: 'rgba(255, 255, 255, 0.9)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mr: 3,
                  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)',
                }}
              >
                <SchoolIcon sx={{ fontSize: 40, color: '#667eea' }} />
              </Box>
              <Box>
                <Typography variant="h3" sx={{ fontWeight: 700, mb: 1 }}>
                  Tenant Management Dashboard
                </Typography>
                <Typography variant="h6" sx={{ opacity: 0.9, fontWeight: 400 }}>
                  Comprehensive platform oversight and control
                </Typography>
              </Box>
          </Box>

          <Typography variant="body1" paragraph sx={{ opacity: 0.9, mb: 2, fontSize: '1.1rem', lineHeight: 1.6 }}>
            Monitor all school tenants, track system performance, and manage resources across your entire educational platform.
            Get real-time insights into tenant health, user activity, and system utilization.
          </Typography>

          {/* Enhanced Stats Grid with Equal Heights */}
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  textAlign: 'center',
                  p: 3,
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  color: 'white',
                  boxShadow: '0 8px 32px rgba(102, 126, 234, 0.3)',
                  transition: 'transform 0.2s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 12px 40px rgba(102, 126, 234, 0.4)',
                  }
                }}
              >
                <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                  <SchoolIcon sx={{ fontSize: 48, mb: 2, color: 'white' }} />
                  <Typography variant="h3" sx={{ fontWeight: 700, mb: 1 }}>
                    {tenant_summary.total_tenants}
                  </Typography>
                  <Typography variant="h6" sx={{ opacity: 0.9, mb: 2 }}>
                    Total Tenants
                  </Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1 }}>
                    <Chip
                      size="small"
                      label={`${tenant_summary.active_tenants} Active`}
                      icon={<CheckCircleIcon />}
                      sx={{
                        bgcolor: 'rgba(255, 255, 255, 0.2)',
                        color: 'white',
                        '& .MuiChip-icon': { color: 'white' }
                      }}
                    />
                  </Box>
                </Box>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  textAlign: 'center',
                  p: 3,
                  background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                  color: 'white',
                  boxShadow: '0 8px 32px rgba(79, 172, 254, 0.3)',
                  transition: 'transform 0.2s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 12px 40px rgba(79, 172, 254, 0.4)',
                  }
                }}
              >
                <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                  <PeopleIcon sx={{ fontSize: 48, mb: 2, color: 'white' }} />
                  <Typography variant="h3" sx={{ fontWeight: 700, mb: 1 }}>
                    {tenant_summary.total_users.toLocaleString()}
                  </Typography>
                  <Typography variant="h6" sx={{ opacity: 0.9, mb: 2 }}>
                    Total Users
                  </Typography>
                  <Box sx={{ height: 32, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <Typography variant="body2" sx={{ opacity: 0.8 }}>
                      Across all tenants
                    </Typography>
                  </Box>
                </Box>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  textAlign: 'center',
                  p: 3,
                  background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
                  color: 'white',
                  boxShadow: '0 8px 32px rgba(250, 112, 154, 0.3)',
                  transition: 'transform 0.2s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 12px 40px rgba(250, 112, 154, 0.4)',
                  }
                }}
              >
                <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                  <SchoolIcon sx={{ fontSize: 48, mb: 2, color: 'white' }} />
                  <Typography variant="h3" sx={{ fontWeight: 700, mb: 1 }}>
                    {tenant_summary.total_students.toLocaleString()}
                  </Typography>
                  <Typography variant="h6" sx={{ opacity: 0.9, mb: 2 }}>
                    Total Students
                  </Typography>
                  <Box sx={{ height: 32, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <Typography variant="body2" sx={{ opacity: 0.8 }}>
                      Platform-wide
                    </Typography>
                  </Box>
                </Box>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  textAlign: 'center',
                  p: 3,
                  background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
                  color: '#2c3e50',
                  boxShadow: '0 8px 32px rgba(168, 237, 234, 0.3)',
                  transition: 'transform 0.2s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 12px 40px rgba(168, 237, 234, 0.4)',
                  }
                }}
              >
                <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                  <StorageIcon sx={{ fontSize: 48, mb: 2, color: '#2c3e50' }} />
                  <Typography variant="h3" sx={{ fontWeight: 700, mb: 1 }}>
                    {formatBytes(tenant_summary.total_storage)}
                  </Typography>
                  <Typography variant="h6" sx={{ opacity: 0.8, mb: 2 }}>
                    Total Storage
                  </Typography>
                  <Box sx={{ height: 32, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <Typography variant="body2" sx={{ opacity: 0.7 }}>
                      Used storage
                    </Typography>
                  </Box>
                </Box>
              </Card>
            </Grid>
          </Grid>
      </Card>

      {/* System Metrics */}
      <Card sx={{ p: 2, mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box
              sx={{
                width: 60,
                height: 60,
                borderRadius: 2,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mr: 3,
                boxShadow: '0 4px 16px rgba(102, 126, 234, 0.3)',
              }}
            >
              <SpeedIcon sx={{ fontSize: 28, color: 'white' }} />
            </Box>
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 700, color: 'text.primary', mb: 0.5 }}>
                System Performance Metrics
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Real-time server and database performance monitoring
              </Typography>
            </Box>
          </Box>
          <Grid container spacing={2}>
            <Grid item xs={12} md={4}>
              <SystemMetricsCard
                title="CPU Usage"
                value={system_metrics.cpu_percent}
                icon={<SpeedIcon />}
                unit="%"
                color={system_metrics.cpu_percent > 80 ? 'error' : system_metrics.cpu_percent > 60 ? 'warning' : 'success'}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <SystemMetricsCard
                title="Memory Usage"
                value={system_metrics.memory_percent}
                icon={<MemoryIcon />}
                unit="%"
                subtext={`${formatBytes(system_metrics.memory_used)} / ${formatBytes(system_metrics.memory_total)}`}
                color={system_metrics.memory_percent > 80 ? 'error' : system_metrics.memory_percent > 60 ? 'warning' : 'success'}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <SystemMetricsCard
                title="Disk Usage"
                value={system_metrics.disk_percent}
                icon={<StorageIcon />}
                unit="%"
                subtext={`${formatBytes(system_metrics.disk_used)} / ${formatBytes(system_metrics.disk_total)}`}
                color={system_metrics.disk_percent > 80 ? 'error' : system_metrics.disk_percent > 60 ? 'warning' : 'success'}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <SystemMetricsCard
                title="Database Connections"
                value={system_metrics.db_connections}
                icon={<BarChartIcon />}
                color="info"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <SystemMetricsCard
                title="Network Traffic"
                value={formatBytes(system_metrics.bytes_recv + system_metrics.bytes_sent)}
                icon={<BarChartIcon />}
                subtext={`↓ ${formatBytes(system_metrics.bytes_recv)} / ↑ ${formatBytes(system_metrics.bytes_sent)}`}
                color="info"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <SystemMetricsCard
                title="Last Updated"
                value={new Date(system_metrics.timestamp).toLocaleString()}
                icon={<RefreshIcon />}
                color="info"
              />
            </Grid>
          </Grid>
      </Card>

      {/* Tenant Health Monitoring */}
      <Card sx={{ p: 2, mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box
              sx={{
                width: 60,
                height: 60,
                borderRadius: 2,
                background: 'linear-gradient(135deg, #4caf50 0%, #45a049 100%)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mr: 3,
                boxShadow: '0 4px 16px rgba(76, 175, 80, 0.3)',
              }}
            >
              <CheckCircleIcon sx={{ fontSize: 28, color: 'white' }} />
            </Box>
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 700, color: 'text.primary', mb: 0.5 }}>
                Tenant Health Status
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Monitor tenant system health and performance indicators
              </Typography>
            </Box>
          </Box>
          <Grid container spacing={2} sx={{ mb: 2 }}>
            <Grid item xs={12} md={4}>
              <SystemMetricsCard
                title="Healthy Tenants"
                value={dashboardData?.tenant_health?.healthy_count || 0}
                icon={<CheckCircleIcon />}
                color="success"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <SystemMetricsCard
                title="Warning Status"
                value={dashboardData?.tenant_health?.warning_count || 0}
                icon={<WarningIcon />}
                color="warning"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <SystemMetricsCard
                title="Critical Status"
                value={dashboardData?.tenant_health?.critical_count || 0}
                icon={<ErrorIcon />}
                color="error"
              />
            </Grid>
          </Grid>
      </Card>

      <Grid container spacing={2} sx={{ mb: 2 }}>
        {(dashboardData?.tenant_health?.tenants || []).map((tenant) => (
          <Grid item xs={12} sm={6} md={4} key={tenant.name}>
            <TenantHealthMonitor tenant={{
              name: tenant.name,
              status: tenant.status as 'healthy' | 'warning' | 'critical',
              metrics: {
                responseTime: (tenant as any).response_time || 0,
                errorRate: (tenant as any).error_rate || 0,
                uptime: (tenant as any).uptime || 0,
              }
            }} />
          </Grid>
        ))}
      </Grid>

      {/* Charts */}
      <Card sx={{ p: 2, mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box
              sx={{
                width: 60,
                height: 60,
                borderRadius: 2,
                background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mr: 3,
                boxShadow: '0 4px 16px rgba(255, 152, 0, 0.3)',
              }}
            >
              <BarChartIcon sx={{ fontSize: 28, color: 'white' }} />
            </Box>
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 700, color: 'text.primary', mb: 0.5 }}>
                Tenant Analytics
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Comprehensive insights and usage patterns across tenants
              </Typography>
            </Box>
          </Box>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Box sx={{ mb: 2 }}>
              <TenantStatusChart
                active={tenant_summary.active_tenants}
                suspended={tenant_summary.suspended_tenants}
                trial={tenant_summary.trial_tenants}
                expired={tenant_summary.expired_tenants}
              />
            </Box>
          </Grid>
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 2, height: '100%', borderRadius: 2 }}>
              <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <TenantUsersChart tenantData={(tenant_summary.top_tenants_by_users || []).map(item => [String(item[0]), item[1]])} />
              </Box>
            </Paper>
          </Grid>
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 2, height: '100%', borderRadius: 2 }}>
              <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <TenantStorageChart tenantData={(tenant_summary.top_tenants_by_storage || []).map(item => [String(item[0]), item[1]])} />
              </Box>
            </Paper>
          </Grid>
          </Grid>
      </Card>

      {/* Recent Tenants */}
      <Card sx={{ p: 2, mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box
              sx={{
                width: 60,
                height: 60,
                borderRadius: 2,
                background: 'linear-gradient(135deg, #9c27b0 0%, #673ab7 100%)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mr: 3,
                boxShadow: '0 4px 16px rgba(156, 39, 176, 0.3)',
              }}
            >
              <SchoolIcon sx={{ fontSize: 28, color: 'white' }} />
            </Box>
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 700, color: 'text.primary', mb: 0.5 }}>
                Recent Tenants
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Latest tenant registrations and activity overview
              </Typography>
            </Box>
          </Box>
          <Card sx={{ borderRadius: 3, boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)', overflow: 'hidden' }}>
            <TenantListTable tenants={recentTenants} maxItems={5} title="Recent Tenants" />
          </Card>
      </Card>

      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification(prev => ({ ...prev, open: false }))}
      >
        <Alert
          onClose={() => setNotification(prev => ({ ...prev, open: false }))}
          severity={notification.severity}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Container>
  )
}

export default TenantDashboardPage
