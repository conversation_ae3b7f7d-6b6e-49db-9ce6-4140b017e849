import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react'
import { Box } from '@mui/material'
import { calculateVisibleItems } from '../utils/performance'

interface VirtualListProps<T> {
  items: T[]
  itemHeight: number
  containerHeight: number
  renderItem: (item: T, index: number) => React.ReactNode
  overscan?: number
  className?: string
  onScroll?: (scrollTop: number) => void
}

function VirtualList<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5,
  className,
  onScroll,
}: VirtualListProps<T>) {
  const [scrollTop, setScrollTop] = useState(0)
  const scrollElementRef = useRef<HTMLDivElement>(null)

  const { start, end, offsetY } = useMemo(
    () => calculateVisibleItems(containerHeight, itemHeight, scrollTop, items.length, overscan),
    [containerHeight, itemHeight, scrollTop, items.length, overscan]
  )

  const visibleItems = useMemo(
    () => items.slice(start, end + 1),
    [items, start, end]
  )

  const totalHeight = items.length * itemHeight

  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    const newScrollTop = event.currentTarget.scrollTop
    setScrollTop(newScrollTop)
    onScroll?.(newScrollTop)
  }, [onScroll])

  // Scroll to specific item
  const scrollToItem = useCallback((index: number) => {
    if (scrollElementRef.current) {
      const scrollTop = index * itemHeight
      scrollElementRef.current.scrollTop = scrollTop
      setScrollTop(scrollTop)
    }
  }, [itemHeight])

  // Expose scrollToItem method
  React.useImperativeHandle(
    React.forwardRef(() => null),
    () => ({
      scrollToItem,
    }),
    [scrollToItem]
  )

  return (
    <Box
      ref={scrollElementRef}
      className={className}
      sx={{
        height: containerHeight,
        overflow: 'auto',
        position: 'relative',
      }}
      onScroll={handleScroll}
    >
      {/* Total height spacer */}
      <Box sx={{ height: totalHeight, position: 'relative' }}>
        {/* Visible items container */}
        <Box
          sx={{
            position: 'absolute',
            top: offsetY,
            left: 0,
            right: 0,
          }}
        >
          {visibleItems.map((item, index) => (
            <Box
              key={start + index}
              sx={{
                height: itemHeight,
                display: 'flex',
                alignItems: 'center',
              }}
            >
              {renderItem(item, start + index)}
            </Box>
          ))}
        </Box>
      </Box>
    </Box>
  )
}

export default VirtualList

// Hook for virtual scrolling
export const useVirtualList = <T,>(
  items: T[],
  itemHeight: number,
  containerHeight: number,
  overscan = 5
) => {
  const [scrollTop, setScrollTop] = useState(0)

  const visibleRange = useMemo(
    () => calculateVisibleItems(containerHeight, itemHeight, scrollTop, items.length, overscan),
    [containerHeight, itemHeight, scrollTop, items.length, overscan]
  )

  const visibleItems = useMemo(
    () => items.slice(visibleRange.start, visibleRange.end + 1),
    [items, visibleRange.start, visibleRange.end]
  )

  const totalHeight = items.length * itemHeight

  return {
    visibleItems,
    visibleRange,
    totalHeight,
    scrollTop,
    setScrollTop,
  }
}

// Virtual Grid component for 2D virtualization
interface VirtualGridProps<T> {
  items: T[]
  itemWidth: number
  itemHeight: number
  containerWidth: number
  containerHeight: number
  columnsCount: number
  renderItem: (item: T, index: number) => React.ReactNode
  overscan?: number
}

export function VirtualGrid<T>({
  items,
  itemWidth,
  itemHeight,
  containerWidth,
  containerHeight,
  columnsCount,
  renderItem,
  overscan = 5,
}: VirtualGridProps<T>) {
  const [scrollTop, setScrollTop] = useState(0)
  const [scrollLeft, setScrollLeft] = useState(0)

  const rowsCount = Math.ceil(items.length / columnsCount)

  const visibleRowStart = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
  const visibleRowEnd = Math.min(
    rowsCount - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  )

  const visibleColStart = Math.max(0, Math.floor(scrollLeft / itemWidth) - overscan)
  const visibleColEnd = Math.min(
    columnsCount - 1,
    Math.ceil((scrollLeft + containerWidth) / itemWidth) + overscan
  )

  const visibleItems = useMemo(() => {
    const visible: Array<{ item: T; index: number; row: number; col: number }> = []
    
    for (let row = visibleRowStart; row <= visibleRowEnd; row++) {
      for (let col = visibleColStart; col <= visibleColEnd; col++) {
        const index = row * columnsCount + col
        if (index < items.length) {
          visible.push({
            item: items[index],
            index,
            row,
            col,
          })
        }
      }
    }
    
    return visible
  }, [items, visibleRowStart, visibleRowEnd, visibleColStart, visibleColEnd, columnsCount])

  const totalWidth = columnsCount * itemWidth
  const totalHeight = rowsCount * itemHeight

  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(event.currentTarget.scrollTop)
    setScrollLeft(event.currentTarget.scrollLeft)
  }, [])

  return (
    <Box
      sx={{
        width: containerWidth,
        height: containerHeight,
        overflow: 'auto',
        position: 'relative',
      }}
      onScroll={handleScroll}
    >
      <Box
        sx={{
          width: totalWidth,
          height: totalHeight,
          position: 'relative',
        }}
      >
        {visibleItems.map(({ item, index, row, col }) => (
          <Box
            key={index}
            sx={{
              position: 'absolute',
              left: col * itemWidth,
              top: row * itemHeight,
              width: itemWidth,
              height: itemHeight,
            }}
          >
            {renderItem(item, index)}
          </Box>
        ))}
      </Box>
    </Box>
  )
}

// Infinite scroll hook
export const useInfiniteScroll = (
  hasMore: boolean,
  loadMore: () => void,
  threshold = 100
) => {
  const [isFetching, setIsFetching] = useState(false)

  const handleScroll = useCallback((scrollTop: number, containerHeight: number, totalHeight: number) => {
    if (isFetching || !hasMore) return

    const scrollBottom = scrollTop + containerHeight
    const shouldLoadMore = totalHeight - scrollBottom <= threshold

    if (shouldLoadMore) {
      setIsFetching(true)
      loadMore()
    }
  }, [isFetching, hasMore, threshold, loadMore])

  useEffect(() => {
    if (isFetching) {
      // Reset fetching state after load more completes
      const timer = setTimeout(() => setIsFetching(false), 1000)
      return () => clearTimeout(timer)
    }
  }, [isFetching])

  return { isFetching, handleScroll }
}

// Memoized list item component
export const MemoizedListItem = React.memo(
  ({ children, ...props }: { children: React.ReactNode; [key: string]: any }) => (
    <Box {...props}>{children}</Box>
  )
)
