from django.shortcuts import render, get_object_or_404
from django.db.models import Count, Q
from django.utils import timezone
from django.http import HttpResponse

from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from .models import Teacher, TeacherQualification, TeacherAssignment
from .serializers import TeacherSerializer, TeacherDetailSerializer, QualificationSerializer, TeacherAssignmentSerializer

# Custom permissions
class IsAdminOrTeacher(permissions.BasePermission):
    """Permission to check if the user is an admin or a teacher."""

    def has_permission(self, request, view):
        return request.user.is_authenticated and (
            request.user.is_staff or
            hasattr(request.user, 'teacher_profile')
        )

class IsAdmin(permissions.BasePermission):
    """Permission to check if the user is an admin."""

    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.is_staff

class IsTeacherOrAdmin(permissions.BasePermission):
    """Permission to check if the user is the teacher in question or an admin."""

    def has_object_permission(self, request, view, obj):
        # Allow if admin
        if request.user.is_staff:
            return True

        # Allow if this is the teacher's own profile
        if hasattr(request.user, 'teacher_profile') and request.user.teacher_profile == obj:
            return True

        return False

# ViewSets
class TeacherViewSet(viewsets.ModelViewSet):
    """API endpoint for managing teachers."""
    queryset = Teacher.objects.all()
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['department', 'employment_status', 'is_active']
    search_fields = ['user__first_name', 'user__last_name', 'employee_id', 'specialization']
    ordering_fields = ['user__last_name', 'user__first_name', 'date_joined']
    ordering = ['user__last_name', 'user__first_name']

    def get_serializer_class(self):
        if self.action == 'retrieve' or self.action == 'update' or self.action == 'partial_update':
            return TeacherDetailSerializer
        return TeacherSerializer

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated]
        elif self.action in ['update', 'partial_update']:
            permission_classes = [IsTeacherOrAdmin]
        else:
            permission_classes = [IsAdmin]
        return [permission() for permission in permission_classes]

    @swagger_auto_schema(
        operation_summary="Get teacher's course assignments",
        operation_description="Returns all course assignments for this teacher."
    )
    @action(detail=True, methods=['get'])
    def assignments(self, request, pk=None):
        """Get all course assignments for a teacher."""
        teacher = self.get_object()
        assignments = TeacherAssignment.objects.filter(teacher=teacher)
        serializer = TeacherAssignmentSerializer(assignments, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        operation_summary="Get teacher's qualifications",
        operation_description="Returns all qualifications for this teacher."
    )
    @action(detail=True, methods=['get'])
    def qualifications(self, request, pk=None):
        """Get all qualifications for a teacher."""
        teacher = self.get_object()
        qualifications = TeacherQualification.objects.filter(teacher=teacher)
        serializer = QualificationSerializer(qualifications, many=True)
        return Response(serializer.data)

class QualificationViewSet(viewsets.ModelViewSet):
    """API endpoint for managing teacher qualifications."""
    queryset = TeacherQualification.objects.all()
    serializer_class = QualificationSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['teacher', 'qualification_type']
    search_fields = ['title', 'institution']
    ordering_fields = ['year_obtained', 'title']
    ordering = ['-year_obtained']

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated]
        elif self.action in ['create', 'update', 'partial_update', 'destroy']:
            permission_classes = [IsTeacherOrAdmin]
        else:
            permission_classes = [IsAdmin]
        return [permission() for permission in permission_classes]

class TeacherAssignmentViewSet(viewsets.ModelViewSet):
    """API endpoint for managing teacher assignments to courses."""
    queryset = TeacherAssignment.objects.all()
    serializer_class = TeacherAssignmentSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['teacher', 'course_offering', 'role', 'is_active']
    search_fields = ['teacher__user__first_name', 'teacher__user__last_name', 'course_offering__course__name']
    ordering_fields = ['assigned_date', 'teacher__user__last_name']
    ordering = ['-assigned_date']

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [IsAdmin]
        return [permission() for permission in permission_classes]

# Template views
def teacher_dashboard(request):
    """Render the teacher dashboard template."""
    if not request.user.is_authenticated:
        return HttpResponse("Please log in to access this page", status=401)

    # Get counts for dashboard
    teacher_count = Teacher.objects.count()
    active_teacher_count = Teacher.objects.filter(is_active=True).count()

    # Get teachers by department
    teachers_by_department = Teacher.objects.values('department__name').annotate(
        count=Count('id')
    ).order_by('-count')

    # Get recent teacher assignments
    recent_assignments = TeacherAssignment.objects.all().order_by('-assigned_date')[:5]

    context = {
        'teacher_count': teacher_count,
        'active_teacher_count': active_teacher_count,
        'teachers_by_department': teachers_by_department,
        'recent_assignments': recent_assignments
    }

    return render(request, 'teachers/dashboard.html', context)