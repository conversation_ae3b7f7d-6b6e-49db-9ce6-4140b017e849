# Generated by Django 5.2.4 on 2025-07-26 12:06

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='BrandingSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('logo', models.ImageField(blank=True, null=True, upload_to='branding/logos/')),
                ('logo_dark', models.ImageField(blank=True, null=True, upload_to='branding/logos/')),
                ('favicon', models.ImageField(blank=True, null=True, upload_to='branding/favicons/')),
                ('hero_background', models.ImageField(blank=True, null=True, upload_to='branding/backgrounds/')),
                ('primary_color', models.CharField(default='#1976d2', max_length=7, validators=[django.core.validators.RegexValidator('^#[0-9A-Fa-f]{6}$', 'Enter a valid hex color')])),
                ('primary_light', models.CharField(default='#42a5f5', max_length=7, validators=[django.core.validators.RegexValidator('^#[0-9A-Fa-f]{6}$', 'Enter a valid hex color')])),
                ('primary_dark', models.CharField(default='#1565c0', max_length=7, validators=[django.core.validators.RegexValidator('^#[0-9A-Fa-f]{6}$', 'Enter a valid hex color')])),
                ('secondary_color', models.CharField(default='#dc004e', max_length=7, validators=[django.core.validators.RegexValidator('^#[0-9A-Fa-f]{6}$', 'Enter a valid hex color')])),
                ('secondary_light', models.CharField(default='#ff5983', max_length=7, validators=[django.core.validators.RegexValidator('^#[0-9A-Fa-f]{6}$', 'Enter a valid hex color')])),
                ('secondary_dark', models.CharField(default='#9a0036', max_length=7, validators=[django.core.validators.RegexValidator('^#[0-9A-Fa-f]{6}$', 'Enter a valid hex color')])),
                ('background_default', models.CharField(default='#fafafa', max_length=7, validators=[django.core.validators.RegexValidator('^#[0-9A-Fa-f]{6}$', 'Enter a valid hex color')])),
                ('background_paper', models.CharField(default='#ffffff', max_length=7, validators=[django.core.validators.RegexValidator('^#[0-9A-Fa-f]{6}$', 'Enter a valid hex color')])),
                ('text_primary', models.CharField(default='#212121', max_length=7, validators=[django.core.validators.RegexValidator('^#[0-9A-Fa-f]{6}$', 'Enter a valid hex color')])),
                ('text_secondary', models.CharField(default='#757575', max_length=7, validators=[django.core.validators.RegexValidator('^#[0-9A-Fa-f]{6}$', 'Enter a valid hex color')])),
                ('success_color', models.CharField(default='#4caf50', max_length=7, validators=[django.core.validators.RegexValidator('^#[0-9A-Fa-f]{6}$', 'Enter a valid hex color')])),
                ('warning_color', models.CharField(default='#ff9800', max_length=7, validators=[django.core.validators.RegexValidator('^#[0-9A-Fa-f]{6}$', 'Enter a valid hex color')])),
                ('error_color', models.CharField(default='#f44336', max_length=7, validators=[django.core.validators.RegexValidator('^#[0-9A-Fa-f]{6}$', 'Enter a valid hex color')])),
                ('info_color', models.CharField(default='#2196f3', max_length=7, validators=[django.core.validators.RegexValidator('^#[0-9A-Fa-f]{6}$', 'Enter a valid hex color')])),
                ('font_family_primary', models.CharField(default='"Roboto", "Helvetica", "Arial", sans-serif', max_length=100)),
                ('font_family_secondary', models.CharField(default='"Roboto", "Helvetica", "Arial", sans-serif', max_length=100)),
                ('border_radius_small', models.IntegerField(default=4)),
                ('border_radius_medium', models.IntegerField(default=8)),
                ('border_radius_large', models.IntegerField(default=12)),
                ('enable_shadows', models.BooleanField(default=True)),
                ('shadow_intensity', models.CharField(choices=[('light', 'Light'), ('medium', 'Medium'), ('heavy', 'Heavy')], default='medium', max_length=10)),
                ('custom_css', models.TextField(blank=True, help_text='Custom CSS to override default styles')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Branding Settings',
                'verbose_name_plural': 'Branding Settings',
            },
        ),
        migrations.CreateModel(
            name='HeroSection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(default='Transform Your School with Kelem SMS', max_length=200)),
                ('subtitle', models.TextField(default='Comprehensive student management system designed for Ethiopian schools')),
                ('description', models.TextField(blank=True, default='Streamline admissions, manage student records, track attendance, and enhance communication between teachers, students, and parents.')),
                ('primary_button_text', models.CharField(default='Get Started', max_length=50)),
                ('primary_button_url', models.CharField(default='/register-school', max_length=200)),
                ('secondary_button_text', models.CharField(default='View Pricing', max_length=50)),
                ('secondary_button_url', models.CharField(default='/pricing', max_length=200)),
                ('background_type', models.CharField(choices=[('gradient', 'Gradient'), ('image', 'Image'), ('video', 'Video'), ('solid', 'Solid Color')], default='gradient', max_length=20)),
                ('background_image', models.ImageField(blank=True, null=True, upload_to='hero/backgrounds/')),
                ('background_video', models.FileField(blank=True, null=True, upload_to='hero/videos/')),
                ('show_stats', models.BooleanField(default=True)),
                ('show_features', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Hero Section',
                'verbose_name_plural': 'Hero Section',
            },
        ),
        migrations.CreateModel(
            name='SiteSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('site_name', models.CharField(default='Kelem SMS', max_length=100)),
                ('site_tagline', models.CharField(default='Empowering Ethiopian Schools with Modern Technology', max_length=200)),
                ('site_description', models.TextField(default='Comprehensive student management system for Ethiopian schools')),
                ('contact_email', models.EmailField(default='<EMAIL>', max_length=254)),
                ('contact_phone', models.CharField(default='+251 11 123 4567', max_length=20)),
                ('contact_address', models.TextField(default='Addis Ababa, Ethiopia')),
                ('facebook_url', models.URLField(blank=True, null=True)),
                ('twitter_url', models.URLField(blank=True, null=True)),
                ('linkedin_url', models.URLField(blank=True, null=True)),
                ('youtube_url', models.URLField(blank=True, null=True)),
                ('meta_title', models.CharField(default='Kelem SMS - School Management System', max_length=60)),
                ('meta_description', models.CharField(default='Modern school management system for Ethiopian schools', max_length=160)),
                ('meta_keywords', models.CharField(default='school management, Ethiopia, education, SMS', max_length=200)),
                ('enable_registration', models.BooleanField(default=True)),
                ('enable_trial', models.BooleanField(default=True)),
                ('enable_contact_form', models.BooleanField(default=True)),
                ('enable_blog', models.BooleanField(default=False)),
                ('enable_testimonials', models.BooleanField(default=True)),
                ('maintenance_mode', models.BooleanField(default=False)),
                ('maintenance_message', models.TextField(blank=True, default='We are currently performing maintenance. Please check back soon.')),
                ('google_analytics_id', models.CharField(blank=True, max_length=20, null=True)),
                ('facebook_pixel_id', models.CharField(blank=True, max_length=20, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Site Settings',
                'verbose_name_plural': 'Site Settings',
            },
        ),
    ]
