from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'teachers'

router = DefaultRouter()
router.register(r'teachers', views.TeacherViewSet)
router.register(r'qualifications', views.QualificationViewSet)
router.register(r'assignments', views.TeacherAssignmentViewSet)

urlpatterns = [
    path('api/', include(router.urls)),
    
    # Dashboard view
    path('dashboard/', views.teacher_dashboard, name='dashboard'),
]
