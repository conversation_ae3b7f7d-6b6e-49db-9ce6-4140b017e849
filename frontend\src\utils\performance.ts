import { lazy, ComponentType } from 'react'

// Performance monitoring utilities
export const measurePerformance = (name: string, fn: () => void) => {
  const start = performance.now()
  fn()
  const end = performance.now()
  console.log(`${name} took ${end - start} milliseconds`)
}

export const measureAsyncPerformance = async (name: string, fn: () => Promise<any>) => {
  const start = performance.now()
  const result = await fn()
  const end = performance.now()
  console.log(`${name} took ${end - start} milliseconds`)
  return result
}

// Lazy loading with error boundary
export const lazyWithRetry = (importFn: () => Promise<{ default: ComponentType<any> }>) => {
  return lazy(async () => {
    try {
      return await importFn()
    } catch (error) {
      console.error('Failed to load component:', error)
      // Retry once
      return await importFn()
    }
  })
}

// Preload components for better UX
export const preloadComponent = (importFn: () => Promise<{ default: ComponentType<any> }>) => {
  const componentImport = importFn()
  return componentImport
}

// Bundle size analyzer
export const analyzeBundleSize = () => {
  if (process.env.NODE_ENV === 'development') {
    const scripts = document.querySelectorAll('script[src]')
    let totalSize = 0
    
    scripts.forEach(script => {
      const src = (script as HTMLScriptElement).src
      if (src.includes('localhost')) {
        fetch(src, { method: 'HEAD' })
          .then(response => {
            const size = response.headers.get('content-length')
            if (size) {
              totalSize += parseInt(size)
              console.log(`Script ${src}: ${(parseInt(size) / 1024).toFixed(2)} KB`)
            }
          })
          .catch(() => {})
      }
    })
    
    setTimeout(() => {
      console.log(`Total bundle size: ${(totalSize / 1024).toFixed(2)} KB`)
    }, 1000)
  }
}

// Memory usage monitoring
export const monitorMemoryUsage = () => {
  if ('memory' in performance) {
    const memory = (performance as any).memory
    console.log('Memory Usage:', {
      used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
      total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
      limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`
    })
  }
}

// Performance observer for Core Web Vitals
export const initPerformanceObserver = () => {
  if ('PerformanceObserver' in window) {
    // Largest Contentful Paint
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const lastEntry = entries[entries.length - 1]
      console.log('LCP:', lastEntry.startTime)
    })
    lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })

    // First Input Delay
    const fidObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry) => {
        console.log('FID:', entry.processingStart - entry.startTime)
      })
    })
    fidObserver.observe({ entryTypes: ['first-input'] })

    // Cumulative Layout Shift
    const clsObserver = new PerformanceObserver((list) => {
      let clsValue = 0
      const entries = list.getEntries()
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value
        }
      })
      console.log('CLS:', clsValue)
    })
    clsObserver.observe({ entryTypes: ['layout-shift'] })
  }
}

// Image optimization utilities
export const createOptimizedImageUrl = (url: string, width?: number, height?: number, quality = 80) => {
  if (!url) return ''
  
  // For development, return original URL
  if (process.env.NODE_ENV === 'development') {
    return url
  }
  
  // In production, you could integrate with image optimization services
  // like Cloudinary, ImageKit, or AWS CloudFront
  const params = new URLSearchParams()
  if (width) params.append('w', width.toString())
  if (height) params.append('h', height.toString())
  params.append('q', quality.toString())
  
  return `${url}?${params.toString()}`
}

// Debounce utility for performance
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

// Throttle utility for performance
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// Virtual scrolling helper
export const calculateVisibleItems = (
  containerHeight: number,
  itemHeight: number,
  scrollTop: number,
  totalItems: number,
  overscan = 5
) => {
  const visibleStart = Math.floor(scrollTop / itemHeight)
  const visibleEnd = Math.min(
    visibleStart + Math.ceil(containerHeight / itemHeight),
    totalItems - 1
  )
  
  return {
    start: Math.max(0, visibleStart - overscan),
    end: Math.min(totalItems - 1, visibleEnd + overscan),
    offsetY: Math.max(0, visibleStart - overscan) * itemHeight
  }
}

// Resource hints for better loading
export const addResourceHints = () => {
  // Preconnect to external domains
  const preconnectDomains = [
    'https://fonts.googleapis.com',
    'https://fonts.gstatic.com',
  ]
  
  preconnectDomains.forEach(domain => {
    const link = document.createElement('link')
    link.rel = 'preconnect'
    link.href = domain
    document.head.appendChild(link)
  })
}

// Service Worker registration
export const registerServiceWorker = async () => {
  if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js')
      console.log('Service Worker registered:', registration)
      return registration
    } catch (error) {
      console.error('Service Worker registration failed:', error)
    }
  }
}

// Critical CSS inlining helper
export const inlineCriticalCSS = (css: string) => {
  const style = document.createElement('style')
  style.textContent = css
  document.head.appendChild(style)
}

// Performance budget checker
export const checkPerformanceBudget = () => {
  const budget = {
    maxBundleSize: 500 * 1024, // 500KB
    maxImageSize: 100 * 1024,  // 100KB
    maxFCP: 2000,              // 2 seconds
    maxLCP: 2500,              // 2.5 seconds
  }
  
  // Check First Contentful Paint
  const fcpEntry = performance.getEntriesByType('paint')
    .find(entry => entry.name === 'first-contentful-paint')
  
  if (fcpEntry && fcpEntry.startTime > budget.maxFCP) {
    console.warn(`FCP (${fcpEntry.startTime}ms) exceeds budget (${budget.maxFCP}ms)`)
  }
  
  return budget
}

// Initialize all performance optimizations
export const initPerformanceOptimizations = () => {
  // Add resource hints
  addResourceHints()
  
  // Initialize performance observer
  initPerformanceObserver()
  
  // Register service worker
  registerServiceWorker()
  
  // Monitor memory usage in development
  if (process.env.NODE_ENV === 'development') {
    setInterval(monitorMemoryUsage, 30000) // Every 30 seconds
    analyzeBundleSize()
  }
  
  // Check performance budget
  setTimeout(checkPerformanceBudget, 5000)
}

// React component performance helpers
export const withPerformanceTracking = <P extends object>(
  Component: ComponentType<P>,
  componentName: string
) => {
  return (props: P) => {
    const renderStart = performance.now()
    
    const result = Component(props)
    
    const renderEnd = performance.now()
    if (process.env.NODE_ENV === 'development') {
      console.log(`${componentName} render time: ${renderEnd - renderStart}ms`)
    }
    
    return result
  }
}
