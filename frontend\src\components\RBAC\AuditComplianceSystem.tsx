import React, { useState, useEffect, useMemo } from 'react'
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  <PERSON><PERSON>graphy,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress,
  Badge,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  DatePicker,
} from '@mui/material'
import {
  Assignment as AssignmentIcon,
  Security as SecurityIcon,
  Download as DownloadIcon,
  Visibility as VisibilityIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
  Assessment as AssessmentIcon,
  Gavel as GavelIcon,
  Timeline as TimelineIcon,
  TrendingUp as TrendingUpIcon,
} from '@mui/icons-material'
import {
  AccessReview,
  AccessReviewFinding,
  ComplianceReport,
  ComplianceViolation,
  RBACEvent,
  User,
  Role,
  Permission,
} from '../../types/rbac'

interface AuditComplianceSystemProps {
  users: User[]
  roles: Role[]
  permissions: Permission[]
  accessReviews: AccessReview[]
  complianceReports: ComplianceReport[]
  auditEvents: RBACEvent[]
  onCreateAccessReview: (reviewData: Partial<AccessReview>) => void
  onGenerateComplianceReport: (type: string, startDate: string, endDate: string) => void
  onExportAuditLog: (startDate: string, endDate: string, format: string) => void
  onResolveFinding: (findingId: string) => void
  loading?: boolean
}

const AuditComplianceSystem: React.FC<AuditComplianceSystemProps> = ({
  users,
  roles,
  permissions,
  accessReviews,
  complianceReports,
  auditEvents,
  onCreateAccessReview,
  onGenerateComplianceReport,
  onExportAuditLog,
  onResolveFinding,
  loading = false,
}) => {
  const [activeTab, setActiveTab] = useState(0)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [dialogType, setDialogType] = useState<'review' | 'report' | 'export'>('review')
  const [selectedReview, setSelectedReview] = useState<AccessReview | null>(null)
  const [formData, setFormData] = useState<any>({})
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0],
  })

  const complianceMetrics = useMemo(() => {
    const totalUsers = users.length
    const usersWithMFA = users.filter(u => u.mfa_enabled).length
    const inactiveUsers = users.filter(u => !u.last_login || 
      new Date(u.last_login) < new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)
    ).length
    const privilegedUsers = users.filter(u => u.is_superuser || 
      u.roles.some(roleId => {
        const role = roles.find(r => r.id === roleId)
        return role && role.level <= 1
      })
    ).length
    const rolesWithExcessivePermissions = roles.filter(r => r.effective_permissions.length > 50).length
    
    const mfaCompliance = (usersWithMFA / totalUsers) * 100
    const accessReviewCompliance = accessReviews.filter(r => r.status === 'completed').length / Math.max(accessReviews.length, 1) * 100
    const overallCompliance = (mfaCompliance + accessReviewCompliance) / 2

    return {
      totalUsers,
      usersWithMFA,
      inactiveUsers,
      privilegedUsers,
      rolesWithExcessivePermissions,
      mfaCompliance,
      accessReviewCompliance,
      overallCompliance,
    }
  }, [users, roles, accessReviews])

  const recentFindings = useMemo(() => {
    const findings: AccessReviewFinding[] = []
    
    accessReviews.forEach(review => {
      if (review.findings) {
        findings.push(...review.findings.filter(f => f.status === 'open'))
      }
    })
    
    return findings.sort((a, b) => {
      const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 }
      return severityOrder[b.severity] - severityOrder[a.severity]
    }).slice(0, 10)
  }, [accessReviews])

  const handleOpenDialog = (type: typeof dialogType, review?: AccessReview) => {
    setDialogType(type)
    setSelectedReview(review || null)
    
    if (type === 'review') {
      setFormData({
        name: '',
        description: '',
        type: 'user_access',
        scope: { users: [], roles: [], resources: [] },
        assigned_to: [],
        due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      })
    } else if (type === 'report') {
      setFormData({
        type: 'sox',
        period_start: dateRange.start,
        period_end: dateRange.end,
      })
    } else if (type === 'export') {
      setFormData({
        start_date: dateRange.start,
        end_date: dateRange.end,
        format: 'csv',
      })
    }
    
    setDialogOpen(true)
  }

  const handleCloseDialog = () => {
    setDialogOpen(false)
    setSelectedReview(null)
    setFormData({})
  }

  const handleSave = () => {
    if (dialogType === 'review') {
      onCreateAccessReview(formData)
    } else if (dialogType === 'report') {
      onGenerateComplianceReport(formData.type, formData.period_start, formData.period_end)
    } else if (dialogType === 'export') {
      onExportAuditLog(formData.start_date, formData.end_date, formData.format)
    }
    handleCloseDialog()
  }

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <ErrorIcon color="error" />
      case 'high':
        return <WarningIcon color="error" />
      case 'medium':
        return <WarningIcon color="warning" />
      case 'low':
        return <InfoIcon color="info" />
      default:
        return <InfoIcon />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success'
      case 'in_progress':
        return 'info'
      case 'pending':
        return 'warning'
      case 'cancelled':
        return 'error'
      default:
        return 'default'
    }
  }

  const ComplianceMetricCard: React.FC<{
    title: string
    value: number
    total?: number
    percentage?: number
    color: 'success' | 'warning' | 'error'
    icon: React.ReactNode
  }> = ({ title, value, total, percentage, color, icon }) => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 700, color: `${color}.main` }}>
              {value}{total && `/${total}`}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {title}
            </Typography>
          </Box>
          <Box sx={{ color: `${color}.main`, fontSize: 40 }}>
            {icon}
          </Box>
        </Box>
        
        {percentage !== undefined && (
          <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="caption" color="text.secondary">
                Compliance
              </Typography>
              <Typography variant="caption" color={`${color}.main`}>
                {percentage.toFixed(1)}%
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={percentage}
              color={color}
              sx={{ height: 6, borderRadius: 3 }}
            />
          </Box>
        )}
      </CardContent>
    </Card>
  )

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h5" sx={{ fontWeight: 700 }}>
            Audit & Compliance System
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Comprehensive audit trails and compliance monitoring
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => window.location.reload()}
            disabled={loading}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<AssignmentIcon />}
            onClick={() => handleOpenDialog('review')}
            sx={{
              background: 'linear-gradient(45deg, #6366f1 30%, #8b5cf6 90%)',
            }}
          >
            New Review
          </Button>
        </Box>
      </Box>

      {/* Compliance Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <ComplianceMetricCard
            title="MFA Compliance"
            value={complianceMetrics.usersWithMFA}
            total={complianceMetrics.totalUsers}
            percentage={complianceMetrics.mfaCompliance}
            color={complianceMetrics.mfaCompliance >= 80 ? 'success' : complianceMetrics.mfaCompliance >= 60 ? 'warning' : 'error'}
            icon={<SecurityIcon />}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <ComplianceMetricCard
            title="Access Reviews"
            value={accessReviews.filter(r => r.status === 'completed').length}
            total={accessReviews.length}
            percentage={complianceMetrics.accessReviewCompliance}
            color={complianceMetrics.accessReviewCompliance >= 80 ? 'success' : 'warning'}
            icon={<AssessmentIcon />}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <ComplianceMetricCard
            title="Inactive Users"
            value={complianceMetrics.inactiveUsers}
            color={complianceMetrics.inactiveUsers === 0 ? 'success' : complianceMetrics.inactiveUsers < 5 ? 'warning' : 'error'}
            icon={<WarningIcon />}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <ComplianceMetricCard
            title="Overall Compliance"
            value={Math.round(complianceMetrics.overallCompliance)}
            percentage={complianceMetrics.overallCompliance}
            color={complianceMetrics.overallCompliance >= 80 ? 'success' : complianceMetrics.overallCompliance >= 60 ? 'warning' : 'error'}
            icon={<GavelIcon />}
          />
        </Grid>
      </Grid>

      {/* Tabs */}
      <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)} sx={{ mb: 3 }}>
        <Tab label="Access Reviews" />
        <Tab label="Compliance Reports" />
        <Tab label="Audit Trail" />
        <Tab label="Findings" />
      </Tabs>

      {/* Access Reviews Tab */}
      {activeTab === 0 && (
        <Card>
          <CardHeader
            title="Access Reviews"
            action={
              <Button
                startIcon={<AssignmentIcon />}
                onClick={() => handleOpenDialog('review')}
              >
                Create Review
              </Button>
            }
          />
          <CardContent>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Review Name</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Assigned To</TableCell>
                    <TableCell>Due Date</TableCell>
                    <TableCell>Findings</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {accessReviews.map((review) => (
                    <TableRow key={review.id}>
                      <TableCell>
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          {review.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {review.description}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={review.type.replace('_', ' ').toUpperCase()}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={review.status.toUpperCase()}
                          size="small"
                          color={getStatusColor(review.status) as any}
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {review.assigned_to.join(', ')}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(review.due_date).toLocaleDateString()}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Badge badgeContent={review.findings.length} color="error">
                          <WarningIcon color="action" />
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Tooltip title="View Details">
                          <IconButton size="small">
                            <VisibilityIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      )}

      {/* Compliance Reports Tab */}
      {activeTab === 1 && (
        <Card>
          <CardHeader
            title="Compliance Reports"
            action={
              <Button
                startIcon={<AssessmentIcon />}
                onClick={() => handleOpenDialog('report')}
              >
                Generate Report
              </Button>
            }
          />
          <CardContent>
            <Grid container spacing={3}>
              {complianceReports.map((report) => (
                <Grid item xs={12} md={6} lg={4} key={report.id}>
                  <Card variant="outlined">
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                        <GavelIcon color="primary" />
                        <Typography variant="h6">{report.name}</Typography>
                      </Box>
                      
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        {report.type.toUpperCase()} Compliance Report
                      </Typography>

                      <Box sx={{ mb: 2 }}>
                        <Typography variant="caption" color="text.secondary">
                          Period: {new Date(report.period_start).toLocaleDateString()} - {new Date(report.period_end).toLocaleDateString()}
                        </Typography>
                      </Box>

                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                        <Typography variant="body2">
                          Compliance Score: <strong>{report.summary.compliance_score}%</strong>
                        </Typography>
                        <Chip
                          label={report.status.toUpperCase()}
                          size="small"
                          color={getStatusColor(report.status) as any}
                        />
                      </Box>

                      <Button
                        fullWidth
                        variant="outlined"
                        startIcon={<DownloadIcon />}
                        disabled={report.status !== 'completed'}
                      >
                        Download Report
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Audit Trail Tab */}
      {activeTab === 2 && (
        <Card>
          <CardHeader
            title="Audit Trail"
            action={
              <Button
                startIcon={<DownloadIcon />}
                onClick={() => handleOpenDialog('export')}
              >
                Export Logs
              </Button>
            }
          />
          <CardContent>
            <TableContainer sx={{ maxHeight: 600 }}>
              <Table stickyHeader>
                <TableHead>
                  <TableRow>
                    <TableCell>Timestamp</TableCell>
                    <TableCell>Event Type</TableCell>
                    <TableCell>User</TableCell>
                    <TableCell>Target</TableCell>
                    <TableCell>Details</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {auditEvents.slice(0, 50).map((event) => (
                    <TableRow key={event.id}>
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(event.timestamp).toLocaleString()}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={event.event_type.replace('_', ' ').toUpperCase()}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">{event.user_id}</Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {event.target_user_id || event.role_id || event.permission_id || '-'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" sx={{ maxWidth: 200, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                          {JSON.stringify(event.details)}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      )}

      {/* Findings Tab */}
      {activeTab === 3 && (
        <Card>
          <CardHeader title="Security Findings" />
          <CardContent>
            <List>
              {recentFindings.map((finding, index) => (
                <React.Fragment key={finding.id}>
                  <ListItem>
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2, width: '100%' }}>
                      {getSeverityIcon(finding.severity)}
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          {finding.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                          {finding.description}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Recommendation: {finding.recommendation}
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', gap: 1 }}>
                        <Chip
                          label={finding.severity.toUpperCase()}
                          size="small"
                          color={finding.severity === 'critical' ? 'error' : finding.severity === 'high' ? 'error' : 'warning'}
                        />
                        <Button
                          size="small"
                          onClick={() => onResolveFinding(finding.id)}
                          disabled={finding.status !== 'open'}
                        >
                          Resolve
                        </Button>
                      </Box>
                    </Box>
                  </ListItem>
                  {index < recentFindings.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
            
            {recentFindings.length === 0 && (
              <Alert severity="success">
                No open security findings. Your system is compliant!
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {/* Dialogs */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {dialogType === 'review' && 'Create Access Review'}
          {dialogType === 'report' && 'Generate Compliance Report'}
          {dialogType === 'export' && 'Export Audit Logs'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            {/* Dialog content would be implemented based on dialogType */}
            <Typography>Dialog content for {dialogType}</Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button variant="contained" onClick={handleSave}>
            {dialogType === 'review' && 'Create Review'}
            {dialogType === 'report' && 'Generate Report'}
            {dialogType === 'export' && 'Export Logs'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default AuditComplianceSystem
