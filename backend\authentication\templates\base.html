<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Kelem Student Management System{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">Kelem SMS</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    {% if user.is_authenticated %}
                        {% if user.user_type == 'admin' %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'authentication:admin_dashboard' %}">Admin Dashboard</a>
                        </li>
                        {% elif user.user_type == 'teacher' %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'authentication:teacher_dashboard' %}">Teacher Dashboard</a>
                        </li>
                        {% elif user.user_type == 'student' %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'authentication:student_dashboard' %}">Student Dashboard</a>
                        </li>
                        {% elif user.user_type == 'parent' %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'authentication:parent_dashboard' %}">Parent Dashboard</a>
                        </li>
                        {% elif user.user_type == 'staff' %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'authentication:staff_dashboard' %}">Staff Dashboard</a>
                        </li>
                        {% endif %}
                    {% endif %}
                </ul>
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            {{ user.get_full_name|default:user.email }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                            <li><a class="dropdown-item" href="{% url 'authentication:profile' %}">Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'authentication:api_logout' %}">Logout</a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'authentication:api_login' %}">Login</a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        {% if messages %}
        <div class="container mt-3">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>Kelem Student Management System</h5>
                    <p>A comprehensive solution for managing educational institutions.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>&copy; {% now "Y" %} Kelem SMS. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    {% block extra_js %}{% endblock %}
</body>
</html>
