"use client";
import {
  require_createSvgIcon
} from "./chunk-VKJE3PGV.js";
import "./chunk-5PQ5PKEE.js";
import "./chunk-BBWB4YLS.js";
import "./chunk-NSDJ2KRB.js";
import {
  require_interopRequireDefault
} from "./chunk-DIQD2LQY.js";
import "./chunk-HJS24R7O.js";
import "./chunk-Q7CPF5VB.js";
import "./chunk-K6FDVZ65.js";
import "./chunk-KGFDDYBK.js";
import {
  require_jsx_runtime
} from "./chunk-OT5EQO2H.js";
import "./chunk-OU5AQDZK.js";
import {
  __commonJS
} from "./chunk-EWTE5DHJ.js";

// node_modules/@mui/icons-material/Warning.js
var require_Warning = __commonJS({
  "node_modules/@mui/icons-material/Warning.js"(exports) {
    var _interopRequireDefault = require_interopRequireDefault();
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _createSvgIcon = _interopRequireDefault(require_createSvgIcon());
    var _jsxRuntime = require_jsx_runtime();
    var _default = exports.default = (0, _createSvgIcon.default)((0, _jsxRuntime.jsx)("path", {
      d: "M1 21h22L12 2zm12-3h-2v-2h2zm0-4h-2v-4h2z"
    }), "Warning");
  }
});
export default require_Warning();
//# sourceMappingURL=@mui_icons-material_Warning.js.map
