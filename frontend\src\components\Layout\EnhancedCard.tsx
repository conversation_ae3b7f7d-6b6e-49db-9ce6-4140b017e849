import React from 'react';
import { Card, Box, Typography } from '@mui/material';

interface EnhancedCardProps {
  children: React.ReactNode;
  title?: string;
  icon?: React.ReactNode;
  variant?: 'default' | 'info' | 'success' | 'warning' | 'error' | 'primary' | 'secondary';
  sx?: any;
}

const EnhancedCard: React.FC<EnhancedCardProps> = ({
  children,
  title,
  icon,
  variant = 'default',
  sx = {}
}) => {
  const getGradientBackground = () => {
    switch (variant) {
      case 'info':
        return 'linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%)';
      case 'success':
        return 'linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%)';
      case 'warning':
        return 'linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%)';
      case 'error':
        return 'linear-gradient(135deg, #fef2f2 0%, #fecaca 100%)';
      case 'primary':
        return 'linear-gradient(135deg, #ede7f6 0%, #d1c4e9 100%)';
      case 'secondary':
        return 'linear-gradient(135deg, #fef7ff 0%, #f3e8ff 100%)';
      default:
        return 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)';
    }
  };

  return (
    <Card
      sx={{
        p: 4,
        mb: 4,
        background: getGradientBackground(),
        border: '1px solid',
        borderColor: 'divider',
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'url("data:image/svg+xml,%3Csvg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23000000" fill-opacity="0.02"%3E%3Ccircle cx="10" cy="10" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
          opacity: 0.5,
        },
        ...sx
      }}
    >
      <Box sx={{ position: 'relative', zIndex: 1 }}>
        {(title || icon) && (
          <Box sx={{ display: 'flex', alignItems: 'center', mb: title ? 3 : 0 }}>
            {icon && (
              <Box
                sx={{
                  bgcolor: 'primary.main',
                  color: 'white',
                  p: 2,
                  borderRadius: 2,
                  mr: title ? 3 : 0,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                {icon}
              </Box>
            )}
            {title && (
              <Typography variant="h5" sx={{ fontWeight: 600, color: 'text.primary' }}>
                {title}
              </Typography>
            )}
          </Box>
        )}
        {children}
      </Box>
    </Card>
  );
};

export default EnhancedCard;
