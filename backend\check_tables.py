import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kelem_sms.settings')
django.setup()

from django.db import connection

def check_tables():
    """Check if specific tables exist in the database."""
    tables_to_check = [
        'authentication_user',
        'authentication_user_groups',
        'authentication_user_user_permissions',
        'auth_group',
        'auth_group_permissions',
        'auth_permission',
    ]
    
    with connection.cursor() as cursor:
        for table in tables_to_check:
            cursor.execute(f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = current_schema()
                    AND table_name = '{table}'
                )
            """)
            exists = cursor.fetchone()[0]
            print(f"Table '{table}': {'EXISTS' if exists else 'DOES NOT EXIST'}")

if __name__ == '__main__':
    check_tables()
