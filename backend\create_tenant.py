import os
import django
import traceback

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kelem_sms.settings')
django.setup()

from django.db import transaction
from django.utils import timezone
from school_registration.models import SchoolRegistrationRequest
from tenants.models import School, Domain
from django_tenants.utils import schema_context

def create_tenant_from_registration(registration_id):
    """Create a tenant from an existing registration request."""
    try:
        # Get the registration request
        registration = SchoolRegistrationRequest.objects.get(id=registration_id)
        print(f"Found registration: {registration.name} (Status: {registration.status})")
        
        # Check if tenant already exists
        existing_school = School.objects.filter(schema_name=registration.schema_name).first()
        if existing_school:
            print(f"Error: A tenant with schema name '{registration.schema_name}' already exists.")
            return
        
        # Check if domain already exists
        existing_domain = Domain.objects.filter(domain=registration.domain).first()
        if existing_domain:
            print(f"Error: A domain with name '{registration.domain}' already exists.")
            return
        
        with transaction.atomic():
            # Update the registration request
            registration.status = 'approved'
            registration.processed_on = timezone.now()
            
            # Make sure schema_name and domain are set
            if not registration.schema_name:
                registration.schema_name = registration.slug
            
            if not registration.domain:
                registration.domain = f"{registration.slug}.kelemsms.com"
            
            registration.save()
            
            print(f"Creating tenant with schema_name: {registration.schema_name}")
            print(f"Domain: {registration.domain}")
            
            # Create the tenant (school)
            school = School.objects.create(
                schema_name=registration.schema_name,
                name=registration.name,
                address=registration.address,
                contact_email=registration.contact_email,
                contact_phone=registration.contact_phone,
            )
            
            # Create the domain for the tenant
            domain = Domain.objects.create(
                domain=registration.domain,
                tenant=school,
                is_primary=True
            )
            
            print(f"Tenant created successfully!")
            print(f"School ID: {school.id}")
            print(f"Domain ID: {domain.id}")
            
            # Initialize the tenant schema with necessary data
            with schema_context(school.schema_name):
                # Here you can add code to initialize the tenant schema
                # For example, create default user groups, settings, etc.
                print(f"Initializing schema for {school.name}...")
                
            print(f"Schema initialization completed.")
            
            return school, domain
    except Exception as e:
        print(f"Error creating tenant: {str(e)}")
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    # Create tenant for Fasiledes school (registration ID 1)
    create_tenant_from_registration(1)
