{"name": "@mui/base", "version": "5.0.0-dev.20240529-082515-213b5e33ab", "private": false, "author": "MUI Team", "description": "Base UI is a library of headless ('unstyled') React components and low-level hooks. You gain complete control over your app's CSS and accessibility features.", "main": "./node/index.js", "keywords": ["react", "react-component", "mui", "unstyled", "a11y"], "repository": {"type": "git", "url": "https://github.com/mui/material-ui.git", "directory": "packages/mui-base"}, "license": "MIT", "bugs": {"url": "https://github.com/mui/material-ui/issues"}, "homepage": "https://mui.com/base-ui/", "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "dependencies": {"@babel/runtime": "^7.24.6", "@floating-ui/react-dom": "^2.0.8", "@popperjs/core": "^2.11.8", "clsx": "^2.1.1", "prop-types": "^15.8.1", "@mui/utils": "^6.0.0-dev.20240529-082515-213b5e33ab", "@mui/types": "^7.2.14-dev.20240529-082515-213b5e33ab"}, "peerDependencies": {"@types/react": "^17.0.0 || ^18.0.0", "react": "^17.0.0 || ^18.0.0", "react-dom": "^17.0.0 || ^18.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "sideEffects": false, "publishConfig": {"access": "public", "directory": "build"}, "engines": {"node": ">=12.0.0"}, "module": "./index.js", "types": "./index.d.ts"}