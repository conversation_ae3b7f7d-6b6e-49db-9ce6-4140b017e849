import os
import django
import sys

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kelem_sms.settings')
django.setup()

from django.contrib.auth import get_user_model, authenticate
from django.db import connection
from django_tenants.utils import schema_context
from tenants.models import School

User = get_user_model()

def check_user_exists(email):
    """Check if a user with the given email exists in any schema."""
    print(f"Checking if user with email {email} exists in any schema...")
    
    # Check public schema
    print("\nChecking public schema...")
    try:
        user = User.objects.filter(email=email).first()
        if user:
            print(f"User found in public schema with ID: {user.id}")
            print(f"  Email: {user.email}")
            print(f"  First name: {user.first_name}")
            print(f"  Last name: {user.last_name}")
            print(f"  User type: {user.user_type}")
            print(f"  Is superuser: {user.is_superuser}")
            print(f"  Is staff: {user.is_staff}")
            print(f"  Is active: {user.is_active}")
            print(f"  Password hash: {user.password[:20]}...")
        else:
            print(f"User with email {email} not found in public schema")
    except Exception as e:
        print(f"Error checking public schema: {str(e)}")
    
    # Check all tenant schemas
    print("\nChecking tenant schemas...")
    for school in School.objects.all():
        print(f"\nChecking schema {school.schema_name} (School: {school.name})...")
        try:
            with schema_context(school.schema_name):
                user = User.objects.filter(email=email).first()
                if user:
                    print(f"User found in schema {school.schema_name} with ID: {user.id}")
                    print(f"  Email: {user.email}")
                    print(f"  First name: {user.first_name}")
                    print(f"  Last name: {user.last_name}")
                    print(f"  User type: {user.user_type}")
                    print(f"  Is superuser: {user.is_superuser}")
                    print(f"  Is staff: {user.is_staff}")
                    print(f"  Is active: {user.is_active}")
                    print(f"  Password hash: {user.password[:20]}...")
                else:
                    print(f"User with email {email} not found in schema {school.schema_name}")
        except Exception as e:
            print(f"Error checking schema {school.schema_name}: {str(e)}")

def test_authenticate(email, password):
    """Test authenticating a user with the given email and password."""
    print(f"\nTesting authentication for user {email}...")
    
    # Get the current schema
    current_schema = connection.schema_name
    print(f"Current schema: {current_schema}")
    
    # Try to authenticate
    user = authenticate(None, email=email, password=password)
    
    if user:
        print("Authentication successful!")
        print(f"User ID: {user.id}")
        print(f"Email: {user.email}")
        print(f"First name: {user.first_name}")
        print(f"Last name: {user.last_name}")
        print(f"User type: {user.user_type}")
        print(f"Is superuser: {user.is_superuser}")
        print(f"Is staff: {user.is_staff}")
        print(f"Schema name: {getattr(user, '_schema_name', 'Not set')}")
    else:
        print("Authentication failed!")

def main():
    if len(sys.argv) < 2:
        print("Usage: python debug_login.py <email> [password]")
        return
    
    email = sys.argv[1]
    password = sys.argv[2] if len(sys.argv) > 2 else None
    
    # Check if the user exists
    check_user_exists(email)
    
    # Test authentication if password is provided
    if password:
        test_authenticate(email, password)

if __name__ == "__main__":
    main()
