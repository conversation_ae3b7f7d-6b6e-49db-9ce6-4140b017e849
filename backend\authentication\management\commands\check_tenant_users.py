from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django_tenants.utils import schema_context
from tenants.models import School

class Command(BaseCommand):
    help = 'Check users in a tenant schema'

    def add_arguments(self, parser):
        parser.add_argument('schema_name', type=str, help='The schema name to check')

    def handle(self, *args, **options):
        schema_name = options['schema_name']
        
        # Check if the schema exists
        try:
            school = School.objects.get(schema_name=schema_name)
            self.stdout.write(self.style.SUCCESS(f'Found school: {school.name} with schema: {schema_name}'))
        except School.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'No school found with schema: {schema_name}'))
            return
        
        # Check users in the schema
        with schema_context(schema_name):
            User = get_user_model()
            users = User.objects.all()
            
            self.stdout.write(self.style.SUCCESS(f'Found {users.count()} users in schema {schema_name}:'))
            
            for user in users:
                self.stdout.write(f'ID: {user.id}, Email: {user.email}, Is Staff: {user.is_staff}, Is Superuser: {user.is_superuser}')
                
            # Check the authentication_user table directly
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT COUNT(*) FROM authentication_user")
                count = cursor.fetchone()[0]
                self.stdout.write(self.style.SUCCESS(f'Direct count from authentication_user table: {count}'))
                
                # Get table structure
                cursor.execute("SELECT column_name FROM information_schema.columns WHERE table_name = 'authentication_user'")
                columns = [row[0] for row in cursor.fetchall()]
                self.stdout.write(self.style.SUCCESS(f'Columns in authentication_user table: {", ".join(columns)}'))
