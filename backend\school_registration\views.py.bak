import datetime
from django.shortcuts import render, get_object_or_404
from django.http import HttpResponse
from django.conf import settings
from django.db import transaction
from django.utils import timezone
from django.contrib.auth import get_user_model
import secrets
import string

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response

from django_tenants.utils import schema_context
from tenants.models import School, Domain

from .models import SchoolRegistrationRequest
from .serializers import SchoolRegistrationSerializer, SchoolRegistrationAdminSerializer
from authentication.decorators import admin_required

# Public views for school registration
class SchoolRegistrationViewSet(viewsets.ModelViewSet):
    """API endpoint for school registration requests."""
    queryset = SchoolRegistrationRequest.objects.all()
    serializer_class = SchoolRegistrationSerializer
    http_method_names = ['get', 'post']

    def get_permissions(self):
        """Only allow listing and retrieving for authenticated users."""
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [permissions.AllowAny]
        return [permission() for permission in permission_classes]

    def create(self, request, *args, **kwargs):
        """Create a new school registration request and auto-approve it."""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # Save the registration request
        registration = self.perform_create(serializer)

        # Make sure we have admin_email and admin_password
        if not registration.admin_email:
            registration.admin_email = registration.contact_email
            registration.save()

        print(f"Processing school registration for {registration.name}")
        print(f"Admin email: {registration.admin_email}")
        print(f"Admin password provided: {'Yes' if registration.admin_password else 'No'}")

        # Auto-approve the registration
        try:
            # Call the auto_approve method to create tenant
            tenant_info = self.auto_approve_registration(registration)

            headers = self.get_success_headers(serializer.data)
            return Response(
                {
                    "message": "School registration has been automatically approved and tenant has been created.",
                    "data": serializer.data,
                    "tenant": tenant_info
                },
                status=status.HTTP_201_CREATED, headers=headers
            )
        except Exception as e:
            # If auto-approval fails, still save the registration as pending
            import traceback
            print(f"Error auto-approving registration: {str(e)}")
            traceback.print_exc()

            headers = self.get_success_headers(serializer.data)
            return Response(
                {
                    "message": "School registration submitted successfully, but automatic tenant creation failed. An administrator will review your request.",
                    "data": serializer.data,
                    "error": str(e)
                },
                status=status.HTTP_201_CREATED, headers=headers
            )

    def perform_create(self, serializer):
        """Save the registration request."""
        return serializer.save()

    def auto_approve_registration(self, registration):
        """Auto-approve a registration and create tenant."""
        print(f"Auto-approving registration for {registration.name}")

        # Check if a tenant with this schema_name already exists
        existing_school = School.objects.filter(schema_name=registration.schema_name).first()
        if existing_school:
            raise Exception(f"A tenant with schema name '{registration.schema_name}' already exists.")

        # Check if a domain with this domain already exists
        existing_domain = Domain.objects.filter(domain=registration.domain).first()
        if existing_domain:
            raise Exception(f"A domain with name '{registration.domain}' already exists.")

        # Update the registration request
        registration.status = 'approved'
        registration.processed_on = timezone.now()

        # Make sure schema_name and domain are set
        if not registration.schema_name:
            # Create a PostgreSQL-compatible schema name (no hyphens, spaces, etc.)
            import re
            schema_name = re.sub(r'[^a-zA-Z0-9_]', '_', registration.slug)

            # Ensure it starts with a letter
            if not schema_name[0].isalpha():
                schema_name = 'sch_' + schema_name

            registration.schema_name = schema_name
            print(f"Set schema_name to {schema_name}")

        if not registration.domain:
            registration.domain = f"{registration.slug}.kelemsms.com"
            print(f"Set domain to {registration.domain}")

        registration.save()
        print(f"Updated registration status to 'approved'")

        # Create the tenant (school) without creating schema
        # Temporarily set the class attribute to False
        original_auto_create_schema = School.auto_create_schema
        School.auto_create_schema = False

        try:
            # Ensure schema_name is PostgreSQL compatible
            import re
            schema_name = registration.schema_name
            if not re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', schema_name):
                # If schema name is not valid, create a valid one
                schema_name = re.sub(r'[^a-zA-Z0-9_]', '_', schema_name)
                if not schema_name[0].isalpha():
                    schema_name = 'sch_' + schema_name
                registration.schema_name = schema_name
                registration.save()

            # Create the tenant (school)
            school = School(
                schema_name=registration.schema_name,
                name=registration.name,
                address=registration.address,
                contact_email=registration.contact_email,
                contact_phone=registration.contact_phone,
            )
            school.save()  # Save without creating schema

            # Create the domain for the tenant
            domain = Domain.objects.create(
                domain=registration.domain,
                tenant=school,
                is_primary=True
            )

            # Create the schema manually
            self.create_schema_for_tenant(school.schema_name)

            # Create an admin user for the tenant
            admin_user = self.create_admin_user_for_tenant(school, registration)

            return {
                "school_id": school.id,
                "domain": domain.domain,
                "schema_name": school.schema_name,
                "admin_user": admin_user
            }
        finally:
            # Restore the original value
            School.auto_create_schema = original_auto_create_schema

    def create_schema_for_tenant(self, schema_name):
        """Create a schema for a tenant and run migrations."""
        from django.db import connection
        import os
        import subprocess
        from django_tenants.utils import schema_context

        # Check if the schema already exists
        with connection.cursor() as cursor:
            cursor.execute("SELECT schema_name FROM information_schema.schemata WHERE schema_name = %s", [schema_name])
            if cursor.fetchone():
                return

        # Create the schema
        with connection.cursor() as cursor:
            cursor.execute(f"CREATE SCHEMA {schema_name}")
            # Set the search path to the new schema
            cursor.execute(f"SET search_path TO {schema_name}, public")

        # Use schema_context to ensure migrations run in the correct schema
        with schema_context(schema_name):
            # Run migrations for the tenant using subprocess instead of os.system
            # This provides better error handling and is more secure
            try:
                # Get the absolute path to manage.py
                manage_py_path = os.path.abspath(os.path.join(os.path.dirname(os.path.abspath(__file__)), '../manage.py'))

                # Run migrations for the specific schema
                subprocess.run(
                    ["python", manage_py_path, "migrate_schemas", "--schema", schema_name],
                    check=True,
                    capture_output=True,
                    text=True
                )

                print(f"Successfully migrated schema: {schema_name}")
            except subprocess.CalledProcessError as e:
                print(f"Error migrating schema {schema_name}: {e.stdout}\n{e.stderr}")
                raise Exception(f"Failed to migrate schema {schema_name}: {e.stderr}")

# Admin views for managing school registrations
class SchoolRegistrationAdminViewSet(viewsets.ModelViewSet):
    """API endpoint for admin management of school registration requests."""
    queryset = SchoolRegistrationRequest.objects.all().order_by('-requested_on')
    serializer_class = SchoolRegistrationAdminSerializer
    permission_classes = [permissions.IsAdminUser]

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """Approve a school registration request and create a tenant."""
        registration_request = self.get_object()

        # Check if a tenant with this schema_name already exists
        existing_school = School.objects.filter(schema_name=registration_request.schema_name).first()
        if existing_school:
            return Response({
                "error": f"A tenant with schema name '{registration_request.schema_name}' already exists."
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check if a domain with this domain already exists
        existing_domain = Domain.objects.filter(domain=registration_request.domain).first()
        if existing_domain:
            return Response({
                "error": f"A domain with name '{registration_request.domain}' already exists."
            }, status=status.HTTP_400_BAD_REQUEST)

        # Allow re-approval if the status is 'approved' but no tenant exists
        if registration_request.status != 'pending' and registration_request.status != 'approved':
            return Response({"error": "This request has already been processed."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Update the registration request
            registration_request.status = 'approved'
            registration_request.processed_on = timezone.now()

            # Make sure schema_name and domain are set
            if not registration_request.schema_name:
                registration_request.schema_name = registration_request.slug

            if not registration_request.domain:
                registration_request.domain = f"{registration_request.slug}.kelemsms.com"

            registration_request.save()

            # Create the tenant (school) without creating schema
            # Temporarily set the class attribute to False
            original_auto_create_schema = School.auto_create_schema
            School.auto_create_schema = False

            try:
                # Create the tenant (school)
                school = School(
                    schema_name=registration_request.schema_name,
                    name=registration_request.name,
                    address=registration_request.address,
                    contact_email=registration_request.contact_email,
                    contact_phone=registration_request.contact_phone,
                )
                school.save()  # Save without creating schema

                # Create the domain for the tenant
                domain = Domain.objects.create(
                    domain=registration_request.domain,
                    tenant=school,
                    is_primary=True
                )

                # Create the schema manually
                self.create_schema_for_tenant(school.schema_name)

                # Create an admin user for the tenant
                admin_user = self.create_admin_user_for_tenant(school, registration_request)

                return Response({
                    "message": f"School registration for {registration_request.name} has been approved and tenant has been created.",
                    "school_id": school.id,
                    "domain": domain.domain,
                    "admin_user": admin_user
                }, status=status.HTTP_200_OK)
            finally:
                # Restore the original value
                School.auto_create_schema = original_auto_create_schema
        except Exception as e:
            import traceback
            traceback.print_exc()
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def create_schema_for_tenant(self, schema_name):
        """Create a schema for a tenant and run migrations."""
        from django.db import connection
        import os

        # Check if the schema already exists
        with connection.cursor() as cursor:
            cursor.execute("SELECT schema_name FROM information_schema.schemata WHERE schema_name = %s", [schema_name])
            if cursor.fetchone():
                return

        # Create the schema
        with connection.cursor() as cursor:
            cursor.execute(f"CREATE SCHEMA {schema_name}")

        # Run migrations for the tenant
        os.system(f"cd {os.path.dirname(os.path.abspath(__file__))} && python ../manage.py migrate_schemas --schema={schema_name}")

    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """Reject a school registration request."""
        registration_request = self.get_object()

        if registration_request.status != 'pending':
            return Response({"error": "This request has already been processed."}, status=status.HTTP_400_BAD_REQUEST)

        rejection_reason = request.data.get('rejection_reason')
        if not rejection_reason:
            return Response({"error": "Rejection reason is required."}, status=status.HTTP_400_BAD_REQUEST)

        registration_request.status = 'rejected'
        registration_request.rejection_reason = rejection_reason
        registration_request.processed_on = timezone.now()
        registration_request.save()

        return Response({
            "message": f"School registration for {registration_request.name} has been rejected."
        }, status=status.HTTP_200_OK)

    def _initialize_tenant_schema(self, school):
        """Initialize the tenant schema with necessary data."""
        # First, create the schema if it doesn't exist
        self.create_schema_for_tenant(school.schema_name)

        # Then initialize the schema with necessary data
        with schema_context(school.schema_name):
            # Here you can add code to initialize the tenant schema with default data
            # For example, create default user groups, settings, etc.
            pass

    def create_admin_user_for_tenant(self, school, registration):
        """Create an admin user for the tenant."""
        print(f"Creating admin user for tenant {school.name} with schema {school.schema_name}")
        print(f"Registration ID: {registration.id}, Status: {registration.status}")
        print(f"Admin Email from registration: {registration.admin_email}")
        print(f"Admin Password provided: {'Yes' if registration.admin_password else 'No'}")

        # Use admin_password if provided, otherwise generate a random password
        if registration.admin_password:
            password = registration.admin_password
            print(f"Using provided admin password")
        else:
            password = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(12))
            print(f"Generated random password: {password}")

        try:
            # Use schema_context instead of manually setting the schema
            with schema_context(school.schema_name):
                # Get the User model inside the schema context
                User = get_user_model()
                print(f"User model class: {User.__name__}")
                print(f"User model fields: {[f.name for f in User._meta.get_fields()]}")

                # Use admin_email if provided, otherwise use contact_email
                email = registration.admin_email if registration.admin_email else registration.contact_email
                print(f"Using email {email} for admin user")

                # Parse the contact person name
                if ' ' in registration.contact_person:
                    first_name = registration.contact_person.split()[0]
                    last_name = registration.contact_person.split()[-1]
                else:
                    first_name = registration.contact_person
                    last_name = ''

                print(f"Creating user with first_name={first_name}, last_name={last_name}")

                # Check if any users exist in this schema
                print(f"Total users in schema: {User.objects.count()}")

                # Check if a user with this email already exists
                existing_user = User.objects.filter(email=email).first()
                if existing_user:
                    print(f"User with email {email} already exists with ID {existing_user.id}")
                    # Return the existing user
                    return {
                        'email': existing_user.email,
                        'password': password,  # We don't know the actual password, so return the one we would have used
                        'first_name': existing_user.first_name,
                        'last_name': existing_user.last_name,
                        'note': 'User already existed'
                    }

                # Create the admin user
                try:
                    # Check if the User model has a create_user method
                    print(f"User model has create_user method: {hasattr(User, 'create_user')}")
                    print(f"User model has objects manager: {hasattr(User, 'objects')}")
                    print(f"User objects manager has create_user method: {hasattr(User.objects, 'create_user')}")

                    # Our custom User model uses email as the username field and has username=None
                    # So we should never try to create a user with a username field
                    admin_user = User.objects.create_user(
                        email=email,
                        password=password,
                        first_name=first_name,
                        last_name=last_name,
                        user_type='admin',
                        is_staff=True,
                        is_active=True,
                        is_superuser=True  # Make them a superuser for this tenant
                    )
                    print(f"Admin user created successfully with ID {admin_user.id}")

                    # Return the admin user and password
                    return {
                        'email': admin_user.email,
                        'password': password,
                        'first_name': admin_user.first_name,
                        'last_name': admin_user.last_name
                    }
                except Exception as inner_e:
                    print(f"Error creating user object: {str(inner_e)}")
                    import traceback
                    print(traceback.format_exc())
                    raise
        except Exception as e:
            import traceback
            print(f"Error creating admin user: {str(e)}")
            print(traceback.format_exc())
            # Return a default response so the tenant creation can still proceed
            return {
                'email': registration.contact_email,
                'password': password,
                'error': str(e)
            }

# Template views for school registration
def school_registration_form(request):
    """Render the school registration form."""
    return render(request, 'school_registration/registration_form.html')

def school_registration_success(request):
    """Render the success page after school registration."""
    return render(request, 'school_registration/registration_success.html')

def school_registration_list(request):
    """Render the list of school registration requests (admin only)."""
    if not request.user.is_staff:
        return HttpResponse("Unauthorized", status=401)

    registrations = SchoolRegistrationRequest.objects.all().order_by('-requested_on')
    return render(request, 'school_registration/registration_list.html', {'registrations': registrations})

@admin_required
def school_registration_detail(request, pk):
    """Render the details of a school registration request (admin only)."""
    registration = get_object_or_404(SchoolRegistrationRequest, pk=pk)
    return render(request, 'school_registration/registration_detail.html', {'registration': registration})
