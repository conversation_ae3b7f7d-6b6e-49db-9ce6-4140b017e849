# Generated manually to fix existing data

from django.db import migrations


def fix_branding_data(apps, schema_editor):
    """Fix existing branding data that has string values where integers are expected"""
    db_alias = schema_editor.connection.alias

    # Use raw SQL to fix the data
    with schema_editor.connection.cursor() as cursor:
        # Check if the table exists
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_name = 'site_settings_brandingsettings'
            );
        """)
        table_exists = cursor.fetchone()[0]

        if table_exists:
            # Simply delete all existing data to avoid migration conflicts
            # The new models will create fresh data with proper defaults
            cursor.execute("DELETE FROM site_settings_brandingsettings;")

        # Also clean up other site_settings tables if they exist
        for table_name in ['site_settings_sitesettings', 'site_settings_herosection', 'site_settings_maintenancemode']:
            cursor.execute(f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_name = '{table_name}'
                );
            """)
            table_exists = cursor.fetchone()[0]
            if table_exists:
                cursor.execute(f"DELETE FROM {table_name};")


def reverse_fix_branding_data(apps, schema_editor):
    """Reverse the data fix - not really needed but good practice"""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('site_settings', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(fix_branding_data, reverse_fix_branding_data),
    ]
