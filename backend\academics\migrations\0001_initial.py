# Generated by Django 5.1.7 on 2025-04-06 22:37

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('courses', '0001_initial'),
        ('students', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AcademicYear',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('is_current', models.BooleanField(default=False)),
            ],
            options={
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='Calendar',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('event_type', models.CharField(choices=[('HOLIDAY', 'Holiday'), ('EXAM', 'Examination'), ('MEETING', 'Meeting'), ('ACTIVITY', 'Activity'), ('OTHER', 'Other')], max_length=20)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('start_time', models.TimeField(blank=True, null=True)),
                ('end_time', models.TimeField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('academic_year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='calendar_events', to='academics.academicyear')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_events', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['start_date', 'start_time'],
            },
        ),
        migrations.CreateModel(
            name='Program',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=20, unique=True)),
                ('level', models.CharField(choices=[('PRIMARY', 'Primary School'), ('SECONDARY', 'Secondary School'), ('PREPARATORY', 'Preparatory School'), ('VOCATIONAL', 'Vocational Training'), ('CERTIFICATE', 'Certificate'), ('DIPLOMA', 'Diploma'), ('DEGREE', 'Degree'), ('MASTERS', 'Masters'), ('DOCTORATE', 'Doctorate')], max_length=20)),
                ('description', models.TextField(blank=True, null=True)),
                ('duration_years', models.PositiveSmallIntegerField(default=4)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('coordinator', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='coordinated_programs', to=settings.AUTH_USER_MODEL)),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='programs', to='courses.department')),
            ],
            options={
                'ordering': ['department', 'name'],
            },
        ),
        migrations.CreateModel(
            name='GradeLevel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('code', models.CharField(max_length=10, unique=True)),
                ('sequence', models.PositiveSmallIntegerField(help_text='Order in the program sequence')),
                ('description', models.TextField(blank=True, null=True)),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='grade_levels', to='academics.program')),
            ],
            options={
                'ordering': ['program', 'sequence'],
                'unique_together': {('program', 'sequence')},
            },
        ),
        migrations.CreateModel(
            name='Section',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('max_students', models.PositiveSmallIntegerField(default=40)),
                ('room', models.CharField(blank=True, max_length=50, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('academic_year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sections', to='academics.academicyear')),
                ('grade_level', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sections', to='academics.gradelevel')),
                ('homeroom_teacher', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='homeroom_sections', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['academic_year', 'grade_level', 'name'],
                'unique_together': {('grade_level', 'name', 'academic_year')},
            },
        ),
        migrations.CreateModel(
            name='ClassSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('day_of_week', models.PositiveSmallIntegerField(choices=[(0, 'Monday'), (1, 'Tuesday'), (2, 'Wednesday'), (3, 'Thursday'), (4, 'Friday'), (5, 'Saturday'), (6, 'Sunday')])),
                ('start_time', models.TimeField()),
                ('end_time', models.TimeField()),
                ('room', models.CharField(blank=True, max_length=50, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('course_offering', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='schedules', to='courses.courseoffering')),
                ('teacher', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='teaching_schedules', to=settings.AUTH_USER_MODEL)),
                ('section', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='schedules', to='academics.section')),
            ],
            options={
                'ordering': ['day_of_week', 'start_time'],
            },
        ),
        migrations.CreateModel(
            name='StudentGradeLevel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('enrollment_date', models.DateField(auto_now_add=True)),
                ('status', models.CharField(choices=[('ACTIVE', 'Active'), ('TRANSFERRED', 'Transferred'), ('WITHDRAWN', 'Withdrawn'), ('GRADUATED', 'Graduated'), ('SUSPENDED', 'Suspended')], default='ACTIVE', max_length=20)),
                ('academic_year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='student_assignments', to='academics.academicyear')),
                ('section', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='student_assignments', to='academics.section')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='grade_assignments', to='students.student')),
            ],
            options={
                'ordering': ['-academic_year__start_date', 'section'],
                'unique_together': {('student', 'academic_year')},
            },
        ),
    ]
