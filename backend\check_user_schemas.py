import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kelem_sms.settings')
django.setup()

from django.db import connection
from django_tenants.utils import schema_context, get_tenant_model
from django.contrib.auth import get_user_model

User = get_user_model()
TenantModel = get_tenant_model()

def check_user_schemas(email="<EMAIL>", user_id=2):
    """Check which schemas a user exists in."""
    print(f"Checking schemas for user: {email} (ID: {user_id})")
    
    # Check public schema
    print("Checking public schema...")
    try:
        user = User.objects.filter(email=email).first()
        if user:
            print(f"Found user in public schema: {user.email} (ID: {user.id})")
            print(f"User details: {user.first_name} {user.last_name}, {user.user_type}, is_superuser={user.is_superuser}")
        else:
            print(f"User not found in public schema")
        
        # Check if user with specific ID exists
        user_by_id = User.objects.filter(id=user_id).first()
        if user_by_id:
            print(f"Found user with ID {user_id} in public schema: {user_by_id.email}")
        else:
            print(f"User with ID {user_id} not found in public schema")
    except Exception as e:
        print(f"Error checking public schema: {str(e)}")
    
    # Check all tenant schemas
    for tenant in TenantModel.objects.all():
        print(f"\nChecking schema {tenant.schema_name}...")
        try:
            with schema_context(tenant.schema_name):
                user = User.objects.filter(email=email).first()
                if user:
                    print(f"Found user in schema {tenant.schema_name}: {user.email} (ID: {user.id})")
                    print(f"User details: {user.first_name} {user.last_name}, {user.user_type}, is_superuser={user.is_superuser}")
                else:
                    print(f"User not found in schema {tenant.schema_name}")
                
                # Check if user with specific ID exists
                user_by_id = User.objects.filter(id=user_id).first()
                if user_by_id:
                    print(f"Found user with ID {user_id} in schema {tenant.schema_name}: {user_by_id.email}")
                else:
                    print(f"User with ID {user_id} not found in schema {tenant.schema_name}")
        except Exception as e:
            print(f"Error checking schema {tenant.schema_name}: {str(e)}")

if __name__ == '__main__':
    if len(sys.argv) > 1:
        email = sys.argv[1]
        user_id = int(sys.argv[2]) if len(sys.argv) > 2 else 2
        check_user_schemas(email, user_id)
    else:
        check_user_schemas()
