from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'tenant_management'

# Create a router for API views
router = DefaultRouter()
router.register(r'tenants', views.TenantViewSet, basename='tenant')
router.register(r'metrics', views.TenantMetricsViewSet, basename='metric')
router.register(r'activities', views.TenantActivityViewSet, basename='activity')
router.register(r'statuses', views.TenantStatusViewSet, basename='status')

urlpatterns = [
    # API endpoints
    path('api/', include(router.urls)),
    path('api/dashboard/', views.DashboardView.as_view(), name='dashboard_api'),
    path('api/collect-metrics/', views.CollectAllMetricsView.as_view(), name='collect_metrics'),

    # Custom tenant actions
    path('api/tenants/<int:pk>/archive/', views.TenantViewSet.as_view({'post': 'archive'}), name='tenant-archive'),
    path('api/tenants/<int:pk>/unarchive/', views.TenantViewSet.as_view({'post': 'unarchive'}), name='tenant-unarchive'),

    # Template views
    path('', views.dashboard, name='dashboard'),
    path('tenants/', views.tenant_list, name='tenant_list'),
    path('tenants/<int:tenant_id>/', views.tenant_detail, name='tenant_detail'),
]

