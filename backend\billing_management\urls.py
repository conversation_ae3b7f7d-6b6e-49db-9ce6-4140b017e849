from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    SubscriptionPlanViewSet, SubscriptionViewSet, InvoiceViewSet,
    PaymentViewSet, BillingMetricsView, PublicSubscriptionPlanListView
)

router = DefaultRouter()
router.register(r'plans', SubscriptionPlanViewSet)
router.register(r'subscriptions', SubscriptionViewSet)
router.register(r'invoices', InvoiceViewSet)
router.register(r'payments', PaymentViewSet)

urlpatterns = [
    path('api/', include(router.urls)),
    path('api/metrics/', BillingMetricsView.as_view(), name='billing-metrics'),
    path('api/public/plans/', PublicSubscriptionPlanListView.as_view(), name='public-plans'),
]
