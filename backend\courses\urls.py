from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'courses'

router = DefaultRouter()
# Register all viewsets
router.register(r'departments', views.DepartmentViewSet)
router.register(r'subjects', views.SubjectViewSet)
router.register(r'courses', views.CourseViewSet)
router.register(r'terms', views.AcademicTermViewSet)
router.register(r'offerings', views.CourseOfferingViewSet)
router.register(r'enrollments', views.EnrollmentViewSet)
router.register(r'lesson-plans', views.LessonPlanViewSet)
router.register(r'resources', views.ResourceViewSet)
router.register(r'assignments', views.AssignmentViewSet)
router.register(r'submissions', views.AssignmentSubmissionViewSet)

urlpatterns = [
    path('api/', include(router.urls)),

    # Dashboard view
    path('dashboard/', views.course_dashboard, name='dashboard'),
]
