import React, { useState, useEffect, useMemo, useCallback } from 'react'
import {
  Box,
  Container,
  Typography,
  Paper,
  Card,
  CardContent,
  CardHeader,
  Grid,
  Button,
  TextField,
  Switch,
  FormControlLabel,
  Alert,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Checkbox,
  Tabs,
  Tab,
  Divider,
  Avatar,
  Menu,
  MenuItem,
  Tooltip,
} from '@mui/material'
import {
  Security as SecurityIcon,
  Person as PersonIcon,
  Group as GroupIcon,
  Shield as ShieldIcon,
  Key as KeyIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  Warning as WarningIcon,
  AdminPanelSettings as AdminIcon,
  SupervisorAccount as SupervisorIcon,
  AccountCircle as UserIcon,
  AccountTree as AccountTreeIcon,
  Settings as SettingsIcon,
  Analytics as AnalyticsIcon,
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import {
  getUsers,
  getRoles,
  getPermissions,
  createUser,
  updateUser,
  deleteUser,
  toggleUserStatus,
  createRole,
  updateRole,
  deleteRole,
  resetUserPassword,
  type User,
  type Role,
  type Permission,
  getPermissionsByCategory,
} from '../../services/userAccessService'
import {
  getRoles as getRBACRoles,
  getPermissions as getRBACPermissions,
  getPermissionMatrix,
  getRoleHierarchy,
  evaluateAccess,
  type Role as RBACRole,
  type Permission as RBACPermission,
  type PermissionMatrix as PermissionMatrixType,
  type RoleHierarchy as RoleHierarchyType,
  type AccessContext,
  type AccessDecision,
} from '../../services/rbacService'
import { ResourceType, ActionType } from '../../types/rbac'
import UserManagement from '../../components/UserAccess/UserManagement'
import RoleManagement from '../../components/UserAccess/RoleManagement'
import PermissionMatrix from '../../components/RBAC/PermissionMatrix'
import RoleHierarchy from '../../components/RBAC/RoleHierarchy'
import DynamicPermissionEngine from '../../components/RBAC/DynamicPermissionEngine'
import AccessControlDashboard from '../../components/RBAC/AccessControlDashboard'

const UserAccessPage: React.FC = () => {
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState(0)
  const [users, setUsers] = useState<User[]>([])
  const [roles, setRoles] = useState<Role[]>([])
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [rbacRoles, setRbacRoles] = useState<RBACRole[]>([])
  const [rbacPermissions, setRbacPermissions] = useState<RBACPermission[]>([])
  const [permissionMatrix, setPermissionMatrix] = useState<PermissionMatrixType[]>([])
  const [roleHierarchy, setRoleHierarchy] = useState<RoleHierarchyType[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)

      // Fetch real data from APIs with fallback to mock data
      try {
        const [usersData, rolesData, permissionsData, rbacRolesData, rbacPermissionsData, matrixData, hierarchyData] = await Promise.all([
          getUsers(),
          getRoles(),
          getPermissions(),
          getRBACRoles(),
          getRBACPermissions(),
          getPermissionMatrix(),
          getRoleHierarchy(),
        ])

        setUsers(usersData.results || usersData)
        setRoles(rolesData)
        setPermissions(permissionsData)
        setRbacRoles(rbacRolesData.results || rbacRolesData)
        setRbacPermissions(rbacPermissionsData.results || rbacPermissionsData)
        setPermissionMatrix(matrixData)
        setRoleHierarchy(hierarchyData)
      } catch (apiError) {
        console.error('Failed to fetch RBAC data:', apiError)
        // Set empty arrays when APIs fail - no mock data
        setUsers([])
        setRoles([])
        setPermissions([])
        setRbacRoles([])
        setRbacPermissions([])
        setPermissionMatrix([])
        setRoleHierarchy([])
      }
    } catch (error) {
      console.error('Error fetching data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue)
  }

  // User handlers
  const handleCreateUser = async (userData: any) => {
    try {
      const newUser = await createUser(userData)
      setUsers(prev => [...prev, newUser])
    } catch (error) {
      console.error('Error creating user:', error)
    }
  }

  const handleUpdateUser = async (id: string, userData: any) => {
    try {
      const updatedUser = await updateUser(id, userData)
      setUsers(prev => prev.map(user => user.id === id ? updatedUser : user))
    } catch (error) {
      console.error('Error updating user:', error)
      // Fallback to local update
      setUsers(prev => prev.map(user =>
        user.id === id ? { ...user, ...userData } : user
      ))
    }
  }

  const handleDeleteUser = async (id: string) => {
    try {
      await deleteUser(id)
      setUsers(prev => prev.filter(user => user.id !== id))
    } catch (error) {
      console.error('Error deleting user:', error)
      // Fallback to local update
      setUsers(prev => prev.filter(user => user.id !== id))
    }
  }

  const handleToggleUserStatus = async (userId: string) => {
    try {
      const updatedUser = await toggleUserStatus(userId)
      setUsers(prev => prev.map(user =>
        user.id === userId ? updatedUser : user
      ))
    } catch (error) {
      console.error('Error toggling user status:', error)
      // Fallback to local update
      setUsers(prev => prev.map(user =>
        user.id === userId ? { ...user, is_active: !user.is_active } : user
      ))
    }
  }

  const handleResetPassword = async (userId: string) => {
    try {
      const result = await resetUserPassword(userId)
      alert(`Password reset successfully. Temporary password: ${result.temporary_password}`)
    } catch (error) {
      console.error('Error resetting password:', error)
      alert('Password reset failed. Please try again.')
    }
  }

  // Role handlers
  const handleCreateRole = async (roleData: any) => {
    try {
      const newRole = await createRole(roleData)
      setRoles(prev => [...prev, newRole])
    } catch (error) {
      console.error('Error creating role:', error)
    }
  }

  const handleUpdateRole = async (id: string, roleData: any) => {
    try {
      const updatedRole = await updateRole(id, roleData)
      setRoles(prev => prev.map(role => role.id === id ? updatedRole : role))
    } catch (error) {
      console.error('Error updating role:', error)
      // Fallback to local update
      setRoles(prev => prev.map(role =>
        role.id === id ? { ...role, ...roleData, updated_at: new Date().toISOString() } : role
      ))
    }
  }

  const handleDeleteRole = async (id: string) => {
    try {
      await deleteRole(id)
      setRoles(prev => prev.filter(role => role.id !== id))
    } catch (error) {
      console.error('Error deleting role:', error)
      // Fallback to local update
      setRoles(prev => prev.filter(role => role.id !== id))
    }
  }



  const filteredUsers = useMemo(() => {
    return users.filter(user =>
      user.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.username.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [users, searchTerm])

  const filteredRoles = useMemo(() => {
    return roles.filter(role => 
      role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      role.description.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [roles, searchTerm])

  return (
    <Box sx={{ bgcolor: '#f5f5f5', minHeight: '100vh' }}>
      {/* Header Section */}
      <Paper
        elevation={0}
        sx={{
          bgcolor: 'white',
          color: '#1f2937',
          py: 4,
          px: 3,
          mb: 3,
          borderRadius: 0,
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        }}
      >
        <Container maxWidth="xl">
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box>
              <Typography
                variant="h4"
                component="h1"
                sx={{
                  fontWeight: 700,
                  mb: 1,
                  color: '#1f2937',
                }}
              >
                User Access Management
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  color: '#6b7280',
                  fontWeight: 400,
                }}
              >
                Manage user roles, permissions, and access controls
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setActiveTab(0)} // Switch to Users tab where Add User button is
                sx={{
                  background: 'linear-gradient(45deg, #6366f1 30%, #8b5cf6 90%)',
                  boxShadow: '0 3px 5px 2px rgba(99, 102, 241, .3)',
                }}
              >
                Manage Users
              </Button>
              <Button
                variant="outlined"
                startIcon={<GroupIcon />}
                onClick={() => setActiveTab(1)} // Switch to Roles tab where Add Role button is
                sx={{
                  color: '#6366f1',
                  borderColor: '#6366f1',
                }}
              >
                Manage Roles
              </Button>
            </Box>
          </Box>
        </Container>
      </Paper>

      {/* Main Content */}
      <Container maxWidth="xl">
        {/* Search and Filters */}
        <Paper sx={{ p: 3, mb: 3, borderRadius: 2 }}>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="Search users, roles, or permissions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                size="small"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                <Chip label={`${users.length} Users`} color="primary" variant="outlined" />
                <Chip label={`${roles.length} Roles`} color="secondary" variant="outlined" />
                <Chip label={`${permissions.length} Permissions`} color="default" variant="outlined" />
              </Box>
            </Grid>
          </Grid>
        </Paper>

        {/* Tabs */}
        <Paper sx={{ borderRadius: 2 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            sx={{ borderBottom: 1, borderColor: 'divider', px: 3 }}
            variant="scrollable"
            scrollButtons="auto"
          >
            <Tab
              icon={<PersonIcon />}
              label="Users"
              iconPosition="start"
            />
            <Tab
              icon={<GroupIcon />}
              label="Roles"
              iconPosition="start"
            />
            <Tab
              icon={<KeyIcon />}
              label="Permissions"
              iconPosition="start"
            />
            <Tab
              icon={<SecurityIcon />}
              label="Permission Matrix"
              iconPosition="start"
            />
            <Tab
              icon={<AccountTreeIcon />}
              label="Role Hierarchy"
              iconPosition="start"
            />
            <Tab
              icon={<SettingsIcon />}
              label="Dynamic Permissions"
              iconPosition="start"
            />
            <Tab
              icon={<AnalyticsIcon />}
              label="Dashboard"
              iconPosition="start"
            />
          </Tabs>

          {/* Tab Content */}
          <Box sx={{ p: 3 }}>
            {/* Users Tab */}
            {activeTab === 0 && (
              <UserManagement
                users={filteredUsers}
                roles={roles}
                onCreateUser={handleCreateUser}
                onUpdateUser={handleUpdateUser}
                onDeleteUser={handleDeleteUser}
                onToggleUserStatus={handleToggleUserStatus}
                onResetPassword={handleResetPassword}
              />
            )}

            {/* Roles Tab */}
            {activeTab === 1 && (
              <RoleManagement
                roles={filteredRoles}
                permissions={permissions}
                onCreateRole={handleCreateRole}
                onUpdateRole={handleUpdateRole}
                onDeleteRole={handleDeleteRole}
              />
            )}

            {/* Permissions Tab */}
            {activeTab === 2 && (
              <Box>
                {Object.entries(getPermissionsByCategory).map(([category, categoryPermissions]) => (
                  <Card key={category} sx={{ mb: 3 }}>
                    <CardHeader
                      title={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <KeyIcon color="primary" />
                          <Typography variant="h6">{category}</Typography>
                          <Chip label={`${categoryPermissions.length} permissions`} size="small" />
                        </Box>
                      }
                    />
                    <CardContent>
                      <List>
                        {categoryPermissions.map((permission, index) => (
                          <React.Fragment key={permission.id}>
                            <ListItem>
                              <ListItemIcon>
                                <SecurityIcon color="action" />
                              </ListItemIcon>
                              <ListItemText
                                primary={permission.name}
                                secondary={
                                  <Box>
                                    <Typography variant="body2" color="text.secondary">
                                      {permission.description}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                      Codename: {permission.codename}
                                    </Typography>
                                  </Box>
                                }
                              />
                              <ListItemSecondaryAction>
                                <Chip
                                  label={permission.codename}
                                  size="small"
                                  variant="outlined"
                                  sx={{ fontFamily: 'monospace' }}
                                />
                              </ListItemSecondaryAction>
                            </ListItem>
                            {index < categoryPermissions.length - 1 && <Divider />}
                          </React.Fragment>
                        ))}
                      </List>
                    </CardContent>
                  </Card>
                ))}
              </Box>
            )}

            {/* Permission Matrix Tab */}
            {activeTab === 3 && (
              <PermissionMatrix
                roles={rbacRoles}
                permissions={rbacPermissions}
                matrix={permissionMatrix}
                onUpdateMatrix={setPermissionMatrix}
                onSaveChanges={() => {
                  // Handle saving permission matrix changes
                  console.log('Saving permission matrix changes')
                }}
                loading={loading}
              />
            )}

            {/* Role Hierarchy Tab */}
            {activeTab === 4 && (
              <RoleHierarchy
                roles={rbacRoles}
                hierarchy={roleHierarchy}
                onRoleSelect={(role) => {
                  console.log('Selected role:', role)
                }}
                onEditRole={(role) => {
                  console.log('Edit role:', role)
                }}
                onCreateSubRole={(parentRole) => {
                  console.log('Create sub-role for:', parentRole)
                }}
                showUserCounts={true}
                showPermissionCounts={true}
              />
            )}

            {/* Dynamic Permissions Tab */}
            {activeTab === 5 && (
              <DynamicPermissionEngine
                permissions={rbacPermissions}
                onUpdatePermission={(permission) => {
                  setRbacPermissions(prev =>
                    prev.map(p => p.id === permission.id ? permission : p)
                  )
                }}
                onTestPermission={async (context: AccessContext): Promise<AccessDecision> => {
                  try {
                    return await evaluateAccess(context)
                  } catch (error) {
                    console.error('Error evaluating access:', error)
                    return {
                      granted: false,
                      reason: 'Error evaluating access',
                      matched_permissions: [],
                      denied_permissions: [],
                      conditions_evaluated: [],
                      context,
                      evaluation_time_ms: 0,
                    }
                  }
                }}
                onCreatePermission={(permissionData) => {
                  console.log('Create permission:', permissionData)
                }}
                onDeletePermission={(permissionId) => {
                  setRbacPermissions(prev => prev.filter(p => p.id !== permissionId))
                }}
              />
            )}

            {/* Access Control Dashboard Tab */}
            {activeTab === 6 && (
              <AccessControlDashboard
                roles={rbacRoles}
                users={users}
                permissions={rbacPermissions}
                onRefreshData={fetchData}
                onViewDetails={(type, id) => {
                  // Switch to the appropriate tab based on type
                  if (type === 'users') setActiveTab(0)
                  else if (type === 'roles') setActiveTab(1)
                  else if (type === 'permissions') setActiveTab(2)
                }}
                loading={loading}
              />
            )}
          </Box>
        </Paper>

      </Container>
    </Box>
  )
}

export default UserAccessPage
