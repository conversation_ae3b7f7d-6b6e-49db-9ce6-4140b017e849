import React, { useState, useEffect } from 'react'
import {
  Box,
  Container,
  Typo<PERSON>,
  Paper,
  Card,
  CardContent,
  CardHeader,
  Grid,
  Button,
  TextField,
  Switch,
  FormControlLabel,
  Alert,
  Chip,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Divider,

  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material'
import {
  Settings as SettingsIcon,
  ArrowBack as ArrowBackIcon,
  Storage as StorageIcon,
  Memory as MemoryIcon,
  Speed as SpeedIcon,
  CloudQueue as CloudIcon,
  DataObject as DatabaseIcon,
  Email as EmailIcon,
  Backup as BackupIcon,
  Security as SecurityIcon,
  Monitor as MonitorIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  Refresh as RefreshIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  RestartAlt as RestartIcon,
  Delete as DeleteIcon,
  Download as DownloadIcon,
  Upload as UploadIcon,
  ExpandMore as ExpandMoreIcon,
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import {
  getSystemMetrics,
  getSystemServices,
  getSystemLogs,
  getSystemConfig,
  updateSystemConfig,
  controlService,
  createBackup,
  clearCache,
  optimizeDatabase,
  restartSystem,
  clearLogs,
  exportSystemConfig,
  transformSystemConfigForForm,
  transformFormDataToSystemConfig,
  formatBytes,
  formatUptime,
  getStatusColor,
  type SystemMetric,
  type SystemService,
  type SystemLog,
  type SystemConfig,
} from '../../services/systemConfigService'

const SystemConfigPage: React.FC = () => {
  const navigate = useNavigate()
  const [loading, setLoading] = useState(true)
  const [metrics, setMetrics] = useState<SystemMetric[]>([])
  const [services, setServices] = useState<SystemService[]>([])
  const [logs, setLogs] = useState<SystemLog[]>([])
  const [dialogOpen, setDialogOpen] = useState(false)
  const [selectedService, setSelectedService] = useState<SystemService | null>(null)
  const [configData, setConfigData] = useState({
    maxMemoryUsage: 80,
    maxCpuUsage: 75,
    maxDiskUsage: 85,
    logRetentionDays: 30,
    backupRetentionDays: 90,
    enableAutoBackup: true,
    enableMonitoring: true,
    enableAlerts: true,
    alertEmail: '<EMAIL>',
    maintenanceWindow: '02:00',
    timeZone: 'Africa/Addis_Ababa',
  })

  useEffect(() => {
    fetchSystemData()
  }, [])

  const fetchSystemData = async () => {
    try {
      setLoading(true)

      // Fetch real data from APIs with fallback to mock data
      try {
        const [metricsData, servicesData, logsData, configData] = await Promise.all([
          getSystemMetrics(),
          getSystemServices(),
          getSystemLogs(50),
          getSystemConfig(),
        ])

        setMetrics(metricsData)
        setServices(servicesData)
        setLogs(logsData)
        setConfigData(transformSystemConfigForForm(configData))
      } catch (apiError) {
        console.warn('API calls failed, using mock data:', apiError)

        // Fallback to mock data
        setMetrics([
          { name: 'CPU Usage', value: 45, unit: '%', status: 'good', threshold: 75, timestamp: new Date().toISOString() },
          { name: 'Memory Usage', value: 68, unit: '%', status: 'warning', threshold: 80, timestamp: new Date().toISOString() },
          { name: 'Disk Usage', value: 32, unit: '%', status: 'good', threshold: 85, timestamp: new Date().toISOString() },
          { name: 'Network I/O', value: 23, unit: 'MB/s', status: 'good', threshold: 100, timestamp: new Date().toISOString() },
          { name: 'Database Connections', value: 45, unit: 'connections', status: 'good', threshold: 100, timestamp: new Date().toISOString() },
          { name: 'Active Users', value: 127, unit: 'users', status: 'good', threshold: 500, timestamp: new Date().toISOString() },
        ])

        setServices([
          { name: 'Django Backend', status: 'running', port: 8001, uptime: '15d 8h 32m', memory: '256MB', cpu: '12%', restart_count: 0, last_restart: '2024-01-10T10:00:00Z' },
          { name: 'React Frontend', status: 'running', port: 3000, uptime: '15d 8h 30m', memory: '128MB', cpu: '5%', restart_count: 0, last_restart: '2024-01-10T10:00:00Z' },
          { name: 'PostgreSQL Database', status: 'running', port: 5432, uptime: '30d 12h 15m', memory: '512MB', cpu: '8%', restart_count: 0, last_restart: '2024-01-01T00:00:00Z' },
          { name: 'Redis Cache', status: 'running', port: 6379, uptime: '30d 12h 10m', memory: '64MB', cpu: '2%', restart_count: 0, last_restart: '2024-01-01T00:00:00Z' },
          { name: 'Nginx Proxy', status: 'running', port: 80, uptime: '30d 12h 20m', memory: '32MB', cpu: '1%', restart_count: 0, last_restart: '2024-01-01T00:00:00Z' },
          { name: 'Celery Worker', status: 'error', port: 0, uptime: '0m', memory: '0MB', cpu: '0%', restart_count: 3, last_restart: '2024-01-26T14:00:00Z' },
        ])

        setLogs([
          { id: '1', timestamp: '2024-01-26 14:30:15', level: 'info', service: 'Django Backend', message: 'User authentication successful' },
          { id: '2', timestamp: '2024-01-26 14:29:45', level: 'warning', service: 'PostgreSQL Database', message: 'High connection count detected' },
          { id: '3', timestamp: '2024-01-26 14:28:30', level: 'error', service: 'Celery Worker', message: 'Worker process crashed unexpectedly' },
          { id: '4', timestamp: '2024-01-26 14:27:12', level: 'info', service: 'Redis Cache', message: 'Cache cleared successfully' },
          { id: '5', timestamp: '2024-01-26 14:25:55', level: 'info', service: 'Nginx Proxy', message: 'SSL certificate renewed' },
        ])
      }
    } catch (error) {
      console.error('Error fetching system data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleServiceAction = async (service: SystemService, action: 'start' | 'stop' | 'restart') => {
    try {
      const updatedService = await controlService(service.name, action)
      setServices(prev => prev.map(s =>
        s.name === service.name ? updatedService : s
      ))
    } catch (error) {
      console.error(`Error ${action}ing service ${service.name}:`, error)
      // Fallback to local update
      setServices(prev => prev.map(s =>
        s.name === service.name
          ? { ...s, status: action === 'stop' ? 'stopped' : 'running' }
          : s
      ))
    }
  }

  const handleSaveConfig = async () => {
    try {
      const configUpdate = transformFormDataToSystemConfig(configData)
      await updateSystemConfig(configUpdate)
      console.log('Configuration saved successfully')
    } catch (error) {
      console.error('Error saving configuration:', error)
    }
  }

  const handleSystemAction = async (action: string) => {
    try {
      switch (action) {
        case 'backup':
          await createBackup()
          console.log('Backup created successfully')
          break
        case 'clearCache':
          await clearCache()
          console.log('Cache cleared successfully')
          break
        case 'optimizeDatabase':
          await optimizeDatabase()
          console.log('Database optimized successfully')
          break
        case 'restart':
          if (window.confirm('Are you sure you want to restart the system?')) {
            await restartSystem()
            console.log('System restart initiated')
          }
          break
        case 'clearLogs':
          await clearLogs()
          console.log('Logs cleared successfully')
          fetchSystemData() // Refresh data
          break
        case 'exportConfig':
          const configBlob = await exportSystemConfig()
          const url = window.URL.createObjectURL(configBlob)
          const a = document.createElement('a')
          a.href = url
          a.download = 'system-config.json'
          a.click()
          window.URL.revokeObjectURL(url)
          break
        default:
          console.log(`Unknown action: ${action}`)
      }
    } catch (error) {
      console.error(`Error performing ${action}:`, error)
    }
  }

  const handleConfigChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : 
                  event.target.type === 'number' ? parseInt(event.target.value) : event.target.value
    setConfigData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': case 'good': return 'success'
      case 'warning': return 'warning'
      case 'error': case 'critical': case 'stopped': return 'error'
      default: return 'default'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': case 'good': return <CheckCircleIcon color="success" />
      case 'warning': return <WarningIcon color="warning" />
      case 'error': case 'critical': case 'stopped': return <ErrorIcon color="error" />
      default: return <InfoIcon color="info" />
    }
  }

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'info': return 'info'
      case 'warning': return 'warning'
      case 'error': return 'error'
      default: return 'default'
    }
  }

  return (
    <Box sx={{ bgcolor: '#f5f5f5', minHeight: '100vh' }}>
      {/* Header Section */}
      <Paper
        elevation={0}
        sx={{
          bgcolor: 'white',
          color: '#1f2937',
          py: 4,
          px: 3,
          mb: 3,
          borderRadius: 0,
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        }}
      >
        <Container maxWidth="xl">
          {/* Header Content */}
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Tooltip title="Go Back">
                <IconButton
                  onClick={() => navigate(-1)}
                  sx={{
                    color: '#6366f1',
                    mr: 2,
                    bgcolor: '#f3f4f6',
                    '&:hover': {
                      bgcolor: '#e5e7eb',
                    },
                  }}
                >
                  <ArrowBackIcon />
                </IconButton>
              </Tooltip>
              <Box>
                <Typography
                  variant="h4"
                  component="h1"
                  sx={{
                    fontWeight: 700,
                    mb: 1,
                    color: '#1f2937',
                  }}
                >
                  System Configuration
                </Typography>
                <Typography
                  variant="body1"
                  sx={{
                    color: '#6b7280',
                    fontWeight: 400,
                  }}
                >
                  Monitor system performance, manage services, and configure advanced settings
                </Typography>
              </Box>
            </Box>
            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              <Button
                variant="outlined"
                startIcon={<DownloadIcon />}
                onClick={() => handleSystemAction('exportConfig')}
                sx={{
                  color: '#6366f1',
                  borderColor: '#6366f1',
                }}
              >
                Export Config
              </Button>
              <Tooltip title="Refresh Data">
                <IconButton
                  onClick={fetchSystemData}
                  sx={{
                    color: '#6366f1',
                    bgcolor: '#f3f4f6',
                    '&:hover': {
                      bgcolor: '#e5e7eb',
                    },
                  }}
                >
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
        </Container>
      </Paper>

      {/* Main Content */}
      <Container maxWidth="xl">
        <Grid container spacing={3}>
          {/* System Metrics */}
          <Grid item xs={12}>
            <Card sx={{ borderRadius: 2 }}>
              <CardHeader
                title="System Metrics"
                subheader="Real-time system performance indicators"
                avatar={<MonitorIcon color="primary" />}
              />
              <CardContent>
                <Grid container spacing={3}>
                  {metrics.map((metric, index) => (
                    <Grid item xs={12} sm={6} md={4} key={index}>
                      <Box sx={{ mb: 2 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body2" color="text.secondary">
                            {metric.name}
                          </Typography>
                          <Chip
                            label={metric.status.toUpperCase()}
                            size="small"
                            color={getStatusColor(metric.status) as any}
                          />
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <Typography variant="h6" sx={{ mr: 1 }}>
                            {metric.value}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {metric.unit}
                          </Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={metric.unit === '%' ? metric.value : (metric.value / metric.threshold) * 100}
                          color={getStatusColor(metric.status) as any}
                          sx={{ height: 8, borderRadius: 4 }}
                        />
                      </Box>
                    </Grid>
                  ))}
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* System Services */}
          <Grid item xs={12} md={8}>
            <Card sx={{ borderRadius: 2 }}>
              <CardHeader
                title="System Services"
                subheader="Manage running services and processes"
                avatar={<SettingsIcon color="primary" />}
              />
              <CardContent>
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Service</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Port</TableCell>
                        <TableCell>Uptime</TableCell>
                        <TableCell>Memory</TableCell>
                        <TableCell>CPU</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {services.map((service, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              {getStatusIcon(service.status)}
                              <Typography sx={{ ml: 1 }}>{service.name}</Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={service.status.toUpperCase()}
                              size="small"
                              color={getStatusColor(service.status) as any}
                            />
                          </TableCell>
                          <TableCell>{service.port || 'N/A'}</TableCell>
                          <TableCell>{service.uptime}</TableCell>
                          <TableCell>{service.memory}</TableCell>
                          <TableCell>{service.cpu}</TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              {service.status === 'stopped' ? (
                                <Tooltip title="Start Service">
                                  <IconButton
                                    size="small"
                                    onClick={() => handleServiceAction(service, 'start')}
                                    sx={{ color: 'success.main' }}
                                  >
                                    <PlayIcon />
                                  </IconButton>
                                </Tooltip>
                              ) : (
                                <Tooltip title="Stop Service">
                                  <IconButton
                                    size="small"
                                    onClick={() => handleServiceAction(service, 'stop')}
                                    sx={{ color: 'error.main' }}
                                  >
                                    <StopIcon />
                                  </IconButton>
                                </Tooltip>
                              )}
                              <Tooltip title="Restart Service">
                                <IconButton
                                  size="small"
                                  onClick={() => handleServiceAction(service, 'restart')}
                                  sx={{ color: 'warning.main' }}
                                >
                                  <RestartIcon />
                                </IconButton>
                              </Tooltip>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* System Configuration */}
          <Grid item xs={12} md={4}>
            <Card sx={{ borderRadius: 2 }}>
              <CardHeader
                title="Configuration"
                subheader="System settings and thresholds"
                avatar={<SettingsIcon color="primary" />}
              />
              <CardContent>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Max Memory Usage (%)"
                      type="number"
                      value={configData.maxMemoryUsage}
                      onChange={handleConfigChange('maxMemoryUsage')}
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Max CPU Usage (%)"
                      type="number"
                      value={configData.maxCpuUsage}
                      onChange={handleConfigChange('maxCpuUsage')}
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Max Disk Usage (%)"
                      type="number"
                      value={configData.maxDiskUsage}
                      onChange={handleConfigChange('maxDiskUsage')}
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Log Retention (days)"
                      type="number"
                      value={configData.logRetentionDays}
                      onChange={handleConfigChange('logRetentionDays')}
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={configData.enableAutoBackup}
                          onChange={handleConfigChange('enableAutoBackup')}
                        />
                      }
                      label="Auto Backup"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={configData.enableMonitoring}
                          onChange={handleConfigChange('enableMonitoring')}
                        />
                      }
                      label="System Monitoring"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Button
                      fullWidth
                      variant="contained"
                      onClick={handleSaveConfig}
                      sx={{
                        background: 'linear-gradient(45deg, #6366f1 30%, #8b5cf6 90%)',
                        mt: 2,
                      }}
                    >
                      Save Configuration
                    </Button>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* System Logs */}
          <Grid item xs={12}>
            <Card sx={{ borderRadius: 2 }}>
              <CardHeader
                title="System Logs"
                subheader="Recent system events and messages"
                avatar={<InfoIcon color="primary" />}
                action={
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<DeleteIcon />}
                    onClick={() => handleSystemAction('clearLogs')}
                  >
                    Clear Logs
                  </Button>
                }
              />
              <CardContent>
                <TableContainer sx={{ maxHeight: 400 }}>
                  <Table stickyHeader>
                    <TableHead>
                      <TableRow>
                        <TableCell>Timestamp</TableCell>
                        <TableCell>Level</TableCell>
                        <TableCell>Service</TableCell>
                        <TableCell>Message</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {logs.map((log) => (
                        <TableRow key={log.id}>
                          <TableCell>
                            <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                              {log.timestamp}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={log.level.toUpperCase()}
                              size="small"
                              color={getLevelColor(log.level) as any}
                            />
                          </TableCell>
                          <TableCell>{log.service}</TableCell>
                          <TableCell>
                            <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                              {log.message}
                            </Typography>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* System Actions */}
          <Grid item xs={12}>
            <Card sx={{ borderRadius: 2 }}>
              <CardHeader
                title="System Actions"
                subheader="Administrative operations and maintenance tasks"
                avatar={<BackupIcon color="primary" />}
              />
              <CardContent>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6} md={3}>
                    <Button
                      fullWidth
                      variant="outlined"
                      startIcon={<BackupIcon />}
                      onClick={() => handleSystemAction('backup')}
                    >
                      Create Backup
                    </Button>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Button
                      fullWidth
                      variant="outlined"
                      startIcon={<DatabaseIcon />}
                      onClick={() => handleSystemAction('optimizeDatabase')}
                    >
                      Optimize Database
                    </Button>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Button
                      fullWidth
                      variant="outlined"
                      startIcon={<MemoryIcon />}
                      onClick={() => handleSystemAction('clearCache')}
                    >
                      Clear Cache
                    </Button>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Button
                      fullWidth
                      variant="outlined"
                      color="warning"
                      startIcon={<RestartIcon />}
                      onClick={() => handleSystemAction('restart')}
                    >
                      Restart System
                    </Button>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Container>
    </Box>
  )
}

export default SystemConfigPage
