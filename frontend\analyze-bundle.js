const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

// Build the project first
console.log('Building project for analysis...')
try {
  execSync('npm run build', { stdio: 'inherit' })
} catch (error) {
  console.error('Build failed:', error.message)
  process.exit(1)
}

// Analyze the build output
const distPath = path.join(__dirname, 'dist')
const assetsPath = path.join(distPath, 'assets')

if (!fs.existsSync(assetsPath)) {
  console.error('Build assets not found. Make sure the build completed successfully.')
  process.exit(1)
}

const files = fs.readdirSync(assetsPath)
const jsFiles = files.filter(file => file.endsWith('.js'))
const cssFiles = files.filter(file => file.endsWith('.css'))

console.log('\n📊 Bundle Analysis Report')
console.log('=' .repeat(50))

let totalJSSize = 0
let totalCSSSize = 0

console.log('\n📦 JavaScript Files:')
jsFiles.forEach(file => {
  const filePath = path.join(assetsPath, file)
  const stats = fs.statSync(filePath)
  const sizeKB = (stats.size / 1024).toFixed(2)
  totalJSSize += stats.size
  
  let type = 'Unknown'
  if (file.includes('index')) type = 'Main Bundle'
  else if (file.includes('vendor')) type = 'Vendor Bundle'
  else if (file.includes('chunk')) type = 'Code Split Chunk'
  
  console.log(`  ${file} - ${sizeKB} KB (${type})`)
})

console.log('\n🎨 CSS Files:')
cssFiles.forEach(file => {
  const filePath = path.join(assetsPath, file)
  const stats = fs.statSync(filePath)
  const sizeKB = (stats.size / 1024).toFixed(2)
  totalCSSSize += stats.size
  
  console.log(`  ${file} - ${sizeKB} KB`)
})

const totalSizeKB = ((totalJSSize + totalCSSSize) / 1024).toFixed(2)
const totalJSSizeKB = (totalJSSize / 1024).toFixed(2)
const totalCSSSizeKB = (totalCSSSize / 1024).toFixed(2)

console.log('\n📈 Summary:')
console.log(`  Total JavaScript: ${totalJSSizeKB} KB`)
console.log(`  Total CSS: ${totalCSSSizeKB} KB`)
console.log(`  Total Bundle Size: ${totalSizeKB} KB`)

// Performance recommendations
console.log('\n💡 Performance Recommendations:')

if (totalJSSize > 500 * 1024) {
  console.log('  ⚠️  JavaScript bundle is large (>500KB). Consider:')
  console.log('     - More aggressive code splitting')
  console.log('     - Tree shaking unused dependencies')
  console.log('     - Lazy loading more components')
}

if (totalJSSize > 1024 * 1024) {
  console.log('  🚨 JavaScript bundle is very large (>1MB). Immediate action needed!')
}

if (jsFiles.length < 3) {
  console.log('  💡 Consider implementing code splitting for better loading performance')
}

if (totalCSSSize > 100 * 1024) {
  console.log('  ⚠️  CSS bundle is large (>100KB). Consider:')
  console.log('     - Removing unused CSS')
  console.log('     - Using CSS-in-JS for component-specific styles')
}

// Check for common performance issues
console.log('\n🔍 Performance Checks:')

// Check if source maps are included in production
const hasSourceMaps = files.some(file => file.endsWith('.map'))
if (hasSourceMaps) {
  console.log('  ⚠️  Source maps found in production build')
} else {
  console.log('  ✅ No source maps in production build')
}

// Check for vendor bundle
const hasVendorBundle = jsFiles.some(file => file.includes('vendor'))
if (hasVendorBundle) {
  console.log('  ✅ Vendor bundle detected (good for caching)')
} else {
  console.log('  💡 Consider separating vendor dependencies into a separate bundle')
}

// Check for code splitting
const hasChunks = jsFiles.some(file => file.includes('chunk'))
if (hasChunks) {
  console.log('  ✅ Code splitting detected')
} else {
  console.log('  💡 No code splitting detected. Consider implementing lazy loading')
}

console.log('\n🎯 Performance Budget Check:')
const budgets = {
  'Main Bundle': 250 * 1024, // 250KB
  'Total JS': 500 * 1024,    // 500KB
  'Total CSS': 100 * 1024,   // 100KB
  'Total Bundle': 600 * 1024 // 600KB
}

const mainBundle = jsFiles.find(file => file.includes('index'))
if (mainBundle) {
  const mainBundleSize = fs.statSync(path.join(assetsPath, mainBundle)).size
  const status = mainBundleSize <= budgets['Main Bundle'] ? '✅' : '❌'
  console.log(`  ${status} Main Bundle: ${(mainBundleSize / 1024).toFixed(2)} KB / ${(budgets['Main Bundle'] / 1024).toFixed(0)} KB`)
}

const jsStatus = totalJSSize <= budgets['Total JS'] ? '✅' : '❌'
console.log(`  ${jsStatus} Total JS: ${totalJSSizeKB} KB / ${(budgets['Total JS'] / 1024).toFixed(0)} KB`)

const cssStatus = totalCSSSize <= budgets['Total CSS'] ? '✅' : '❌'
console.log(`  ${cssStatus} Total CSS: ${totalCSSSizeKB} KB / ${(budgets['Total CSS'] / 1024).toFixed(0)} KB`)

const totalStatus = (totalJSSize + totalCSSSize) <= budgets['Total Bundle'] ? '✅' : '❌'
console.log(`  ${totalStatus} Total Bundle: ${totalSizeKB} KB / ${(budgets['Total Bundle'] / 1024).toFixed(0)} KB`)

// Generate optimization suggestions
console.log('\n🚀 Optimization Suggestions:')

if (totalJSSize > budgets['Total JS']) {
  console.log('  1. Implement more aggressive code splitting')
  console.log('  2. Use dynamic imports for large components')
  console.log('  3. Analyze and remove unused dependencies')
  console.log('  4. Consider using a smaller UI library or custom components')
}

if (jsFiles.length === 1) {
  console.log('  1. Split vendor dependencies into a separate bundle')
  console.log('  2. Implement route-based code splitting')
  console.log('  3. Use React.lazy() for component-level splitting')
}

if (totalCSSSize > budgets['Total CSS']) {
  console.log('  1. Remove unused CSS with tools like PurgeCSS')
  console.log('  2. Use CSS-in-JS for component-specific styles')
  console.log('  3. Minimize CSS with cssnano')
}

console.log('\n📋 Next Steps:')
console.log('  1. Run this analysis regularly to monitor bundle size')
console.log('  2. Set up bundle size monitoring in CI/CD')
console.log('  3. Consider using webpack-bundle-analyzer for detailed analysis')
console.log('  4. Implement performance budgets in your build process')

console.log('\n' + '='.repeat(50))
console.log('Analysis complete! 🎉')
