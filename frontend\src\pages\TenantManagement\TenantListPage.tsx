import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  Box,
  Paper,
  Card,
  Container,
  Typography,
  Button,
  TextField,
  InputAdornment,
  IconButton,
  Chip,
  Menu,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  CircularProgress,
  Alert,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Grid,
  FormControlLabel,
  Checkbox,
} from '@mui/material'
import {
  Search as SearchIcon,
  Clear as ClearIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
  Add as AddIcon,
  Download as DownloadIcon,
  Upload as UploadIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  MoreVert as MoreIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  School as SchoolIcon,
  Domain as DomainIcon,
  Email as EmailIcon,
  CalendarToday as CalendarIcon,
  People as PeopleIcon,
  Storage as StorageIcon,
  TrendingUp as TrendingUpIcon,
} from '@mui/icons-material'
import { DataGrid, GridColDef, GridRenderCellParams } from '@mui/x-data-grid'
import { getTenants, Tenant, collectAllMetrics, approveRegistration, rejectRegistration } from '../../services/tenantService'
import TenantStatusBadge from '../../components/TenantManagement/TenantStatusBadge'

const TenantListPage = () => {
  const navigate = useNavigate()
  const [tenants, setTenants] = useState<Tenant[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterAnchorEl, setFilterAnchorEl] = useState<null | HTMLElement>(null)
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [planFilter, setPlanFilter] = useState<string>('all')
  const [showArchived, setShowArchived] = useState(false)
  const [exportDialogOpen, setExportDialogOpen] = useState(false)

  const fetchTenants = async () => {
    setLoading(true)
    setError(null)
    try {
      const params: any = {}
      if (searchTerm) params.search = searchTerm
      if (statusFilter !== 'all') params.status = statusFilter
      if (planFilter !== 'all') params.plan = planFilter
      if (showArchived) params.archived = 'true'

      const data = await getTenants(params)
      setTenants(data.results || [])
    } catch (err: any) {
      console.error('Error fetching tenants:', err)
      setError(err.response?.data?.message || 'Failed to fetch tenants')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchTenants()
  }, [statusFilter, planFilter, showArchived])

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value)
  }

  const handleSearchSubmit = (event: React.FormEvent) => {
    event.preventDefault()
    fetchTenants()
  }

  const handleClearSearch = () => {
    setSearchTerm('')
    fetchTenants()
  }

  const handleFilterClick = (event: React.MouseEvent<HTMLElement>) => {
    setFilterAnchorEl(event.currentTarget)
  }

  const handleFilterClose = () => {
    setFilterAnchorEl(null)
  }

  const handleStatusFilterChange = (event: any) => {
    setStatusFilter(event.target.value)
  }

  const handlePlanFilterChange = (event: any) => {
    setPlanFilter(event.target.value)
  }

  const handleRefreshMetrics = async () => {
    setRefreshing(true)
    try {
      await collectAllMetrics()
      await fetchTenants()
    } catch (err: any) {
      console.error('Error refreshing metrics:', err)
    } finally {
      setRefreshing(false)
    }
  }

  const handleExportClick = () => {
    setExportDialogOpen(true)
  }

  const handleExportClose = () => {
    setExportDialogOpen(false)
  }

  const handleExportConfirm = () => {
    // Implement export functionality
    setExportDialogOpen(false)
  }

  const handleViewTenant = (id: number | string) => {
    // Don't navigate to detail page for pending registrations
    if (typeof id === 'string' && id.startsWith('pending_')) {
      console.log('Cannot view details for pending registration:', id)
      return
    }
    navigate(`/dashboard/tenant-management/tenants/${id}`)
  }

  const handleApproveRegistration = async (registrationId: string) => {
    try {
      await approveRegistration(registrationId)
      await fetchTenants() // Refresh the list
    } catch (err: any) {
      console.error('Error approving registration:', err)
      setError(err.response?.data?.message || 'Failed to approve registration')
    }
  }

  const handleRejectRegistration = async (registrationId: string, reason?: string) => {
    try {
      await rejectRegistration(registrationId, reason)
      await fetchTenants() // Refresh the list
    } catch (err: any) {
      console.error('Error rejecting registration:', err)
      setError(err.response?.data?.message || 'Failed to reject registration')
    }
  }

  const columns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'School Information',
      width: 300,
      renderCell: (params: GridRenderCellParams) => {
        const tenant = params.row
        return (
          <Box sx={{ py: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
              <SchoolIcon sx={{ fontSize: 16, mr: 1, color: 'primary.main' }} />
              <Typography variant="body2" sx={{ fontWeight: 600, color: 'text.primary' }}>
                {params.value}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <DomainIcon sx={{ fontSize: 14, mr: 1, color: 'text.secondary' }} />
              <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                {tenant.domain || 'No domain set'}
              </Typography>
            </Box>
          </Box>
        )
      },
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 140,
      renderCell: (params: GridRenderCellParams) => (
        <TenantStatusBadge status={params.value} />
      ),
    },
    {
      field: 'contact_email',
      headerName: 'Contact Information',
      width: 280,
      renderCell: (params: GridRenderCellParams) => {
        const tenant = params.row
        return (
          <Box sx={{ py: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
              <EmailIcon sx={{ fontSize: 16, mr: 1, color: 'primary.main' }} />
              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                {params.value || 'No email'}
              </Typography>
            </Box>
            {tenant.phone && (
              <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                📞 {tenant.phone}
              </Typography>
            )}
          </Box>
        )
      },
    },
    {
      field: 'created_on',
      headerName: 'Created Date',
      width: 160,
      renderCell: (params: GridRenderCellParams) => (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <CalendarIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
          <Box>
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              {new Date(params.value).toLocaleDateString()}
            </Typography>
            <Typography variant="caption" sx={{ color: 'text.secondary' }}>
              {new Date(params.value).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 200,
      sortable: false,
      renderCell: (params: GridRenderCellParams) => {
        const tenant = params.row

        // Show approve/reject buttons only for pending registrations
        if (tenant.status === 'pending' && tenant.registration_id) {
          return (
            <Box sx={{ display: 'flex', gap: 0.5 }}>
              <Tooltip title="View Registration Details">
                <IconButton
                  size="small"
                  sx={{
                    bgcolor: 'primary.main',
                    color: 'white',
                    '&:hover': { bgcolor: 'primary.dark' }
                  }}
                  onClick={(e) => {
                    e.stopPropagation()
                    navigate(`/dashboard/tenant-management/registrations/${tenant.registration_id}`)
                  }}
                >
                  <ViewIcon sx={{ fontSize: 16 }} />
                </IconButton>
              </Tooltip>
              <Tooltip title="Approve Registration">
                <IconButton
                  size="small"
                  sx={{
                    bgcolor: 'success.main',
                    color: 'white',
                    '&:hover': { bgcolor: 'success.dark' }
                  }}
                  onClick={(e) => {
                    e.stopPropagation()
                    handleApproveRegistration(tenant.registration_id)
                  }}
                >
                  <ApproveIcon sx={{ fontSize: 16 }} />
                </IconButton>
              </Tooltip>
              <Tooltip title="Reject Registration">
                <IconButton
                  size="small"
                  sx={{
                    bgcolor: 'error.main',
                    color: 'white',
                    '&:hover': { bgcolor: 'error.dark' }
                  }}
                  onClick={(e) => {
                    e.stopPropagation()
                    handleRejectRegistration(tenant.registration_id)
                  }}
                >
                  <RejectIcon sx={{ fontSize: 16 }} />
                </IconButton>
              </Tooltip>
            </Box>
          )
        }

        // For active tenants, show view and edit buttons
        if (typeof tenant.id === 'string' && tenant.id.startsWith('pending_')) {
          return null // No actions for pending registrations other than approve/reject
        }

        return (
          <Box sx={{ display: 'flex', gap: 0.5 }}>
            <Tooltip title="View Tenant Details">
              <IconButton
                size="small"
                sx={{
                  bgcolor: 'primary.main',
                  color: 'white',
                  '&:hover': { bgcolor: 'primary.dark' }
                }}
                onClick={(e) => {
                  e.stopPropagation()
                  handleViewTenant(tenant.id)
                }}
              >
                <ViewIcon sx={{ fontSize: 16 }} />
              </IconButton>
            </Tooltip>
            <Tooltip title="Edit Tenant">
              <IconButton
                size="small"
                sx={{
                  bgcolor: 'secondary.main',
                  color: 'white',
                  '&:hover': { bgcolor: 'secondary.dark' }
                }}
                onClick={(e) => {
                  e.stopPropagation()
                  navigate(`/dashboard/tenant-management/tenants/${tenant.id}/edit`)
                }}
              >
                <EditIcon sx={{ fontSize: 16 }} />
              </IconButton>
            </Tooltip>
          </Box>
        )
      },
    },
  ]

  if (error) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        {error}
      </Alert>
    )
  }

  return (
    <Box sx={{ bgcolor: '#f5f5f5', minHeight: '100vh' }}>
        {/* White Header Section */}
        <Box
          sx={{
            bgcolor: 'white',
            color: '#1f2937',
            py: 4,
            mb: 3,
            borderRadius: 0,
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          }}
        >
        <Container maxWidth="lg">
        <Box sx={{ width: '100%', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4" component="h1" sx={{ fontWeight: 700, mb: 1 }}>
              Tenant Management
            </Typography>
            <Typography variant="body1" sx={{ color: '#6b7280' }}>
              Manage all your school tenants in one place
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={handleExportClick}
              sx={{
                borderColor: '#6366f1',
                color: '#6366f1',
                fontWeight: 600,
                '&:hover': {
                  borderColor: '#4f46e5',
                  bgcolor: '#f3f4f6',
                },
              }}
            >
              Export
            </Button>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={handleRefreshMetrics}
              disabled={refreshing}
              sx={{
                borderColor: '#6366f1',
                color: '#6366f1',
                fontWeight: 600,
                '&:hover': {
                  borderColor: '#4f46e5',
                  bgcolor: '#f3f4f6',
                },
              }}
            >
              {refreshing ? 'Refreshing...' : 'Refresh Metrics'}
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => navigate('/dashboard/tenant-management/tenants/new')}
              sx={{
                bgcolor: '#6366f1',
                color: 'white',
                fontWeight: 600,
                '&:hover': {
                  bgcolor: '#4f46e5',
                },
              }}
            >
              Add Tenant
            </Button>
            </Box>
          </Box>
          
          {/* Quick Stats in Header */}
          <Grid container spacing={3}>
            <Grid item xs={6} sm={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" sx={{ fontWeight: 700, mb: 0.5 }}>
                  {tenants.length}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8 }}>
                  Total Tenants
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" sx={{ fontWeight: 700, mb: 0.5 }}>
                  {tenants.filter(t => t.status === 'active').length}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8 }}>
                  Active
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" sx={{ fontWeight: 700, mb: 0.5 }}>
                  {tenants.filter(t => t.status === 'pending').length}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8 }}>
                  Pending
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" sx={{ fontWeight: 700, mb: 0.5 }}>
                  {tenants.filter(t => t.status === 'suspended').length}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8 }}>
                  Suspended
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Container>
        </Box>

        <Container maxWidth="lg" sx={{ pb: 4 }}>
        {/* Main Content */}
        <Card sx={{ borderRadius: 0, boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)', bgcolor: 'white' }}>
        {/* Enhanced Search and Filter Section */}
        <Box sx={{ p: 3, borderBottom: '1px solid', borderColor: 'divider' }}>
          <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 2, alignItems: { xs: 'stretch', md: 'center' }, justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h5" component="h2" sx={{ fontWeight: 600, display: 'flex', alignItems: 'center', gap: 1 }}>
              <SchoolIcon color="primary" />
              All Tenants ({tenants.length})
            </Typography>

            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center' }}>
              <form onSubmit={handleSearchSubmit} style={{ display: 'flex', gap: 1 }}>
                <TextField
                  size="small"
                  placeholder="Search by name, domain, or email..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon color="action" />
                      </InputAdornment>
                    ),
                    endAdornment: searchTerm && (
                      <InputAdornment position="end">
                        <IconButton size="small" onClick={handleClearSearch}>
                          <ClearIcon />
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                  sx={{ minWidth: 300 }}
                />
                <Button
                  type="submit"
                  variant="contained"
                  size="small"
                  sx={{
                    bgcolor: '#6366f1',
                    color: 'white',
                    '&:hover': {
                      bgcolor: '#4f46e5',
                    },
                  }}
                >
                  Search
                </Button>
              </form>

              <IconButton
                onClick={handleFilterClick}
                sx={{
                  bgcolor: statusFilter !== 'all' || planFilter !== 'all' || showArchived ? 'primary.main' : 'action.hover',
                  color: statusFilter !== 'all' || planFilter !== 'all' || showArchived ? 'white' : 'inherit',
                  '&:hover': {
                    bgcolor: statusFilter !== 'all' || planFilter !== 'all' || showArchived ? 'primary.dark' : 'action.selected',
                  }
                }}
              >
                <FilterIcon />
              </IconButton>
            </Box>
          </Box>

          {/* Filter Chips */}
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {statusFilter !== 'all' && (
              <Chip
                label={`Status: ${statusFilter}`}
                onDelete={() => setStatusFilter('all')}
                color="primary"
                variant="outlined"
                size="small"
              />
            )}
            {planFilter !== 'all' && (
              <Chip
                label={`Plan: ${planFilter}`}
                onDelete={() => setPlanFilter('all')}
                color="primary"
                variant="outlined"
                size="small"
              />
            )}
            {showArchived && (
              <Chip
                label="Including Archived"
                onDelete={() => setShowArchived(false)}
                color="primary"
                variant="outlined"
                size="small"
              />
            )}
          </Box>
        </Box>

        {/* Enhanced Data Grid */}
        <Box sx={{ p: 3 }}>
          {loading ? (
            <Box sx={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              height: 400,
              bgcolor: 'background.paper',
              borderRadius: 2,
              border: '1px solid',
              borderColor: 'divider'
            }}>
              <CircularProgress size={60} sx={{ mb: 2 }} />
              <Typography variant="h6" sx={{ mb: 1, color: 'text.primary' }}>
                Loading tenants...
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Please wait while we fetch the latest data
              </Typography>
            </Box>
          ) : tenants.length === 0 ? (
            <Box sx={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              height: 400,
              bgcolor: 'background.paper',
              borderRadius: 2,
              border: '1px solid',
              borderColor: 'divider'
            }}>
              <SchoolIcon sx={{ fontSize: 80, color: 'text.disabled', mb: 2 }} />
              <Typography variant="h6" sx={{ mb: 1, color: 'text.primary' }}>
                No tenants found
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary', mb: 3 }}>
                {searchTerm ? 'Try adjusting your search criteria' : 'Get started by adding your first tenant'}
              </Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => navigate('/dashboard/tenant-management/tenants/new')}
              >
                Add First Tenant
              </Button>
            </Box>
          ) : (
            <Box sx={{ height: 600, width: '100%' }}>
              <DataGrid
                rows={tenants}
                columns={columns}
                initialState={{
                  pagination: {
                    paginationModel: { page: 0, pageSize: 15 },
                  },
                }}
                pageSizeOptions={[10, 15, 25, 50, 100]}
                checkboxSelection
                disableRowSelectionOnClick
                onRowClick={(params) => {
                  // Navigate to appropriate page based on tenant type
                  if (typeof params.row.id === 'string' && params.row.id.startsWith('pending_')) {
                    // Navigate to pending registration detail page
                    navigate(`/dashboard/tenant-management/registrations/${params.row.registration_id}`)
                  } else {
                    // Navigate to active tenant detail page
                    handleViewTenant(params.row.id)
                  }
                }}
                sx={{
                  border: 'none',
                  '& .MuiDataGrid-cell': {
                    borderBottom: '1px solid',
                    borderColor: 'divider',
                    '&:focus': {
                      outline: 'none',
                    },
                  },
                  '& .MuiDataGrid-row': {
                    '&:hover': {
                      bgcolor: 'action.hover',
                      cursor: 'pointer',
                    },
                    '&.Mui-selected': {
                      bgcolor: 'action.selected',
                      '&:hover': {
                        bgcolor: 'action.selected',
                      },
                    },
                  },
                  '& .MuiDataGrid-columnHeaders': {
                    bgcolor: 'background.paper',
                    borderBottom: '2px solid',
                    borderColor: 'divider',
                    '& .MuiDataGrid-columnHeader': {
                      fontWeight: 600,
                      fontSize: '0.875rem',
                    },
                  },
                  '& .MuiDataGrid-footerContainer': {
                    borderTop: '1px solid',
                    borderColor: 'divider',
                    bgcolor: 'background.paper',
                  },
                }}
              />
            </Box>
          )}
        </Box>
      </Card>
        </Container>

      {/* Filter Menu */}
      <Menu
        anchorEl={filterAnchorEl}
        open={Boolean(filterAnchorEl)}
        onClose={handleFilterClose}
        PaperProps={{
          sx: {
            mt: 1,
            minWidth: 280,
            borderRadius: 2,
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
          }
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            Filter Tenants
          </Typography>

          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Status</InputLabel>
            <Select
              value={statusFilter}
              label="Status"
              onChange={handleStatusFilterChange}
              size="small"
            >
              <MenuItem value="all">All Statuses</MenuItem>
              <MenuItem value="active">Active</MenuItem>
              <MenuItem value="pending">Pending</MenuItem>
              <MenuItem value="suspended">Suspended</MenuItem>
              <MenuItem value="trial">Trial</MenuItem>
              <MenuItem value="expired">Expired</MenuItem>
            </Select>
          </FormControl>

          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Plan</InputLabel>
            <Select
              value={planFilter}
              label="Plan"
              onChange={handlePlanFilterChange}
              size="small"
            >
              <MenuItem value="all">All Plans</MenuItem>
              <MenuItem value="basic">Basic</MenuItem>
              <MenuItem value="standard">Standard</MenuItem>
              <MenuItem value="premium">Premium</MenuItem>
              <MenuItem value="enterprise">Enterprise</MenuItem>
            </Select>
          </FormControl>

          <FormControlLabel
            control={
              <Checkbox
                checked={showArchived}
                onChange={(e) => setShowArchived(e.target.checked)}
                color="primary"
              />
            }
            label="Include archived tenants"
            sx={{ mb: 2 }}
          />

          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
            <Button
              size="small"
              onClick={() => {
                setStatusFilter('all')
                setPlanFilter('all')
                setShowArchived(false)
                handleFilterClose()
              }}
            >
              Clear All
            </Button>
            <Button
              size="small"
              variant="contained"
              onClick={handleFilterClose}
            >
              Apply Filters
            </Button>
          </Box>
        </Box>
      </Menu>

      {/* Export Dialog */}
      <Dialog open={exportDialogOpen} onClose={handleExportClose}>
        <DialogTitle>Export Tenant Data</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Select the format and data you want to export.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={handleExportClose}
            sx={{
              color: '#6b7280',
              '&:hover': {
                bgcolor: '#f3f4f6',
              },
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleExportConfirm}
            variant="contained"
            sx={{
              bgcolor: '#6366f1',
              color: 'white',
              '&:hover': {
                bgcolor: '#4f46e5',
              },
            }}
          >
            Export
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default TenantListPage
