from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON>ult<PERSON><PERSON><PERSON>
from . import views

app_name = 'user_management'

# Create a router for API views
router = DefaultRouter()
router.register(r'users', views.UserManagementViewSet, basename='user')
router.register(r'groups', views.GroupManagementViewSet, basename='group')
router.register(r'permissions', views.PermissionViewSet, basename='permission')

urlpatterns = [
    # API endpoints
    path('api/', include(router.urls)),
]
