# Generated by Django 5.1.7 on 2025-04-06 22:37

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('academics', '0001_initial'),
        ('courses', '0002_alter_academicterm_options_alter_course_options_and_more'),
        ('students', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='guardian',
            options={'ordering': ['student', 'last_name', 'first_name'], 'verbose_name': 'Guardian', 'verbose_name_plural': 'Guardians'},
        ),
        migrations.AlterModelOptions(
            name='student',
            options={'ordering': ['last_name', 'first_name'], 'verbose_name': 'Student', 'verbose_name_plural': 'Students'},
        ),
        migrations.AddField(
            model_name='guardian',
            name='alternate_phone',
            field=models.CharField(blank=True, max_length=20, null=True, validators=[django.core.validators.RegexValidator(message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.", regex='^\\+?1?\\d{9,15}$')]),
        ),
        migrations.AddField(
            model_name='guardian',
            name='can_pickup_student',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='guardian',
            name='city',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='guardian',
            name='country',
            field=models.CharField(default='Ethiopia', max_length=100),
        ),
        migrations.AddField(
            model_name='guardian',
            name='created_at',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name='guardian',
            name='employer',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='guardian',
            name='first_name',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='guardian',
            name='is_emergency_contact',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='guardian',
            name='last_name',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='guardian',
            name='middle_name',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='guardian',
            name='notes',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='guardian',
            name='occupation',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='guardian',
            name='photo',
            field=models.ImageField(blank=True, null=True, upload_to='guardian_photos/'),
        ),
        migrations.AddField(
            model_name='guardian',
            name='postal_code',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='guardian',
            name='receives_reports',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='guardian',
            name='state',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='guardian',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='admission_type',
            field=models.CharField(blank=True, choices=[('NEW', 'New Admission'), ('TRANSFER', 'Transfer'), ('READMISSION', 'Re-Admission')], default='NEW', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='allergies',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='blood_group',
            field=models.CharField(blank=True, choices=[('A+', 'A+'), ('A-', 'A-'), ('B+', 'B+'), ('B-', 'B-'), ('AB+', 'AB+'), ('AB-', 'AB-'), ('O+', 'O+'), ('O-', 'O-')], max_length=3, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='city',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='country',
            field=models.CharField(default='Ethiopia', max_length=100),
        ),
        migrations.AddField(
            model_name='student',
            name='created_at',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name='student',
            name='current_grade_level',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='current_students', to='academics.gradelevel'),
        ),
        migrations.AddField(
            model_name='student',
            name='current_section',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='current_students', to='academics.section'),
        ),
        migrations.AddField(
            model_name='student',
            name='emergency_contact_name',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='emergency_contact_phone',
            field=models.CharField(blank=True, max_length=20, null=True, validators=[django.core.validators.RegexValidator(message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.", regex='^\\+?1?\\d{9,15}$')]),
        ),
        migrations.AddField(
            model_name='student',
            name='emergency_contact_relationship',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='medical_conditions',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='medications',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='middle_name',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='mother_tongue',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='nationality',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='notes',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='place_of_birth',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='postal_code',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='previous_school',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='registration_number',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='religion',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='state',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='status',
            field=models.CharField(choices=[('ACTIVE', 'Active'), ('INACTIVE', 'Inactive'), ('GRADUATED', 'Graduated'), ('TRANSFERRED', 'Transferred'), ('SUSPENDED', 'Suspended'), ('WITHDRAWN', 'Withdrawn')], default='ACTIVE', max_length=20),
        ),
        migrations.AddField(
            model_name='student',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, null=True),
        ),
        migrations.AlterField(
            model_name='guardian',
            name='name',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='guardian',
            name='phone_number',
            field=models.CharField(blank=True, max_length=20, null=True, validators=[django.core.validators.RegexValidator(message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.", regex='^\\+?1?\\d{9,15}$')]),
        ),
        migrations.AlterField(
            model_name='guardian',
            name='relationship',
            field=models.CharField(choices=[('FATHER', 'Father'), ('MOTHER', 'Mother'), ('GRANDFATHER', 'Grandfather'), ('GRANDMOTHER', 'Grandmother'), ('UNCLE', 'Uncle'), ('AUNT', 'Aunt'), ('SIBLING', 'Sibling'), ('LEGAL_GUARDIAN', 'Legal Guardian'), ('OTHER', 'Other')], max_length=50),
        ),
        migrations.AlterField(
            model_name='student',
            name='enrollment_date',
            field=models.DateField(),
        ),
        migrations.AlterField(
            model_name='student',
            name='phone_number',
            field=models.CharField(blank=True, max_length=20, null=True, validators=[django.core.validators.RegexValidator(message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.", regex='^\\+?1?\\d{9,15}$')]),
        ),
        migrations.AlterField(
            model_name='student',
            name='user',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='student_profile', to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='StudentAchievement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100)),
                ('achievement_type', models.CharField(choices=[('ACADEMIC', 'Academic'), ('SPORTS', 'Sports'), ('ARTS', 'Arts'), ('LEADERSHIP', 'Leadership'), ('COMMUNITY_SERVICE', 'Community Service'), ('OTHER', 'Other')], max_length=50)),
                ('description', models.TextField()),
                ('date_achieved', models.DateField()),
                ('issuing_organization', models.CharField(blank=True, max_length=100, null=True)),
                ('certificate', models.FileField(blank=True, null=True, upload_to='student_achievements/')),
                ('is_verified', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='achievements', to='students.student')),
                ('verified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_achievements', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Student Achievement',
                'verbose_name_plural': 'Student Achievements',
                'ordering': ['-date_achieved'],
            },
        ),
        migrations.CreateModel(
            name='StudentDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_type', models.CharField(choices=[('BIRTH_CERTIFICATE', 'Birth Certificate'), ('ID_CARD', 'ID Card'), ('PASSPORT', 'Passport'), ('PREVIOUS_SCHOOL_RECORD', 'Previous School Record'), ('MEDICAL_RECORD', 'Medical Record'), ('VACCINATION_RECORD', 'Vaccination Record'), ('PHOTO', 'Photograph'), ('OTHER', 'Other')], max_length=50)),
                ('title', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('file', models.FileField(upload_to='student_documents/')),
                ('is_verified', models.BooleanField(default=False)),
                ('verified_date', models.DateTimeField(blank=True, null=True)),
                ('uploaded_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='students.student')),
                ('uploaded_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='uploaded_documents', to=settings.AUTH_USER_MODEL)),
                ('verified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_documents', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Student Document',
                'verbose_name_plural': 'Student Documents',
                'ordering': ['-uploaded_at'],
            },
        ),
        migrations.CreateModel(
            name='StudentFee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('fee_type', models.CharField(choices=[('TUITION', 'Tuition Fee'), ('REGISTRATION', 'Registration Fee'), ('EXAM', 'Examination Fee'), ('LIBRARY', 'Library Fee'), ('LABORATORY', 'Laboratory Fee'), ('TRANSPORTATION', 'Transportation Fee'), ('UNIFORM', 'Uniform Fee'), ('BOOKS', 'Books Fee'), ('ACTIVITY', 'Activity Fee'), ('OTHER', 'Other Fee')], max_length=50)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('due_date', models.DateField()),
                ('payment_status', models.CharField(choices=[('PENDING', 'Pending'), ('PARTIAL', 'Partially Paid'), ('PAID', 'Paid'), ('OVERDUE', 'Overdue'), ('WAIVED', 'Waived')], default='PENDING', max_length=20)),
                ('amount_paid', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('balance', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('last_payment_date', models.DateField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('academic_year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='student_fees', to='academics.academicyear')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_fees', to=settings.AUTH_USER_MODEL)),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fees', to='students.student')),
                ('term', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='student_fees', to='courses.academicterm')),
            ],
            options={
                'verbose_name': 'Student Fee',
                'verbose_name_plural': 'Student Fees',
                'ordering': ['student', 'due_date'],
            },
        ),
        migrations.CreateModel(
            name='FeePayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('payment_date', models.DateField()),
                ('payment_method', models.CharField(choices=[('CASH', 'Cash'), ('BANK_TRANSFER', 'Bank Transfer'), ('MOBILE_MONEY', 'Mobile Money'), ('CHECK', 'Check'), ('CREDIT_CARD', 'Credit Card'), ('OTHER', 'Other')], max_length=20)),
                ('transaction_id', models.CharField(blank=True, max_length=100, null=True)),
                ('receipt_number', models.CharField(blank=True, max_length=100, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('received_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='received_payments', to=settings.AUTH_USER_MODEL)),
                ('student_fee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='students.studentfee')),
            ],
            options={
                'verbose_name': 'Fee Payment',
                'verbose_name_plural': 'Fee Payments',
                'ordering': ['-payment_date'],
            },
        ),
    ]
