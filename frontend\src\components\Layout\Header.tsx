import { useState } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import {
  AppBar,
  <PERSON>lbar,
  Typography,
  IconButton,
  Menu,
  MenuItem,
  Badge,
  Tooltip,
  Avatar,
  Box,
  Chip,
  Divider,
  Button,
  Breadcrumbs,
  Link,
  alpha,
} from '@mui/material'
import {
  Menu as MenuIcon,
  Notifications as NotificationsIcon,
  Brightness4 as DarkModeIcon,
  Brightness7 as LightModeIcon,
  AccountCircle,
  School as SchoolIcon,
  Settings as SettingsIcon,

  Logout as LogoutIcon,
  Person as PersonIcon,
  NavigateNext as NavigateNextIcon,
  Home as HomeIcon,
  Dashboard as DashboardIcon,
  Business as BusinessIcon,
  MenuBook as MenuBookIcon,
} from '@mui/icons-material'
import { styled } from '@mui/material/styles'
import { useAuth } from '../../contexts/AuthContext'
import { useTheme } from '../../contexts/ThemeContext'

const drawerWidth = 275
const drawerWidthClosed = 64

interface HeaderProps {
  open: boolean
  toggleDrawer: () => void
}

const StyledAppBar = styled(AppBar, {
  shouldForwardProp: (prop) => prop !== 'open',
})<{ open?: boolean }>(({ theme, open }) => ({
  zIndex: theme.zIndex.drawer + 1,
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  color: 'white',
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.12)',
  backdropFilter: 'blur(20px)',
  borderBottom: `1px solid ${alpha('#ffffff', 0.1)}`,
  // Always account for sidebar space, whether open or closed, with additional spacing
  marginLeft: open ? drawerWidth + 12 : drawerWidthClosed + 12,
  width: open ? `calc(100% - ${drawerWidth + 12}px)` : `calc(100% - ${drawerWidthClosed + 12}px)`,
  transition: theme.transitions.create(['width', 'margin'], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.enteringScreen,
  }),
}))



const Header = ({ open, toggleDrawer }: HeaderProps) => {
  const { user, currentTenant, logout } = useAuth()
  const { mode, toggleColorMode } = useTheme()
  const navigate = useNavigate()
  const location = useLocation()
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [notificationAnchorEl, setNotificationAnchorEl] = useState<null | HTMLElement>(null)

  // Generate breadcrumbs based on current path
  const generateBreadcrumbs = () => {
    const pathSegments = location.pathname.split('/').filter(Boolean)
    const breadcrumbs = [
      { label: 'Home', path: '/dashboard', icon: <HomeIcon sx={{ fontSize: 16 }} /> }
    ]

    let currentPath = ''
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`
      
      // Skip the first 'dashboard' segment as it's already included as Home
      if (segment === 'dashboard') return
      
      let label = segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' ')
      let icon = <DashboardIcon sx={{ fontSize: 16 }} />
      
      // Customize labels and icons based on path
      switch (segment) {
        case 'tenant-management':
          label = 'Tenant Management'
          icon = <BusinessIcon sx={{ fontSize: 16 }} />
          break
        case 'tenants':
          label = 'Tenants'
          icon = <SchoolIcon sx={{ fontSize: 16 }} />
          break
        case 'students':
          label = 'Students'
          icon = <PersonIcon sx={{ fontSize: 16 }} />
          break
        case 'courses':
          label = 'Courses'
          break
        default:
          // If it's a number (ID), don't add it as a breadcrumb
          if (!isNaN(Number(segment))) return
      }
      
      breadcrumbs.push({
        label,
        path: currentPath,
        icon
      })
    })

    return breadcrumbs
  }

  const breadcrumbs = generateBreadcrumbs()

  const handleMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleNotificationMenu = (event: React.MouseEvent<HTMLElement>) => {
    setNotificationAnchorEl(event.currentTarget)
  }

  const handleNotificationClose = () => {
    setNotificationAnchorEl(null)
  }



  const handleLogout = () => {
    handleClose()
    logout()
    navigate('/login')
  }

  const handleProfile = () => {
    handleClose()
    navigate('/dashboard/profile')
  }

  return (
    <StyledAppBar position="absolute" open={open}>
      <Toolbar sx={{ minHeight: '64px !important', px: { xs: 1, sm: 2 } }}>
        <IconButton
          color="inherit"
          aria-label="open drawer"
          onClick={toggleDrawer}
          edge="start"
          sx={{
            marginRight: 2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: 48,
            height: 48,
            ...(open && { display: 'none' }),
            '&:hover': {
              backgroundColor: alpha('#ffffff', 0.1),
            },
          }}
        >
          <MenuIcon sx={{ fontSize: 24 }} />
        </IconButton>

        {/* Logo and Brand */}
        <Box sx={{ display: 'flex', alignItems: 'center', mr: 3 }}>
          <Box
            sx={{
              width: 40,
              height: 40,
              borderRadius: '50%',
              backgroundColor: '#ffffff',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mr: 2,
              boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            }}
          >
            <Typography
              sx={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#4f46e5',
                lineHeight: 1,
                fontFamily: 'serif',
              }}
            >
              🎓
            </Typography>
          </Box>
          <Typography
            component="h1"
            variant="h6"
            sx={{
              fontWeight: 700,
              color: 'white',
              letterSpacing: '-0.5px',
            }}
            noWrap
          >
            Kelem SMS
          </Typography>
        </Box>

        {/* Breadcrumbs */}
        <Box sx={{ display: { xs: 'none', md: 'flex' }, alignItems: 'center', mr: 3 }}>
          <Breadcrumbs
            separator={<NavigateNextIcon sx={{ fontSize: 16, color: alpha('#ffffff', 0.7) }} />}
            sx={{
              '& .MuiBreadcrumbs-separator': {
                mx: 1,
              },
            }}
          >
            {breadcrumbs.map((crumb, index) => (
              <Link
                key={crumb.path}
                component="button"
                variant="body2"
                onClick={() => navigate(crumb.path)}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  color: index === breadcrumbs.length - 1 ? 'white' : alpha('#ffffff', 0.8),
                  textDecoration: 'none',
                  fontWeight: index === breadcrumbs.length - 1 ? 600 : 400,
                  '&:hover': {
                    color: 'white',
                    textDecoration: 'none',
                  },
                  border: 'none',
                  background: 'none',
                  cursor: 'pointer',
                  p: 0,
                }}
              >
                {crumb.icon && (
                  <Box sx={{ mr: 0.5, display: 'flex', alignItems: 'center' }}>
                    {crumb.icon}
                  </Box>
                )}
                {crumb.label}
              </Link>
            ))}
          </Breadcrumbs>
        </Box>



        <Box sx={{ flexGrow: 1 }} />

        {/* Right side actions */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {/* Current Tenant Info */}
          {currentTenant && (
            <Box sx={{
              display: { xs: 'none', lg: 'flex' },
              alignItems: 'center',
              mr: 2,
              px: 2,
              py: 1,
              borderRadius: 2,
              backgroundColor: alpha('#ffffff', 0.1),
              border: `1px solid ${alpha('#ffffff', 0.2)}`,
            }}>
              <Box
                sx={{
                  width: 32,
                  height: 32,
                  borderRadius: 1.5,
                  background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mr: 1.5,
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                }}
              >
                <SchoolIcon sx={{ fontSize: 18, color: '#667eea' }} />
              </Box>
              <Box sx={{ mr: 2 }}>
                <Typography variant="body2" sx={{ fontWeight: 600, color: 'white', lineHeight: 1.2 }} noWrap>
                  {currentTenant.name || 'Unknown School'}
                </Typography>
                <Typography variant="caption" sx={{ color: alpha('#ffffff', 0.8), lineHeight: 1 }} noWrap>
                  Active School
                </Typography>
              </Box>
            </Box>
          )}

          {/* Notifications */}
          <Tooltip title="Notifications">
            <IconButton
              color="inherit"
              onClick={handleNotificationMenu}
              sx={{
                '&:hover': {
                  backgroundColor: alpha('#ffffff', 0.1),
                },
              }}
            >
              <Badge badgeContent={3} color="error">
                <NotificationsIcon />
              </Badge>
            </IconButton>
          </Tooltip>

          {/* Theme Toggle */}
          <Tooltip title={`Switch to ${mode === 'light' ? 'dark' : 'light'} mode`}>
            <IconButton
              color="inherit"
              onClick={toggleColorMode}
              sx={{
                '&:hover': {
                  backgroundColor: alpha('#ffffff', 0.1),
                },
              }}
            >
              {mode === 'dark' ? <LightModeIcon /> : <DarkModeIcon />}
            </IconButton>
          </Tooltip>



          {/* User Profile */}
          <Tooltip title="Account">
            <IconButton
              onClick={handleMenu}
              sx={{
                ml: 1,
                '&:hover': {
                  backgroundColor: alpha('#ffffff', 0.1),
                },
              }}
            >
              <Avatar
                sx={{
                  width: 32,
                  height: 32,
                  bgcolor: alpha('#ffffff', 0.2),
                  color: 'white',
                  fontSize: '0.875rem',
                  fontWeight: 600,
                }}
              >
                {user?.first_name?.[0] || user?.email?.[0]?.toUpperCase() || 'U'}
              </Avatar>
            </IconButton>
          </Tooltip>
        </Box>

        {/* User Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleClose}
          PaperProps={{
            sx: {
              mt: 1.5,
              minWidth: 200,
              borderRadius: 2,
              boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
            },
          }}
        >
          <Box sx={{ px: 2, py: 1.5, borderBottom: 1, borderColor: 'divider' }}>
            <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
              {user?.first_name && user?.last_name
                ? `${user.first_name} ${user.last_name}`
                : user?.email || 'User'
              }
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {user?.email}
            </Typography>
          </Box>
          <MenuItem onClick={handleProfile} sx={{ py: 1.5 }}>
            <PersonIcon sx={{ mr: 2, fontSize: 20 }} />
            Profile
          </MenuItem>
          <MenuItem onClick={handleClose} sx={{ py: 1.5 }}>
            <SettingsIcon sx={{ mr: 2, fontSize: 20 }} />
            Settings
          </MenuItem>
          <Divider />
          <MenuItem onClick={handleLogout} sx={{ py: 1.5, color: 'error.main' }}>
            <LogoutIcon sx={{ mr: 2, fontSize: 20 }} />
            Logout
          </MenuItem>
        </Menu>

        {/* Notifications Menu */}
        <Menu
          anchorEl={notificationAnchorEl}
          open={Boolean(notificationAnchorEl)}
          onClose={handleNotificationClose}
          PaperProps={{
            sx: {
              mt: 1.5,
              minWidth: 320,
              maxWidth: 400,
              borderRadius: 2,
              boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
            },
          }}
        >
          <Box sx={{ px: 2, py: 1.5, borderBottom: 1, borderColor: 'divider' }}>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              Notifications
            </Typography>
          </Box>
          <MenuItem sx={{ py: 2, alignItems: 'flex-start' }}>
            <Avatar sx={{ mr: 2, bgcolor: 'primary.main', width: 32, height: 32 }}>
              <PersonIcon sx={{ fontSize: 18 }} />
            </Avatar>
            <Box>
              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                New student registration
              </Typography>
              <Typography variant="caption" color="text.secondary">
                John Doe has registered for Mathematics 101
              </Typography>
              <Typography variant="caption" color="text.secondary" display="block">
                2 minutes ago
              </Typography>
            </Box>
          </MenuItem>
          <Divider />
          <MenuItem onClick={handleNotificationClose} sx={{ justifyContent: 'center', py: 1.5 }}>
            <Typography variant="body2" color="primary">
              View All Notifications
            </Typography>
          </MenuItem>
        </Menu>
      </Toolbar>
    </StyledAppBar>
  )
}

export default Header
