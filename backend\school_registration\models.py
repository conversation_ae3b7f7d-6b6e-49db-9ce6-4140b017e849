from django.db import models
from django.utils.text import slugify
from django.core.validators import RegexValidator

class SchoolRegistrationRequest(models.Model):
    """Model for school registration requests."""

    STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    )

    # School Information
    name = models.CharField(max_length=100, help_text="School/Institution name")
    slug = models.SlugField(max_length=100, unique=True, blank=True, help_text="Unique identifier for the school's URL")
    address = models.TextField(help_text="Physical address of the school")
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=100)
    country = models.Char<PERSON>ield(max_length=100, default="Ethiopia")
    postal_code = models.CharField(max_length=20, blank=True, null=True)

    # Contact Information
    contact_person = models.CharField(max_length=100, help_text="Name of the contact person")
    contact_email = models.Em<PERSON><PERSON>ield(help_text="Email address for school communications")
    phone_regex = RegexValidator(regex=r'^\+?1?\d{9,15}$', message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.")
    contact_phone = models.CharField(validators=[phone_regex], max_length=17, help_text="Phone number for school communications")
    website = models.URLField(blank=True, null=True, help_text="School website URL")

    # School Details
    established_year = models.PositiveIntegerField(help_text="Year the school was established")
    school_type = models.CharField(max_length=50, help_text="Type of school (e.g., Primary, Secondary, College)")
    student_capacity = models.PositiveIntegerField(help_text="Maximum number of students")
    description = models.TextField(help_text="Brief description of the school")

    # Admin User Information
    admin_email = models.EmailField(blank=True, null=True, help_text="Email for the school admin account")
    admin_password = models.CharField(max_length=100, blank=True, null=True, help_text="Password for the school admin account")

    # Registration Status
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    requested_on = models.DateTimeField(auto_now_add=True)
    processed_on = models.DateTimeField(blank=True, null=True)
    rejection_reason = models.TextField(blank=True, null=True, help_text="Reason for rejection if status is 'rejected'")

    # Tenant Information (filled after approval)
    schema_name = models.CharField(max_length=100, blank=True, null=True, help_text="Database schema name for the tenant")
    domain = models.CharField(max_length=100, blank=True, null=True, help_text="Subdomain for the school")

    def save(self, *args, **kwargs):
        # Generate slug if not provided
        if not self.slug:
            self.slug = slugify(self.name)

        # Generate schema_name if not provided and status is approved
        if self.status == 'approved' and not self.schema_name:
            # Create a PostgreSQL-compatible schema name (no hyphens, spaces, etc.)
            # Replace hyphens with underscores and remove any other invalid characters
            import re
            schema_name = re.sub(r'[^a-zA-Z0-9_]', '_', self.slug)

            # Ensure it starts with a letter
            if not schema_name[0].isalpha():
                schema_name = 'sch_' + schema_name

            self.schema_name = schema_name

        # Generate domain if not provided and status is approved
        if self.status == 'approved' and not self.domain:
            self.domain = f"{self.slug}.kelemsms.com"

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.name} ({self.get_status_display()})"
