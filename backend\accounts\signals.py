from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import User, AdminProfile, TeacherProfile, StudentProfile, ParentProfile, StaffProfile

@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    """
    Create the appropriate profile when a user is created based on user_type.
    """
    if created:
        if instance.user_type == 'admin':
            AdminProfile.objects.create(user=instance)
        elif instance.user_type == 'teacher':
            # Note: This will need employee_id to be set manually
            pass
        elif instance.user_type == 'student':
            # Create a student profile with a placeholder student_id
            # The actual student_id will need to be set manually
            StudentProfile.objects.create(
                user=instance,
                student_id=f"TEMP-{instance.id}"
            )
        elif instance.user_type == 'parent':
            ParentProfile.objects.create(
                user=instance,
                guardian_name=f"{instance.first_name} {instance.last_name}"
            )
        elif instance.user_type == 'staff':
            # Note: This will need employee_id to be set manually
            pass

@receiver(post_save, sender=User)
def save_user_profile(sender, instance, **kwargs):
    """
    Save the user profile when the user is updated.
    """
    if instance.user_type == 'admin' and hasattr(instance, 'admin_profile'):
        instance.admin_profile.save()
    elif instance.user_type == 'teacher' and hasattr(instance, 'teacher_profile'):
        instance.teacher_profile.save()
    elif instance.user_type == 'student' and hasattr(instance, 'student_profile'):
        instance.student_profile.save()
    elif instance.user_type == 'parent' and hasattr(instance, 'parent_profile'):
        instance.parent_profile.save()
    elif instance.user_type == 'staff' and hasattr(instance, 'staff_profile'):
        instance.staff_profile.save()
