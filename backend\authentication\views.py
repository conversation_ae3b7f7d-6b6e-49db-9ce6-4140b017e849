from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout, update_session_auth_hash, get_user_model
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from django.contrib.auth.tokens import default_token_generator
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.utils.encoding import force_bytes
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.contrib import messages
from django.conf import settings
from django_tenants.utils import schema_context, tenant_context

from .models import User
from .decorators import admin_required, teacher_required, student_required, parent_required, staff_required
from tenants.models import School

@csrf_exempt
def api_login(request):
    """API endpoint for user login."""
    # Handle GET requests (for redirects)
    if request.method == 'GET':
        # Return a simple login page or redirect to the frontend
        return JsonResponse({
            'success': False,
            'message': 'Please use POST method for login',
            'login_url': '/auth/api/login/',
            'docs': 'Send a POST request with email and password fields'
        }, status=405)

    # Handle POST requests
    # Parse the request data
    if request.content_type == 'application/json':
        import json
        try:
            data = json.loads(request.body)
            email = data.get('email')
            password = data.get('password')
        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'message': 'Invalid JSON'}, status=400)
    else:
        email = request.POST.get('email')
        password = request.POST.get('password')

    if not email or not password:
        return JsonResponse({'success': False, 'message': 'Email and password are required'}, status=400)

    # Try to authenticate the user with our custom backend
    # Don't pass request to avoid session issues
    user = authenticate(None, email=email, password=password)

    if user is not None:
        # Get the schema name if it exists
        schema_name = getattr(user, '_schema_name', None)

        # Generate a token for the user using DRF's Token model
        from rest_framework.authtoken.models import Token
        from django_tenants.utils import schema_context

        # Get the schema name where the user was found
        user_schema = getattr(user, '_schema_name', 'public')
        print(f"User {user.email} was found in schema: {user_schema}")

        # Make sure we're in the correct schema for token operations
        try:
            with schema_context(user_schema):
                try:
                    # First try to get an existing token
                    token = Token.objects.get(user=user)
                    print(f"Found existing token for user {user.email}: {token.key[:10]}... in schema {user_schema}")
                except Token.DoesNotExist:
                    # Create a new token if one doesn't exist
                    token = Token.objects.create(user=user)
                    print(f"Created new token for user {user.email}: {token.key[:10]}... in schema {user_schema}")
                except Exception as e:
                    # Handle other exceptions (like IntegrityError)
                    print(f"Error getting token in schema {user_schema}: {str(e)}")
                    # Clean up any existing tokens for this user
                    Token.objects.filter(user=user).delete()
                    # Try to create a new token
                    token = Token.objects.create(user=user)
                    print(f"Created new token after cleanup: {token.key[:10]}... in schema {user_schema}")

                # Get the token key for the response
                token_key = token.key
        except Exception as e:
            print(f"Unexpected error handling token in schema {user_schema}: {str(e)}")
            # Last resort fallback
            return JsonResponse({'success': False, 'message': f'Error generating token: {str(e)}'}, status=500)

        # Use the token key in the response
        token = token_key

        # Store the token in the user's session
        try:
            # Store tenant information
            tenant_id = None
            tenant_name = None

            # If the user was found in a tenant schema, get the tenant info
            if schema_name and schema_name != 'public':
                try:
                    tenant = School.objects.get(schema_name=schema_name)
                    tenant_id = tenant.id
                    tenant_name = tenant.name
                except School.DoesNotExist:
                    pass
            elif schema_name == 'public' or not schema_name:
                # For public schema users (superadmins), set the public tenant
                try:
                    public_tenant = School.objects.get(schema_name='public')
                    tenant_id = public_tenant.id
                    tenant_name = public_tenant.name
                except School.DoesNotExist:
                    pass

            # Store the token in the user's session without using Django's session framework
            # We'll use the token as a key in the response

            # Return success response with the token
            return JsonResponse({
                'success': True,
                'token': token,
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'user_type': user.user_type,
                    'is_staff': user.is_staff,
                    'is_superuser': user.is_superuser,
                    'schema_name': schema_name,
                    'tenant_id': tenant_id,
                    'tenant_name': tenant_name
                }
            })
        except Exception as e:
            print(f"Error generating token: {str(e)}")
            return JsonResponse({'success': False, 'message': f'Login error: {str(e)}'}, status=500)

    # If we get here, authentication failed
    # Check if the user exists in any schema
    from django.contrib.auth import get_user_model
    from django_tenants.utils import schema_context
    User = get_user_model()
    found_schemas = []

    # Check public schema
    try:
        public_user = User.objects.filter(email=email).first()
        if public_user:
            found_schemas.append('public')
    except Exception:
        pass

    # Check tenant schemas
    for tenant in School.objects.all():
        try:
            with schema_context(tenant.schema_name):
                tenant_user = User.objects.filter(email=email).first()
                if tenant_user:
                    found_schemas.append(tenant.schema_name)
        except Exception:
            pass

    if found_schemas:
        return JsonResponse({'success': False, 'message': 'Invalid credentials'}, status=401)
    else:
        return JsonResponse({'success': False, 'message': 'User not found'}, status=401)

@csrf_exempt
@require_POST
@login_required
def api_logout(request):
    """API endpoint for user logout."""
    try:
        # Get the session key before logout
        session_key = request.session.session_key

        # Perform logout which will clear the session
        logout(request)

        # Create a new session to prevent SessionInterrupted errors
        request.session.create()

        return JsonResponse({
            'success': True,
            'message': 'Logged out successfully',
            'previous_session': session_key,
            'new_session': request.session.session_key
        })
    except Exception as e:
        print(f"Error during logout: {str(e)}")
        # Still return success to the client
        return JsonResponse({'success': True, 'message': 'Logged out with warnings'})

@csrf_exempt
@require_POST
def api_debug_check_user_schemas(request):
    """Debug endpoint to check which schemas a user exists in."""
    # Get the email from the request
    if request.content_type == 'application/json':
        import json
        try:
            data = json.loads(request.body)
            email = data.get('email')
        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'message': 'Invalid JSON'}, status=400)
    else:
        email = request.POST.get('email')

    if not email:
        return JsonResponse({'success': False, 'message': 'Email is required'}, status=400)

    # Check all schemas for this user
    found_schemas = []
    user_details = {}

    # Check public schema first
    try:
        User = get_user_model()
        user = User.objects.filter(email=email).first()
        if user:
            found_schemas.append('public')
            user_details['public'] = {
                'id': user.id,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'user_type': user.user_type,
                'is_active': user.is_active,
                'is_staff': user.is_staff,
                'is_superuser': user.is_superuser,
            }
    except Exception as e:
        print(f"Error checking public schema: {str(e)}")

    # Check all tenant schemas
    for tenant in School.objects.all():
        try:
            with schema_context(tenant.schema_name):
                User = get_user_model()
                user = User.objects.filter(email=email).first()
                if user:
                    found_schemas.append(tenant.schema_name)
                    user_details[tenant.schema_name] = {
                        'id': user.id,
                        'email': user.email,
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'user_type': user.user_type,
                        'is_active': user.is_active,
                        'is_staff': user.is_staff,
                        'is_superuser': user.is_superuser,
                        'tenant_name': tenant.name,
                        'tenant_id': tenant.id,
                    }
        except Exception as e:
            print(f"Error checking schema {tenant.schema_name}: {str(e)}")

    return JsonResponse({
        'success': True,
        'email': email,
        'found_in_schemas': found_schemas,
        'user_details': user_details,
    })

def api_user(request):
    """API endpoint to get current user information."""
    # Print debug information
    print(f"api_user: Request headers: {request.headers}")
    print(f"api_user: User authenticated: {request.user.is_authenticated}")

    # Check for token in Authorization header
    auth_header = request.headers.get('Authorization')
    if auth_header and auth_header.startswith('Token '):
        token_key = auth_header.split(' ')[1]
        print(f"api_user: Found token in Authorization header: {token_key[:10]}...")

        # Try to authenticate with the token
        from authentication.backends import TokenAuthBackend
        token_backend = TokenAuthBackend()
        user = token_backend.authenticate(request=request, token=token_key)

        if user:
            print(f"api_user: Successfully authenticated with token: {user.email}")
            # Continue with the authenticated user
        else:
            print(f"api_user: Failed to authenticate with token")
            return JsonResponse({'success': False, 'message': 'Invalid token'}, status=401)
    else:
        # Check if the user is authenticated through session
        if not request.user.is_authenticated:
            print(f"api_user: User not authenticated and no token provided")
            # Instead of redirecting, return a 401 Unauthorized response
            return JsonResponse({'success': False, 'message': 'Authentication required'}, status=401)

        # Use the session-authenticated user
        user = request.user
        print(f"api_user: Using session-authenticated user: {user.email}")

    # Get the schema name if it exists
    schema_name = getattr(user, '_schema_name', None)

    # Get current tenant information if available
    current_tenant = None
    if hasattr(request, 'tenant') and request.tenant.schema_name != 'public':
        try:
            current_tenant = {
                'id': request.tenant.id,
                'name': request.tenant.name,
                'schema_name': request.tenant.schema_name,
                'domain': request.tenant.domains.first().domain if request.tenant.domains.exists() else None
            }
        except Exception as e:
            print(f"Error getting current tenant from request: {str(e)}")

    # Get the current tenant from the session
    session_tenant = None
    tenant_id = None

    try:
        # Try to get tenant_id from session
        tenant_id = request.session.get('tenant_id')
    except Exception as e:
        print(f"Error accessing session: {str(e)}")

    if tenant_id:
        try:
            tenant = School.objects.get(id=tenant_id)
            session_tenant = {
                'id': tenant.id,
                'name': tenant.name,
                'schema_name': tenant.schema_name,
                'domain': tenant.domains.first().domain if tenant.domains.exists() else None
            }
        except School.DoesNotExist:
            print(f"Tenant with ID {tenant_id} not found")
            # Don't modify the session here to avoid potential errors

    # If no tenant is set but user is a superuser, set the public tenant
    if not session_tenant and not current_tenant and user.is_superuser:
        try:
            public_tenant = School.objects.get(schema_name='public')
            session_tenant = {
                'id': public_tenant.id,
                'name': public_tenant.name,
                'schema_name': public_tenant.schema_name,
                'domain': public_tenant.domains.first().domain if public_tenant.domains.exists() else None
            }
            # Try to set the tenant in the session
            try:
                request.session['tenant_id'] = public_tenant.id
            except Exception as e:
                print(f"Error setting tenant in session: {str(e)}")
        except School.DoesNotExist:
            print("Public tenant not found")

    # Return the user information
    return JsonResponse({
        'id': user.id,
        'email': user.email,
        'first_name': user.first_name,
        'last_name': user.last_name,
        'user_type': user.user_type,
        'is_staff': user.is_staff,
        'is_superuser': user.is_superuser,
        'schema_name': schema_name,
        'current_tenant': current_tenant,
        'session_tenant': session_tenant
    })

@csrf_exempt
@require_POST
def api_password_reset_request(request):
    """API endpoint to request a password reset."""
    email = request.POST.get('email')

    if not email:
        return JsonResponse({'success': False, 'message': 'Email is required'}, status=400)

    try:
        user = User.objects.get(email=email)

        # Generate password reset token
        token = default_token_generator.make_token(user)
        uid = urlsafe_base64_encode(force_bytes(user.pk))

        # Build reset URL
        reset_url = f"{settings.FRONTEND_URL}/reset-password/{uid}/{token}/"

        # Send email
        subject = 'Password Reset Request'
        message = render_to_string('authentication/password_reset_email.html', {
            'user': user,
            'reset_url': reset_url,
        })

        send_mail(
            subject,
            message,
            settings.DEFAULT_FROM_EMAIL,
            [user.email],
            fail_silently=False,
        )

        return JsonResponse({'success': True, 'message': 'Password reset email sent'})
    except User.DoesNotExist:
        # Don't reveal that the user doesn't exist
        return JsonResponse({'success': True, 'message': 'If your email is registered, you will receive a password reset link'})

@csrf_exempt
@require_POST
def api_password_reset_confirm(request):
    """API endpoint to confirm a password reset."""
    uid = request.POST.get('uid')
    token = request.POST.get('token')
    new_password = request.POST.get('new_password')

    if not uid or not token or not new_password:
        return JsonResponse({'success': False, 'message': 'All fields are required'}, status=400)

    try:
        # Decode the user ID
        user_id = urlsafe_base64_decode(uid).decode()
        user = User.objects.get(pk=user_id)

        # Check if the token is valid
        if default_token_generator.check_token(user, token):
            # Set the new password
            user.set_password(new_password)
            user.save()
            return JsonResponse({'success': True, 'message': 'Password has been reset successfully'})
        else:
            return JsonResponse({'success': False, 'message': 'Invalid or expired token'}, status=400)
    except (TypeError, ValueError, OverflowError, User.DoesNotExist):
        return JsonResponse({'success': False, 'message': 'Invalid reset link'}, status=400)

@login_required
def user_profile(request):
    """View for user profile."""
    return render(request, 'authentication/profile.html')

@login_required
def profile_edit(request):
    """View for editing user profile."""
    if request.method == 'POST':
        # Process form data
        first_name = request.POST.get('first_name')
        last_name = request.POST.get('last_name')
        phone_number = request.POST.get('phone_number')

        # Update user
        user = request.user
        user.first_name = first_name
        user.last_name = last_name
        user.phone_number = phone_number
        user.save()

        messages.success(request, 'Profile updated successfully!')
        return redirect('authentication:profile')

    return render(request, 'authentication/profile_edit.html')

@login_required
def password_change(request):
    """View for changing password."""
    if request.method == 'POST':
        # Process form data
        current_password = request.POST.get('current_password')
        new_password = request.POST.get('new_password')
        confirm_password = request.POST.get('confirm_password')

        # Validate passwords
        if not request.user.check_password(current_password):
            messages.error(request, 'Current password is incorrect.')
            return render(request, 'authentication/password_change.html')

        if new_password != confirm_password:
            messages.error(request, 'New passwords do not match.')
            return render(request, 'authentication/password_change.html')

        # Update password
        request.user.set_password(new_password)
        request.user.save()

        # Update session to prevent logout
        update_session_auth_hash(request, request.user)

        messages.success(request, 'Password changed successfully!')
        return redirect('authentication:profile')

    return render(request, 'authentication/password_change.html')

@admin_required
def admin_dashboard(request):
    """Dashboard for administrators."""
    return render(request, 'authentication/admin_dashboard.html')

@teacher_required
def teacher_dashboard(request):
    """Dashboard for teachers."""
    return render(request, 'authentication/teacher_dashboard.html')

@student_required
def student_dashboard(request):
    """Dashboard for students."""
    return render(request, 'authentication/student_dashboard.html')

@parent_required
def parent_dashboard(request):
    """Dashboard for parents."""
    return render(request, 'authentication/parent_dashboard.html')

@staff_required
def staff_dashboard(request):
    """Dashboard for staff members."""
    return render(request, 'authentication/staff_dashboard.html')

def api_user_tenants(request):
    """API endpoint to get tenants the user has access to."""
    # Print debug information
    print(f"api_user_tenants: Request headers: {request.headers}")
    print(f"api_user_tenants: User authenticated: {request.user.is_authenticated}")

    # Check for token in Authorization header
    auth_header = request.headers.get('Authorization')
    if auth_header and auth_header.startswith('Token '):
        token_key = auth_header.split(' ')[1]
        print(f"api_user_tenants: Found token in Authorization header: {token_key[:10]}...")

        # Try to authenticate with the token
        from authentication.backends import TokenAuthBackend
        token_backend = TokenAuthBackend()
        user = token_backend.authenticate(request=request, token=token_key)

        if user:
            print(f"api_user_tenants: Successfully authenticated with token: {user.email}")
            # Continue with the authenticated user
        else:
            print(f"api_user_tenants: Failed to authenticate with token")
            return JsonResponse({'success': False, 'message': 'Invalid token'}, status=401)
    else:
        # Check if the user is authenticated through session
        if not request.user.is_authenticated:
            print(f"api_user_tenants: User not authenticated and no token provided")
            # Instead of redirecting, return a 401 Unauthorized response
            return JsonResponse({'success': False, 'message': 'Authentication required'}, status=401)

        # Use the session-authenticated user
        user = request.user
        print(f"api_user_tenants: Using session-authenticated user: {user.email}")
    tenants = []

    print(f"Getting tenants for user: {user.email}")
    print(f"User is_superuser: {user.is_superuser}")
    print(f"User user_type: {user.user_type}")

    # Check if the user has a schema_name attribute (set by our custom auth backend)
    schema_name = getattr(user, '_schema_name', None)
    print(f"User authenticated in schema: {schema_name}")

    # If the user is a superuser in the public schema, they have access to all tenants
    if user.is_superuser and (not schema_name or schema_name == 'public'):
        print("User is a superuser in the public schema, returning all tenants")
        schools = School.objects.all()
        for school in schools:
            tenants.append({
                'id': school.id,
                'name': school.name,
                'schema_name': school.schema_name,
                'domain': school.domains.first().domain if school.domains.exists() else None,
            })
    else:
        # If the user is authenticated in a tenant schema and is an admin there,
        # they should only have access to that tenant
        if schema_name and schema_name != 'public' and user.user_type == 'admin':
            print(f"User is an admin in schema {schema_name}, returning only that tenant")
            try:
                school = School.objects.get(schema_name=schema_name)
                tenants.append({
                    'id': school.id,
                    'name': school.name,
                    'schema_name': school.schema_name,
                    'domain': school.domains.first().domain if school.domains.exists() else None,
                    'user_type': 'admin',  # Add user_type to the response
                })

                # Make sure the tenant is set in the session
                request.session['tenant_id'] = school.id
                print(f"Set tenant_id in session: {school.id}")

                # For tenant admins, we want to return early to ensure they only see their tenant
                return JsonResponse(tenants, safe=False)
            except School.DoesNotExist:
                print(f"School with schema {schema_name} not found")
        else:
            # For regular users, check which tenants they have access to
            print("Checking all tenants for user access")
            schools = School.objects.all()

            for school in schools:
                # Use tenant_context to check if the user exists in this tenant
                with tenant_context(school):
                    try:
                        # Try to find the user in this tenant's database
                        tenant_user = User.objects.get(email=user.email)
                        print(f"User found in tenant {school.name} with user_type: {tenant_user.user_type}")

                        # If we get here, the user exists in this tenant
                        tenants.append({
                            'id': school.id,
                            'name': school.name,
                            'schema_name': school.schema_name,
                            'domain': school.domains.first().domain if school.domains.exists() else None,
                            'user_type': tenant_user.user_type,
                        })
                    except User.DoesNotExist:
                        # User doesn't exist in this tenant
                        print(f"User not found in tenant {school.name}")
                        pass

    print(f"Returning {len(tenants)} tenants for user")
    return JsonResponse(tenants, safe=False)

@csrf_exempt
@require_POST
@login_required
def api_set_tenant(request):
    """API endpoint to set the current tenant."""
    print(f"Setting tenant for user: {request.user.email}")

    # Handle both form data and JSON
    if request.content_type == 'application/json':
        import json
        try:
            data = json.loads(request.body)
            tenant_id = data.get('tenant_id')
            print(f"Received tenant_id from JSON: {tenant_id}")
        except json.JSONDecodeError:
            print("Invalid JSON received")
            return JsonResponse({'success': False, 'message': 'Invalid JSON'}, status=400)
    else:
        tenant_id = request.POST.get('tenant_id')
        print(f"Received tenant_id from POST: {tenant_id}")

    # If no tenant_id is provided but user is a tenant admin, try to find their tenant
    if not tenant_id:
        schema_name = getattr(request.user, '_schema_name', None)
        if schema_name and schema_name != 'public' and request.user.user_type == 'admin':
            print(f"No tenant_id provided, but user is admin in schema {schema_name}. Trying to find their tenant.")
            try:
                tenant = School.objects.get(schema_name=schema_name)
                tenant_id = tenant.id
                print(f"Found tenant for admin: {tenant.name} (ID: {tenant.id})")
            except School.DoesNotExist:
                print(f"Could not find tenant with schema {schema_name}")
                pass

    if not tenant_id:
        print("No tenant_id provided and could not determine tenant automatically")
        return JsonResponse({'success': False, 'message': 'Tenant ID is required'}, status=400)

    try:
        # Get the tenant
        tenant = School.objects.get(id=tenant_id)
        print(f"Found tenant: {tenant.name} (Schema: {tenant.schema_name})")

        # Create a new session if it doesn't exist
        if not request.session.session_key:
            request.session.create()

        # Store the tenant ID in the session
        request.session['tenant_id'] = tenant.id
        print(f"Set tenant_id in session: {tenant.id}")

        # Make sure the session is saved
        request.session.modified = True
        request.session.save()
        print(f"Session saved with key: {request.session.session_key}")

        # Return success with detailed tenant info
        response_data = {
            'success': True,
            'tenant': {
                'id': tenant.id,
                'name': tenant.name,
                'schema_name': tenant.schema_name,
                'domain': tenant.domains.first().domain if tenant.domains.exists() else None,
            },
            'session_id': request.session.session_key,
        }
        print(f"Returning success response with tenant info")
        return JsonResponse(response_data)
    except School.DoesNotExist:
        print(f"Tenant with ID {tenant_id} not found")
        return JsonResponse({'success': False, 'message': 'Tenant not found'}, status=404)
    except Exception as e:
        print(f"Error setting tenant: {str(e)}")
        return JsonResponse({'success': False, 'message': f'Error setting tenant: {str(e)}'}, status=500)
