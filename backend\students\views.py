from rest_framework import viewsets, permissions, status, filters, generics
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Su<PERSON>, <PERSON>, Q
from django.db import connection
from django.utils import timezone
from django.core.files.uploadedfile import UploadedFile

from .models import Student, Guardian, StudentDocument, StudentAchievement, StudentFee, FeePayment, StudentFormConfig
from .serializers import (
    StudentListSerializer, StudentDetailSerializer, GuardianSerializer,
    StudentDocumentSerializer, StudentAchievementSerializer,
    StudentFeeSerializer, FeePaymentSerializer, StudentRegistrationSerializer,
    StudentFormConfigSerializer
)
from authentication.permissions import (
    IsAdmin, IsTeacher, IsStudent, IsParent, IsAdminOrTeacher,
    IsOwnerOrAdmin, IsParentOfStudent, ReadOnly
)

class StudentViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing students.
    """
    queryset = Student.objects.all()
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'gender', 'current_grade_level', 'current_section', 'is_active']
    search_fields = ['first_name', 'last_name', 'student_id', 'email', 'phone_number']
    ordering_fields = ['last_name', 'first_name', 'enrollment_date', 'current_grade_level']
    ordering = ['last_name', 'first_name']

    def get_queryset(self):
        # Print debug information
        print(f"StudentViewSet.get_queryset() called")
        print(f"User: {self.request.user}, authenticated: {self.request.user.is_authenticated}")
        print(f"Tenant: {getattr(self.request, 'tenant', None)}")
        print(f"Headers: {self.request.headers}")

        # Check if we're in the public schema
        from django.db import connection
        if connection.schema_name == 'public':
            # Return an empty queryset for the public schema
            print(f"In public schema, returning empty queryset")
            return Student.objects.none()

        try:
            # Get the base queryset
            queryset = super().get_queryset()

            # Apply any additional filtering
            tenant_id = self.request.GET.get('tenant_id')
            if tenant_id:
                print(f"Filtering by tenant_id: {tenant_id}")
                # If we have a tenant_id parameter, we can use it for filtering
                # This would require adding a tenant field to the Student model
                # queryset = queryset.filter(tenant_id=tenant_id)

            return queryset
        except Exception as e:
            # Check if the error is related to missing tables
            if 'relation' in str(e) and 'does not exist' in str(e):
                print(f"Error in get_queryset: {str(e)}")
                return Student.objects.none()
            # Re-raise other exceptions
            raise

    def get_serializer_class(self):
        if self.action == 'list':
            return StudentListSerializer
        return StudentDetailSerializer

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated]
        elif self.action in ['create', 'update', 'partial_update', 'destroy']:
            permission_classes = [IsAdmin]
        else:
            permission_classes = [IsAdmin]
        return [permission() for permission in permission_classes]

    def perform_create(self, serializer):
        # Additional logic for student creation
        student = serializer.save()

        # Create a TenantActivity record for the new student
        from tenant_management.models import TenantActivity
        TenantActivity.objects.create(
            tenant=self.request.tenant,
            activity_type='student_created',
            description=f'New student created: {student.full_name()}',
            user_count=1
        )

    @action(detail=True, methods=['get'])
    def attendance(self, request, pk=None):
        """
        Get attendance records for a student.
        """
        student = self.get_object()

        # Import here to avoid circular imports
        from assessments.models import Attendance
        from assessments.serializers import AttendanceSerializer

        # Get query parameters
        course_offering_id = request.query_params.get('course_offering')
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')

        # Filter attendance records
        attendance_records = Attendance.objects.filter(student=student)

        if course_offering_id:
            attendance_records = attendance_records.filter(course_offering_id=course_offering_id)

        if start_date:
            attendance_records = attendance_records.filter(date__gte=start_date)

        if end_date:
            attendance_records = attendance_records.filter(date__lte=end_date)

        # Order by date
        attendance_records = attendance_records.order_by('-date')

        # Serialize and return
        serializer = AttendanceSerializer(attendance_records, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def grades(self, request, pk=None):
        """
        Get grades for a student.
        """
        student = self.get_object()

        # Import here to avoid circular imports
        from assessments.models import AssessmentResult
        from assessments.serializers import AssessmentResultSerializer

        # Get query parameters
        course_offering_id = request.query_params.get('course_offering')
        assessment_type = request.query_params.get('assessment_type')

        # Filter assessment results
        assessment_results = AssessmentResult.objects.filter(student=student)

        if course_offering_id:
            assessment_results = assessment_results.filter(assessment__course_offering_id=course_offering_id)

        if assessment_type:
            assessment_results = assessment_results.filter(assessment__assessment_type__code=assessment_type)

        # Order by date
        assessment_results = assessment_results.order_by('-assessment__assessment_date')

        # Serialize and return
        serializer = AssessmentResultSerializer(assessment_results, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def report_cards(self, request, pk=None):
        """
        Get report cards for a student.
        """
        student = self.get_object()

        # Import here to avoid circular imports
        from assessments.models import ReportCard
        from assessments.serializers import ReportCardSerializer

        # Get query parameters
        academic_year_id = request.query_params.get('academic_year')
        term_id = request.query_params.get('term')

        # Filter report cards
        report_cards = ReportCard.objects.filter(student=student)

        if academic_year_id:
            report_cards = report_cards.filter(academic_year_id=academic_year_id)

        if term_id:
            report_cards = report_cards.filter(term_id=term_id)

        # Order by academic year and term
        report_cards = report_cards.order_by('-academic_year__start_date', '-term__start_date')

        # Serialize and return
        serializer = ReportCardSerializer(report_cards, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def enrollments(self, request, pk=None):
        """
        Get course enrollments for a student.
        """
        student = self.get_object()

        # Import here to avoid circular imports
        from courses.models import Enrollment
        from courses.serializers import EnrollmentSerializer

        # Get query parameters
        academic_year_id = request.query_params.get('academic_year')
        term_id = request.query_params.get('term')
        status = request.query_params.get('status')

        # Filter enrollments
        enrollments = Enrollment.objects.filter(student=student)

        if academic_year_id:
            enrollments = enrollments.filter(course_offering__term__academic_year_id=academic_year_id)

        if term_id:
            enrollments = enrollments.filter(course_offering__term_id=term_id)

        if status:
            enrollments = enrollments.filter(status=status)

        # Order by term and course
        enrollments = enrollments.order_by('-course_offering__term__start_date', 'course_offering__course__name')

        # Serialize and return
        serializer = EnrollmentSerializer(enrollments, many=True)
        return Response(serializer.data)

class GuardianViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing guardians.
    """
    queryset = Guardian.objects.all()
    serializer_class = GuardianSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['student', 'relationship', 'is_primary_contact', 'is_emergency_contact']
    search_fields = ['first_name', 'last_name', 'email', 'phone_number']

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated]
        elif self.action in ['create', 'update', 'partial_update', 'destroy']:
            permission_classes = [IsAdmin]
        else:
            permission_classes = [IsAdmin]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        queryset = super().get_queryset()

        # If user is a parent, only show their own guardianships
        if self.request.user.user_type == 'parent':
            queryset = queryset.filter(user=self.request.user)

        # If user is a student, only show their own guardians
        elif self.request.user.user_type == 'student':
            try:
                student = self.request.user.student_profile
                queryset = queryset.filter(student=student)
            except:
                queryset = Guardian.objects.none()

        return queryset

class StudentDocumentViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing student documents.
    """
    queryset = StudentDocument.objects.all()
    serializer_class = StudentDocumentSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['student', 'document_type', 'is_verified']
    search_fields = ['title', 'description']

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated]
        elif self.action in ['create']:
            permission_classes = [IsAdminOrTeacher]
        elif self.action in ['update', 'partial_update', 'destroy']:
            permission_classes = [IsAdmin]
        else:
            permission_classes = [IsAdmin]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        queryset = super().get_queryset()

        # If user is a parent, only show documents for their children
        if self.request.user.user_type == 'parent':
            # Get all students where this user is a guardian
            student_ids = Guardian.objects.filter(user=self.request.user).values_list('student_id', flat=True)
            queryset = queryset.filter(student_id__in=student_ids)

        # If user is a student, only show their own documents
        elif self.request.user.user_type == 'student':
            try:
                student = self.request.user.student_profile
                queryset = queryset.filter(student=student)
            except:
                queryset = StudentDocument.objects.none()

        return queryset

    @action(detail=True, methods=['post'])
    def verify(self, request, pk=None):
        """
        Verify a student document.
        """
        document = self.get_object()
        document.is_verified = True
        document.verified_by = request.user
        document.verified_date = timezone.now()
        document.save()

        serializer = self.get_serializer(document)
        return Response(serializer.data)

class StudentFormConfigViewSet(viewsets.ModelViewSet):
    queryset = StudentFormConfig.objects.all()
    serializer_class = StudentFormConfigSerializer
    permission_classes = [permissions.IsAuthenticated]

    def dispatch(self, request, *args, **kwargs):
        # Log authentication information for debugging
        print(f"StudentFormConfigViewSet.dispatch() called")
        print(f"User: {request.user}, authenticated: {request.user.is_authenticated}")
        print(f"Auth header: {request.headers.get('Authorization', 'None')}")
        print(f"GET params: {request.GET}")
        print(f"Current schema: {connection.schema_name}")

        # Continue with normal dispatch
        return super().dispatch(request, *args, **kwargs)

    def get_queryset(self):
        # Log authentication information for debugging
        print(f"StudentFormConfigViewSet.get_queryset() called")
        print(f"User: {self.request.user}, authenticated: {self.request.user.is_authenticated}")
        print(f"Auth header: {self.request.headers.get('Authorization', 'None')}")
        print(f"GET params: {self.request.GET}")
        print(f"Current schema: {connection.schema_name}")

        # Filter by tenant if tenant_id is provided in query params
        tenant_id = self.request.GET.get('tenant_id')
        queryset = StudentFormConfig.objects.all()

        if tenant_id:
            print(f"Filtering form configs by tenant_id: {tenant_id}")
            queryset = queryset.filter(tenant_id=tenant_id)
            print(f"Found {queryset.count()} form configs for tenant {tenant_id}")

            # Log all form configs for this tenant for debugging
            configs = list(queryset)
            print(f"Form configs for tenant {tenant_id}: {configs}")
            for config in configs:
                print(f"  - {config.field_name} (custom: {config.is_custom})")

        # Filter by section if provided
        section = self.request.query_params.get('section')
        if section:
            print(f"Further filtering by section: {section}")
            queryset = queryset.filter(section=section)
            print(f"Found {queryset.count()} form configs for section {section}")

        return queryset

    @action(detail=False, methods=['get'])
    def by_tenant(self, request):
        """Get all form configurations for a specific tenant"""
        tenant_id = request.GET.get('tenant_id')
        if not tenant_id:
            return Response({'error': 'tenant_id is required'}, status=status.HTTP_400_BAD_REQUEST)

        print(f"Getting form configs for tenant {tenant_id} in schema {connection.schema_name}")

        # Get all form configs for this tenant
        queryset = StudentFormConfig.objects.filter(tenant_id=tenant_id)
        print(f"Found {queryset.count()} form configs for tenant {tenant_id}")

        # Log all form configs for debugging
        configs = list(queryset)
        for config in configs:
            print(f"  - {config.field_name} (custom: {config.is_custom})")

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def default_config(self, request):
        """Get default field configurations for a new tenant"""
        # Log authentication information for debugging
        print(f"StudentFormConfigViewSet.default_config() called")
        print(f"User: {request.user}, authenticated: {request.user.is_authenticated}")
        print(f"Auth header: {request.headers.get('Authorization', 'None')}")

        # This will be used to initialize a new tenant's form configuration
        default_configs = [
            # Personal Information
            {'field_name': 'first_name', 'display_name': 'First Name', 'field_type': 'text', 'section': 'personal', 'status': 'required', 'order': 1},
            {'field_name': 'middle_name', 'display_name': 'Middle Name', 'field_type': 'text', 'section': 'personal', 'status': 'optional', 'order': 2},
            {'field_name': 'last_name', 'display_name': 'Last Name', 'field_type': 'text', 'section': 'personal', 'status': 'required', 'order': 3},
            {'field_name': 'gender', 'display_name': 'Gender', 'field_type': 'select', 'section': 'personal', 'status': 'required', 'order': 4,
             'options': [{'value': 'M', 'label': 'Male'}, {'value': 'F', 'label': 'Female'}, {'value': 'O', 'label': 'Other'}]},
            {'field_name': 'date_of_birth', 'display_name': 'Date of Birth', 'field_type': 'date', 'section': 'personal', 'status': 'optional', 'order': 5},
            {'field_name': 'nationality', 'display_name': 'Nationality', 'field_type': 'text', 'section': 'personal', 'status': 'optional', 'order': 6},
            {'field_name': 'mother_tongue', 'display_name': 'Mother Tongue', 'field_type': 'text', 'section': 'personal', 'status': 'optional', 'order': 7},
            {'field_name': 'religion', 'display_name': 'Religion', 'field_type': 'text', 'section': 'personal', 'status': 'optional', 'order': 8},

            # Contact Information
            {'field_name': 'email', 'display_name': 'Email', 'field_type': 'email', 'section': 'contact', 'status': 'optional', 'order': 1},
            {'field_name': 'phone_number', 'display_name': 'Phone Number', 'field_type': 'phone', 'section': 'contact', 'status': 'optional', 'order': 2},
            {'field_name': 'address', 'display_name': 'Address', 'field_type': 'textarea', 'section': 'contact', 'status': 'optional', 'order': 3},
            {'field_name': 'city', 'display_name': 'City', 'field_type': 'text', 'section': 'contact', 'status': 'optional', 'order': 4},
            {'field_name': 'state', 'display_name': 'State/Province', 'field_type': 'text', 'section': 'contact', 'status': 'optional', 'order': 5},
            {'field_name': 'postal_code', 'display_name': 'Postal Code', 'field_type': 'text', 'section': 'contact', 'status': 'optional', 'order': 6},
            {'field_name': 'country', 'display_name': 'Country', 'field_type': 'text', 'section': 'contact', 'status': 'optional', 'order': 7, 'default_value': 'Ethiopia'},

            # Academic Information
            {'field_name': 'enrollment_date', 'display_name': 'Enrollment Date', 'field_type': 'date', 'section': 'academic', 'status': 'required', 'order': 1},
            {'field_name': 'current_grade_level', 'display_name': 'Grade Level', 'field_type': 'select', 'section': 'academic', 'status': 'optional', 'order': 2},
            {'field_name': 'current_section', 'display_name': 'Section', 'field_type': 'select', 'section': 'academic', 'status': 'optional', 'order': 3},
            {'field_name': 'previous_school', 'display_name': 'Previous School', 'field_type': 'text', 'section': 'academic', 'status': 'optional', 'order': 4},
            {'field_name': 'admission_type', 'display_name': 'Admission Type', 'field_type': 'select', 'section': 'academic', 'status': 'optional', 'order': 5,
             'options': [{'value': 'NEW', 'label': 'New Admission'}, {'value': 'TRANSFER', 'label': 'Transfer'}, {'value': 'READMISSION', 'label': 'Re-Admission'}]},

            # Health Information
            {'field_name': 'blood_group', 'display_name': 'Blood Group', 'field_type': 'select', 'section': 'health', 'status': 'optional', 'order': 1,
             'options': [{'value': 'A+', 'label': 'A+'}, {'value': 'A-', 'label': 'A-'}, {'value': 'B+', 'label': 'B+'}, {'value': 'B-', 'label': 'B-'},
                        {'value': 'AB+', 'label': 'AB+'}, {'value': 'AB-', 'label': 'AB-'}, {'value': 'O+', 'label': 'O+'}, {'value': 'O-', 'label': 'O-'}]},
            {'field_name': 'medical_conditions', 'display_name': 'Medical Conditions', 'field_type': 'textarea', 'section': 'health', 'status': 'optional', 'order': 2},
            {'field_name': 'allergies', 'display_name': 'Allergies', 'field_type': 'textarea', 'section': 'health', 'status': 'optional', 'order': 3},
            {'field_name': 'medications', 'display_name': 'Medications', 'field_type': 'textarea', 'section': 'health', 'status': 'optional', 'order': 4},

            # Emergency Contact
            {'field_name': 'emergency_contact_name', 'display_name': 'Emergency Contact Name', 'field_type': 'text', 'section': 'emergency', 'status': 'optional', 'order': 1},
            {'field_name': 'emergency_contact_phone', 'display_name': 'Emergency Contact Phone', 'field_type': 'phone', 'section': 'emergency', 'status': 'optional', 'order': 2},
            {'field_name': 'emergency_contact_relationship', 'display_name': 'Relationship', 'field_type': 'text', 'section': 'emergency', 'status': 'optional', 'order': 3},

            # Guardian Information
            {'field_name': 'guardian_first_name', 'display_name': 'First Name', 'field_type': 'text', 'section': 'guardian', 'status': 'required', 'order': 1},
            {'field_name': 'guardian_last_name', 'display_name': 'Last Name', 'field_type': 'text', 'section': 'guardian', 'status': 'required', 'order': 2},
            {'field_name': 'guardian_relationship', 'display_name': 'Relationship', 'field_type': 'text', 'section': 'guardian', 'status': 'required', 'order': 3},
            {'field_name': 'guardian_phone', 'display_name': 'Phone Number', 'field_type': 'phone', 'section': 'guardian', 'status': 'required', 'order': 4},
            {'field_name': 'guardian_email', 'display_name': 'Email', 'field_type': 'email', 'section': 'guardian', 'status': 'optional', 'order': 5},
            {'field_name': 'guardian_address', 'display_name': 'Address', 'field_type': 'textarea', 'section': 'guardian', 'status': 'optional', 'order': 6},

            # Second Guardian (Optional)
            {'field_name': 'guardian2_first_name', 'display_name': 'First Name', 'field_type': 'text', 'section': 'guardian', 'status': 'optional', 'order': 7},
            {'field_name': 'guardian2_last_name', 'display_name': 'Last Name', 'field_type': 'text', 'section': 'guardian', 'status': 'optional', 'order': 8},
            {'field_name': 'guardian2_relationship', 'display_name': 'Relationship', 'field_type': 'text', 'section': 'guardian', 'status': 'optional', 'order': 9},
            {'field_name': 'guardian2_phone', 'display_name': 'Phone Number', 'field_type': 'phone', 'section': 'guardian', 'status': 'optional', 'order': 10},
            {'field_name': 'guardian2_email', 'display_name': 'Email', 'field_type': 'email', 'section': 'guardian', 'status': 'optional', 'order': 11},

            # Other Information
            {'field_name': 'photo', 'display_name': 'Photo', 'field_type': 'file', 'section': 'other', 'status': 'optional', 'order': 1},
            {'field_name': 'notes', 'display_name': 'Notes', 'field_type': 'textarea', 'section': 'other', 'status': 'optional', 'order': 2},
        ]

        return Response(default_configs)

    @action(detail=False, methods=['post'])
    def initialize_tenant(self, request):
        """Initialize form configuration for a new tenant"""
        tenant_id = request.data.get('tenant_id')
        if not tenant_id:
            return Response({'error': 'tenant_id is required'}, status=status.HTTP_400_BAD_REQUEST)

        # Check if tenant exists
        try:
            from tenant_management.models import School
            tenant = School.objects.get(id=tenant_id)
        except (ImportError, School.DoesNotExist):
            return Response({'error': f'Tenant with ID {tenant_id} does not exist'}, status=status.HTTP_404_NOT_FOUND)

        # Check if tenant already has form configurations
        existing_configs = StudentFormConfig.objects.filter(tenant_id=tenant_id).count()
        if existing_configs > 0:
            # Return existing configs instead of creating new ones
            print(f"Tenant {tenant_id} already has {existing_configs} form configurations")
            existing_data = StudentFormConfigSerializer(StudentFormConfig.objects.filter(tenant_id=tenant_id), many=True).data
            return Response(existing_data, status=status.HTTP_200_OK)

        # Get default configurations
        default_configs = self.default_config(request).data
        print(f"Initializing tenant {tenant_id} with {len(default_configs)} default configurations")

        # Create configurations for the tenant
        created_configs = []
        errors = []

        try:
            for config in default_configs:
                config['tenant'] = tenant_id
                serializer = self.get_serializer(data=config)
                if serializer.is_valid():
                    try:
                        serializer.save()
                        created_configs.append(serializer.data)
                    except Exception as e:
                        errors.append({
                            'field_name': config.get('field_name'),
                            'error': str(e)
                        })
                else:
                    errors.append({
                        'field_name': config.get('field_name'),
                        'error': serializer.errors
                    })

            # If we have any errors but also created some configs, we'll return what we created
            if errors and created_configs:
                print(f"Created {len(created_configs)} configs with {len(errors)} errors")
                return Response({
                    'created': created_configs,
                    'errors': errors,
                    'message': 'Some configurations were created with errors'
                }, status=status.HTTP_207_MULTI_STATUS)
            elif errors and not created_configs:
                # If all configs failed, rollback and return error
                StudentFormConfig.objects.filter(tenant_id=tenant_id).delete()
                return Response({
                    'errors': errors,
                    'message': 'Failed to create any configurations'
                }, status=status.HTTP_400_BAD_REQUEST)
            else:
                # All configs created successfully
                return Response(created_configs, status=status.HTTP_201_CREATED)
        except Exception as e:
            # If any unexpected error occurs, rollback and return error
            StudentFormConfig.objects.filter(tenant_id=tenant_id).delete()
            return Response({
                'error': str(e),
                'message': 'An unexpected error occurred while initializing tenant form configurations'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class StudentAchievementViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing student achievements.
    """
    queryset = StudentAchievement.objects.all()
    serializer_class = StudentAchievementSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['student', 'achievement_type', 'is_verified']
    search_fields = ['title', 'description', 'issuing_organization']

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated]
        elif self.action in ['create']:
            permission_classes = [IsAdminOrTeacher]
        elif self.action in ['update', 'partial_update', 'destroy']:
            permission_classes = [IsAdmin]
        else:
            permission_classes = [IsAdmin]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        queryset = super().get_queryset()

        # If user is a parent, only show achievements for their children
        if self.request.user.user_type == 'parent':
            # Get all students where this user is a guardian
            student_ids = Guardian.objects.filter(user=self.request.user).values_list('student_id', flat=True)
            queryset = queryset.filter(student_id__in=student_ids)

        # If user is a student, only show their own achievements
        elif self.request.user.user_type == 'student':
            try:
                student = self.request.user.student_profile
                queryset = queryset.filter(student=student)
            except:
                queryset = StudentAchievement.objects.none()

        return queryset

    @action(detail=True, methods=['post'])
    def verify(self, request, pk=None):
        """
        Verify a student achievement.
        """
        achievement = self.get_object()
        achievement.is_verified = True
        achievement.verified_by = request.user
        achievement.save()

        serializer = self.get_serializer(achievement)
        return Response(serializer.data)

class StudentFeeViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing student fees.
    """
    queryset = StudentFee.objects.all()
    serializer_class = StudentFeeSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['student', 'academic_year', 'term', 'fee_type', 'payment_status']
    search_fields = ['notes']

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated]
        elif self.action in ['create', 'update', 'partial_update', 'destroy']:
            permission_classes = [IsAdmin]
        else:
            permission_classes = [IsAdmin]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        queryset = super().get_queryset()

        # If user is a parent, only show fees for their children
        if self.request.user.user_type == 'parent':
            # Get all students where this user is a guardian
            student_ids = Guardian.objects.filter(user=self.request.user).values_list('student_id', flat=True)
            queryset = queryset.filter(student_id__in=student_ids)

        # If user is a student, only show their own fees
        elif self.request.user.user_type == 'student':
            try:
                student = self.request.user.student_profile
                queryset = queryset.filter(student=student)
            except:
                queryset = StudentFee.objects.none()

        return queryset

    @action(detail=True, methods=['post'])
    def record_payment(self, request, pk=None):
        """
        Record a payment for a student fee.
        """
        fee = self.get_object()

        # Validate payment data
        payment_serializer = FeePaymentSerializer(data=request.data, context={'request': request})
        if payment_serializer.is_valid():
            # Create the payment
            payment = payment_serializer.save(
                student_fee=fee,
                received_by=request.user
            )

            # Update the fee
            fee.amount_paid = fee.payments.aggregate(total=Sum('amount'))['total'] or 0
            fee.balance = fee.amount - fee.amount_paid
            fee.last_payment_date = payment.payment_date

            # Update payment status
            if fee.balance <= 0:
                fee.payment_status = 'PAID'
            elif fee.amount_paid > 0:
                fee.payment_status = 'PARTIAL'

            fee.save()

            # Return updated fee and payment
            return Response({
                'fee': StudentFeeSerializer(fee).data,
                'payment': payment_serializer.data
            })
        else:
            return Response(payment_serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class FeePaymentViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing fee payments.
    """
    queryset = FeePayment.objects.all()
    serializer_class = FeePaymentSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['student_fee', 'payment_method']
    search_fields = ['transaction_id', 'receipt_number', 'notes']

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated]
        elif self.action in ['create', 'update', 'partial_update', 'destroy']:
            permission_classes = [IsAdmin]
        else:
            permission_classes = [IsAdmin]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        queryset = super().get_queryset()

        # If user is a parent, only show payments for their children's fees
        if self.request.user.user_type == 'parent':
            # Get all students where this user is a guardian
            student_ids = Guardian.objects.filter(user=self.request.user).values_list('student_id', flat=True)
            queryset = queryset.filter(student_fee__student_id__in=student_ids)

        # If user is a student, only show their own payments
        elif self.request.user.user_type == 'student':
            try:
                student = self.request.user.student_profile
                queryset = queryset.filter(student_fee__student=student)
            except:
                queryset = FeePayment.objects.none()

        return queryset

class StudentRegistrationView(generics.CreateAPIView):
    """
    API endpoint for registering new students.
    This endpoint allows school administrators and teachers to register new students
    along with their guardian information in a single request.
    """
    serializer_class = StudentRegistrationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_permissions(self):
        # Print debug information
        print(f"StudentRegistrationView.get_permissions() called")
        print(f"User: {self.request.user}, authenticated: {self.request.user.is_authenticated}")
        print(f"Tenant: {getattr(self.request, 'tenant', None)}")
        print(f"Headers: {self.request.headers}")
        print(f"URL: {self.request.path}")
        print(f"Method: {self.request.method}")

        # For now, allow any authenticated user to register students
        return [permissions.IsAuthenticated()]

    def create(self, request, *args, **kwargs):
        # Print debug information
        print(f"StudentRegistrationView.create() called")
        print(f"User: {request.user}, authenticated: {request.user.is_authenticated}")
        print(f"Tenant: {getattr(request, 'tenant', None)}")
        print(f"Headers: {request.headers}")
        print(f"Content-Type: {request.content_type}")

        # Don't print the entire data object as it might contain binary file data
        if hasattr(request.data, 'keys'):
            print(f"Data keys: {list(request.data.keys())}")
        else:
            print(f"Data type: {type(request.data)}")

        # Check if we're in the public schema
        from django.db import connection
        print(f"Schema: {connection.schema_name}")

        if connection.schema_name == 'public':
            # Return a helpful error message
            print(f"In public schema, returning error message")
            return Response({
                'success': False,
                'message': 'Student registration is only available in tenant schemas. Please select a school/tenant first.'
            }, status=400)

        # Check if the necessary tables exist
        try:
            # Check if the academics_gradelevel table exists
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = current_schema()
                        AND table_name = 'academics_gradelevel'
                    )
                """)
                academics_table_exists = cursor.fetchone()[0]

            # Check if the courses_academicterm table exists
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = current_schema()
                        AND table_name = 'courses_academicterm'
                    )
                """)
                academicterm_table_exists = cursor.fetchone()[0]

            if not academics_table_exists or not academicterm_table_exists:
                print(f"Required tables do not exist in schema {connection.schema_name}")
                print(f"academics_gradelevel exists: {academics_table_exists}")
                print(f"courses_academicterm exists: {academicterm_table_exists}")
                return Response({
                    'success': False,
                    'message': 'The tenant schema is not fully set up. Please run the fix_circular_dependencies.py script to set up the tenant schema.'
                }, status=400)
        except Exception as e:
            print(f"Error checking required tables: {str(e)}")
            # Return a helpful error message
            return Response({
                'success': False,
                'message': f'Error checking required tables: {str(e)}. Please run the fix_circular_dependencies.py script to set up the tenant schema.'
            }, status=400)

        # Check if grade level and section exist
        data = request.data.copy()

        # Handle grade level
        if 'current_grade_level' in data and data['current_grade_level']:
            try:
                # Try to get the grade level
                from academics.models import GradeLevel
                grade_level_id = int(data['current_grade_level'])
                try:
                    grade_level = GradeLevel.objects.get(id=grade_level_id)
                    print(f"Found grade level: {grade_level.name}")
                except GradeLevel.DoesNotExist:
                    print(f"Grade level with ID {grade_level_id} does not exist. Creating a default grade level.")
                    # Create a default grade level
                    from academics.models import Program
                    # Get or create a default program
                    program, created = Program.objects.get_or_create(
                        name="Default Program",
                        defaults={
                            'code': 'DEFAULT',
                            'level': 'PRIMARY',
                            'description': 'Default program created automatically',
                            'duration_years': 6,
                            'is_active': True,
                            'created_at': timezone.now(),
                            'updated_at': timezone.now(),
                        }
                    )
                    # Create a default grade level
                    grade_level = GradeLevel.objects.create(
                        name=f"Grade {grade_level_id}",
                        code=f"G{grade_level_id}",
                        sequence=grade_level_id,
                        program=program
                    )
                    print(f"Created default grade level: {grade_level.name}")
                    data['current_grade_level'] = grade_level.id
            except Exception as e:
                print(f"Error handling grade level: {str(e)}")
                # Remove the grade level from the data
                data.pop('current_grade_level', None)

        # Handle section
        if 'current_section' in data and data['current_section']:
            try:
                # Try to get the section
                from academics.models import Section, AcademicYear
                section_id = int(data['current_section'])
                try:
                    section = Section.objects.get(id=section_id)
                    print(f"Found section: {section.name}")
                except Section.DoesNotExist:
                    print(f"Section with ID {section_id} does not exist. Creating a default section.")
                    # Create a default section
                    # Get or create a default academic year
                    academic_year, created = AcademicYear.objects.get_or_create(
                        name="2025-2026",
                        defaults={
                            'start_date': timezone.now().date(),
                            'end_date': (timezone.now() + timezone.timedelta(days=365)).date(),
                            'is_current': True
                        }
                    )
                    # Get the grade level
                    grade_level = None
                    if 'current_grade_level' in data and data['current_grade_level']:
                        try:
                            from academics.models import GradeLevel
                            grade_level = GradeLevel.objects.get(id=int(data['current_grade_level']))
                        except Exception:
                            pass

                    if not grade_level:
                        # Get or create a default program
                        from academics.models import Program
                        program, created = Program.objects.get_or_create(
                            name="Default Program",
                            defaults={
                                'code': 'DEFAULT',
                                'level': 'PRIMARY',
                                'description': 'Default program created automatically',
                                'duration_years': 6,
                                'is_active': True,
                                'created_at': timezone.now(),
                                'updated_at': timezone.now(),
                            }
                        )
                        # Create a default grade level
                        grade_level = GradeLevel.objects.create(
                            name=f"Grade 1",
                            code="G1",
                            sequence=1,
                            program=program
                        )
                        print(f"Created default grade level: {grade_level.name}")
                        data['current_grade_level'] = grade_level.id

                    # Create a default section
                    section = Section.objects.create(
                        name=f"Section {section_id}",
                        max_students=30,
                        is_active=True,
                        academic_year=academic_year,
                        grade_level=grade_level
                    )
                    print(f"Created default section: {section.name}")
                    data['current_section'] = section.id
            except Exception as e:
                print(f"Error handling section: {str(e)}")
                # Remove the section from the data
                data.pop('current_section', None)

        # Validate the request data
        serializer = self.get_serializer(data=data)
        if not serializer.is_valid():
            print(f"Validation errors: {serializer.errors}")
            # Print the serializer fields for debugging
            print(f"Serializer fields: {serializer.fields}")
            return Response({
                'success': False,
                'message': 'Invalid student data',
                'errors': serializer.errors
            }, status=400)

        # Try to continue with the normal create process
        try:
            print(f"Creating student in schema: {connection.schema_name}")
            student = serializer.save()
            print(f"Student created: {student}")

            # Return the full student details
            detail_serializer = StudentDetailSerializer(student, context={'request': request})
            headers = self.get_success_headers(serializer.data)

            return Response({
                'success': True,
                'message': 'Student registered successfully',
                'student': detail_serializer.data
            }, status=status.HTTP_201_CREATED, headers=headers)
        except Exception as e:
            # Check if the error is related to missing tables
            if 'relation' in str(e) and 'does not exist' in str(e):
                print(f"Error: {str(e)}")
                return Response({
                    'success': False,
                    'message': 'Student registration is only available in tenant schemas. Please select a school/tenant first.',
                    'error': str(e)
                }, status=400)
            # Print the full error details
            import traceback
            print(f"Error creating student: {str(e)}")
            print(traceback.format_exc())
            # Return a helpful error message
            return Response({
                'success': False,
                'message': 'An error occurred while registering the student',
                'error': str(e)
            }, status=500)

    def perform_create(self, serializer):
        # Create the student
        student = serializer.save()

        # Process document uploads
        try:
            request = self.request
            print("Processing document uploads for student:", student.full_name())
            print("Available request data keys:", list(request.data.keys()))

            # Check for document uploads in the request
            for key in request.data.keys():
                # Look for document_X fields (document_0, document_1, etc.) or specific document types
                if key.startswith('document_') and isinstance(request.data[key], UploadedFile):
                    # Extract the document identifier (index or type)
                    doc_identifier = key.split('_')[1]
                    doc_file = request.data[key]

                    # Handle different document naming patterns
                    if doc_identifier.isdigit():
                        # Numbered documents (document_0, document_1, etc.)
                        doc_type = request.data.get(f'document_{doc_identifier}_type', 'OTHER')
                        doc_title = request.data.get(f'document_{doc_identifier}_title', f'Document {doc_identifier}')
                    else:
                        # Named documents (document_birth, document_school, etc.)
                        doc_type = request.data.get(f'document_{doc_identifier}_type', 'OTHER')
                        doc_title = request.data.get(f'document_{doc_identifier}_title', f'Document {doc_identifier.capitalize()}')

                    print(f"Processing document: {doc_title} of type {doc_type}")
                    print(f"File info: {doc_file.name}, size: {doc_file.size} bytes")

                    # Create the document
                    document = StudentDocument.objects.create(
                        student=student,
                        document_type=doc_type,
                        title=doc_title,
                        description=f'Uploaded during registration',
                        file=doc_file,
                        uploaded_by=request.user
                    )
                    print(f"Created document: {doc_title} for student {student.full_name()}, ID: {document.id}")

            # Count documents after creation
            doc_count = StudentDocument.objects.filter(student=student).count()
            print(f"Total documents created for student {student.full_name()}: {doc_count}")

        except Exception as e:
            print(f"Error processing document uploads: {str(e)}")
            import traceback
            print(traceback.format_exc())
            # Continue even if document creation fails
            pass

        # Create a TenantActivity record for the new student
        try:
            from tenant_management.models import TenantActivity
            TenantActivity.objects.create(
                tenant=self.request.tenant,
                activity_type='student_registered',
                description=f'New student registered: {student.full_name()}',
                user_count=1
            )
        except Exception as e:
            print(f"Error creating TenantActivity: {str(e)}")
            # Continue even if TenantActivity creation fails
            pass

        return student


class DashboardDataView(APIView):
    """
    API endpoint for dashboard data.
    Provides summary statistics for the dashboard.
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, format=None):
        # Check if we're in the public schema
        from django.db import connection
        if connection.schema_name == 'public':
            # Return default data for the public schema
            print(f"In public schema, returning default dashboard data")
            return Response({
                'totalStudents': 0,
                'activeStudents': 0,
                'totalTeachers': 0,
                'totalCourses': 0,
                'recentStudents': [],
                'message': 'Dashboard data is only available in tenant schemas. Please select a school/tenant first.'
            })

        try:
            # Get counts
            total_students = Student.objects.count()
            active_students = Student.objects.filter(status='ACTIVE').count()

            # Get recent students
            recent_students = Student.objects.all().order_by('-created_at')[:5]

            # Serialize the recent students
            student_serializer = StudentListSerializer(recent_students, many=True, context={'request': request})

            # Return dashboard data
            return Response({
                'totalStudents': total_students,
                'activeStudents': active_students,
                'totalTeachers': 0,  # Placeholder
                'totalCourses': 0,   # Placeholder
                'recentStudents': student_serializer.data
            })
        except Exception as e:
            # Check if the error is related to missing tables
            if 'relation' in str(e) and 'does not exist' in str(e):
                print(f"Error in DashboardDataView: {str(e)}")
                return Response({
                    'totalStudents': 0,
                    'activeStudents': 0,
                    'totalTeachers': 0,
                    'totalCourses': 0,
                    'recentStudents': [],
                    'error': str(e)
                })
            # Re-raise other exceptions
            raise