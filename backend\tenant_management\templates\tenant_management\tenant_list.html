{% extends "base.html" %}

{% block title %}Tenant List{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1>Tenant List</h1>
                <div>
                    <a href="{% url 'tenant_management:dashboard' %}" class="btn btn-secondary me-2">
                        <i class="bi bi-speedometer2"></i> Dashboard
                    </a>
                    <button id="collectAllMetrics" class="btn btn-success">
                        <i class="bi bi-graph-up"></i> Collect All Metrics
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="card-title mb-0">All Tenants</h5>
                        </div>
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" id="tenantSearch" class="form-control" placeholder="Search tenants...">
                                <button class="btn btn-outline-secondary" type="button" id="searchButton">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Schema</th>
                                    <th>Domain</th>
                                    <th>Status</th>
                                    <th>Users</th>
                                    <th>Storage</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for tenant in tenants %}
                                <tr>
                                    <td>{{ tenant.name }}</td>
                                    <td>{{ tenant.schema_name }}</td>
                                    <td>
                                        {% for domain in tenant.domains.all %}
                                            {% if domain.is_primary %}
                                                <a href="http://{{ domain.domain }}" target="_blank">{{ domain.domain }}</a>
                                            {% endif %}
                                        {% endfor %}
                                    </td>
                                    <td>
                                        {% if tenant.status.status == 'active' %}
                                            <span class="badge bg-success">Active</span>
                                        {% elif tenant.status.status == 'suspended' %}
                                            <span class="badge bg-danger">Suspended</span>
                                        {% elif tenant.status.status == 'trial' %}
                                            <span class="badge bg-warning">Trial</span>
                                        {% elif tenant.status.status == 'expired' %}
                                            <span class="badge bg-secondary">Expired</span>
                                        {% else %}
                                            <span class="badge bg-info">Unknown</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if tenant.metrics.first %}
                                            {{ tenant.metrics.first.total_users }}
                                        {% else %}
                                            --
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if tenant.metrics.first %}
                                            {{ tenant.metrics.first.database_size|filesizeformat }}
                                        {% else %}
                                            --
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{% url 'tenant_management:tenant_detail' tenant.id %}" class="btn btn-sm btn-primary">
                                                <i class="bi bi-eye"></i> View
                                            </a>
                                            <button type="button" class="btn btn-sm btn-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                                                <span class="visually-hidden">Toggle Dropdown</span>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item collect-metrics" href="#" data-tenant-id="{{ tenant.id }}">
                                                        <i class="bi bi-graph-up"></i> Collect Metrics
                                                    </a>
                                                </li>
                                                {% if tenant.status.status == 'active' %}
                                                <li>
                                                    <a class="dropdown-item suspend-tenant" href="#" data-tenant-id="{{ tenant.id }}">
                                                        <i class="bi bi-pause-circle"></i> Suspend
                                                    </a>
                                                </li>
                                                {% else %}
                                                <li>
                                                    <a class="dropdown-item activate-tenant" href="#" data-tenant-id="{{ tenant.id }}">
                                                        <i class="bi bi-play-circle"></i> Activate
                                                    </a>
                                                </li>
                                                {% endif %}
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <a class="dropdown-item update-subscription" href="#" data-tenant-id="{{ tenant.id }}">
                                                        <i class="bi bi-calendar-plus"></i> Update Subscription
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center">No tenants found</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Suspend Tenant Modal -->
<div class="modal fade" id="suspendTenantModal" tabindex="-1" aria-labelledby="suspendTenantModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="suspendTenantModalLabel">Suspend Tenant</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to suspend this tenant? Users will not be able to access the system.</p>
                <div class="mb-3">
                    <label for="suspendNotes" class="form-label">Notes (optional)</label>
                    <textarea class="form-control" id="suspendNotes" rows="3"></textarea>
                </div>
                <input type="hidden" id="suspendTenantId">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmSuspend">Suspend Tenant</button>
            </div>
        </div>
    </div>
</div>

<!-- Activate Tenant Modal -->
<div class="modal fade" id="activateTenantModal" tabindex="-1" aria-labelledby="activateTenantModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="activateTenantModalLabel">Activate Tenant</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to activate this tenant?</p>
                <div class="mb-3">
                    <label for="activateNotes" class="form-label">Notes (optional)</label>
                    <textarea class="form-control" id="activateNotes" rows="3"></textarea>
                </div>
                <input type="hidden" id="activateTenantId">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="confirmActivate">Activate Tenant</button>
            </div>
        </div>
    </div>
</div>

<!-- Update Subscription Modal -->
<div class="modal fade" id="updateSubscriptionModal" tabindex="-1" aria-labelledby="updateSubscriptionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="updateSubscriptionModalLabel">Update Subscription</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="subscriptionPlan" class="form-label">Subscription Plan</label>
                    <select class="form-select" id="subscriptionPlan">
                        <option value="free">Free</option>
                        <option value="basic">Basic</option>
                        <option value="standard">Standard</option>
                        <option value="premium">Premium</option>
                        <option value="enterprise">Enterprise</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="subscriptionStartDate" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="subscriptionStartDate">
                </div>
                <div class="mb-3">
                    <label for="subscriptionEndDate" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="subscriptionEndDate">
                </div>
                <div class="mb-3">
                    <label for="maxUsers" class="form-label">Max Users</label>
                    <input type="number" class="form-control" id="maxUsers" min="1" value="100">
                </div>
                <div class="mb-3">
                    <label for="maxStorage" class="form-label">Max Storage (GB)</label>
                    <input type="number" class="form-control" id="maxStorage" min="1" value="1">
                </div>
                <input type="hidden" id="updateSubscriptionTenantId">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmUpdateSubscription">Update Subscription</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Function to get CSRF token from cookies
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
    
    // Function to collect metrics for a tenant
    function collectMetrics(tenantId) {
        fetch(`/tenant-management/api/tenants/${tenantId}/collect_metrics/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);
            window.location.reload();
        })
        .catch(error => {
            console.error('Error collecting metrics:', error);
            alert('Error collecting metrics. Please try again.');
        });
    }
    
    // Function to collect metrics for all tenants
    function collectAllMetrics() {
        const button = document.getElementById('collectAllMetrics');
        button.disabled = true;
        button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Collecting...';
        
        fetch('/tenant-management/api/collect-metrics/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);
            window.location.reload();
        })
        .catch(error => {
            console.error('Error collecting metrics:', error);
            alert('Error collecting metrics. Please try again.');
        })
        .finally(() => {
            button.disabled = false;
            button.innerHTML = '<i class="bi bi-graph-up"></i> Collect All Metrics';
        });
    }
    
    // Function to suspend a tenant
    function suspendTenant(tenantId, notes) {
        fetch(`/tenant-management/api/tenants/${tenantId}/suspend/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({ notes: notes })
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);
            window.location.reload();
        })
        .catch(error => {
            console.error('Error suspending tenant:', error);
            alert('Error suspending tenant. Please try again.');
        });
    }
    
    // Function to activate a tenant
    function activateTenant(tenantId, notes) {
        fetch(`/tenant-management/api/tenants/${tenantId}/activate/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({ notes: notes })
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);
            window.location.reload();
        })
        .catch(error => {
            console.error('Error activating tenant:', error);
            alert('Error activating tenant. Please try again.');
        });
    }
    
    // Function to update subscription
    function updateSubscription(tenantId, subscriptionData) {
        fetch(`/tenant-management/api/tenants/${tenantId}/update_subscription/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify(subscriptionData)
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);
            window.location.reload();
        })
        .catch(error => {
            console.error('Error updating subscription:', error);
            alert('Error updating subscription. Please try again.');
        });
    }
    
    // Event listeners
    document.addEventListener('DOMContentLoaded', function() {
        // Collect metrics for a tenant
        document.querySelectorAll('.collect-metrics').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const tenantId = this.getAttribute('data-tenant-id');
                collectMetrics(tenantId);
            });
        });
        
        // Collect metrics for all tenants
        document.getElementById('collectAllMetrics').addEventListener('click', collectAllMetrics);
        
        // Suspend tenant
        document.querySelectorAll('.suspend-tenant').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const tenantId = this.getAttribute('data-tenant-id');
                document.getElementById('suspendTenantId').value = tenantId;
                
                const modal = new bootstrap.Modal(document.getElementById('suspendTenantModal'));
                modal.show();
            });
        });
        
        // Confirm suspend
        document.getElementById('confirmSuspend').addEventListener('click', function() {
            const tenantId = document.getElementById('suspendTenantId').value;
            const notes = document.getElementById('suspendNotes').value;
            
            suspendTenant(tenantId, notes);
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('suspendTenantModal'));
            modal.hide();
        });
        
        // Activate tenant
        document.querySelectorAll('.activate-tenant').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const tenantId = this.getAttribute('data-tenant-id');
                document.getElementById('activateTenantId').value = tenantId;
                
                const modal = new bootstrap.Modal(document.getElementById('activateTenantModal'));
                modal.show();
            });
        });
        
        // Confirm activate
        document.getElementById('confirmActivate').addEventListener('click', function() {
            const tenantId = document.getElementById('activateTenantId').value;
            const notes = document.getElementById('activateNotes').value;
            
            activateTenant(tenantId, notes);
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('activateTenantModal'));
            modal.hide();
        });
        
        // Update subscription
        document.querySelectorAll('.update-subscription').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const tenantId = this.getAttribute('data-tenant-id');
                document.getElementById('updateSubscriptionTenantId').value = tenantId;
                
                // Set default dates
                const today = new Date().toISOString().split('T')[0];
                document.getElementById('subscriptionStartDate').value = today;
                
                const nextYear = new Date();
                nextYear.setFullYear(nextYear.getFullYear() + 1);
                document.getElementById('subscriptionEndDate').value = nextYear.toISOString().split('T')[0];
                
                const modal = new bootstrap.Modal(document.getElementById('updateSubscriptionModal'));
                modal.show();
            });
        });
        
        // Confirm update subscription
        document.getElementById('confirmUpdateSubscription').addEventListener('click', function() {
            const tenantId = document.getElementById('updateSubscriptionTenantId').value;
            const subscriptionPlan = document.getElementById('subscriptionPlan').value;
            const subscriptionStartDate = document.getElementById('subscriptionStartDate').value;
            const subscriptionEndDate = document.getElementById('subscriptionEndDate').value;
            const maxUsers = document.getElementById('maxUsers').value;
            const maxStorage = document.getElementById('maxStorage').value;
            
            // Convert GB to bytes
            const maxStorageBytes = maxStorage * 1024 * 1024 * 1024;
            
            const subscriptionData = {
                subscription_plan: subscriptionPlan,
                subscription_start_date: subscriptionStartDate,
                subscription_end_date: subscriptionEndDate,
                max_users: maxUsers,
                max_storage: maxStorageBytes
            };
            
            updateSubscription(tenantId, subscriptionData);
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('updateSubscriptionModal'));
            modal.hide();
        });
        
        // Search functionality
        document.getElementById('searchButton').addEventListener('click', function() {
            const searchTerm = document.getElementById('tenantSearch').value.toLowerCase();
            const rows = document.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
        
        // Search on Enter key
        document.getElementById('tenantSearch').addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('searchButton').click();
            }
        });
    });
</script>
{% endblock %}
