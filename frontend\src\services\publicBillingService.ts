import axios from 'axios'
import { SubscriptionPlan, PlanFeature } from './billingService'

const API_BASE_URL = 'http://localhost:8001/billing-management/api'

// Create axios instance for public API calls (no auth required)
const publicBillingApi = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Utility function to transform snake_case API response to camelCase
const transformPlanData = (apiPlan: any): SubscriptionPlan => {
  return {
    id: apiPlan.id,
    name: apiPlan.name,
    description: apiPlan.description,
    price: parseFloat(apiPlan.price),
    billingCycle: apiPlan.billing_cycle,
    features: apiPlan.features || [],
    maxUsers: apiPlan.max_users,
    maxStorage: apiPlan.max_storage,
    isActive: apiPlan.is_active,
    isFeatured: apiPlan.is_featured,
    trialDays: apiPlan.trial_days,
    setupFee: parseFloat(apiPlan.setup_fee),
    currency: apiPlan.currency,
    category: apiPlan.category,
    sortOrder: apiPlan.sort_order,
    createdAt: apiPlan.created_at,
    updatedAt: apiPlan.updated_at,
  }
}

// Public API Functions (no authentication required)
export const getPublicPlans = async (): Promise<SubscriptionPlan[]> => {
  try {
    const response = await publicBillingApi.get('/public/plans/')
    // Handle paginated response from Django REST Framework
    if (response.data && response.data.results) {
      return response.data.results.map(transformPlanData)
    }
    // Fallback for non-paginated response
    const plans = response.data || []
    return Array.isArray(plans) ? plans.map(transformPlanData) : []
  } catch (error) {
    console.error('Error fetching public plans:', error)
    // Return empty array instead of throwing error for public pages
    return []
  }
}

export const getPublicPlan = async (id: string): Promise<SubscriptionPlan | null> => {
  try {
    const response = await publicBillingApi.get(`/plans/${id}/`)
    return transformPlanData(response.data)
  } catch (error) {
    console.error('Error fetching public plan:', error)
    return null
  }
}

// Re-export formatCurrency from the main billing service
export { formatCurrency } from './billingService'
