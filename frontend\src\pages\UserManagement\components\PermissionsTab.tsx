import React, { useState, useEffect } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typo<PERSON>,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  CircularProgress,
  Alert,
  TextField,
  InputAdornment,
  Chip,
  Grid,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material'
import {
  Security as SecurityIcon,
  Search as SearchIcon,
  ExpandMore as ExpandMoreIcon,
  VpnKey as PermissionIcon,
} from '@mui/icons-material'
import { getPermissions, type Permission } from '../../../services/userManagementService'

interface PermissionsTabProps {
  refreshTrigger: number
  onPermissionAction: (message: string, severity?: 'success' | 'error' | 'warning' | 'info') => void
}

const PermissionsTab: React.FC<PermissionsTabProps> = ({
  refreshTrigger,
  onPermissionAction,
}) => {
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [expandedPanel, setExpandedPanel] = useState<string | false>(false)

  const fetchPermissions = async () => {
    setLoading(true)
    try {
      const permissionsData = await getPermissions()
      setPermissions(permissionsData)
    } catch (error) {
      onPermissionAction('Failed to fetch permissions', 'error')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPermissions()
  }, [refreshTrigger])

  const handleAccordionChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedPanel(isExpanded ? panel : false)
  }

  // Filter permissions based on search term
  const filteredPermissions = permissions.filter(permission =>
    permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    permission.codename.toLowerCase().includes(searchTerm.toLowerCase()) ||
    permission.contentTypeName.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Group permissions by content type
  const groupedPermissions = filteredPermissions.reduce((acc, permission) => {
    const contentType = permission.contentTypeName
    if (!acc[contentType]) {
      acc[contentType] = []
    }
    acc[contentType].push(permission)
    return acc
  }, {} as Record<string, Permission[]>)

  // Get permission statistics
  const stats = {
    total: permissions.length,
    contentTypes: Object.keys(groupedPermissions).length,
    filtered: filteredPermissions.length,
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
        <CircularProgress />
      </Box>
    )
  }

  return (
    <Box sx={{ maxWidth: '100%', mx: 'auto' }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
          System Permissions
        </Typography>
        <Typography variant="body2" color="text.secondary">
          View and manage all available system permissions
        </Typography>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={4}>
          <Paper sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main', mb: 1 }}>
              {stats.total}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Total Permissions
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={4}>
          <Paper sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ fontWeight: 700, color: 'success.main', mb: 1 }}>
              {stats.contentTypes}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Content Types
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={4}>
          <Paper sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ fontWeight: 700, color: 'info.main', mb: 1 }}>
              {stats.filtered}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Filtered Results
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <TextField
            fullWidth
            placeholder="Search permissions by name, codename, or content type..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon color="action" />
                </InputAdornment>
              ),
            }}
          />
        </CardContent>
      </Card>

      {/* Permissions List */}
      <Card>
        <CardContent>
          {Object.keys(groupedPermissions).length === 0 ? (
            <Alert severity="info" sx={{ textAlign: 'center' }}>
              <Typography variant="h6" sx={{ mb: 1 }}>
                No permissions found
              </Typography>
              <Typography variant="body2">
                {searchTerm ? 'Try adjusting your search criteria.' : 'No permissions are available in the system.'}
              </Typography>
            </Alert>
          ) : (
            <Box>
              {Object.entries(groupedPermissions).map(([contentType, perms]) => (
                <Accordion
                  key={contentType}
                  expanded={expandedPanel === contentType}
                  onChange={handleAccordionChange(contentType)}
                  sx={{ mb: 1 }}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls={`${contentType}-content`}
                    id={`${contentType}-header`}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                      <PermissionIcon color="primary" />
                      <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        {contentType}
                      </Typography>
                      <Chip
                        label={`${perms.length} permissions`}
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                    </Box>
                  </AccordionSummary>
                  <AccordionDetails>
                    <List dense>
                      {perms.map((permission) => (
                        <ListItem key={permission.id} divider>
                          <ListItemIcon>
                            <SecurityIcon color="action" fontSize="small" />
                          </ListItemIcon>
                          <ListItemText
                            primary={
                              <Typography variant="body1" sx={{ fontWeight: 500 }}>
                                {permission.name}
                              </Typography>
                            }
                            secondary={
                              <Box sx={{ mt: 0.5 }}>
                                <Typography variant="body2" color="text.secondary">
                                  Codename: <code>{permission.codename}</code>
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  ID: {permission.id} • Content Type: {permission.contentType}
                                </Typography>
                              </Box>
                            }
                          />
                        </ListItem>
                      ))}
                    </List>
                  </AccordionDetails>
                </Accordion>
              ))}
            </Box>
          )}
        </CardContent>
      </Card>
    </Box>
  )
}

export default PermissionsTab
