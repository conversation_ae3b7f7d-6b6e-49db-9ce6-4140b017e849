from functools import wraps
from django.http import HttpResponseForbidden
from django.contrib.auth.decorators import login_required

def role_required(allowed_roles):
    """
    Decorator to check if the user has the required role.
    
    Args:
        allowed_roles: List of allowed user types (e.g., ['admin', 'teacher'])
    """
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def _wrapped_view(request, *args, **kwargs):
            if request.user.user_type in allowed_roles or request.user.is_superuser:
                return view_func(request, *args, **kwargs)
            return HttpResponseForbidden("You don't have permission to access this page.")
        return _wrapped_view
    return decorator

def admin_required(view_func):
    """Decorator to check if the user is an administrator."""
    return role_required(['admin'])(view_func)

def teacher_required(view_func):
    """Decorator to check if the user is a teacher."""
    return role_required(['teacher'])(view_func)

def student_required(view_func):
    """Decorator to check if the user is a student."""
    return role_required(['student'])(view_func)

def parent_required(view_func):
    """Decorator to check if the user is a parent."""
    return role_required(['parent'])(view_func)

def staff_required(view_func):
    """Decorator to check if the user is a staff member."""
    return role_required(['staff'])(view_func)

def admin_or_teacher_required(view_func):
    """Decorator to check if the user is an administrator or teacher."""
    return role_required(['admin', 'teacher'])(view_func)
