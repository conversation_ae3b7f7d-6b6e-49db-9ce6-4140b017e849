from django.contrib.admin import AdminSite
from django.utils.translation import gettext_lazy as _

class TenantManagementAdminSite(AdminSite):
    """Custom admin site for tenant management."""
    
    site_title = _('Kelem SMS Admin')
    site_header = _('Kelem Student Management System')
    index_title = _('Tenant Management')
    
    def has_permission(self, request):
        """Check if the user has permission to access the admin site."""
        return request.user.is_active and request.user.is_superuser

tenant_admin_site = TenantManagementAdminSite(name='tenant_admin')
