from rest_framework import serializers
from .models import SchoolRegistrationRequest

class SchoolRegistrationSerializer(serializers.ModelSerializer):
    """Serializer for school registration requests."""

    class Meta:
        model = SchoolRegistrationRequest
        fields = [
            'id', 'name', 'slug', 'address', 'city', 'state', 'country', 'postal_code',
            'contact_person', 'contact_email', 'contact_phone', 'website',
            'established_year', 'school_type', 'student_capacity', 'description',
            'admin_email', 'admin_password',
            'status', 'requested_on'
        ]
        read_only_fields = ['id', 'slug', 'status', 'requested_on']

    def validate_name(self, value):
        """Validate that the school name is unique."""
        if SchoolRegistrationRequest.objects.filter(name__iexact=value).exists():
            raise serializers.ValidationError("A school with this name already exists.")
        return value

    def validate_established_year(self, value):
        """Validate that the established year is reasonable."""
        import datetime
        current_year = datetime.datetime.now().year
        if value < 1800 or value > current_year:
            raise serializers.ValidationError(f"Established year must be between 1800 and {current_year}.")
        return value

class SchoolRegistrationAdminSerializer(serializers.ModelSerializer):
    """Serializer for admin management of school registration requests."""

    class Meta:
        model = SchoolRegistrationRequest
        fields = [
            'id', 'name', 'slug', 'address', 'city', 'state', 'country', 'postal_code',
            'contact_person', 'contact_email', 'contact_phone', 'website',
            'established_year', 'school_type', 'student_capacity', 'description',
            'admin_email', 'admin_password',
            'status', 'requested_on', 'processed_on', 'rejection_reason',
            'schema_name', 'domain'
        ]
        read_only_fields = ['id', 'name', 'slug', 'address', 'city', 'state', 'country', 'postal_code',
                           'contact_person', 'contact_email', 'contact_phone', 'website',
                           'established_year', 'school_type', 'student_capacity', 'description',
                           'requested_on']
