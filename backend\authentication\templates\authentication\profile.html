{% extends "base.html" %}

{% block title %}User Profile{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3>User Profile</h3>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Name:</strong>
                        </div>
                        <div class="col-md-8">
                            {{ user.get_full_name }}
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Email:</strong>
                        </div>
                        <div class="col-md-8">
                            {{ user.email }}
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>User Type:</strong>
                        </div>
                        <div class="col-md-8">
                            {{ user.get_user_type_display }}
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Phone Number:</strong>
                        </div>
                        <div class="col-md-8">
                            {{ user.phone_number|default:"Not provided" }}
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Date Joined:</strong>
                        </div>
                        <div class="col-md-8">
                            {{ user.date_joined|date:"F j, Y" }}
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{% url 'authentication:profile_edit' %}" class="btn btn-primary">Edit Profile</a>
                    <a href="{% url 'authentication:password_change' %}" class="btn btn-secondary">Change Password</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
