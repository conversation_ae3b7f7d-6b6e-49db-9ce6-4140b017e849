# Generated by Django 5.1.7 on 2025-04-10 15:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('school_registration', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='schoolregistrationrequest',
            name='admin_email',
            field=models.EmailField(blank=True, help_text='Email for the school admin account', max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='schoolregistrationrequest',
            name='admin_password',
            field=models.Char<PERSON>ield(blank=True, help_text='Password for the school admin account', max_length=100, null=True),
        ),
    ]
