{% extends "base.html" %}

{% block title %}Tenant Details - {{ tenant.name }}{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1>{{ tenant.name }}</h1>
                <div>
                    <a href="{% url 'tenant_management:tenant_list' %}" class="btn btn-secondary me-2">
                        <i class="bi bi-arrow-left"></i> Back to List
                    </a>
                    <button id="collectMetrics" class="btn btn-success" data-tenant-id="{{ tenant.id }}">
                        <i class="bi bi-graph-up"></i> Collect Metrics
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Tenant Information</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th>Name:</th>
                            <td>{{ tenant.name }}</td>
                        </tr>
                        <tr>
                            <th>Schema:</th>
                            <td>{{ tenant.schema_name }}</td>
                        </tr>
                        <tr>
                            <th>Domain:</th>
                            <td>
                                {% for domain in tenant.domains.all %}
                                    {% if domain.is_primary %}
                                        <a href="http://{{ domain.domain }}" target="_blank">{{ domain.domain }}</a>
                                    {% endif %}
                                {% endfor %}
                            </td>
                        </tr>
                        <tr>
                            <th>Address:</th>
                            <td>{{ tenant.address }}</td>
                        </tr>
                        <tr>
                            <th>Contact Email:</th>
                            <td>{{ tenant.contact_email }}</td>
                        </tr>
                        <tr>
                            <th>Contact Phone:</th>
                            <td>{{ tenant.contact_phone }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Status</h5>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="statusDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            Actions
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="statusDropdown">
                            {% if status.status == 'active' %}
                            <li>
                                <a class="dropdown-item suspend-tenant" href="#" data-tenant-id="{{ tenant.id }}">
                                    <i class="bi bi-pause-circle"></i> Suspend
                                </a>
                            </li>
                            {% else %}
                            <li>
                                <a class="dropdown-item activate-tenant" href="#" data-tenant-id="{{ tenant.id }}">
                                    <i class="bi bi-play-circle"></i> Activate
                                </a>
                            </li>
                            {% endif %}
                            <li>
                                <a class="dropdown-item update-subscription" href="#" data-tenant-id="{{ tenant.id }}">
                                    <i class="bi bi-calendar-plus"></i> Update Subscription
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6>Current Status</h6>
                        {% if status.status == 'active' %}
                            <span class="badge bg-success">Active</span>
                        {% elif status.status == 'suspended' %}
                            <span class="badge bg-danger">Suspended</span>
                        {% elif status.status == 'trial' %}
                            <span class="badge bg-warning">Trial</span>
                        {% elif status.status == 'expired' %}
                            <span class="badge bg-secondary">Expired</span>
                        {% else %}
                            <span class="badge bg-info">Unknown</span>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <h6>Subscription</h6>
                        <p>
                            <strong>Plan:</strong> {{ status.subscription_plan|title }}<br>
                            <strong>Start Date:</strong> {{ status.subscription_start_date|default:"Not set" }}<br>
                            <strong>End Date:</strong> {{ status.subscription_end_date|default:"Not set" }}<br>
                            <strong>Max Users:</strong> {{ status.max_users }}<br>
                            <strong>Max Storage:</strong> {{ status.max_storage|filesizeformat }}
                        </p>
                    </div>
                    
                    {% if status.notes %}
                    <div class="mb-3">
                        <h6>Notes</h6>
                        <p>{{ status.notes }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Usage Metrics</h5>
                </div>
                <div class="card-body">
                    {% if metrics %}
                    <div class="mb-3">
                        <h6>Users</h6>
                        <p>
                            <strong>Total Users:</strong> {{ metrics.total_users }}<br>
                            <strong>Active Users:</strong> {{ metrics.active_users }}<br>
                            <strong>Admin Users:</strong> {{ metrics.admin_users }}<br>
                            <strong>Teacher Users:</strong> {{ metrics.teacher_users }}<br>
                            <strong>Student Users:</strong> {{ metrics.student_users }}<br>
                            <strong>Parent Users:</strong> {{ metrics.parent_users }}<br>
                            <strong>Staff Users:</strong> {{ metrics.staff_users }}
                        </p>
                    </div>
                    
                    <div class="mb-3">
                        <h6>Data</h6>
                        <p>
                            <strong>Total Students:</strong> {{ metrics.total_students }}<br>
                            <strong>Total Courses:</strong> {{ metrics.total_courses }}<br>
                            <strong>Total Departments:</strong> {{ metrics.total_departments }}<br>
                            <strong>Total Enrollments:</strong> {{ metrics.total_enrollments }}
                        </p>
                    </div>
                    
                    <div class="mb-3">
                        <h6>Storage</h6>
                        <p>
                            <strong>Database Size:</strong> {{ metrics.database_size|filesizeformat }}<br>
                            <strong>File Storage Size:</strong> {{ metrics.file_storage_size|filesizeformat }}<br>
                            <strong>Total Storage:</strong> {{ metrics.database_size|add:metrics.file_storage_size|filesizeformat }}
                        </p>
                    </div>
                    
                    <div class="text-muted small">
                        Last updated: {{ metrics.timestamp }}
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        No metrics available. Click "Collect Metrics" to gather data.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Recent Activities</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Activity Type</th>
                                    <th>Description</th>
                                    <th>User Count</th>
                                    <th>Timestamp</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for activity in activities %}
                                <tr>
                                    <td>{{ activity.activity_type }}</td>
                                    <td>{{ activity.description }}</td>
                                    <td>{{ activity.user_count }}</td>
                                    <td>{{ activity.timestamp }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center">No activities found</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Suspend Tenant Modal -->
<div class="modal fade" id="suspendTenantModal" tabindex="-1" aria-labelledby="suspendTenantModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="suspendTenantModalLabel">Suspend Tenant</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to suspend <strong>{{ tenant.name }}</strong>? Users will not be able to access the system.</p>
                <div class="mb-3">
                    <label for="suspendNotes" class="form-label">Notes (optional)</label>
                    <textarea class="form-control" id="suspendNotes" rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmSuspend">Suspend Tenant</button>
            </div>
        </div>
    </div>
</div>

<!-- Activate Tenant Modal -->
<div class="modal fade" id="activateTenantModal" tabindex="-1" aria-labelledby="activateTenantModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="activateTenantModalLabel">Activate Tenant</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to activate <strong>{{ tenant.name }}</strong>?</p>
                <div class="mb-3">
                    <label for="activateNotes" class="form-label">Notes (optional)</label>
                    <textarea class="form-control" id="activateNotes" rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="confirmActivate">Activate Tenant</button>
            </div>
        </div>
    </div>
</div>

<!-- Update Subscription Modal -->
<div class="modal fade" id="updateSubscriptionModal" tabindex="-1" aria-labelledby="updateSubscriptionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="updateSubscriptionModalLabel">Update Subscription for {{ tenant.name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="subscriptionPlan" class="form-label">Subscription Plan</label>
                    <select class="form-select" id="subscriptionPlan">
                        <option value="free" {% if status.subscription_plan == 'free' %}selected{% endif %}>Free</option>
                        <option value="basic" {% if status.subscription_plan == 'basic' %}selected{% endif %}>Basic</option>
                        <option value="standard" {% if status.subscription_plan == 'standard' %}selected{% endif %}>Standard</option>
                        <option value="premium" {% if status.subscription_plan == 'premium' %}selected{% endif %}>Premium</option>
                        <option value="enterprise" {% if status.subscription_plan == 'enterprise' %}selected{% endif %}>Enterprise</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="subscriptionStartDate" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="subscriptionStartDate" value="{{ status.subscription_start_date|date:'Y-m-d' }}">
                </div>
                <div class="mb-3">
                    <label for="subscriptionEndDate" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="subscriptionEndDate" value="{{ status.subscription_end_date|date:'Y-m-d' }}">
                </div>
                <div class="mb-3">
                    <label for="maxUsers" class="form-label">Max Users</label>
                    <input type="number" class="form-control" id="maxUsers" min="1" value="{{ status.max_users }}">
                </div>
                <div class="mb-3">
                    <label for="maxStorage" class="form-label">Max Storage (GB)</label>
                    <input type="number" class="form-control" id="maxStorage" min="1" value="{{ status.max_storage|filesizeformat|cut:' bytes'|cut:' KB'|cut:' MB'|cut:' GB'|cut:' TB'|floatformat:0 }}">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmUpdateSubscription">Update Subscription</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Function to get CSRF token from cookies
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
    
    // Function to collect metrics for a tenant
    function collectMetrics(tenantId) {
        const button = document.getElementById('collectMetrics');
        button.disabled = true;
        button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Collecting...';
        
        fetch(`/tenant-management/api/tenants/${tenantId}/collect_metrics/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);
            window.location.reload();
        })
        .catch(error => {
            console.error('Error collecting metrics:', error);
            alert('Error collecting metrics. Please try again.');
        })
        .finally(() => {
            button.disabled = false;
            button.innerHTML = '<i class="bi bi-graph-up"></i> Collect Metrics';
        });
    }
    
    // Function to suspend a tenant
    function suspendTenant(tenantId, notes) {
        fetch(`/tenant-management/api/tenants/${tenantId}/suspend/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({ notes: notes })
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);
            window.location.reload();
        })
        .catch(error => {
            console.error('Error suspending tenant:', error);
            alert('Error suspending tenant. Please try again.');
        });
    }
    
    // Function to activate a tenant
    function activateTenant(tenantId, notes) {
        fetch(`/tenant-management/api/tenants/${tenantId}/activate/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({ notes: notes })
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);
            window.location.reload();
        })
        .catch(error => {
            console.error('Error activating tenant:', error);
            alert('Error activating tenant. Please try again.');
        });
    }
    
    // Function to update subscription
    function updateSubscription(tenantId, subscriptionData) {
        fetch(`/tenant-management/api/tenants/${tenantId}/update_subscription/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify(subscriptionData)
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);
            window.location.reload();
        })
        .catch(error => {
            console.error('Error updating subscription:', error);
            alert('Error updating subscription. Please try again.');
        });
    }
    
    // Event listeners
    document.addEventListener('DOMContentLoaded', function() {
        // Collect metrics
        document.getElementById('collectMetrics').addEventListener('click', function() {
            const tenantId = this.getAttribute('data-tenant-id');
            collectMetrics(tenantId);
        });
        
        // Suspend tenant
        document.querySelectorAll('.suspend-tenant').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const modal = new bootstrap.Modal(document.getElementById('suspendTenantModal'));
                modal.show();
            });
        });
        
        // Confirm suspend
        document.getElementById('confirmSuspend').addEventListener('click', function() {
            const tenantId = {{ tenant.id }};
            const notes = document.getElementById('suspendNotes').value;
            
            suspendTenant(tenantId, notes);
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('suspendTenantModal'));
            modal.hide();
        });
        
        // Activate tenant
        document.querySelectorAll('.activate-tenant').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const modal = new bootstrap.Modal(document.getElementById('activateTenantModal'));
                modal.show();
            });
        });
        
        // Confirm activate
        document.getElementById('confirmActivate').addEventListener('click', function() {
            const tenantId = {{ tenant.id }};
            const notes = document.getElementById('activateNotes').value;
            
            activateTenant(tenantId, notes);
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('activateTenantModal'));
            modal.hide();
        });
        
        // Update subscription
        document.querySelectorAll('.update-subscription').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const modal = new bootstrap.Modal(document.getElementById('updateSubscriptionModal'));
                modal.show();
            });
        });
        
        // Confirm update subscription
        document.getElementById('confirmUpdateSubscription').addEventListener('click', function() {
            const tenantId = {{ tenant.id }};
            const subscriptionPlan = document.getElementById('subscriptionPlan').value;
            const subscriptionStartDate = document.getElementById('subscriptionStartDate').value;
            const subscriptionEndDate = document.getElementById('subscriptionEndDate').value;
            const maxUsers = document.getElementById('maxUsers').value;
            const maxStorage = document.getElementById('maxStorage').value;
            
            // Convert GB to bytes
            const maxStorageBytes = maxStorage * 1024 * 1024 * 1024;
            
            const subscriptionData = {
                subscription_plan: subscriptionPlan,
                subscription_start_date: subscriptionStartDate,
                subscription_end_date: subscriptionEndDate,
                max_users: maxUsers,
                max_storage: maxStorageBytes
            };
            
            updateSubscription(tenantId, subscriptionData);
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('updateSubscriptionModal'));
            modal.hide();
        });
    });
</script>
{% endblock %}
