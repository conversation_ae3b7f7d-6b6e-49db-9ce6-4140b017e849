import os
import django
import traceback

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kelem_sms.settings')
django.setup()

from django.db import connection
from tenants.models import School
from django_tenants.utils import schema_context

def create_schema_for_tenant(schema_name):
    """Create a schema for a tenant and run migrations."""
    try:
        # Check if the schema already exists
        with connection.cursor() as cursor:
            cursor.execute("SELECT schema_name FROM information_schema.schemata WHERE schema_name = %s", [schema_name])
            if cursor.fetchone():
                print(f"Schema '{schema_name}' already exists.")
                return
        
        # Create the schema
        with connection.cursor() as cursor:
            cursor.execute(f"CREATE SCHEMA {schema_name}")
            print(f"Schema '{schema_name}' created successfully.")
        
        # Run migrations for the tenant
        print(f"Running migrations for schema '{schema_name}'...")
        os.system(f"cd {os.path.dirname(os.path.abspath(__file__))} && python manage.py migrate_schemas --schema={schema_name}")
        
        print(f"Schema creation and migrations completed for '{schema_name}'.")
    except Exception as e:
        print(f"Error creating schema: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    # Get the tenant
    try:
        tenant = School.objects.get(schema_name='fasiledes')
        print(f"Found tenant: {tenant.name} (Schema: {tenant.schema_name})")
        
        # Create schema for the tenant
        create_schema_for_tenant(tenant.schema_name)
    except School.DoesNotExist:
        print("Tenant 'fasiledes' not found.")
    except Exception as e:
        print(f"Error: {str(e)}")
        traceback.print_exc()
