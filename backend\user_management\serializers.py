from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from tenants.models import School
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timedelta

User = get_user_model()


class UserListSerializer(serializers.ModelSerializer):
    """Serializer for user list view"""
    full_name = serializers.SerializerMethodField()
    tenant_count = serializers.SerializerMethodField()
    last_login_display = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'email', 'first_name', 'last_name', 'full_name',
            'user_type', 'is_active', 'is_staff', 'is_superuser',
            'date_joined', 'last_login', 'last_login_display',
            'phone_number', 'tenant_count', 'status'
        ]
    
    def get_full_name(self, obj):
        return obj.get_full_name() or obj.email
    
    def get_tenant_count(self, obj):
        # Count tenants the user has access to
        if obj.is_superuser:
            return School.objects.count()
        # For regular users, count their associated tenants
        # This would need to be implemented based on your tenant-user relationship
        return 0
    
    def get_last_login_display(self, obj):
        if obj.last_login:
            return obj.last_login.strftime('%Y-%m-%d %H:%M')
        return 'Never'
    
    def get_status(self, obj):
        if not obj.is_active:
            return 'inactive'
        elif obj.last_login and obj.last_login > timezone.now() - timedelta(days=7):
            return 'active'
        elif obj.last_login:
            return 'idle'
        else:
            return 'new'


class UserDetailSerializer(serializers.ModelSerializer):
    """Serializer for detailed user view"""
    full_name = serializers.SerializerMethodField()
    groups = serializers.StringRelatedField(many=True, read_only=True)
    user_permissions = serializers.StringRelatedField(many=True, read_only=True)
    tenant_access = serializers.SerializerMethodField()
    activity_summary = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'email', 'first_name', 'last_name', 'full_name',
            'user_type', 'is_active', 'is_staff', 'is_superuser',
            'date_joined', 'last_login', 'phone_number',
            'groups', 'user_permissions', 'tenant_access', 'activity_summary'
        ]
    
    def get_full_name(self, obj):
        return obj.get_full_name() or obj.email
    
    def get_tenant_access(self, obj):
        if obj.is_superuser:
            return {
                'type': 'superuser',
                'access_level': 'all',
                'tenant_count': School.objects.count()
            }
        # Implement tenant-specific access logic here
        return {
            'type': 'regular',
            'access_level': 'limited',
            'tenant_count': 0
        }
    
    def get_activity_summary(self, obj):
        return {
            'last_login': obj.last_login,
            'date_joined': obj.date_joined,
            'login_count': 0,  # Would need to track this separately
            'active_sessions': 0,  # Would need session tracking
        }


class UserCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating new users"""
    password = serializers.CharField(write_only=True, min_length=8)
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = [
            'email', 'first_name', 'last_name', 'user_type',
            'phone_number', 'is_active', 'is_staff', 'is_superuser',
            'password', 'password_confirm'
        ]
    
    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords don't match")
        return attrs
    
    def create(self, validated_data):
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()
        return user


class UserUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating users"""
    
    class Meta:
        model = User
        fields = [
            'first_name', 'last_name', 'user_type', 'phone_number',
            'is_active', 'is_staff', 'is_superuser'
        ]
    
    def update(self, instance, validated_data):
        # Prevent non-superusers from modifying superuser status
        request = self.context.get('request')
        if request and not request.user.is_superuser:
            validated_data.pop('is_superuser', None)
            validated_data.pop('is_staff', None)
        
        return super().update(instance, validated_data)


class GroupSerializer(serializers.ModelSerializer):
    """Serializer for user groups"""
    user_count = serializers.SerializerMethodField()
    permissions = serializers.StringRelatedField(many=True, read_only=True)
    permission_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False,
        help_text="List of permission IDs to assign to the group"
    )
    description = serializers.CharField(required=False, allow_blank=True)

    class Meta:
        model = Group
        fields = ['id', 'name', 'permissions', 'permission_ids', 'user_count', 'description']

    def get_user_count(self, obj):
        return obj.user_set.count()

    def create(self, validated_data):
        permission_ids = validated_data.pop('permission_ids', [])
        description = validated_data.pop('description', '')

        group = Group.objects.create(**validated_data)

        if permission_ids:
            permissions = Permission.objects.filter(id__in=permission_ids)
            group.permissions.set(permissions)

        # Store description in a custom field if your Group model has it
        # For now, we'll just ignore it since Django's default Group model doesn't have description

        return group

    def update(self, instance, validated_data):
        permission_ids = validated_data.pop('permission_ids', None)
        description = validated_data.pop('description', '')

        instance = super().update(instance, validated_data)

        if permission_ids is not None:
            permissions = Permission.objects.filter(id__in=permission_ids)
            instance.permissions.set(permissions)

        return instance


class PermissionSerializer(serializers.ModelSerializer):
    """Serializer for permissions"""
    content_type_name = serializers.CharField(source='content_type.name', read_only=True)
    
    class Meta:
        model = Permission
        fields = ['id', 'name', 'codename', 'content_type', 'content_type_name']


class UserStatsSerializer(serializers.Serializer):
    """Serializer for user statistics"""
    total_users = serializers.IntegerField()
    active_users = serializers.IntegerField()
    inactive_users = serializers.IntegerField()
    superusers = serializers.IntegerField()
    staff_users = serializers.IntegerField()
    new_users_this_month = serializers.IntegerField()
    user_types = serializers.DictField()
    recent_logins = serializers.IntegerField()


class BulkUserActionSerializer(serializers.Serializer):
    """Serializer for bulk user actions"""
    user_ids = serializers.ListField(child=serializers.IntegerField())
    action = serializers.ChoiceField(choices=[
        ('activate', 'Activate'),
        ('deactivate', 'Deactivate'),
        ('delete', 'Delete'),
        ('make_staff', 'Make Staff'),
        ('remove_staff', 'Remove Staff'),
    ])

    def validate_user_ids(self, value):
        if not value:
            raise serializers.ValidationError("At least one user must be selected")
        return value


class PasswordResetSerializer(serializers.Serializer):
    """Serializer for password reset"""
    new_password = serializers.CharField(min_length=8, write_only=True)
    new_password_confirm = serializers.CharField(write_only=True)

    def validate(self, attrs):
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError("Passwords don't match")
        return attrs
