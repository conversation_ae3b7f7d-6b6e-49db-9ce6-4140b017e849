import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kelem_sms.settings')
django.setup()

from django.db import connection, transaction
from django.conf import settings

def fix_auth_tables():
    """Fix missing authentication tables."""
    print("Starting authentication tables fix...")
    
    # Use transaction management to ensure clean state
    with transaction.atomic():
        try:
            # Check if the authentication_user_groups table exists
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = current_schema()
                        AND table_name = 'authentication_user_groups'
                    )
                """)
                table_exists = cursor.fetchone()[0]
            
            # Create the authentication_user_groups table if it doesn't exist
            if not table_exists:
                print("Creating authentication_user_groups table...")
                with connection.cursor() as cursor:
                    cursor.execute("""
                        CREATE TABLE authentication_user_groups (
                            id bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
                            user_id bigint NOT NULL,
                            group_id integer NOT NULL,
                            CONSTRAINT authentication_user_groups_user_id_group_id_key UNIQUE (user_id, group_id),
                            CONSTRAINT authentication_user_groups_group_id_fkey FOREIGN KEY (group_id) REFERENCES auth_group(id) DEFERRABLE INITIALLY DEFERRED,
                            CONSTRAINT authentication_user_groups_user_id_fkey FOREIGN KEY (user_id) REFERENCES authentication_user(id) DEFERRABLE INITIALLY DEFERRED
                        )
                    """)
                print("Created authentication_user_groups table.")
            
            # Check if the authentication_user_user_permissions table exists
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = current_schema()
                        AND table_name = 'authentication_user_user_permissions'
                    )
                """)
                table_exists = cursor.fetchone()[0]
            
            # Create the authentication_user_user_permissions table if it doesn't exist
            if not table_exists:
                print("Creating authentication_user_user_permissions table...")
                with connection.cursor() as cursor:
                    cursor.execute("""
                        CREATE TABLE authentication_user_user_permissions (
                            id bigint NOT NULL PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
                            user_id bigint NOT NULL,
                            permission_id integer NOT NULL,
                            CONSTRAINT authentication_user_user_permissions_user_id_permission_id_key UNIQUE (user_id, permission_id),
                            CONSTRAINT authentication_user_user_permissions_permission_id_fkey FOREIGN KEY (permission_id) REFERENCES auth_permission(id) DEFERRABLE INITIALLY DEFERRED,
                            CONSTRAINT authentication_user_user_permissions_user_id_fkey FOREIGN KEY (user_id) REFERENCES authentication_user(id) DEFERRABLE INITIALLY DEFERRED
                        )
                    """)
                print("Created authentication_user_user_permissions table.")
            
            print("Authentication tables fix completed.")
        except Exception as e:
            # Roll back the transaction on error
            print(f"Error in fix_auth_tables: {str(e)}")
            transaction.set_rollback(True)
            raise

if __name__ == '__main__':
    fix_auth_tables()
