import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  Box,
  Grid,
  Typography,
  Button,
  Card,

  CircularProgress,
  Al<PERSON>,
  Chip,
} from '@mui/material'
import {
  PeopleAlt as StudentsIcon,
  School as TeachersIcon,
  MenuBook as CoursesIcon,

  Domain as TenantIcon,
  Payment as BillingIcon,
  AdminPanelSettings as AccessIcon,

  Apartment as PlatformIcon,
  MonetizationOn as RevenueIcon,
  Groups as UsersIcon,

} from '@mui/icons-material'
import { getDashboardData } from '@/services/dashboardService'
import { useAuth } from '@/contexts/AuthContext'
import api from '@/services/api'

const DashboardPage = () => {
  const navigate = useNavigate()
  const { user, currentTenant, setCurrentTenant } = useAuth()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [stats, setStats] = useState({
    totalStudents: 0,
    activeStudents: 0,
    totalTeachers: 0,
    totalCourses: 0,
    recentStudents: [] as any[],
  })

  // Function to fetch and set tenant automatically
  const fetchAndSetTenant = async () => {
    try {
      const response = await api.get('/tenants/')
      const tenants = response.data.results || response.data
      if (tenants && tenants.length > 0) {
        setCurrentTenant(tenants[0])
      }
    } catch (error) {
      console.error('Error fetching tenants:', error)
    }
  }

  const isPlatformOwner = user?.is_superuser && (!user?.schema_name || user?.schema_name === 'public')

  // Effect to handle tenant initialization (only for non-platform owners)
  useEffect(() => {
    if (!isPlatformOwner && !currentTenant) {
      fetchAndSetTenant()
    }
  }, [isPlatformOwner])

  // Effect to fetch dashboard data when tenant changes (only for school dashboards)
  useEffect(() => {
    if (isPlatformOwner) {
      setLoading(false)
      return
    }

    if (!currentTenant) {
      return
    }

    const fetchDashboardData = async () => {
      setLoading(true)
      setError(null)
      try {
        const dashboardData = await getDashboardData()
        setStats({
          totalStudents: dashboardData.totalStudents,
          activeStudents: dashboardData.activeStudents,
          totalTeachers: dashboardData.totalTeachers,
          totalCourses: dashboardData.totalCourses,
          recentStudents: dashboardData.recentStudents,
        })
      } catch (err: any) {
        console.error('Error fetching dashboard data:', err)
        setError(err.response?.data?.message || 'Failed to fetch dashboard data. Please try again.')
      } finally {
        setLoading(false)
      }
    }

    fetchDashboardData()
  }, [currentTenant?.id, isPlatformOwner])

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    )
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        {error}
      </Alert>
    )
  }

  return (
    <Box sx={{ bgcolor: 'background.default' }}>
      {isPlatformOwner ? (
          // Platform Owner Dashboard
          <>
            <Card
              sx={{
                py: 4,
                px: 4,
                mb: 4,
                bgcolor: 'white',
                color: '#1f2937',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                borderRadius: 0,
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%', mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box
                    sx={{
                      p: 2,
                      borderRadius: 2,
                      bgcolor: '#f3f4f6',
                      mr: 3,
                    }}
                  >
                    <PlatformIcon sx={{ fontSize: 32, color: '#6366f1' }} />
                  </Box>
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 700, mb: 0.5 }}>
                      Kelem SMS Platform
                    </Typography>
                    <Typography variant="body1" sx={{ color: '#6b7280' }}>
                      Multi-Tenant SaaS Management Dashboard
                    </Typography>
                  </Box>
                </Box>

                {/* Action Buttons - Moved to top right */}
                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                  <Button
                    variant="contained"
                    onClick={() => navigate('/dashboard/tenant-management')}
                    sx={{
                      bgcolor: '#6366f1',
                      color: 'white',
                      '&:hover': {
                        bgcolor: '#4f46e5',
                      },
                      fontWeight: 600,
                    }}
                  >
                    Manage Schools
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={() => navigate('/dashboard/platform-analytics')}
                    sx={{
                      borderColor: '#6366f1',
                      color: '#6366f1',
                      '&:hover': {
                        borderColor: '#4f46e5',
                        bgcolor: '#f3f4f6',
                        color: '#4f46e5',
                      },
                      fontWeight: 600,
                    }}
                  >
                    View Analytics
                  </Button>
                </Box>
              </Box>

              <Typography variant="body1" paragraph sx={{ color: '#6b7280', mb: 0 }}>
                Welcome to the platform administration center. Manage all schools, subscriptions, users, and platform-wide settings.
              </Typography>
            </Card>

            <Grid container spacing={3} sx={{ mb: 4 }}>
              <Grid item xs={12} md={6} lg={4}>
                <Card
                  sx={{
                    p: 3,
                    height: '100%',
                    cursor: 'pointer',
                    '&:hover': { transform: 'translateY(-4px)' },
                  }}
                  onClick={() => navigate('/dashboard/tenant-management')}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Box sx={{ p: 1.5, borderRadius: 2, bgcolor: 'primary.main', color: 'white', mr: 2 }}>
                      <TenantIcon />
                    </Box>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      Tenant Management
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Manage schools/institutions, onboard new tenants, configure tenant settings
                  </Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Chip label="12 Active Schools" size="small" color="success" />
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main' }}>
                      12
                    </Typography>
                  </Box>
                </Card>
              </Grid>

              <Grid item xs={12} md={6} lg={4}>
                <Card
                  sx={{
                    p: 3,
                    height: '100%',
                    cursor: 'pointer',
                    '&:hover': { transform: 'translateY(-4px)' },
                  }}
                  onClick={() => navigate('/dashboard/billing-management')}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Box sx={{ p: 1.5, borderRadius: 2, bgcolor: 'success.main', color: 'white', mr: 2 }}>
                      <BillingIcon />
                    </Box>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      Subscription & Billing
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Manage subscription plans, billing cycles, payments, and revenue tracking
                  </Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Chip label="$24,500 MRR" size="small" color="success" />
                    <RevenueIcon sx={{ color: 'success.main', fontSize: 28 }} />
                  </Box>
                </Card>
              </Grid>

              <Grid item xs={12} md={6} lg={4}>
                <Card
                  sx={{
                    p: 3,
                    height: '100%',
                    cursor: 'pointer',
                    '&:hover': { transform: 'translateY(-4px)' },
                  }}
                  onClick={() => navigate('/dashboard/user-management')}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Box sx={{ p: 1.5, borderRadius: 2, bgcolor: 'warning.main', color: 'white', mr: 2 }}>
                      <AccessIcon />
                    </Box>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      User & Access Management
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Global user roles, permissions, access control, and security policies
                  </Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Chip label="1,247 Total Users" size="small" color="warning" />
                    <UsersIcon sx={{ color: 'warning.main', fontSize: 28 }} />
                  </Box>
                </Card>
              </Grid>
            </Grid>
          </>
        ) : (
          currentTenant ? (
            // School Dashboard
            <Card sx={{ p: 4 }}>
              <Typography variant="h4" sx={{ mb: 3, fontWeight: 700 }}>
                {currentTenant.name} Dashboard
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={3}>
                  <Card sx={{ p: 3, textAlign: 'center' }}>
                    <StudentsIcon sx={{ fontSize: 40, color: 'primary.main', mb: 2 }} />
                    <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
                      {stats.totalStudents}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Students
                    </Typography>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Card sx={{ p: 3, textAlign: 'center' }}>
                    <TeachersIcon sx={{ fontSize: 40, color: 'success.main', mb: 2 }} />
                    <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
                      {stats.totalTeachers}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Teachers
                    </Typography>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Card sx={{ p: 3, textAlign: 'center' }}>
                    <CoursesIcon sx={{ fontSize: 40, color: 'warning.main', mb: 2 }} />
                    <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
                      {stats.totalCourses}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Courses
                    </Typography>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Card sx={{ p: 3, textAlign: 'center' }}>
                    <StudentsIcon sx={{ fontSize: 40, color: 'info.main', mb: 2 }} />
                    <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
                      {stats.activeStudents}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Active Students
                    </Typography>
                  </Card>
                </Grid>
              </Grid>
            </Card>
          ) : (
            // No tenant selected - Show tenant selection
            <Card sx={{ p: 4, textAlign: 'center' }}>
              <Typography variant="h5" sx={{ mb: 2, fontWeight: 600 }}>
                No School Selected
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                Please select a school to view the dashboard.
              </Typography>
              <Button
                variant="contained"
                onClick={() => navigate('/dashboard/tenant-management')}
                sx={{ fontWeight: 600 }}
              >
                Select School
              </Button>
            </Card>
          )
        )}
    </Box>
  )
}

export default DashboardPage
