import React, { useState, useEffect } from 'react'
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardHeader,
  TextField,
  Button,
  Typography,
  Switch,
  FormControlLabel,
  Divider,
  CircularProgress,
  Alert,
} from '@mui/material'
import {
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Info as InfoIcon,
} from '@mui/icons-material'
import {
  getSiteSettings,
  updateSiteSettings,
  transformSiteSettingsForForm,
  transformFormDataToSiteSettings,
  type SiteSettings,
} from '../../../services/siteSettingsService'

interface GeneralSettingsTabProps {
  onSaveSuccess: () => void
  onSaveError: (error: string) => void
}

const GeneralSettingsTab: React.FC<GeneralSettingsTabProps> = ({
  onSaveSuccess,
  onSaveError,
}) => {
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [formData, setFormData] = useState({
    siteName: '',
    siteTagline: '',
    siteDescription: '',
    contactEmail: '',
    contactPhone: '',
    contactAddress: '',
    facebookUrl: '',
    twitterUrl: '',
    linkedinUrl: '',
    youtubeUrl: '',
    metaTitle: '',
    metaDescription: '',
    metaKeywords: '',
    enableRegistration: true,
    enableTrial: true,
    enableContactForm: true,
    enableBlog: false,
    enableTestimonials: true,
  })

  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      setLoading(true)
      const settings = await getSiteSettings()
      setFormData(transformSiteSettingsForForm(settings))
    } catch (error) {
      console.error('Error fetching settings:', error)
      onSaveError('Failed to load settings')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      const updateData = transformFormDataToSiteSettings(formData)
      await updateSiteSettings(updateData)
      onSaveSuccess()
    } catch (error) {
      console.error('Error saving settings:', error)
      onSaveError('Failed to save settings')
    } finally {
      setSaving(false)
    }
  }

  const handleReset = () => {
    fetchSettings()
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <CircularProgress />
      </Box>
    )
  }

  return (
    <Box>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
          General Settings
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Configure basic site information, contact details, and SEO settings
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Site Information */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Site Information"
              subheader="Basic information about your platform"
              avatar={<InfoIcon color="primary" />}
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Site Name"
                    value={formData.siteName}
                    onChange={handleInputChange('siteName')}
                    required
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Site Tagline"
                    value={formData.siteTagline}
                    onChange={handleInputChange('siteTagline')}
                    helperText="A short description of your platform"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Site Description"
                    value={formData.siteDescription}
                    onChange={handleInputChange('siteDescription')}
                    multiline
                    rows={3}
                    helperText="Detailed description for search engines"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Contact Information */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Contact Information"
              subheader="How users can reach you"
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Contact Email"
                    type="email"
                    value={formData.contactEmail}
                    onChange={handleInputChange('contactEmail')}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Contact Phone"
                    value={formData.contactPhone}
                    onChange={handleInputChange('contactPhone')}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Contact Address"
                    value={formData.contactAddress}
                    onChange={handleInputChange('contactAddress')}
                    multiline
                    rows={2}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Social Media */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Social Media Links"
              subheader="Connect your social media profiles"
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Facebook URL"
                    value={formData.facebookUrl}
                    onChange={handleInputChange('facebookUrl')}
                    placeholder="https://facebook.com/yourpage"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Twitter URL"
                    value={formData.twitterUrl}
                    onChange={handleInputChange('twitterUrl')}
                    placeholder="https://twitter.com/youraccount"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="LinkedIn URL"
                    value={formData.linkedinUrl}
                    onChange={handleInputChange('linkedinUrl')}
                    placeholder="https://linkedin.com/company/yourcompany"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="YouTube URL"
                    value={formData.youtubeUrl}
                    onChange={handleInputChange('youtubeUrl')}
                    placeholder="https://youtube.com/c/yourchannel"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* SEO Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="SEO Settings"
              subheader="Search engine optimization"
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Meta Title"
                    value={formData.metaTitle}
                    onChange={handleInputChange('metaTitle')}
                    helperText="Title that appears in search results"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Meta Description"
                    value={formData.metaDescription}
                    onChange={handleInputChange('metaDescription')}
                    multiline
                    rows={2}
                    helperText="Description that appears in search results"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Meta Keywords"
                    value={formData.metaKeywords}
                    onChange={handleInputChange('metaKeywords')}
                    helperText="Comma-separated keywords"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Feature Toggles */}
        <Grid item xs={12}>
          <Card>
            <CardHeader
              title="Feature Toggles"
              subheader="Enable or disable platform features"
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={4}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.enableRegistration}
                        onChange={handleInputChange('enableRegistration')}
                      />
                    }
                    label="Enable Registration"
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.enableTrial}
                        onChange={handleInputChange('enableTrial')}
                      />
                    }
                    label="Enable Trial"
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.enableContactForm}
                        onChange={handleInputChange('enableContactForm')}
                      />
                    }
                    label="Enable Contact Form"
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.enableBlog}
                        onChange={handleInputChange('enableBlog')}
                      />
                    }
                    label="Enable Blog"
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.enableTestimonials}
                        onChange={handleInputChange('enableTestimonials')}
                      />
                    }
                    label="Enable Testimonials"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Action Buttons */}
      <Box sx={{ mt: 4, display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={handleReset}
          disabled={saving}
        >
          Reset
        </Button>
        <Button
          variant="contained"
          startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
          onClick={handleSave}
          disabled={saving}
          sx={{
            background: 'linear-gradient(45deg, #6366f1 30%, #8b5cf6 90%)',
            boxShadow: '0 3px 5px 2px rgba(99, 102, 241, .3)',
          }}
        >
          {saving ? 'Saving...' : 'Save Changes'}
        </Button>
      </Box>
    </Box>
  )
}

export default GeneralSettingsTab
