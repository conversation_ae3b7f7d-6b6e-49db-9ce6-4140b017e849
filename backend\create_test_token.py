#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to create a test authentication token for API testing.
"""

import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kelem_sms.settings')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework.authtoken.models import Token

User = get_user_model()

def create_test_token():
    """Create a test superuser and authentication token."""
    
    # Create or get superuser
    email = '<EMAIL>'
    password = 'admin123'
    
    try:
        user = User.objects.get(email=email)
        print(f"Using existing superuser: {user.email}")
    except User.DoesNotExist:
        user = User.objects.create_superuser(
            email=email,
            password=password,
            first_name='Admin',
            last_name='User',
            user_type='admin'
        )
        print(f"Created new superuser: {user.email}")
    
    # Create or get token
    token, created = Token.objects.get_or_create(user=user)
    
    if created:
        print(f"Created new token: {token.key}")
    else:
        print(f"Using existing token: {token.key}")
    
    print(f"\nTest credentials:")
    print(f"Email: {email}")
    print(f"Password: {password}")
    print(f"Token: {token.key}")
    
    print(f"\nTo test the API, use this token in the Authorization header:")
    print(f"Authorization: Token {token.key}")
    
    print(f"\nOr set it in localStorage for the frontend:")
    print(f"localStorage.setItem('token', '{token.key}')")
    
    return token.key

if __name__ == '__main__':
    print("Creating test authentication token...\n")
    token = create_test_token()
    print(f"\nToken creation completed!")
