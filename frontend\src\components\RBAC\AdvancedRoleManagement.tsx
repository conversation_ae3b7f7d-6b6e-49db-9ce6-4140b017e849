import React, { useState, useMemo } from 'react'
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  <PERSON>ton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress,
  Badge,
  Menu,
} from '@mui/material'
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ContentCopy as CopyIcon,
  Save as SaveIcon,
  Template as TemplateIcon,
  Analytics as AnalyticsIcon,
  Security as SecurityIcon,
  Group as GroupIcon,
  Person as PersonIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  TrendingUp as TrendingUpIcon,
  MoreVert as MoreVertIcon,
  Visibility as VisibilityIcon,
  Assignment as AssignmentIcon,
} from '@mui/icons-material'
import {
  Role,
  RoleTemplate,
  RoleAnalytics,
  Permission,
  User,
} from '../../types/rbac'

interface AdvancedRoleManagementProps {
  roles: Role[]
  roleTemplates: RoleTemplate[]
  permissions: Permission[]
  users: User[]
  onCreateRole: (roleData: Partial<Role>) => void
  onUpdateRole: (id: string, roleData: Partial<Role>) => void
  onDeleteRole: (id: string) => void
  onCloneRole: (id: string, newName: string) => void
  onCreateFromTemplate: (templateId: string, roleName: string) => void
  onSaveAsTemplate: (roleId: string, templateData: Partial<RoleTemplate>) => void
  onGetRoleAnalytics: (roleId: string) => Promise<RoleAnalytics>
  loading?: boolean
}

const AdvancedRoleManagement: React.FC<AdvancedRoleManagementProps> = ({
  roles,
  roleTemplates,
  permissions,
  users,
  onCreateRole,
  onUpdateRole,
  onDeleteRole,
  onCloneRole,
  onCreateFromTemplate,
  onSaveAsTemplate,
  onGetRoleAnalytics,
  loading = false,
}) => {
  const [activeTab, setActiveTab] = useState(0)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [dialogType, setDialogType] = useState<'create' | 'edit' | 'clone' | 'template' | 'analytics'>('create')
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)
  const [selectedTemplate, setSelectedTemplate] = useState<RoleTemplate | null>(null)
  const [roleAnalytics, setRoleAnalytics] = useState<RoleAnalytics | null>(null)
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [formData, setFormData] = useState<any>({})

  const rolesByCategory = useMemo(() => {
    const categories = {
      system: roles.filter(r => r.is_system),
      administrative: roles.filter(r => !r.is_system && r.level <= 1),
      operational: roles.filter(r => !r.is_system && r.level > 1 && r.level <= 3),
      basic: roles.filter(r => !r.is_system && r.level > 3),
    }
    return categories
  }, [roles])

  const templatesByCategory = useMemo(() => {
    return roleTemplates.reduce((acc, template) => {
      const category = template.category || 'General'
      if (!acc[category]) acc[category] = []
      acc[category].push(template)
      return acc
    }, {} as Record<string, RoleTemplate[]>)
  }, [roleTemplates])

  const getRoleRiskScore = (role: Role) => {
    let score = 0
    
    // High permission count increases risk
    if (role.effective_permissions.length > 50) score += 30
    else if (role.effective_permissions.length > 25) score += 15
    
    // System roles are higher risk
    if (role.is_system) score += 20
    
    // High user count increases risk
    if (role.users_count > 100) score += 25
    else if (role.users_count > 50) score += 15
    
    // Low level (high privilege) increases risk
    if (role.level === 0) score += 25
    else if (role.level === 1) score += 15
    
    return Math.min(100, score)
  }

  const getRiskColor = (score: number) => {
    if (score >= 70) return 'error'
    if (score >= 40) return 'warning'
    return 'success'
  }

  const handleOpenDialog = (type: typeof dialogType, role?: Role, template?: RoleTemplate) => {
    setDialogType(type)
    setSelectedRole(role || null)
    setSelectedTemplate(template || null)
    
    if (type === 'create') {
      setFormData({
        name: '',
        display_name: '',
        description: '',
        level: 4,
        permissions: [],
        attributes: {},
      })
    } else if (type === 'edit' && role) {
      setFormData({
        name: role.name,
        display_name: role.display_name,
        description: role.description,
        level: role.level,
        permissions: role.permissions,
        attributes: role.attributes,
      })
    } else if (type === 'clone' && role) {
      setFormData({
        name: `${role.name}_copy`,
        display_name: `${role.display_name} (Copy)`,
        description: role.description,
        level: role.level,
        permissions: role.permissions,
        attributes: role.attributes,
      })
    } else if (type === 'template') {
      setFormData({
        name: selectedRole?.name || '',
        display_name: selectedRole?.display_name || '',
        description: selectedRole?.description || '',
        category: 'General',
      })
    }
    
    setDialogOpen(true)
  }

  const handleCloseDialog = () => {
    setDialogOpen(false)
    setSelectedRole(null)
    setSelectedTemplate(null)
    setRoleAnalytics(null)
    setFormData({})
  }

  const handleSave = () => {
    if (dialogType === 'create') {
      onCreateRole(formData)
    } else if (dialogType === 'edit' && selectedRole) {
      onUpdateRole(selectedRole.id, formData)
    } else if (dialogType === 'clone' && selectedRole) {
      onCloneRole(selectedRole.id, formData.name)
    } else if (dialogType === 'template' && selectedRole) {
      onSaveAsTemplate(selectedRole.id, formData)
    }
    handleCloseDialog()
  }

  const handleShowAnalytics = async (role: Role) => {
    try {
      const analytics = await onGetRoleAnalytics(role.id)
      setRoleAnalytics(analytics)
      setSelectedRole(role)
      setDialogType('analytics')
      setDialogOpen(true)
    } catch (error) {
      console.error('Error fetching role analytics:', error)
    }
  }

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, role: Role) => {
    setAnchorEl(event.currentTarget)
    setSelectedRole(role)
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
    setSelectedRole(null)
  }

  const RoleCard: React.FC<{ role: Role }> = ({ role }) => {
    const riskScore = getRoleRiskScore(role)
    const riskColor = getRiskColor(riskScore)

    return (
      <Card sx={{ height: '100%' }}>
        <CardHeader
          title={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <SecurityIcon color="primary" />
              <Typography variant="h6">{role.display_name || role.name}</Typography>
              {role.is_system && (
                <Chip label="System" size="small" color="warning" />
              )}
              {role.is_template && (
                <Chip label="Template" size="small" color="info" />
              )}
            </Box>
          }
          action={
            <IconButton onClick={(e) => handleMenuOpen(e, role)}>
              <MoreVertIcon />
            </IconButton>
          }
        />
        <CardContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {role.description}
          </Typography>

          <Grid container spacing={2} sx={{ mb: 2 }}>
            <Grid item xs={6}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h6">{role.users_count}</Typography>
                <Typography variant="caption" color="text.secondary">Users</Typography>
              </Box>
            </Grid>
            <Grid item xs={6}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h6">{role.effective_permissions.length}</Typography>
                <Typography variant="caption" color="text.secondary">Permissions</Typography>
              </Box>
            </Grid>
          </Grid>

          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="caption" color="text.secondary">
                Risk Score
              </Typography>
              <Typography variant="caption" color={`${riskColor}.main`}>
                {riskScore}%
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={riskScore}
              color={riskColor as any}
              sx={{ height: 6, borderRadius: 3 }}
            />
          </Box>

          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            <Chip
              label={`Level ${role.level}`}
              size="small"
              color={role.level <= 1 ? 'error' : role.level <= 3 ? 'warning' : 'default'}
            />
            {role.parent_role && (
              <Chip label="Inherited" size="small" variant="outlined" />
            )}
          </Box>
        </CardContent>
      </Card>
    )
  }

  const TemplateCard: React.FC<{ template: RoleTemplate }> = ({ template }) => (
    <Card>
      <CardHeader
        title={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <TemplateIcon color="primary" />
            <Typography variant="h6">{template.display_name}</Typography>
            {template.is_builtin && (
              <Chip label="Built-in" size="small" color="info" />
            )}
          </Box>
        }
        action={
          <Button
            size="small"
            onClick={() => {
              const roleName = prompt('Enter role name:')
              if (roleName) {
                onCreateFromTemplate(template.id, roleName)
              }
            }}
          >
            Use Template
          </Button>
        }
      />
      <CardContent>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {template.description}
        </Typography>
        
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="caption" color="text.secondary">
            {template.permissions.length} permissions
          </Typography>
          <Typography variant="caption" color="text.secondary">
            Used {template.usage_count} times
          </Typography>
        </Box>

        <Chip label={template.category} size="small" variant="outlined" />
      </CardContent>
    </Card>
  )

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">Advanced Role Management</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog('create')}
          sx={{
            background: 'linear-gradient(45deg, #6366f1 30%, #8b5cf6 90%)',
          }}
        >
          Create Role
        </Button>
      </Box>

      {/* Tabs */}
      <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)} sx={{ mb: 3 }}>
        <Tab label="Role Management" />
        <Tab label="Role Templates" />
        <Tab label="Role Analytics" />
      </Tabs>

      {/* Role Management Tab */}
      {activeTab === 0 && (
        <Box>
          {Object.entries(rolesByCategory).map(([category, categoryRoles]) => (
            <Box key={category} sx={{ mb: 4 }}>
              <Typography variant="h6" sx={{ mb: 2, textTransform: 'capitalize' }}>
                {category} Roles ({categoryRoles.length})
              </Typography>
              <Grid container spacing={3}>
                {categoryRoles.map((role) => (
                  <Grid item xs={12} md={6} lg={4} key={role.id}>
                    <RoleCard role={role} />
                  </Grid>
                ))}
              </Grid>
            </Box>
          ))}
        </Box>
      )}

      {/* Role Templates Tab */}
      {activeTab === 1 && (
        <Box>
          {Object.entries(templatesByCategory).map(([category, templates]) => (
            <Box key={category} sx={{ mb: 4 }}>
              <Typography variant="h6" sx={{ mb: 2 }}>
                {category} Templates ({templates.length})
              </Typography>
              <Grid container spacing={3}>
                {templates.map((template) => (
                  <Grid item xs={12} md={6} lg={4} key={template.id}>
                    <TemplateCard template={template} />
                  </Grid>
                ))}
              </Grid>
            </Box>
          ))}
        </Box>
      )}

      {/* Role Analytics Tab */}
      {activeTab === 2 && (
        <Box>
          <Alert severity="info" sx={{ mb: 3 }}>
            Click on a role to view detailed analytics and usage patterns.
          </Alert>
          
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Role</TableCell>
                  <TableCell>Users</TableCell>
                  <TableCell>Permissions</TableCell>
                  <TableCell>Risk Score</TableCell>
                  <TableCell>Last Used</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {roles.map((role) => {
                  const riskScore = getRoleRiskScore(role)
                  const riskColor = getRiskColor(riskScore)
                  
                  return (
                    <TableRow key={role.id}>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <SecurityIcon color="primary" />
                          <Typography variant="body2">{role.display_name || role.name}</Typography>
                        </Box>
                      </TableCell>
                      <TableCell>{role.users_count}</TableCell>
                      <TableCell>{role.effective_permissions.length}</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <LinearProgress
                            variant="determinate"
                            value={riskScore}
                            color={riskColor as any}
                            sx={{ width: 60, height: 6, borderRadius: 3 }}
                          />
                          <Typography variant="caption" color={`${riskColor}.main`}>
                            {riskScore}%
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(role.updated_at).toLocaleDateString()}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Button
                          size="small"
                          startIcon={<AnalyticsIcon />}
                          onClick={() => handleShowAnalytics(role)}
                        >
                          View Analytics
                        </Button>
                      </TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      )}

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => {
          handleMenuClose()
          if (selectedRole) handleOpenDialog('edit', selectedRole)
        }}>
          <EditIcon sx={{ mr: 1 }} /> Edit Role
        </MenuItem>
        <MenuItem onClick={() => {
          handleMenuClose()
          if (selectedRole) handleOpenDialog('clone', selectedRole)
        }}>
          <CopyIcon sx={{ mr: 1 }} /> Clone Role
        </MenuItem>
        <MenuItem onClick={() => {
          handleMenuClose()
          if (selectedRole) handleOpenDialog('template', selectedRole)
        }}>
          <TemplateIcon sx={{ mr: 1 }} /> Save as Template
        </MenuItem>
        <MenuItem onClick={() => {
          handleMenuClose()
          if (selectedRole) handleShowAnalytics(selectedRole)
        }}>
          <AnalyticsIcon sx={{ mr: 1 }} /> View Analytics
        </MenuItem>
        <Divider />
        <MenuItem 
          onClick={() => {
            handleMenuClose()
            if (selectedRole && !selectedRole.is_system) {
              onDeleteRole(selectedRole.id)
            }
          }}
          disabled={selectedRole?.is_system}
          sx={{ color: 'error.main' }}
        >
          <DeleteIcon sx={{ mr: 1 }} /> Delete Role
        </MenuItem>
      </Menu>

      {/* Dialogs would go here - simplified for brevity */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {dialogType === 'create' && 'Create Role'}
          {dialogType === 'edit' && 'Edit Role'}
          {dialogType === 'clone' && 'Clone Role'}
          {dialogType === 'template' && 'Save as Template'}
          {dialogType === 'analytics' && 'Role Analytics'}
        </DialogTitle>
        <DialogContent>
          {/* Dialog content would be implemented based on dialogType */}
          <Typography>Dialog content for {dialogType}</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button variant="contained" onClick={handleSave}>
            Save
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default AdvancedRoleManagement
