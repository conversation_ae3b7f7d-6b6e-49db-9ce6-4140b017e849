import React, { useState } from 'react'
import {
  <PERSON>alog,
  DialogT<PERSON>le,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Box,
  CircularProgress,
  IconButton,
  Alert,
} from '@mui/material'
import { Close as CloseIcon } from '@mui/icons-material'
import { bulkUserAction, type BulkAction } from '../../../services/userManagementService'

interface BulkActionDialogProps {
  open: boolean
  userIds: number[]
  onClose: () => void
  onActionCompleted: (message: string) => void
  onError: (error: string) => void
}

const BulkActionDialog: React.FC<BulkActionDialogProps> = ({
  open,
  userIds,
  onClose,
  onActionCompleted,
  onError,
}) => {
  const [loading, setLoading] = useState(false)
  const [action, setAction] = useState<BulkAction['action']>('activate')

  const handleSubmit = async () => {
    setLoading(true)
    try {
      const result = await bulkUserAction({ userIds, action })
      onActionCompleted(result.message)
      handleClose()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || 'Failed to perform bulk action'
      onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setAction('activate')
    onClose()
  }

  const getActionDescription = (actionType: BulkAction['action']) => {
    switch (actionType) {
      case 'activate':
        return 'Activate selected users'
      case 'deactivate':
        return 'Deactivate selected users'
      case 'delete':
        return 'Permanently delete selected users'
      case 'make_staff':
        return 'Grant staff privileges to selected users'
      case 'remove_staff':
        return 'Remove staff privileges from selected users'
      default:
        return ''
    }
  }

  const isDestructiveAction = action === 'delete'

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Bulk Action
          </Typography>
          <IconButton onClick={handleClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        <Alert severity="info" sx={{ mb: 3 }}>
          You have selected <strong>{userIds.length}</strong> user(s) for bulk action.
        </Alert>

        <FormControl fullWidth sx={{ mb: 3 }}>
          <InputLabel>Action</InputLabel>
          <Select
            value={action}
            label="Action"
            onChange={(e) => setAction(e.target.value as BulkAction['action'])}
          >
            <MenuItem value="activate">Activate Users</MenuItem>
            <MenuItem value="deactivate">Deactivate Users</MenuItem>
            <MenuItem value="make_staff">Make Staff</MenuItem>
            <MenuItem value="remove_staff">Remove Staff</MenuItem>
            <MenuItem value="delete">Delete Users</MenuItem>
          </Select>
        </FormControl>

        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {getActionDescription(action)}
        </Typography>

        {isDestructiveAction && (
          <Alert severity="warning">
            <Typography variant="body2">
              <strong>Warning:</strong> This action cannot be undone. Deleted users will be permanently removed from the system.
            </Typography>
          </Alert>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button onClick={handleClose} disabled={loading}>
          Cancel
        </Button>
        <Button
          variant="contained"
          color={isDestructiveAction ? 'error' : 'primary'}
          onClick={handleSubmit}
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : null}
        >
          {isDestructiveAction ? 'Delete Users' : 'Apply Action'}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default BulkActionDialog
