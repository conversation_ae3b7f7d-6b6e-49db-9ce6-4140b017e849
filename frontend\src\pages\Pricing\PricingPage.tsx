import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Switch,
  FormControlLabel,
  CircularProgress,
  Alert,
  Divider,
  useTheme,
  useMediaQuery,
} from '@mui/material'
import {
  Check as CheckIcon,
  Star as StarIcon,
  People as PeopleIcon,
  Storage as StorageIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material'
import { getPublicPlans, formatCurrency } from '../../services/publicBillingService'
import { SubscriptionPlan } from '../../services/billingService'

const PricingPage = () => {
  const navigate = useNavigate()
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  
  const [plans, setPlans] = useState<SubscriptionPlan[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isYearly, setIsYearly] = useState(false)

  useEffect(() => {
    fetchPlans()
  }, [])

  const fetchPlans = async () => {
    try {
      setLoading(true)
      setError(null)
      const plansData = await getPublicPlans()
      // Filter only active plans and sort by sort_order
      const activePlans = plansData
        .filter(plan => plan.isActive)
        .sort((a, b) => a.sortOrder - b.sortOrder)
      setPlans(activePlans)
    } catch (error) {
      console.error('Error fetching plans:', error)
      setError('Failed to load pricing plans. Please try again later.')
    } finally {
      setLoading(false)
    }
  }

  const getStorageDisplay = (bytes: number) => {
    const gb = bytes / (1024 * 1024 * 1024)
    return gb >= 1 ? `${gb.toFixed(0)} GB` : `${(bytes / (1024 * 1024)).toFixed(0)} MB`
  }

  const getPlanPrice = (plan: SubscriptionPlan) => {
    if (isYearly && plan.billingCycle === 'monthly') {
      // Calculate yearly price with 20% discount
      return plan.price * 12 * 0.8
    }
    return plan.price
  }

  const getPlanBillingCycle = (plan: SubscriptionPlan) => {
    if (isYearly && plan.billingCycle === 'monthly') {
      return 'year'
    }
    return plan.billingCycle === 'monthly' ? 'month' : 'year'
  }

  const handleGetStarted = (plan: SubscriptionPlan) => {
    // Navigate to registration with plan pre-selected
    navigate('/register-school', { state: { selectedPlan: plan } })
  }

  const getCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case 'basic':
        return 'primary'
      case 'premium':
        return 'secondary'
      case 'enterprise':
        return 'success'
      default:
        return 'default'
    }
  }

  if (loading) {
    return (
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: 'background.default',
        }}
      >
        <CircularProgress size={60} />
      </Box>
    )
  }

  if (error) {
    return (
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: 'background.default',
          p: 3,
        }}
      >
        <Container maxWidth="sm">
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
          <Button variant="contained" onClick={fetchPlans} fullWidth>
            Try Again
          </Button>
        </Container>
      </Box>
    )
  }

  return (
    <Box sx={{ bgcolor: 'background.default', minHeight: '100vh', py: 8 }}>
      <Container maxWidth="lg">
        {/* Header */}
        <Box sx={{ textAlign: 'center', mb: 6 }}>
          <Typography
            variant="h2"
            component="h1"
            sx={{
              fontWeight: 700,
              mb: 2,
              fontSize: { xs: '2.5rem', md: '3.5rem' },
            }}
          >
            Choose Your Plan
          </Typography>
          <Typography
            variant="h5"
            color="text.secondary"
            sx={{ mb: 4, maxWidth: 600, mx: 'auto' }}
          >
            Select the perfect plan for your school's needs. All plans include our core features with different limits and capabilities.
          </Typography>

          {/* Billing Toggle */}
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', gap: 2, mb: 4 }}>
            <Typography variant="body1" color={!isYearly ? 'primary.main' : 'text.secondary'}>
              Monthly
            </Typography>
            <FormControlLabel
              control={
                <Switch
                  checked={isYearly}
                  onChange={(e) => setIsYearly(e.target.checked)}
                  color="primary"
                />
              }
              label=""
            />
            <Typography variant="body1" color={isYearly ? 'primary.main' : 'text.secondary'}>
              Yearly
            </Typography>
            <Chip
              label="Save 20%"
              color="secondary"
              size="small"
              sx={{ ml: 1 }}
            />
          </Box>
        </Box>

        {/* Pricing Cards */}
        <Grid container spacing={4} justifyContent="center">
          {plans.map((plan) => (
            <Grid item xs={12} sm={6} lg={4} key={plan.id}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  position: 'relative',
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: theme.shadows[20],
                  },
                  ...(plan.isFeatured && {
                    border: '2px solid',
                    borderColor: 'secondary.main',
                    boxShadow: theme.shadows[10],
                    transform: 'scale(1.05)',
                  }),
                }}
              >
                {/* Featured Badge */}
                {plan.isFeatured && (
                  <Box
                    sx={{
                      position: 'absolute',
                      top: -12,
                      left: '50%',
                      transform: 'translateX(-50%)',
                      zIndex: 1,
                    }}
                  >
                    <Chip
                      label="Most Popular"
                      color="secondary"
                      icon={<StarIcon />}
                      sx={{
                        fontWeight: 600,
                        px: 2,
                        py: 1,
                        height: 'auto',
                      }}
                    />
                  </Box>
                )}

                <CardContent sx={{ flexGrow: 1, p: 4, pt: plan.isFeatured ? 5 : 4 }}>
                  {/* Plan Header */}
                  <Box sx={{ textAlign: 'center', mb: 3 }}>
                    <Chip
                      label={plan.category.charAt(0).toUpperCase() + plan.category.slice(1)}
                      color={getCategoryColor(plan.category) as any}
                      size="small"
                      sx={{ mb: 2 }}
                    />
                    <Typography variant="h4" component="h3" sx={{ fontWeight: 700, mb: 1 }}>
                      {plan.name}
                    </Typography>
                    <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                      {plan.description}
                    </Typography>

                    {/* Pricing */}
                    <Box sx={{ mb: 3 }}>
                      <Typography
                        variant="h3"
                        component="div"
                        sx={{
                          fontWeight: 700,
                          color: 'primary.main',
                          display: 'flex',
                          alignItems: 'baseline',
                          justifyContent: 'center',
                          gap: 1,
                        }}
                      >
                        {formatCurrency(getPlanPrice(plan), plan.currency)}
                        <Typography component="span" variant="h6" color="text.secondary">
                          /{getPlanBillingCycle(plan)}
                        </Typography>
                      </Typography>
                      {plan.setupFee > 0 && (
                        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                          + {formatCurrency(plan.setupFee, plan.currency)} setup fee
                        </Typography>
                      )}
                      {isYearly && plan.billingCycle === 'monthly' && (
                        <Typography variant="body2" color="success.main" sx={{ mt: 1 }}>
                          Save {formatCurrency(plan.price * 12 * 0.2, plan.currency)} per year
                        </Typography>
                      )}
                    </Box>
                  </Box>

                  <Divider sx={{ mb: 3 }} />

                  {/* Plan Details */}
                  <List dense sx={{ mb: 3 }}>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemIcon sx={{ minWidth: 36 }}>
                        <PeopleIcon color="primary" fontSize="small" />
                      </ListItemIcon>
                      <ListItemText
                        primary={`Up to ${plan.maxUsers.toLocaleString()} users`}
                        primaryTypographyProps={{ variant: 'body2' }}
                      />
                    </ListItem>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemIcon sx={{ minWidth: 36 }}>
                        <StorageIcon color="primary" fontSize="small" />
                      </ListItemIcon>
                      <ListItemText
                        primary={`${getStorageDisplay(plan.maxStorage)} storage`}
                        primaryTypographyProps={{ variant: 'body2' }}
                      />
                    </ListItem>
                    {plan.trialDays > 0 && (
                      <ListItem sx={{ px: 0 }}>
                        <ListItemIcon sx={{ minWidth: 36 }}>
                          <ScheduleIcon color="primary" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText
                          primary={`${plan.trialDays} days free trial`}
                          primaryTypographyProps={{ variant: 'body2' }}
                        />
                      </ListItem>
                    )}
                  </List>

                  {/* Features */}
                  {plan.features && plan.features.length > 0 && (
                    <>
                      <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 2 }}>
                        Features Included:
                      </Typography>
                      <List dense>
                        {plan.features.slice(0, 6).map((feature, index) => (
                          <ListItem key={index} sx={{ px: 0, py: 0.5 }}>
                            <ListItemIcon sx={{ minWidth: 24 }}>
                              <CheckIcon color="success" fontSize="small" />
                            </ListItemIcon>
                            <ListItemText
                              primary={feature.name}
                              primaryTypographyProps={{ variant: 'body2' }}
                            />
                          </ListItem>
                        ))}
                        {plan.features.length > 6 && (
                          <ListItem sx={{ px: 0, py: 0.5 }}>
                            <ListItemText
                              primary={`+ ${plan.features.length - 6} more features`}
                              primaryTypographyProps={{ 
                                variant: 'body2', 
                                color: 'text.secondary',
                                fontStyle: 'italic'
                              }}
                            />
                          </ListItem>
                        )}
                      </List>
                    </>
                  )}
                </CardContent>

                <CardActions sx={{ p: 4, pt: 0 }}>
                  <Button
                    variant={plan.isFeatured ? 'contained' : 'outlined'}
                    color={plan.isFeatured ? 'secondary' : 'primary'}
                    size="large"
                    fullWidth
                    onClick={() => handleGetStarted(plan)}
                    sx={{
                      py: 1.5,
                      fontWeight: 600,
                      fontSize: '1rem',
                    }}
                  >
                    Get Started
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* FAQ Section */}
        <Box sx={{ mt: 12, mb: 8 }}>
          <Typography
            variant="h3"
            component="h2"
            sx={{
              fontWeight: 700,
              textAlign: 'center',
              mb: 6,
              fontSize: { xs: '2rem', md: '2.5rem' },
            }}
          >
            Frequently Asked Questions
          </Typography>

          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                  Can I change my plan later?
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.
                </Typography>
              </Box>

              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                  Is there a free trial?
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Most of our plans include a free trial period. You can see the trial duration for each plan above.
                </Typography>
              </Box>

              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                  What payment methods do you accept?
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  We accept major credit cards, bank transfers, and mobile money payments for Ethiopian customers.
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                  Is my data secure?
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Yes, we use enterprise-grade security measures including data encryption, regular backups, and secure hosting.
                </Typography>
              </Box>

              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                  Do you provide training and support?
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  All plans include comprehensive onboarding, training materials, and ongoing customer support.
                </Typography>
              </Box>

              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                  Can I cancel anytime?
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Yes, you can cancel your subscription at any time. No long-term contracts or cancellation fees.
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Box>

        {/* Additional Info */}
        <Box sx={{ textAlign: 'center', mt: 8 }}>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
            All plans include 24/7 support, regular updates, and data security.
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Need a custom solution? <Button variant="text" size="small">Contact us</Button>
          </Typography>
        </Box>
      </Container>
    </Box>
  )
}

export default PricingPage
