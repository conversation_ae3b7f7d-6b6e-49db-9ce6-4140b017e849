import React, { useState, useEffect } from 'react'
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardHeader,
  TextField,
  Button,
  Typography,
  Switch,
  FormControlLabel,
  Slider,
  CircularProgress,
  Paper,
  Chip,
  Alert,
} from '@mui/material'
import {
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Palette as PaletteIcon,
  Image as ImageIcon,
  Code as CodeIcon,
} from '@mui/icons-material'
import {
  getBrandingSettings,
  updateBrandingSettings,
  type BrandingSettings,
} from '../../../services/siteSettingsService'

interface BrandingSettingsTabProps {
  onSaveSuccess: () => void
  onSaveError: (error: string) => void
}

const BrandingSettingsTab: React.FC<BrandingSettingsTabProps> = ({
  onSaveSuccess,
  onSaveError,
}) => {
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [formData, setFormData] = useState({
    primaryColor: '#6366f1',
    secondaryColor: '#8b5cf6',
    accentColor: '#f59e0b',
    backgroundColor: '#ffffff',
    textColor: '#1f2937',
    fontFamily: 'Inter, sans-serif',
    logoUrl: '',
    faviconUrl: '',
    customCss: '',
    enableDarkMode: false,
    borderRadius: 8,
  })

  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      setLoading(true)
      const settings = await getBrandingSettings()
      setFormData({
        primaryColor: settings.primary_color,
        secondaryColor: settings.secondary_color,
        accentColor: settings.accent_color,
        backgroundColor: settings.background_color,
        textColor: settings.text_color,
        fontFamily: settings.font_family,
        logoUrl: settings.logo_url,
        faviconUrl: settings.favicon_url,
        customCss: settings.custom_css,
        enableDarkMode: settings.enable_dark_mode,
        borderRadius: settings.border_radius,
      })
    } catch (error) {
      console.error('Error fetching branding settings:', error)
      onSaveError('Failed to load branding settings')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSliderChange = (field: string) => (event: Event, newValue: number | number[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: newValue as number
    }))
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      const updateData: Partial<BrandingSettings> = {
        primary_color: formData.primaryColor,
        secondary_color: formData.secondaryColor,
        accent_color: formData.accentColor,
        background_color: formData.backgroundColor,
        text_color: formData.textColor,
        font_family: formData.fontFamily,
        logo_url: formData.logoUrl,
        favicon_url: formData.faviconUrl,
        custom_css: formData.customCss,
        enable_dark_mode: formData.enableDarkMode,
        border_radius: formData.borderRadius,
      }
      await updateBrandingSettings(updateData)
      onSaveSuccess()
    } catch (error) {
      console.error('Error saving branding settings:', error)
      onSaveError('Failed to save branding settings')
    } finally {
      setSaving(false)
    }
  }

  const handleReset = () => {
    fetchSettings()
  }

  const colorPresets = [
    { name: 'Default', primary: '#6366f1', secondary: '#8b5cf6' },
    { name: 'Ocean', primary: '#0ea5e9', secondary: '#06b6d4' },
    { name: 'Forest', primary: '#10b981', secondary: '#059669' },
    { name: 'Sunset', primary: '#f59e0b', secondary: '#f97316' },
    { name: 'Rose', primary: '#e11d48', secondary: '#f43f5e' },
    { name: 'Purple', primary: '#7c3aed', secondary: '#a855f7' },
  ]

  const fontOptions = [
    'Inter, sans-serif',
    'Roboto, sans-serif',
    'Open Sans, sans-serif',
    'Lato, sans-serif',
    'Montserrat, sans-serif',
    'Poppins, sans-serif',
  ]

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <CircularProgress />
      </Box>
    )
  }

  return (
    <Box>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
          Branding Settings
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Customize the visual appearance and branding of your platform
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Color Scheme */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Color Scheme"
              subheader="Define your brand colors"
              avatar={<PaletteIcon color="primary" />}
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Primary Color"
                    type="color"
                    value={formData.primaryColor}
                    onChange={handleInputChange('primaryColor')}
                    InputProps={{
                      sx: { height: 56 }
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Secondary Color"
                    type="color"
                    value={formData.secondaryColor}
                    onChange={handleInputChange('secondaryColor')}
                    InputProps={{
                      sx: { height: 56 }
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Accent Color"
                    type="color"
                    value={formData.accentColor}
                    onChange={handleInputChange('accentColor')}
                    InputProps={{
                      sx: { height: 56 }
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Background Color"
                    type="color"
                    value={formData.backgroundColor}
                    onChange={handleInputChange('backgroundColor')}
                    InputProps={{
                      sx: { height: 56 }
                    }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Text Color"
                    type="color"
                    value={formData.textColor}
                    onChange={handleInputChange('textColor')}
                    InputProps={{
                      sx: { height: 56 }
                    }}
                  />
                </Grid>
              </Grid>

              {/* Color Presets */}
              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle2" sx={{ mb: 2 }}>
                  Color Presets
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {colorPresets.map((preset) => (
                    <Chip
                      key={preset.name}
                      label={preset.name}
                      onClick={() => {
                        setFormData(prev => ({
                          ...prev,
                          primaryColor: preset.primary,
                          secondaryColor: preset.secondary,
                        }))
                      }}
                      sx={{
                        background: `linear-gradient(45deg, ${preset.primary}, ${preset.secondary})`,
                        color: 'white',
                        '&:hover': {
                          opacity: 0.8,
                        },
                      }}
                    />
                  ))}
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Typography */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Typography"
              subheader="Font and text styling"
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    select
                    label="Font Family"
                    value={formData.fontFamily}
                    onChange={handleInputChange('fontFamily')}
                    SelectProps={{
                      native: true,
                    }}
                  >
                    {fontOptions.map((font) => (
                      <option key={font} value={font}>
                        {font.split(',')[0]}
                      </option>
                    ))}
                  </TextField>
                </Grid>
                <Grid item xs={12}>
                  <Typography gutterBottom>Border Radius</Typography>
                  <Slider
                    value={formData.borderRadius}
                    onChange={handleSliderChange('borderRadius')}
                    min={0}
                    max={20}
                    step={1}
                    marks
                    valueLabelDisplay="auto"
                    valueLabelFormat={(value) => `${value}px`}
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.enableDarkMode}
                        onChange={handleInputChange('enableDarkMode')}
                      />
                    }
                    label="Enable Dark Mode Support"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Assets */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Brand Assets"
              subheader="Logo and favicon URLs"
              avatar={<ImageIcon color="primary" />}
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Logo URL"
                    value={formData.logoUrl}
                    onChange={handleInputChange('logoUrl')}
                    placeholder="https://example.com/logo.png"
                    helperText="URL to your logo image"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Favicon URL"
                    value={formData.faviconUrl}
                    onChange={handleInputChange('faviconUrl')}
                    placeholder="https://example.com/favicon.ico"
                    helperText="URL to your favicon"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Custom CSS */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Custom CSS"
              subheader="Additional styling"
              avatar={<CodeIcon color="primary" />}
            />
            <CardContent>
              <TextField
                fullWidth
                label="Custom CSS"
                value={formData.customCss}
                onChange={handleInputChange('customCss')}
                multiline
                rows={8}
                placeholder="/* Add your custom CSS here */"
                helperText="Custom CSS will be applied globally"
                sx={{
                  '& .MuiInputBase-input': {
                    fontFamily: 'monospace',
                    fontSize: '0.875rem',
                  },
                }}
              />
            </CardContent>
          </Card>
        </Grid>

        {/* Preview */}
        <Grid item xs={12}>
          <Card>
            <CardHeader title="Preview" subheader="See how your branding looks" />
            <CardContent>
              <Paper
                sx={{
                  p: 3,
                  background: `linear-gradient(45deg, ${formData.primaryColor}, ${formData.secondaryColor})`,
                  color: 'white',
                  borderRadius: `${formData.borderRadius}px`,
                  fontFamily: formData.fontFamily,
                }}
              >
                <Typography variant="h4" sx={{ mb: 2, fontWeight: 600 }}>
                  Kelem SMS Platform
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  This is a preview of how your branding will look with the selected colors and typography.
                </Typography>
                <Button
                  variant="contained"
                  sx={{
                    bgcolor: formData.accentColor,
                    borderRadius: `${formData.borderRadius}px`,
                    '&:hover': {
                      bgcolor: formData.accentColor,
                      opacity: 0.9,
                    },
                  }}
                >
                  Sample Button
                </Button>
              </Paper>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Action Buttons */}
      <Box sx={{ mt: 4, display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={handleReset}
          disabled={saving}
        >
          Reset
        </Button>
        <Button
          variant="contained"
          startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
          onClick={handleSave}
          disabled={saving}
          sx={{
            background: 'linear-gradient(45deg, #6366f1 30%, #8b5cf6 90%)',
            boxShadow: '0 3px 5px 2px rgba(99, 102, 241, .3)',
          }}
        >
          {saving ? 'Saving...' : 'Save Changes'}
        </Button>
      </Box>
    </Box>
  )
}

export default BrandingSettingsTab
