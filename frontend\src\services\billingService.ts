import axios from 'axios'

const API_BASE_URL = 'http://localhost:8001/billing-management/api'

// Create axios instance with default config
const billingApi = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Add auth token to requests
billingApi.interceptors.request.use((config) => {
  const token = localStorage.getItem('token')
  if (token) {
    config.headers.Authorization = `Token ${token}`
  }
  return config
})

// Types
export interface BillingMetrics {
  totalRevenue: number
  monthlyRecurringRevenue: number
  annualRecurringRevenue: number
  totalSubscriptions: number
  activeSubscriptions: number
  trialSubscriptions: number
  cancelledSubscriptions: number
  churnRate: number
  averageRevenuePerUser: number
  lifetimeValue: number
  conversionRate: number
  totalInvoices: number
  paidInvoices: number
  overdueInvoices: number
  totalPayments: number
  successfulPayments: number
  failedPayments: number
  pendingPayments: number // Calculated field for compatibility
  timestamp: string
}

export interface Invoice {
  id: string
  invoiceNumber: string
  tenantId: string
  tenantName: string
  amount: number
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled'
  dueDate: string
  issueDate: string
  plan: string
  description: string
  items: InvoiceItem[]
  subtotal: number
  tax: number
  total: number
  notes?: string
}

export interface InvoiceItem {
  description: string
  quantity: number
  rate: number
  amount: number
}

export interface Subscription {
  id: string
  tenantId: string
  tenantName: string
  plan: string
  status: 'active' | 'cancelled' | 'expired' | 'trial'
  startDate: string
  endDate?: string
  amount: number
  billingCycle: 'monthly' | 'yearly'
  nextBillingDate: string
}

export interface Payment {
  id: string
  invoiceId: string
  tenantId: string
  tenantName: string
  amount: number
  status: 'completed' | 'pending' | 'failed' | 'refunded'
  paymentMethod: string
  transactionId: string
  processedAt: string
}

export interface PlanFeature {
  id: string
  name: string
  description: string
  included: boolean
}

export interface SubscriptionPlan {
  id: string
  name: string
  description: string
  price: number
  billingCycle: 'monthly' | 'yearly'
  features: PlanFeature[]
  maxUsers: number
  maxStorage: number
  isActive: boolean
  isFeatured: boolean
  trialDays: number
  setupFee: number
  currency: string
  category: string
  sortOrder: number
  createdAt: string
  updatedAt: string
}

export interface CreatePlanData {
  name: string
  description: string
  price: number
  billingCycle: 'monthly' | 'yearly'
  maxUsers: number
  maxStorage: number
  isActive: boolean
  isFeatured: boolean
  features: PlanFeature[]
  trialDays: number
  setupFee: number
  currency: string
  category: string
  sortOrder: number
}

// Utility function to transform snake_case API response to camelCase
const transformPlanData = (apiPlan: any): SubscriptionPlan => {
  return {
    id: apiPlan.id,
    name: apiPlan.name,
    description: apiPlan.description,
    price: parseFloat(apiPlan.price),
    billingCycle: apiPlan.billing_cycle,
    features: apiPlan.features || [],
    maxUsers: apiPlan.max_users,
    maxStorage: apiPlan.max_storage,
    isActive: apiPlan.is_active,
    isFeatured: apiPlan.is_featured,
    trialDays: apiPlan.trial_days,
    setupFee: parseFloat(apiPlan.setup_fee),
    currency: apiPlan.currency,
    category: apiPlan.category,
    sortOrder: apiPlan.sort_order,
    createdAt: apiPlan.created_at,
    updatedAt: apiPlan.updated_at,
  }
}

// Utility function to transform camelCase frontend data to snake_case for API
const transformCreatePlanData = (planData: CreatePlanData | Partial<CreatePlanData>): any => {
  return {
    name: planData.name,
    description: planData.description,
    price: planData.price,
    billing_cycle: planData.billingCycle,
    max_users: planData.maxUsers,
    max_storage: planData.maxStorage,
    is_active: planData.isActive,
    is_featured: planData.isFeatured,
    trial_days: planData.trialDays,
    setup_fee: planData.setupFee,
    currency: planData.currency,
    category: planData.category,
    sort_order: planData.sortOrder,
    features: planData.features,
  }
}

// API Functions
export const getBillingMetrics = async (): Promise<BillingMetrics> => {
  try {
    const response = await billingApi.get('/metrics/')
    const data = response.data

    // Transform snake_case API response to camelCase
    return {
      totalRevenue: parseFloat(data.total_revenue),
      monthlyRecurringRevenue: parseFloat(data.monthly_recurring_revenue),
      annualRecurringRevenue: parseFloat(data.annual_recurring_revenue),
      totalSubscriptions: data.total_subscriptions,
      activeSubscriptions: data.active_subscriptions,
      trialSubscriptions: data.trial_subscriptions,
      cancelledSubscriptions: data.cancelled_subscriptions,
      churnRate: data.churn_rate,
      averageRevenuePerUser: parseFloat(data.average_revenue_per_user),
      lifetimeValue: parseFloat(data.lifetime_value),
      conversionRate: data.conversion_rate,
      totalInvoices: data.total_invoices,
      paidInvoices: data.paid_invoices,
      overdueInvoices: data.overdue_invoices,
      totalPayments: data.total_payments,
      successfulPayments: data.successful_payments,
      failedPayments: data.failed_payments,
      pendingPayments: data.total_payments - data.successful_payments - data.failed_payments, // Calculated
      timestamp: data.timestamp,
    }
  } catch (error) {
    console.error('Error fetching billing metrics:', error)
    throw error
  }
}

// Plans API
export const getPlans = async (): Promise<SubscriptionPlan[]> => {
  try {
    const response = await billingApi.get('/plans/')
    // Handle paginated response from Django REST Framework
    if (response.data && response.data.results) {
      return response.data.results.map(transformPlanData)
    }
    // Fallback for non-paginated response
    const plans = response.data || []
    return Array.isArray(plans) ? plans.map(transformPlanData) : []
  } catch (error) {
    console.error('Error fetching plans:', error)
    throw error
  }
}

export const getPlan = async (id: string): Promise<SubscriptionPlan> => {
  try {
    const response = await billingApi.get(`/plans/${id}/`)
    return transformPlanData(response.data)
  } catch (error) {
    console.error('Error fetching plan:', error)
    throw error
  }
}

export const createPlan = async (planData: CreatePlanData): Promise<SubscriptionPlan> => {
  try {
    console.log('Creating plan with data:', planData)
    const apiData = transformCreatePlanData(planData)
    const response = await billingApi.post('/plans/', apiData)
    return transformPlanData(response.data)
  } catch (error: any) {
    console.error('Error creating plan:', error)
    throw error
  }
}

export const updatePlan = async (id: string, planData: Partial<CreatePlanData>): Promise<SubscriptionPlan> => {
  try {
    const apiData = transformCreatePlanData(planData)
    const response = await billingApi.put(`/plans/${id}/`, apiData)
    return transformPlanData(response.data)
  } catch (error) {
    console.error('Error updating plan:', error)
    throw error
  }
}

export const deletePlan = async (id: string): Promise<void> => {
  try {
    await billingApi.delete(`/plans/${id}/`)
  } catch (error) {
    console.error('Error deleting plan:', error)
    throw error
  }
}

export const getInvoices = async (params?: {
  page?: number
  search?: string
  status?: string
}): Promise<{ results: Invoice[]; count: number; next?: string; previous?: string }> => {
  try {
    const response = await billingApi.get('/invoices/', { params })
    return response.data
  } catch (error) {
    console.error('Error fetching invoices:', error)
    throw error
  }
}

export const getInvoice = async (id: string): Promise<Invoice> => {
  try {
    const response = await billingApi.get(`/invoices/${id}/`)
    return response.data
  } catch (error) {
    console.error('Error fetching invoice:', error)
    throw error
  }
}

export const createInvoice = async (invoiceData: Partial<Invoice>): Promise<Invoice> => {
  try {
    const response = await billingApi.post('/invoices/', invoiceData)
    return response.data
  } catch (error) {
    console.error('Error creating invoice:', error)
    throw error
  }
}

export const updateInvoice = async (id: string, invoiceData: Partial<Invoice>): Promise<Invoice> => {
  try {
    const response = await billingApi.put(`/invoices/${id}/`, invoiceData)
    return response.data
  } catch (error) {
    console.error('Error updating invoice:', error)
    throw error
  }
}

export const deleteInvoice = async (id: string): Promise<void> => {
  try {
    await billingApi.delete(`/invoices/${id}/`)
  } catch (error) {
    console.error('Error deleting invoice:', error)
    throw error
  }
}

export const sendInvoice = async (id: string): Promise<void> => {
  try {
    await billingApi.post(`/invoices/${id}/send/`)
  } catch (error) {
    console.error('Error sending invoice:', error)
    throw error
  }
}

export const getSubscriptions = async (params?: {
  page?: number
  search?: string
  status?: string
}): Promise<{ results: Subscription[]; count: number; next?: string; previous?: string }> => {
  try {
    const response = await billingApi.get('/subscriptions/', { params })
    return response.data
  } catch (error) {
    console.error('Error fetching subscriptions:', error)
    throw error
  }
}

export const getSubscription = async (id: string): Promise<Subscription> => {
  try {
    const response = await billingApi.get(`/subscriptions/${id}/`)
    return response.data
  } catch (error) {
    console.error('Error fetching subscription:', error)
    throw error
  }
}

export const createSubscription = async (subscriptionData: Partial<Subscription>): Promise<Subscription> => {
  try {
    const response = await billingApi.post('/subscriptions/', subscriptionData)
    return response.data
  } catch (error) {
    console.error('Error creating subscription:', error)
    throw error
  }
}

export const updateSubscription = async (id: string, subscriptionData: Partial<Subscription>): Promise<Subscription> => {
  try {
    const response = await billingApi.put(`/subscriptions/${id}/`, subscriptionData)
    return response.data
  } catch (error) {
    console.error('Error updating subscription:', error)
    throw error
  }
}

export const cancelSubscription = async (id: string): Promise<void> => {
  try {
    await billingApi.post(`/subscriptions/${id}/cancel/`)
  } catch (error) {
    console.error('Error cancelling subscription:', error)
    throw error
  }
}

export const getPayments = async (params?: {
  page?: number
  search?: string
  status?: string
}): Promise<{ results: Payment[]; count: number; next?: string; previous?: string }> => {
  try {
    const response = await billingApi.get('/payments/', { params })
    return response.data
  } catch (error) {
    console.error('Error fetching payments:', error)
    throw error
  }
}

export const getPayment = async (id: string): Promise<Payment> => {
  try {
    const response = await billingApi.get(`/payments/${id}/`)
    return response.data
  } catch (error) {
    console.error('Error fetching payment:', error)
    throw error
  }
}

export const processPayment = async (paymentData: {
  invoiceId: string
  paymentMethod: string
  amount: number
}): Promise<Payment> => {
  try {
    const response = await billingApi.post('/payments/', paymentData)
    return response.data
  } catch (error) {
    console.error('Error processing payment:', error)
    throw error
  }
}

export const refundPayment = async (id: string, amount?: number): Promise<Payment> => {
  try {
    const response = await billingApi.post(`/payments/${id}/refund/`, { amount })
    return response.data
  } catch (error) {
    console.error('Error refunding payment:', error)
    throw error
  }
}

export const getSubscriptionPlans = async (): Promise<SubscriptionPlan[]> => {
  try {
    const response = await billingApi.get('/plans/')
    return response.data.results || response.data
  } catch (error) {
    console.error('Error fetching subscription plans:', error)
    throw error
  }
}

export const createSubscriptionPlan = async (planData: Partial<SubscriptionPlan>): Promise<SubscriptionPlan> => {
  try {
    const response = await billingApi.post('/plans/', planData)
    return response.data
  } catch (error) {
    console.error('Error creating subscription plan:', error)
    throw error
  }
}

export const updateSubscriptionPlan = async (id: string, planData: Partial<SubscriptionPlan>): Promise<SubscriptionPlan> => {
  try {
    const response = await billingApi.put(`/plans/${id}/`, planData)
    return response.data
  } catch (error) {
    console.error('Error updating subscription plan:', error)
    throw error
  }
}

// Utility functions
export const formatCurrency = (amount: number, currency: string = 'ETB'): string => {
  // Handle ETB formatting specially since it's not widely supported in Intl.NumberFormat
  if (currency === 'ETB') {
    return `Br ${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
  }

  try {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
    }).format(amount)
  } catch (error) {
    // Fallback for unsupported currencies
    return `${currency} ${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
  }
}

export const calculateInvoiceTotal = (items: InvoiceItem[], taxRate: number = 0): number => {
  const subtotal = items.reduce((sum, item) => sum + item.amount, 0)
  const tax = subtotal * taxRate
  return subtotal + tax
}

export const getStatusColor = (status: string): 'success' | 'warning' | 'error' | 'info' | 'default' => {
  switch (status.toLowerCase()) {
    case 'paid':
    case 'completed':
    case 'active':
      return 'success'
    case 'pending':
    case 'sent':
    case 'trial':
      return 'warning'
    case 'failed':
    case 'overdue':
    case 'cancelled':
    case 'expired':
      return 'error'
    case 'processing':
      return 'info'
    default:
      return 'default'
  }
}

export default billingApi
