from django.urls import path
from django.views.generic import TemplateView
from django.http import JsonResponse
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response

# Simple view to serve the Swagger UI
class SwaggerUIView(TemplateView):
    template_name = 'swagger-ui.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['schema_url'] = 'openapi-schema'
        return context

# Simple view to serve a basic OpenAPI schema
@api_view(['GET'])
@permission_classes([AllowAny])
def openapi_schema(request):
    """Return a basic OpenAPI schema"""
    schema = {
        "openapi": "3.0.0",
        "info": {
            "title": "Kelem SMS API",
            "version": "v1",
            "description": "API documentation for Kelem Student Management System"
        },
        "paths": {
            "/api/docs/": {
                "get": {
                    "summary": "API Documentation",
                    "description": "Returns the API documentation",
                    "responses": {
                        "200": {
                            "description": "Successful response"
                        }
                    }
                }
            }
        }
    }
    return Response(schema)

urlpatterns = [
    # Swagger documentation URLs
    path('schema/', openapi_schema, name='openapi-schema'),
    path('swagger/', SwaggerUIView.as_view(), name='swagger-ui'),
    path('', SwaggerUIView.as_view(), name='swagger-ui-default'),
]
