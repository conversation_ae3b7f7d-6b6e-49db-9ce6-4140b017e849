import axios from 'axios'
import { createCachedApiCall } from './cacheService'

const API_BASE_URL = 'http://localhost:8001/api/security'

// Create axios instance for security API calls
const securityApi = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Add auth token to requests
securityApi.interceptors.request.use((config) => {
  const token = localStorage.getItem('token')
  if (token) {
    config.headers.Authorization = `Token ${token}`
  }
  return config
})

// Response interceptor for error handling
securityApi.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('Security API Error:', error)
    return Promise.reject(error)
  }
)

// Types
export interface SecurityEvent {
  id: string
  type: 'login' | 'logout' | 'failed_login' | 'password_change' | 'permission_change' | 'data_access' | 'system_change'
  user: string
  description: string
  timestamp: string
  ip_address: string
  user_agent: string
  location?: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  status: 'success' | 'failed' | 'blocked'
  metadata?: Record<string, any>
}

export interface ActiveSession {
  id: string
  user: string
  user_id: string
  ip_address: string
  location: string
  device: string
  browser: string
  login_time: string
  last_activity: string
  is_current: boolean
  session_key: string
}

export interface SecuritySettings {
  password_policy: {
    min_length: number
    require_uppercase: boolean
    require_lowercase: boolean
    require_numbers: boolean
    require_symbols: boolean
    password_expiry_days: number
    password_history_count: number
  }
  session_settings: {
    session_timeout_minutes: number
    max_concurrent_sessions: number
    require_2fa: boolean
    remember_me_duration_days: number
  }
  login_security: {
    max_failed_attempts: number
    lockout_duration_minutes: number
    enable_captcha: boolean
    enable_ip_whitelist: boolean
    allowed_ips: string[]
  }
  audit_settings: {
    log_retention_days: number
    enable_real_time_alerts: boolean
    alert_email: string
    log_failed_logins: boolean
    log_data_access: boolean
  }
}

export interface SecurityMetrics {
  total_users: number
  active_sessions: number
  failed_logins_24h: number
  security_events_24h: number
  blocked_ips: number
  password_expiring_soon: number
  users_without_2fa: number
  security_score: number
  vulnerability_count: number
  last_security_scan: string
}

export interface SecurityAlert {
  id: string
  type: 'failed_login' | 'suspicious_activity' | 'password_expiry' | 'vulnerability' | 'system_breach'
  title: string
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  timestamp: string
  is_read: boolean
  user_affected?: string
  ip_address?: string
  action_required: boolean
}

export interface IPWhitelistEntry {
  id: string
  ip_address: string
  description: string
  created_by: string
  created_at: string
  is_active: boolean
}

export interface SecurityScanResult {
  id: string
  scan_type: 'vulnerability' | 'penetration' | 'compliance'
  status: 'running' | 'completed' | 'failed'
  started_at: string
  completed_at?: string
  findings: {
    critical: number
    high: number
    medium: number
    low: number
  }
  details?: any
}

// API Functions

// Security Events
const getSecurityEventsUncached = async (params?: any): Promise<{ results: SecurityEvent[]; count: number }> => {
  try {
    const response = await securityApi.get('/events/', { params })
    return response.data
  } catch (error) {
    console.error('Error fetching security events:', error)
    throw error
  }
}

export const getSecurityEvents = createCachedApiCall(
  getSecurityEventsUncached,
  (params?: any) => {
    const currentTenant = JSON.parse(localStorage.getItem('currentTenant') || '{}')
    const tenantId = currentTenant?.id || 'default'
    const paramsStr = params ? JSON.stringify(params) : 'no-params'
    return `security.events.${tenantId}.${paramsStr}`
  },
  1 * 60 * 1000 // 1 minute cache for security events
)

export const getSecurityEvent = async (id: string): Promise<SecurityEvent> => {
  try {
    const response = await securityApi.get(`/events/${id}/`)
    return response.data
  } catch (error) {
    console.error('Error fetching security event:', error)
    throw error
  }
}

// Active Sessions
const getActiveSessionsUncached = async (): Promise<ActiveSession[]> => {
  try {
    const response = await securityApi.get('/sessions/')
    return response.data.results || response.data
  } catch (error) {
    console.error('Error fetching active sessions:', error)
    throw error
  }
}

export const getActiveSessions = createCachedApiCall(
  getActiveSessionsUncached,
  () => {
    const currentTenant = JSON.parse(localStorage.getItem('currentTenant') || '{}')
    const tenantId = currentTenant?.id || 'default'
    return `security.sessions.${tenantId}`
  },
  30 * 1000 // 30 seconds cache for active sessions
)

export const terminateSession = async (sessionId: string): Promise<void> => {
  try {
    await securityApi.delete(`/sessions/${sessionId}/`)
  } catch (error) {
    console.error('Error terminating session:', error)
    throw error
  }
}

export const terminateAllSessions = async (userId?: string): Promise<void> => {
  try {
    const params = userId ? { user_id: userId } : {}
    await securityApi.post('/sessions/terminate-all/', params)
  } catch (error) {
    console.error('Error terminating all sessions:', error)
    throw error
  }
}

// Security Settings
const getSecuritySettingsUncached = async (): Promise<SecuritySettings> => {
  try {
    const response = await securityApi.get('/settings/')
    return response.data
  } catch (error) {
    console.error('Error fetching security settings:', error)
    throw error
  }
}

export const getSecuritySettings = createCachedApiCall(
  getSecuritySettingsUncached,
  () => {
    const currentTenant = JSON.parse(localStorage.getItem('currentTenant') || '{}')
    const tenantId = currentTenant?.id || 'default'
    return `security.settings.${tenantId}`
  },
  5 * 60 * 1000 // 5 minutes cache for settings
)

export const updateSecuritySettings = async (settings: Partial<SecuritySettings>): Promise<SecuritySettings> => {
  try {
    const response = await securityApi.patch('/settings/', settings)
    return response.data
  } catch (error) {
    console.error('Error updating security settings:', error)
    throw error
  }
}

// Security Metrics
const getSecurityMetricsUncached = async (): Promise<SecurityMetrics> => {
  try {
    const response = await securityApi.get('/metrics/')
    return response.data
  } catch (error) {
    console.error('Error fetching security metrics:', error)
    throw error
  }
}

export const getSecurityMetrics = createCachedApiCall(
  getSecurityMetricsUncached,
  () => {
    const currentTenant = JSON.parse(localStorage.getItem('currentTenant') || '{}')
    const tenantId = currentTenant?.id || 'default'
    return `security.metrics.${tenantId}`
  },
  2 * 60 * 1000 // 2 minutes cache for metrics
)

// Security Alerts
export const getSecurityAlerts = async (): Promise<SecurityAlert[]> => {
  try {
    const response = await securityApi.get('/alerts/')
    return response.data.results || response.data
  } catch (error) {
    console.error('Error fetching security alerts:', error)
    throw error
  }
}

export const markAlertAsRead = async (alertId: string): Promise<void> => {
  try {
    await securityApi.patch(`/alerts/${alertId}/`, { is_read: true })
  } catch (error) {
    console.error('Error marking alert as read:', error)
    throw error
  }
}

export const dismissAlert = async (alertId: string): Promise<void> => {
  try {
    await securityApi.delete(`/alerts/${alertId}/`)
  } catch (error) {
    console.error('Error dismissing alert:', error)
    throw error
  }
}

// IP Whitelist
export const getIPWhitelist = async (): Promise<IPWhitelistEntry[]> => {
  try {
    const response = await securityApi.get('/ip-whitelist/')
    return response.data.results || response.data
  } catch (error) {
    console.error('Error fetching IP whitelist:', error)
    throw error
  }
}

export const addIPToWhitelist = async (ip: string, description: string): Promise<IPWhitelistEntry> => {
  try {
    const response = await securityApi.post('/ip-whitelist/', {
      ip_address: ip,
      description,
    })
    return response.data
  } catch (error) {
    console.error('Error adding IP to whitelist:', error)
    throw error
  }
}

export const removeIPFromWhitelist = async (id: string): Promise<void> => {
  try {
    await securityApi.delete(`/ip-whitelist/${id}/`)
  } catch (error) {
    console.error('Error removing IP from whitelist:', error)
    throw error
  }
}

// Security Scans
export const getSecurityScans = async (): Promise<SecurityScanResult[]> => {
  try {
    const response = await securityApi.get('/scans/')
    return response.data.results || response.data
  } catch (error) {
    console.error('Error fetching security scans:', error)
    throw error
  }
}

export const startSecurityScan = async (scanType: 'vulnerability' | 'penetration' | 'compliance'): Promise<SecurityScanResult> => {
  try {
    const response = await securityApi.post('/scans/', { scan_type: scanType })
    return response.data
  } catch (error) {
    console.error('Error starting security scan:', error)
    throw error
  }
}

export const getSecurityScanResult = async (scanId: string): Promise<SecurityScanResult> => {
  try {
    const response = await securityApi.get(`/scans/${scanId}/`)
    return response.data
  } catch (error) {
    console.error('Error fetching security scan result:', error)
    throw error
  }
}

// Audit Logs Export
export const exportAuditLogs = async (startDate: string, endDate: string, format: 'csv' | 'json' | 'pdf' = 'csv'): Promise<Blob> => {
  try {
    const response = await securityApi.get('/events/export/', {
      params: { start_date: startDate, end_date: endDate, format },
      responseType: 'blob',
    })
    return response.data
  } catch (error) {
    console.error('Error exporting audit logs:', error)
    throw error
  }
}

// Password Security
export const checkPasswordStrength = async (password: string): Promise<{ score: number; feedback: string[] }> => {
  try {
    const response = await securityApi.post('/password/check-strength/', { password })
    return response.data
  } catch (error) {
    console.error('Error checking password strength:', error)
    throw error
  }
}

export const getPasswordExpiryReport = async (): Promise<Array<{ user: string; expires_at: string; days_remaining: number }>> => {
  try {
    const response = await securityApi.get('/password/expiry-report/')
    return response.data
  } catch (error) {
    console.error('Error fetching password expiry report:', error)
    throw error
  }
}

// Two-Factor Authentication
export const get2FAStatus = async (): Promise<{ enabled_users: number; total_users: number; percentage: number }> => {
  try {
    const response = await securityApi.get('/2fa/status/')
    return response.data
  } catch (error) {
    console.error('Error fetching 2FA status:', error)
    throw error
  }
}

export const enforce2FA = async (userIds: string[]): Promise<void> => {
  try {
    await securityApi.post('/2fa/enforce/', { user_ids: userIds })
  } catch (error) {
    console.error('Error enforcing 2FA:', error)
    throw error
  }
}

// Utility functions
export const formatEventType = (type: string): string => {
  return type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
}

export const getEventSeverityColor = (severity: string): string => {
  switch (severity) {
    case 'low':
      return '#10b981'
    case 'medium':
      return '#f59e0b'
    case 'high':
      return '#ef4444'
    case 'critical':
      return '#dc2626'
    default:
      return '#6b7280'
  }
}

export const getSecurityScoreColor = (score: number): string => {
  if (score >= 80) return '#10b981'
  if (score >= 60) return '#f59e0b'
  return '#ef4444'
}

export const calculateSecurityScore = (metrics: SecurityMetrics): number => {
  let score = 100
  
  // Deduct points for security issues
  if (metrics.failed_logins_24h > 10) score -= 10
  if (metrics.users_without_2fa > metrics.total_users * 0.5) score -= 20
  if (metrics.password_expiring_soon > 0) score -= 5
  if (metrics.blocked_ips > 5) score -= 10
  if (metrics.vulnerability_count > 0) score -= metrics.vulnerability_count * 2
  
  return Math.max(0, Math.min(100, score))
}

export const isHighRiskEvent = (event: SecurityEvent): boolean => {
  return event.severity === 'high' || event.severity === 'critical' || 
         event.type === 'failed_login' && event.status === 'blocked'
}

export const getDeviceType = (userAgent: string): 'desktop' | 'mobile' | 'tablet' | 'unknown' => {
  const ua = userAgent.toLowerCase()
  if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
    return 'mobile'
  }
  if (ua.includes('tablet') || ua.includes('ipad')) {
    return 'tablet'
  }
  if (ua.includes('windows') || ua.includes('mac') || ua.includes('linux')) {
    return 'desktop'
  }
  return 'unknown'
}

export const getBrowserName = (userAgent: string): string => {
  const ua = userAgent.toLowerCase()
  if (ua.includes('chrome')) return 'Chrome'
  if (ua.includes('firefox')) return 'Firefox'
  if (ua.includes('safari')) return 'Safari'
  if (ua.includes('edge')) return 'Edge'
  if (ua.includes('opera')) return 'Opera'
  return 'Unknown'
}
