import React, { useState } from 'react'
import {
  Box,
  Grid,
  TextField,
  Typography,
  Button,
  Card,
  CardContent,
  CardHeader,
  Switch,
  FormControlLabel,
  Alert,
  CircularProgress,
  Chip,
  Paper,
  Divider,
} from '@mui/material'
import {
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Warning as WarningIcon,
  Build as BuildIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material'

interface MaintenanceTabProps {
  onSaveSuccess: () => void
  onSaveError: (error: string) => void
}

interface MaintenanceForm {
  maintenanceMode: boolean
  maintenanceMessage: string
}

const MaintenanceTab: React.FC<MaintenanceTabProps> = ({
  onSaveSuccess,
  onSaveError,
}) => {
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [formData, setFormData] = useState<MaintenanceForm>({
    maintenanceMode: false,
    maintenanceMessage: 'We are currently performing maintenance to improve your experience. Please check back soon.',
  })

  const handleInputChange = (field: keyof MaintenanceForm) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      onSaveSuccess()
    } catch (error) {
      onSaveError(error instanceof Error ? error.message : 'Unknown error')
    } finally {
      setSaving(false)
    }
  }

  const handleRefresh = async () => {
    setLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
    } catch (error) {
      onSaveError('Failed to refresh maintenance status')
    } finally {
      setLoading(false)
    }
  }

  const handleQuickToggle = async () => {
    setSaving(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
      setFormData(prev => ({ ...prev, maintenanceMode: !prev.maintenanceMode }))
      onSaveSuccess()
    } catch (error) {
      onSaveError('Failed to toggle maintenance mode')
    } finally {
      setSaving(false)
    }
  }

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto' }}>
      <Grid container spacing={3}>
        {/* Current Status */}
        <Grid item xs={12}>
          <Card>
            <CardHeader
              title="Current Maintenance Status"
              subheader="Monitor and control your site's maintenance mode"
            />
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                {formData.maintenanceMode ? (
                  <>
                    <WarningIcon color="warning" sx={{ fontSize: 32 }} />
                    <Box>
                      <Typography variant="h6" color="warning.main" sx={{ fontWeight: 600 }}>
                        Maintenance Mode Active
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Your site is currently in maintenance mode
                      </Typography>
                    </Box>
                    <Chip
                      label="MAINTENANCE"
                      color="warning"
                      variant="filled"
                      sx={{ ml: 'auto', fontWeight: 600 }}
                    />
                  </>
                ) : (
                  <>
                    <CheckCircleIcon color="success" sx={{ fontSize: 32 }} />
                    <Box>
                      <Typography variant="h6" color="success.main" sx={{ fontWeight: 600 }}>
                        Site is Live
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Your site is accessible to all users
                      </Typography>
                    </Box>
                    <Chip
                      label="LIVE"
                      color="success"
                      variant="filled"
                      sx={{ ml: 'auto', fontWeight: 600 }}
                    />
                  </>
                )}
              </Box>
              
              <Button
                variant={formData.maintenanceMode ? "outlined" : "contained"}
                color={formData.maintenanceMode ? "success" : "warning"}
                onClick={handleQuickToggle}
                disabled={saving}
                startIcon={saving ? <CircularProgress size={20} /> : <BuildIcon />}
                sx={{ mr: 2 }}
              >
                {formData.maintenanceMode ? 'Disable Maintenance Mode' : 'Enable Maintenance Mode'}
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* Maintenance Settings */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardHeader
              title="Maintenance Configuration"
              subheader="Configure maintenance mode settings"
            />
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.maintenanceMode}
                        onChange={handleInputChange('maintenanceMode')}
                        color="warning"
                      />
                    }
                    label={
                      <Box>
                        <Typography variant="body1" sx={{ fontWeight: 600 }}>
                          Enable Maintenance Mode
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          When enabled, visitors will see a maintenance page instead of your site
                        </Typography>
                      </Box>
                    }
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <Divider sx={{ my: 2 }} />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Maintenance Message"
                    value={formData.maintenanceMessage}
                    onChange={handleInputChange('maintenanceMessage')}
                    multiline
                    rows={4}
                    helperText="This message will be displayed to visitors during maintenance"
                    placeholder="We are currently performing maintenance to improve your experience. Please check back soon."
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Preview */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardHeader
              title="Maintenance Page Preview"
              subheader="How visitors will see your maintenance page"
            />
            <CardContent>
              <Paper
                sx={{
                  p: 3,
                  background: 'linear-gradient(135deg, #2E7D32 0%, #4CAF50 50%, #FFC107 100%)',
                  color: 'white',
                  textAlign: 'center',
                  minHeight: 200,
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  position: 'relative',
                  overflow: 'hidden',
                }}
              >
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    background: 'rgba(255, 255, 255, 0.95)',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    p: 2,
                  }}
                >
                  <BuildIcon sx={{ fontSize: 40, color: 'warning.main', mb: 2 }} />
                  <Typography variant="h6" sx={{ fontWeight: 700, mb: 1, color: 'text.primary' }}>
                    Under Maintenance
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary', textAlign: 'center' }}>
                    {formData.maintenanceMessage || 'Maintenance message will appear here'}
                  </Typography>
                </Box>
              </Paper>
            </CardContent>
          </Card>
        </Grid>

        {/* Important Information */}
        <Grid item xs={12}>
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
              Important Information
            </Typography>
            <Typography variant="body2">
              • When maintenance mode is enabled, all visitors (except administrators) will see the maintenance page
              <br />
              • Administrators can still access the dashboard and admin areas
              <br />
              • API endpoints will continue to function for mobile apps and integrations
              <br />
              • Search engines will receive a 503 status code indicating temporary unavailability
            </Typography>
          </Alert>
        </Grid>

        {/* Best Practices */}
        <Grid item xs={12}>
          <Alert severity="warning">
            <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
              Best Practices
            </Typography>
            <Typography variant="body2">
              • Schedule maintenance during low-traffic hours
              <br />
              • Provide an estimated time for completion in your message
              <br />
              • Test your changes thoroughly before disabling maintenance mode
              <br />
              • Consider notifying users in advance through email or social media
            </Typography>
          </Alert>
        </Grid>

        {/* Action Buttons */}
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
            <Button
              variant="outlined"
              startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}
              onClick={handleRefresh}
              disabled={loading || saving}
            >
              Refresh
            </Button>
            <Button
              variant="contained"
              startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
              onClick={handleSave}
              disabled={loading || saving}
            >
              Save Changes
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  )
}

export default MaintenanceTab
