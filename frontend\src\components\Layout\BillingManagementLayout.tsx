import { ReactNode } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  Box,
  Typography,
  Breadcrumbs,
  Link,
  Paper,
  Container,
  IconButton,
  Tooltip,
} from '@mui/material'
import {
  Home as HomeIcon,
  NavigateNext as NavigateNextIcon,
  ArrowBack as ArrowBackIcon,
  Receipt as BillingIcon,
} from '@mui/icons-material'

interface BillingManagementLayoutProps {
  children: ReactNode
  title: string
  subtitle?: string
  breadcrumbs?: Array<{
    label: string
    href?: string
  }>
  actions?: ReactNode
  showBreadcrumbs?: boolean
}

const BillingManagementLayout = ({
  children,
  title,
  subtitle,
  breadcrumbs = [],
  actions,
  showBreadcrumbs = true,
}: BillingManagementLayoutProps) => {
  const navigate = useNavigate()

  const defaultBreadcrumbs = [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Billing Management', href: '/dashboard/billing-management' },
    ...breadcrumbs,
  ]

  return (
    <Box sx={{ bgcolor: '#f5f5f5', minHeight: '100vh' }}>
      {/* Header Section */}
      <Paper
        elevation={0}
        sx={{
          bgcolor: 'white',
          color: '#1f2937',
          py: 4,
          px: 3,
          mb: 3,
          borderRadius: 0,
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        }}
      >
        <Container maxWidth="xl">
          {/* Breadcrumbs */}
          {showBreadcrumbs && (
            <Breadcrumbs
              separator={<NavigateNextIcon fontSize="small" sx={{ color: '#6b7280' }} />}
              sx={{ mb: 2 }}
            >
              {defaultBreadcrumbs.map((crumb, index) => (
                <Link
                  key={index}
                  color="inherit"
                  href={crumb.href}
                  onClick={(e) => {
                    e.preventDefault()
                    if (crumb.href) navigate(crumb.href)
                  }}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    color: '#6b7280',
                    textDecoration: 'none',
                    fontSize: '0.875rem',
                    '&:hover': {
                      color: '#6366f1',
                      textDecoration: 'underline',
                    },
                  }}
                >
                  {index === 0 && <HomeIcon sx={{ mr: 0.5, fontSize: '1rem' }} />}
                  {index === 1 && <BillingIcon sx={{ mr: 0.5, fontSize: '1rem' }} />}
                  {crumb.label}
                </Link>
              ))}
            </Breadcrumbs>
          )}

          {/* Header Content */}
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Tooltip title="Go Back">
                <IconButton
                  onClick={() => navigate(-1)}
                  sx={{
                    color: '#6366f1',
                    mr: 2,
                    bgcolor: '#f3f4f6',
                    '&:hover': {
                      bgcolor: '#e5e7eb',
                    },
                  }}
                >
                  <ArrowBackIcon />
                </IconButton>
              </Tooltip>
              <Box>
                <Typography
                  variant="h4"
                  component="h1"
                  sx={{
                    fontWeight: 700,
                    mb: subtitle ? 1 : 0,
                    color: '#1f2937',
                  }}
                >
                  {title}
                </Typography>
                {subtitle && (
                  <Typography
                    variant="body1"
                    sx={{
                      color: '#6b7280',
                      fontWeight: 400,
                    }}
                  >
                    {subtitle}
                  </Typography>
                )}
              </Box>
            </Box>
            {actions && (
              <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                {actions}
              </Box>
            )}
          </Box>
        </Container>
      </Paper>

      {/* Main Content */}
      <Container maxWidth="xl" sx={{ pb: 4 }}>
        {children}
      </Container>
    </Box>
  )
}

export default BillingManagementLayout
