import React, { useState, useEffect } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Grid,
  Chip,
  IconButton,
  Collapse,
  Alert,
} from '@mui/material'
import {
  Speed as SpeedIcon,
  Memory as MemoryIcon,
  Storage as StorageIcon,
  NetworkCheck as NetworkIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
} from '@mui/icons-material'
import { cacheService } from '../services/cacheService'

interface PerformanceMetrics {
  memory: {
    used: number
    total: number
    limit: number
  }
  cache: {
    size: number
    expired: number
    memory: number
  }
  timing: {
    fcp: number
    lcp: number
    cls: number
    fid: number
  }
  network: {
    effectiveType: string
    downlink: number
    rtt: number
  }
}

const PerformanceMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)
  const [expanded, setExpanded] = useState(false)
  const [alerts, setAlerts] = useState<string[]>([])

  useEffect(() => {
    const updateMetrics = () => {
      const newMetrics: PerformanceMetrics = {
        memory: getMemoryMetrics(),
        cache: getCacheMetrics(),
        timing: getTimingMetrics(),
        network: getNetworkMetrics(),
      }
      
      setMetrics(newMetrics)
      checkPerformanceAlerts(newMetrics)
    }

    // Update metrics immediately
    updateMetrics()

    // Update metrics every 5 seconds
    const interval = setInterval(updateMetrics, 5000)

    return () => clearInterval(interval)
  }, [])

  const getMemoryMetrics = () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit,
      }
    }
    return { used: 0, total: 0, limit: 0 }
  }

  const getCacheMetrics = () => {
    return cacheService.getStats()
  }

  const getTimingMetrics = () => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    const paint = performance.getEntriesByType('paint')
    
    const fcp = paint.find(entry => entry.name === 'first-contentful-paint')?.startTime || 0
    const lcp = 0 // Would need PerformanceObserver for real LCP
    
    return {
      fcp,
      lcp,
      cls: 0, // Would need PerformanceObserver for real CLS
      fid: 0, // Would need PerformanceObserver for real FID
    }
  }

  const getNetworkMetrics = () => {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      return {
        effectiveType: connection.effectiveType || 'unknown',
        downlink: connection.downlink || 0,
        rtt: connection.rtt || 0,
      }
    }
    return { effectiveType: 'unknown', downlink: 0, rtt: 0 }
  }

  const checkPerformanceAlerts = (metrics: PerformanceMetrics) => {
    const newAlerts: string[] = []

    // Memory usage alert
    if (metrics.memory.used > metrics.memory.limit * 0.8) {
      newAlerts.push('High memory usage detected')
    }

    // Cache size alert
    if (metrics.cache.size > 50) {
      newAlerts.push('Cache size is getting large')
    }

    // FCP alert
    if (metrics.timing.fcp > 3000) {
      newAlerts.push('Slow First Contentful Paint')
    }

    // Network alert
    if (metrics.network.effectiveType === 'slow-2g' || metrics.network.effectiveType === '2g') {
      newAlerts.push('Slow network connection detected')
    }

    setAlerts(newAlerts)
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getPerformanceScore = () => {
    if (!metrics) return 0
    
    let score = 100
    
    // Memory usage penalty
    const memoryUsage = metrics.memory.used / metrics.memory.limit
    if (memoryUsage > 0.8) score -= 20
    else if (memoryUsage > 0.6) score -= 10
    
    // FCP penalty
    if (metrics.timing.fcp > 3000) score -= 20
    else if (metrics.timing.fcp > 2000) score -= 10
    
    // Cache size penalty
    if (metrics.cache.size > 50) score -= 10
    
    // Network penalty
    if (metrics.network.effectiveType === 'slow-2g' || metrics.network.effectiveType === '2g') {
      score -= 15
    }
    
    return Math.max(0, score)
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'success'
    if (score >= 60) return 'warning'
    return 'error'
  }

  if (!metrics || process.env.NODE_ENV !== 'development') {
    return null
  }

  const score = getPerformanceScore()

  return (
    <Box sx={{ position: 'fixed', bottom: 16, right: 16, zIndex: 1000 }}>
      <Card sx={{ minWidth: 300, maxWidth: 400 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <SpeedIcon color="primary" />
              <Typography variant="h6">Performance</Typography>
              <Chip
                label={`${score}/100`}
                color={getScoreColor(score) as any}
                size="small"
              />
            </Box>
            <IconButton
              size="small"
              onClick={() => setExpanded(!expanded)}
            >
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </Box>

          {alerts.length > 0 && (
            <Alert severity="warning" sx={{ mb: 2, fontSize: '0.75rem' }}>
              {alerts.join(', ')}
            </Alert>
          )}

          <Collapse in={expanded}>
            <Grid container spacing={2}>
              {/* Memory Usage */}
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <MemoryIcon fontSize="small" />
                  <Typography variant="body2">Memory</Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={(metrics.memory.used / metrics.memory.limit) * 100}
                  color={metrics.memory.used > metrics.memory.limit * 0.8 ? 'error' : 'primary'}
                  sx={{ mb: 0.5 }}
                />
                <Typography variant="caption" color="text.secondary">
                  {formatBytes(metrics.memory.used)} / {formatBytes(metrics.memory.limit)}
                </Typography>
              </Grid>

              {/* Cache Stats */}
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <StorageIcon fontSize="small" />
                  <Typography variant="body2">Cache</Typography>
                </Box>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  <Chip label={`${metrics.cache.size} entries`} size="small" />
                  <Chip label={`${metrics.cache.expired} expired`} size="small" color="warning" />
                  <Chip label={formatBytes(metrics.cache.memory)} size="small" />
                </Box>
              </Grid>

              {/* Network Info */}
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <NetworkIcon fontSize="small" />
                  <Typography variant="body2">Network</Typography>
                </Box>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  <Chip label={metrics.network.effectiveType} size="small" />
                  <Chip label={`${metrics.network.downlink} Mbps`} size="small" />
                  <Chip label={`${metrics.network.rtt}ms RTT`} size="small" />
                </Box>
              </Grid>

              {/* Timing Metrics */}
              <Grid item xs={12}>
                <Typography variant="body2" sx={{ mb: 1 }}>Timing</Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  <Chip 
                    label={`FCP: ${metrics.timing.fcp.toFixed(0)}ms`} 
                    size="small" 
                    color={metrics.timing.fcp > 2000 ? 'error' : 'success'}
                  />
                </Box>
              </Grid>
            </Grid>
          </Collapse>
        </CardContent>
      </Card>
    </Box>
  )
}

export default PerformanceMonitor
