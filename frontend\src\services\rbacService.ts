import axios from 'axios'
import { createCachedApiCall } from './cacheService'
import {
  Role,
  User,
  Permission,
  Resource,
  RoleTemplate,
  AccessContext,
  AccessDecision,
  PermissionMatrix,
  RoleAnalytics,
  AccessReview,
  ComplianceReport,
  RBACConfiguration,
  EmergencyAccess,
  RiskAssessment,
  PaginatedResponse,
  BulkOperationResult,
  PermissionCheck,
  PermissionCheckResult,
  RBACEvent,
  ResourceType,
  ActionType,
} from '../types/rbac'

const API_BASE_URL = 'http://localhost:8001/api/rbac'

// Create axios instance for RBAC API calls
const rbacApi = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Add auth token to requests
rbacApi.interceptors.request.use((config) => {
  const token = localStorage.getItem('token')
  if (token) {
    config.headers.Authorization = `Token ${token}`
  }
  return config
})

// Response interceptor for error handling
rbacApi.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('RBAC API Error:', error)
    return Promise.reject(error)
  }
)

// ============================================================================
// ROLE MANAGEMENT
// ============================================================================

const getRolesUncached = async (params?: any): Promise<PaginatedResponse<Role>> => {
  try {
    const response = await rbacApi.get('/roles/', { params })
    return response.data
  } catch (error) {
    console.error('Error fetching roles:', error)
    throw error
  }
}

export const getRoles = createCachedApiCall(
  getRolesUncached,
  (params?: any) => {
    const currentTenant = JSON.parse(localStorage.getItem('currentTenant') || '{}')
    const tenantId = currentTenant?.id || 'default'
    const paramsStr = params ? JSON.stringify(params) : 'no-params'
    return `rbac.roles.${tenantId}.${paramsStr}`
  },
  5 * 60 * 1000 // 5 minutes cache
)

export const getRole = async (id: string): Promise<Role> => {
  try {
    const response = await rbacApi.get(`/roles/${id}/`)
    return response.data
  } catch (error) {
    console.error('Error fetching role:', error)
    throw error
  }
}

export const createRole = async (roleData: Partial<Role>): Promise<Role> => {
  try {
    const response = await rbacApi.post('/roles/', roleData)
    return response.data
  } catch (error) {
    console.error('Error creating role:', error)
    throw error
  }
}

export const updateRole = async (id: string, roleData: Partial<Role>): Promise<Role> => {
  try {
    const response = await rbacApi.patch(`/roles/${id}/`, roleData)
    return response.data
  } catch (error) {
    console.error('Error updating role:', error)
    throw error
  }
}

export const deleteRole = async (id: string): Promise<void> => {
  try {
    await rbacApi.delete(`/roles/${id}/`)
  } catch (error) {
    console.error('Error deleting role:', error)
    throw error
  }
}

export const cloneRole = async (id: string, newName: string): Promise<Role> => {
  try {
    const response = await rbacApi.post(`/roles/${id}/clone/`, { name: newName })
    return response.data
  } catch (error) {
    console.error('Error cloning role:', error)
    throw error
  }
}

export const getRoleHierarchy = async (): Promise<any> => {
  try {
    const response = await rbacApi.get('/roles/hierarchy/')
    return response.data
  } catch (error) {
    console.error('Error fetching role hierarchy:', error)
    throw error
  }
}

export const getRoleAnalytics = async (roleId: string): Promise<RoleAnalytics> => {
  try {
    const response = await rbacApi.get(`/roles/${roleId}/analytics/`)
    return response.data
  } catch (error) {
    console.error('Error fetching role analytics:', error)
    throw error
  }
}

// ============================================================================
// PERMISSION MANAGEMENT
// ============================================================================

const getPermissionsUncached = async (params?: any): Promise<PaginatedResponse<Permission>> => {
  try {
    const response = await rbacApi.get('/permissions/', { params })
    return response.data
  } catch (error) {
    console.error('Error fetching permissions:', error)
    throw error
  }
}

export const getPermissions = createCachedApiCall(
  getPermissionsUncached,
  (params?: any) => {
    const paramsStr = params ? JSON.stringify(params) : 'no-params'
    return `rbac.permissions.${paramsStr}`
  },
  10 * 60 * 1000 // 10 minutes cache
)

export const getPermissionMatrix = async (): Promise<PermissionMatrix[]> => {
  try {
    const response = await rbacApi.get('/permissions/matrix/')
    return response.data
  } catch (error) {
    console.error('Error fetching permission matrix:', error)
    throw error
  }
}

export const createPermission = async (permissionData: Partial<Permission>): Promise<Permission> => {
  try {
    const response = await rbacApi.post('/permissions/', permissionData)
    return response.data
  } catch (error) {
    console.error('Error creating permission:', error)
    throw error
  }
}

export const updatePermission = async (id: string, permissionData: Partial<Permission>): Promise<Permission> => {
  try {
    const response = await rbacApi.patch(`/permissions/${id}/`, permissionData)
    return response.data
  } catch (error) {
    console.error('Error updating permission:', error)
    throw error
  }
}

export const deletePermission = async (id: string): Promise<void> => {
  try {
    await rbacApi.delete(`/permissions/${id}/`)
  } catch (error) {
    console.error('Error deleting permission:', error)
    throw error
  }
}

// ============================================================================
// USER MANAGEMENT
// ============================================================================

const getUsersUncached = async (params?: any): Promise<PaginatedResponse<User>> => {
  try {
    const response = await rbacApi.get('/users/', { params })
    return response.data
  } catch (error) {
    console.error('Error fetching users:', error)
    throw error
  }
}

export const getUsers = createCachedApiCall(
  getUsersUncached,
  (params?: any) => {
    const currentTenant = JSON.parse(localStorage.getItem('currentTenant') || '{}')
    const tenantId = currentTenant?.id || 'default'
    const paramsStr = params ? JSON.stringify(params) : 'no-params'
    return `rbac.users.${tenantId}.${paramsStr}`
  },
  2 * 60 * 1000 // 2 minutes cache
)

export const getUser = async (id: string): Promise<User> => {
  try {
    const response = await rbacApi.get(`/users/${id}/`)
    return response.data
  } catch (error) {
    console.error('Error fetching user:', error)
    throw error
  }
}

export const getUserEffectivePermissions = async (userId: string): Promise<string[]> => {
  try {
    const response = await rbacApi.get(`/users/${userId}/effective-permissions/`)
    return response.data.permissions
  } catch (error) {
    console.error('Error fetching user effective permissions:', error)
    throw error
  }
}

export const assignRoleToUser = async (userId: string, roleId: string): Promise<void> => {
  try {
    await rbacApi.post(`/users/${userId}/roles/`, { role_id: roleId })
  } catch (error) {
    console.error('Error assigning role to user:', error)
    throw error
  }
}

export const removeRoleFromUser = async (userId: string, roleId: string): Promise<void> => {
  try {
    await rbacApi.delete(`/users/${userId}/roles/${roleId}/`)
  } catch (error) {
    console.error('Error removing role from user:', error)
    throw error
  }
}

export const bulkAssignRoles = async (userIds: string[], roleIds: string[]): Promise<BulkOperationResult> => {
  try {
    const response = await rbacApi.post('/users/bulk-assign-roles/', {
      user_ids: userIds,
      role_ids: roleIds,
    })
    return response.data
  } catch (error) {
    console.error('Error bulk assigning roles:', error)
    throw error
  }
}

// ============================================================================
// ACCESS CONTROL
// ============================================================================

export const checkPermission = async (check: PermissionCheck): Promise<PermissionCheckResult> => {
  try {
    const response = await rbacApi.post('/access/check/', check)
    return response.data
  } catch (error) {
    console.error('Error checking permission:', error)
    throw error
  }
}

export const evaluateAccess = async (context: AccessContext): Promise<AccessDecision> => {
  try {
    const response = await rbacApi.post('/access/evaluate/', context)
    return response.data
  } catch (error) {
    console.error('Error evaluating access:', error)
    throw error
  }
}

export const bulkCheckPermissions = async (checks: PermissionCheck[]): Promise<PermissionCheckResult[]> => {
  try {
    const response = await rbacApi.post('/access/bulk-check/', { checks })
    return response.data
  } catch (error) {
    console.error('Error bulk checking permissions:', error)
    throw error
  }
}

// ============================================================================
// ROLE TEMPLATES
// ============================================================================

export const getRoleTemplates = async (): Promise<RoleTemplate[]> => {
  try {
    const response = await rbacApi.get('/role-templates/')
    return response.data.results || response.data
  } catch (error) {
    console.error('Error fetching role templates:', error)
    throw error
  }
}

export const createRoleFromTemplate = async (templateId: string, roleName: string): Promise<Role> => {
  try {
    const response = await rbacApi.post(`/role-templates/${templateId}/create-role/`, {
      name: roleName,
    })
    return response.data
  } catch (error) {
    console.error('Error creating role from template:', error)
    throw error
  }
}

export const saveRoleAsTemplate = async (roleId: string, templateData: Partial<RoleTemplate>): Promise<RoleTemplate> => {
  try {
    const response = await rbacApi.post(`/roles/${roleId}/save-as-template/`, templateData)
    return response.data
  } catch (error) {
    console.error('Error saving role as template:', error)
    throw error
  }
}

// ============================================================================
// ACCESS REVIEWS & COMPLIANCE
// ============================================================================

export const getAccessReviews = async (): Promise<AccessReview[]> => {
  try {
    const response = await rbacApi.get('/access-reviews/')
    return response.data.results || response.data
  } catch (error) {
    console.error('Error fetching access reviews:', error)
    throw error
  }
}

export const createAccessReview = async (reviewData: Partial<AccessReview>): Promise<AccessReview> => {
  try {
    const response = await rbacApi.post('/access-reviews/', reviewData)
    return response.data
  } catch (error) {
    console.error('Error creating access review:', error)
    throw error
  }
}

export const getComplianceReports = async (): Promise<ComplianceReport[]> => {
  try {
    const response = await rbacApi.get('/compliance/reports/')
    return response.data.results || response.data
  } catch (error) {
    console.error('Error fetching compliance reports:', error)
    throw error
  }
}

export const generateComplianceReport = async (type: string, periodStart: string, periodEnd: string): Promise<ComplianceReport> => {
  try {
    const response = await rbacApi.post('/compliance/reports/', {
      type,
      period_start: periodStart,
      period_end: periodEnd,
    })
    return response.data
  } catch (error) {
    console.error('Error generating compliance report:', error)
    throw error
  }
}

// ============================================================================
// RISK ASSESSMENT
// ============================================================================

export const getUserRiskAssessment = async (userId: string): Promise<RiskAssessment> => {
  try {
    const response = await rbacApi.get(`/users/${userId}/risk-assessment/`)
    return response.data
  } catch (error) {
    console.error('Error fetching user risk assessment:', error)
    throw error
  }
}

export const getSystemRiskOverview = async (): Promise<any> => {
  try {
    const response = await rbacApi.get('/risk/overview/')
    return response.data
  } catch (error) {
    console.error('Error fetching system risk overview:', error)
    throw error
  }
}

// ============================================================================
// EMERGENCY ACCESS
// ============================================================================

export const requestEmergencyAccess = async (requestData: Partial<EmergencyAccess>): Promise<EmergencyAccess> => {
  try {
    const response = await rbacApi.post('/emergency-access/', requestData)
    return response.data
  } catch (error) {
    console.error('Error requesting emergency access:', error)
    throw error
  }
}

export const approveEmergencyAccess = async (requestId: string): Promise<EmergencyAccess> => {
  try {
    const response = await rbacApi.post(`/emergency-access/${requestId}/approve/`)
    return response.data
  } catch (error) {
    console.error('Error approving emergency access:', error)
    throw error
  }
}

// ============================================================================
// CONFIGURATION
// ============================================================================

export const getRBACConfiguration = async (): Promise<RBACConfiguration> => {
  try {
    const response = await rbacApi.get('/configuration/')
    return response.data
  } catch (error) {
    console.error('Error fetching RBAC configuration:', error)
    throw error
  }
}

export const updateRBACConfiguration = async (config: Partial<RBACConfiguration>): Promise<RBACConfiguration> => {
  try {
    const response = await rbacApi.patch('/configuration/', config)
    return response.data
  } catch (error) {
    console.error('Error updating RBAC configuration:', error)
    throw error
  }
}

// ============================================================================
// AUDIT & EVENTS
// ============================================================================

export const getRBACEvents = async (params?: any): Promise<PaginatedResponse<RBACEvent>> => {
  try {
    const response = await rbacApi.get('/events/', { params })
    return response.data
  } catch (error) {
    console.error('Error fetching RBAC events:', error)
    throw error
  }
}

export const exportAuditLog = async (startDate: string, endDate: string, format: 'csv' | 'json' | 'pdf' = 'csv'): Promise<Blob> => {
  try {
    const response = await rbacApi.get('/events/export/', {
      params: { start_date: startDate, end_date: endDate, format },
      responseType: 'blob',
    })
    return response.data
  } catch (error) {
    console.error('Error exporting audit log:', error)
    throw error
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

export const hasPermission = (user: User, permission: string, resourceId?: string): boolean => {
  return user.effective_permissions.includes(permission) || user.is_superuser
}

export const hasAnyPermission = (user: User, permissions: string[]): boolean => {
  return permissions.some(permission => hasPermission(user, permission)) || user.is_superuser
}

export const hasAllPermissions = (user: User, permissions: string[]): boolean => {
  return permissions.every(permission => hasPermission(user, permission)) || user.is_superuser
}

export const getRoleLevel = (role: Role): number => {
  return role.level || 0
}

export const isRoleHigherThan = (role1: Role, role2: Role): boolean => {
  return getRoleLevel(role1) > getRoleLevel(role2)
}

export const canManageRole = (userRoles: Role[], targetRole: Role): boolean => {
  const maxUserLevel = Math.max(...userRoles.map(getRoleLevel))
  return maxUserLevel > getRoleLevel(targetRole)
}

export const formatPermissionName = (permission: Permission): string => {
  return `${permission.action.toUpperCase()} ${permission.resource_type.toUpperCase()}`
}

export const getPermissionDescription = (permission: Permission): string => {
  return permission.description || `${permission.action} access to ${permission.resource_type} resources`
}

export const calculateRiskScore = (user: User): number => {
  let score = 0
  
  // High privilege roles increase risk
  if (user.is_superuser) score += 50
  if (user.roles.length > 3) score += 20
  if (user.effective_permissions.length > 50) score += 30
  
  // Account status factors
  if (!user.mfa_enabled) score += 25
  if (user.failed_login_attempts > 0) score += 10
  
  return Math.min(100, score)
}
