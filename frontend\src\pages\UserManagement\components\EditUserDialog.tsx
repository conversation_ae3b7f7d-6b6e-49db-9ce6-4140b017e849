import React, { useState, useEffect } from 'react'
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Typography,
  Box,
  CircularProgress,
  IconButton,
} from '@mui/material'
import { Close as CloseIcon } from '@mui/icons-material'
import { updateUser, type User, type UserUpdate } from '../../../services/userManagementService'

interface EditUserDialogProps {
  open: boolean
  user: User
  onClose: () => void
  onUserUpdated: () => void
  onError: (error: string) => void
}

const EditUserDialog: React.FC<EditUserDialogProps> = ({
  open,
  user,
  onClose,
  onUserUpdated,
  onError,
}) => {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<UserUpdate>({
    firstName: '',
    lastName: '',
    userType: '',
    phoneNumber: '',
    isActive: true,
    isStaff: false,
    isSuperuser: false,
  })

  useEffect(() => {
    if (user) {
      setFormData({
        firstName: user.firstName,
        lastName: user.lastName,
        userType: user.userType,
        phoneNumber: user.phoneNumber,
        isActive: user.isActive,
        isStaff: user.isStaff,
        isSuperuser: user.isSuperuser,
      })
    }
  }, [user])

  const handleInputChange = (field: keyof UserUpdate) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async () => {
    setLoading(true)
    try {
      await updateUser(user.id, formData)
      onUserUpdated()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || 'Failed to update user'
      onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Edit User: {user.fullName || user.email}
          </Typography>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="First Name"
              value={formData.firstName}
              onChange={handleInputChange('firstName')}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Last Name"
              value={formData.lastName}
              onChange={handleInputChange('lastName')}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Phone Number"
              value={formData.phoneNumber}
              onChange={handleInputChange('phoneNumber')}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>User Type</InputLabel>
              <Select
                value={formData.userType}
                label="User Type"
                onChange={(e) => setFormData(prev => ({ ...prev, userType: e.target.value }))}
              >
                <MenuItem value="admin">Admin</MenuItem>
                <MenuItem value="teacher">Teacher</MenuItem>
                <MenuItem value="student">Student</MenuItem>
                <MenuItem value="parent">Parent</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.isActive}
                  onChange={handleInputChange('isActive')}
                />
              }
              label="Active User"
            />
          </Grid>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.isStaff}
                  onChange={handleInputChange('isStaff')}
                />
              }
              label="Staff Member"
            />
          </Grid>
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.isSuperuser}
                  onChange={handleInputChange('isSuperuser')}
                />
              }
              label="Superuser"
            />
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button
          onClick={onClose}
          disabled={loading}
          sx={{
            color: '#6b7280',
            '&:hover': {
              bgcolor: '#f3f4f6',
            },
          }}
        >
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={handleSubmit}
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : null}
          sx={{
            bgcolor: '#6366f1',
            color: 'white',
            '&:hover': {
              bgcolor: '#4f46e5',
            },
          }}
        >
          Update User
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default EditUserDialog
