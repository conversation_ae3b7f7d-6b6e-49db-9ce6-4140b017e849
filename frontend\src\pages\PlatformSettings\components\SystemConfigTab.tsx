import React, { useState } from 'react'
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardHeader,
  TextField,
  Button,
  Typography,
  Switch,
  FormControlLabel,
  Alert,
  Chip,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
} from '@mui/material'
import {
  Storage as StorageIcon,
  Memory as MemoryIcon,
  Speed as SpeedIcon,
  CloudQueue as CloudIcon,
  DataObject as DatabaseIcon,
  Email as EmailIcon,
  Backup as BackupIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material'

interface SystemConfigTabProps {
  onSaveSuccess: () => void
  onSaveError: (error: string) => void
}

const SystemConfigTab: React.FC<SystemConfigTabProps> = ({
  onSaveSuccess,
  onSaveError,
}) => {
  const [formData, setFormData] = useState({
    maxFileSize: 10,
    maxUsers: 1000,
    enableCaching: true,
    cacheTimeout: 3600,
    enableCompression: true,
    enableCdn: false,
    cdnUrl: '',
    backupFrequency: 'daily',
    retentionDays: 30,
    enableNotifications: true,
    smtpHost: '',
    smtpPort: 587,
    smtpUser: '',
    smtpPassword: '',
    enableSsl: true,
  })

  const handleInputChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : 
                  event.target.type === 'number' ? parseInt(event.target.value) : event.target.value
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = async () => {
    try {
      // This would integrate with a system config API
      console.log('Saving system configuration:', formData)
      onSaveSuccess()
    } catch (error) {
      onSaveError('Failed to save system configuration')
    }
  }

  const systemStats = [
    { label: 'CPU Usage', value: 45, color: 'success' },
    { label: 'Memory Usage', value: 68, color: 'warning' },
    { label: 'Disk Usage', value: 32, color: 'success' },
    { label: 'Network I/O', value: 23, color: 'success' },
  ]

  const systemInfo = [
    { label: 'Platform Version', value: '2.1.0' },
    { label: 'Database Version', value: 'PostgreSQL 14.2' },
    { label: 'Python Version', value: '3.11.0' },
    { label: 'Django Version', value: '4.2.0' },
    { label: 'Uptime', value: '15 days, 8 hours' },
    { label: 'Last Backup', value: '2 hours ago' },
  ]

  return (
    <Box>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
          System Configuration
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Configure system-level settings, performance, and infrastructure
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* System Performance */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Performance Settings"
              subheader="System performance and optimization"
              avatar={<SpeedIcon color="primary" />}
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Max File Size (MB)"
                    type="number"
                    value={formData.maxFileSize}
                    onChange={handleInputChange('maxFileSize')}
                    helperText="Maximum file upload size"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Max Concurrent Users"
                    type="number"
                    value={formData.maxUsers}
                    onChange={handleInputChange('maxUsers')}
                    helperText="Maximum simultaneous users"
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.enableCaching}
                        onChange={handleInputChange('enableCaching')}
                      />
                    }
                    label="Enable Caching"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Cache Timeout (seconds)"
                    type="number"
                    value={formData.cacheTimeout}
                    onChange={handleInputChange('cacheTimeout')}
                    disabled={!formData.enableCaching}
                    helperText="Cache expiration time"
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.enableCompression}
                        onChange={handleInputChange('enableCompression')}
                      />
                    }
                    label="Enable Compression"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* CDN Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="CDN Settings"
              subheader="Content delivery network configuration"
              avatar={<CloudIcon color="primary" />}
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.enableCdn}
                        onChange={handleInputChange('enableCdn')}
                      />
                    }
                    label="Enable CDN"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="CDN URL"
                    value={formData.cdnUrl}
                    onChange={handleInputChange('cdnUrl')}
                    disabled={!formData.enableCdn}
                    placeholder="https://cdn.example.com"
                    helperText="CDN endpoint URL"
                  />
                </Grid>
                <Grid item xs={12}>
                  <Alert severity="info">
                    <Typography variant="body2">
                      CDN helps improve performance by serving static assets from geographically distributed servers.
                    </Typography>
                  </Alert>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Backup Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Backup Settings"
              subheader="Data backup and retention"
              avatar={<BackupIcon color="primary" />}
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    select
                    label="Backup Frequency"
                    value={formData.backupFrequency}
                    onChange={handleInputChange('backupFrequency')}
                    SelectProps={{
                      native: true,
                    }}
                  >
                    <option value="hourly">Hourly</option>
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                  </TextField>
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Retention Period (days)"
                    type="number"
                    value={formData.retentionDays}
                    onChange={handleInputChange('retentionDays')}
                    helperText="How long to keep backups"
                  />
                </Grid>
                <Grid item xs={12}>
                  <Button
                    variant="outlined"
                    fullWidth
                    startIcon={<BackupIcon />}
                    onClick={() => {
                      // Trigger manual backup
                      onSaveSuccess()
                    }}
                  >
                    Create Manual Backup
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Email Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Email Settings"
              subheader="SMTP configuration for notifications"
              avatar={<EmailIcon color="primary" />}
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.enableNotifications}
                        onChange={handleInputChange('enableNotifications')}
                      />
                    }
                    label="Enable Email Notifications"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="SMTP Host"
                    value={formData.smtpHost}
                    onChange={handleInputChange('smtpHost')}
                    disabled={!formData.enableNotifications}
                    placeholder="smtp.gmail.com"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="SMTP Port"
                    type="number"
                    value={formData.smtpPort}
                    onChange={handleInputChange('smtpPort')}
                    disabled={!formData.enableNotifications}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="SMTP Username"
                    value={formData.smtpUser}
                    onChange={handleInputChange('smtpUser')}
                    disabled={!formData.enableNotifications}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="SMTP Password"
                    type="password"
                    value={formData.smtpPassword}
                    onChange={handleInputChange('smtpPassword')}
                    disabled={!formData.enableNotifications}
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.enableSsl}
                        onChange={handleInputChange('enableSsl')}
                        disabled={!formData.enableNotifications}
                      />
                    }
                    label="Enable SSL/TLS"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* System Status */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="System Status"
              subheader="Current system performance metrics"
              avatar={<MemoryIcon color="primary" />}
            />
            <CardContent>
              <Grid container spacing={2}>
                {systemStats.map((stat, index) => (
                  <Grid item xs={12} key={index}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body2" sx={{ minWidth: 100 }}>
                        {stat.label}
                      </Typography>
                      <Box sx={{ flexGrow: 1, mx: 2 }}>
                        <LinearProgress
                          variant="determinate"
                          value={stat.value}
                          color={stat.color as any}
                          sx={{ height: 8, borderRadius: 4 }}
                        />
                      </Box>
                      <Typography variant="body2" sx={{ minWidth: 40 }}>
                        {stat.value}%
                      </Typography>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* System Information */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="System Information"
              subheader="Platform and infrastructure details"
              avatar={<DatabaseIcon color="primary" />}
            />
            <CardContent>
              <List dense>
                {systemInfo.map((info, index) => (
                  <React.Fragment key={index}>
                    <ListItem>
                      <ListItemText
                        primary={info.label}
                        secondary={info.value}
                      />
                      <Chip
                        label={info.value}
                        size="small"
                        variant="outlined"
                      />
                    </ListItem>
                    {index < systemInfo.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* System Actions */}
        <Grid item xs={12}>
          <Card>
            <CardHeader
              title="System Actions"
              subheader="Administrative operations"
              avatar={<SettingsIcon color="primary" />}
            />
            <CardContent>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Button
                  variant="outlined"
                  startIcon={<MemoryIcon />}
                  onClick={() => onSaveSuccess()}
                >
                  Clear Cache
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<DatabaseIcon />}
                  onClick={() => onSaveSuccess()}
                >
                  Optimize Database
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<BackupIcon />}
                  onClick={() => onSaveSuccess()}
                >
                  Run Backup
                </Button>
                <Button
                  variant="outlined"
                  color="warning"
                  onClick={() => {
                    if (window.confirm('Are you sure you want to restart the system?')) {
                      onSaveSuccess()
                    }
                  }}
                >
                  Restart System
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Action Buttons */}
      <Box sx={{ mt: 4, display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
        <Button
          variant="outlined"
          onClick={() => window.location.reload()}
        >
          Reset
        </Button>
        <Button
          variant="contained"
          onClick={handleSave}
          sx={{
            background: 'linear-gradient(45deg, #6366f1 30%, #8b5cf6 90%)',
            boxShadow: '0 3px 5px 2px rgba(99, 102, 241, .3)',
          }}
        >
          Save Configuration
        </Button>
      </Box>
    </Box>
  )
}

export default SystemConfigTab
