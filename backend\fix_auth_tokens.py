import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kelem_sms.settings')
django.setup()

from django.db import connection, transaction
from django_tenants.utils import schema_context, get_tenant_model
from rest_framework.authtoken.models import Token
from django.contrib.auth import get_user_model

User = get_user_model()
TenantModel = get_tenant_model()

def fix_auth_tokens():
    """Fix issues with authentication tokens."""
    print("Starting authentication token fix...")
    
    # First, check if the authtoken_token table exists in the public schema
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = current_schema()
                AND table_name = 'authtoken_token'
            )
        """)
        table_exists = cursor.fetchone()[0]
    
    if not table_exists:
        print("The authtoken_token table doesn't exist in the public schema. Creating it...")
        with connection.cursor() as cursor:
            cursor.execute("""
                CREATE TABLE authtoken_token (
                    key character varying(40) NOT NULL PRIMARY KEY,
                    created timestamp with time zone NOT NULL,
                    user_id bigint NOT NULL UNIQUE,
                    CONSTRAINT authtoken_token_user_id_fkey FOREIGN KEY (user_id) REFERENCES authentication_user(id) ON DELETE CASCADE
                )
            """)
        print("Created authtoken_token table.")
    
    # Check for duplicate tokens
    print("Checking for duplicate tokens...")
    try:
        # Get all tokens
        tokens = Token.objects.all()
        user_tokens = {}
        
        # Check for duplicate tokens for the same user
        for token in tokens:
            if token.user_id in user_tokens:
                print(f"Found duplicate token for user ID {token.user_id}")
                # Keep the newest token and delete the older one
                if token.created > user_tokens[token.user_id].created:
                    print(f"Deleting older token {user_tokens[token.user_id].key[:10]}...")
                    user_tokens[token.user_id].delete()
                    user_tokens[token.user_id] = token
                else:
                    print(f"Deleting newer token {token.key[:10]}...")
                    token.delete()
            else:
                user_tokens[token.user_id] = token
        
        # Now check all users and make sure they have a token
        print("Ensuring all users have a token...")
        users = User.objects.all()
        for user in users:
            try:
                token = Token.objects.get(user=user)
                print(f"User {user.email} already has token {token.key[:10]}...")
            except Token.DoesNotExist:
                # Create a new token for this user
                token = Token.objects.create(user=user)
                print(f"Created new token {token.key[:10]} for user {user.email}")
            except Exception as e:
                print(f"Error checking token for user {user.email}: {str(e)}")
    
    except Exception as e:
        print(f"Error fixing tokens in public schema: {str(e)}")
    
    # Now check all tenant schemas
    for tenant in TenantModel.objects.all():
        print(f"Checking schema {tenant.schema_name}...")
        try:
            with schema_context(tenant.schema_name):
                # Check if the authtoken_token table exists
                with connection.cursor() as cursor:
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables
                            WHERE table_schema = current_schema()
                            AND table_name = 'authtoken_token'
                        )
                    """)
                    table_exists = cursor.fetchone()[0]
                
                if not table_exists:
                    print(f"The authtoken_token table doesn't exist in schema {tenant.schema_name}. Creating it...")
                    with connection.cursor() as cursor:
                        cursor.execute("""
                            CREATE TABLE authtoken_token (
                                key character varying(40) NOT NULL PRIMARY KEY,
                                created timestamp with time zone NOT NULL,
                                user_id bigint NOT NULL UNIQUE,
                                CONSTRAINT authtoken_token_user_id_fkey FOREIGN KEY (user_id) REFERENCES authentication_user(id) ON DELETE CASCADE
                            )
                        """)
                    print(f"Created authtoken_token table in schema {tenant.schema_name}.")
                
                # Check for duplicate tokens
                print(f"Checking for duplicate tokens in schema {tenant.schema_name}...")
                try:
                    # Get all tokens
                    tokens = Token.objects.all()
                    user_tokens = {}
                    
                    # Check for duplicate tokens for the same user
                    for token in tokens:
                        if token.user_id in user_tokens:
                            print(f"Found duplicate token for user ID {token.user_id}")
                            # Keep the newest token and delete the older one
                            if token.created > user_tokens[token.user_id].created:
                                print(f"Deleting older token {user_tokens[token.user_id].key[:10]}...")
                                user_tokens[token.user_id].delete()
                                user_tokens[token.user_id] = token
                            else:
                                print(f"Deleting newer token {token.key[:10]}...")
                                token.delete()
                        else:
                            user_tokens[token.user_id] = token
                    
                    # Now check all users and make sure they have a token
                    print(f"Ensuring all users in schema {tenant.schema_name} have a token...")
                    users = User.objects.all()
                    for user in users:
                        try:
                            token = Token.objects.get(user=user)
                            print(f"User {user.email} already has token {token.key[:10]}...")
                        except Token.DoesNotExist:
                            # Create a new token for this user
                            token = Token.objects.create(user=user)
                            print(f"Created new token {token.key[:10]} for user {user.email}")
                        except Exception as e:
                            print(f"Error checking token for user {user.email}: {str(e)}")
                
                except Exception as e:
                    print(f"Error fixing tokens in schema {tenant.schema_name}: {str(e)}")
        
        except Exception as e:
            print(f"Error processing schema {tenant.schema_name}: {str(e)}")
    
    print("Authentication token fix completed.")

if __name__ == '__main__':
    fix_auth_tokens()
