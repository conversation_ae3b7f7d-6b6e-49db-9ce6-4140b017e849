import React, { useState, useEffect } from 'react'
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  CircularProgress,
  Alert,
} from '@mui/material'
import {
  Group as GroupIcon,
  Security as SecurityIcon,
  Add as AddIcon,
} from '@mui/icons-material'
import { getGroups, getPermissions, type Group, type Permission } from '../../../services/userManagementService'

interface GroupManagementTabProps {
  refreshTrigger: number
  onGroupAction: (message: string, severity?: 'success' | 'error' | 'warning' | 'info') => void
}

const GroupManagementTab: React.FC<GroupManagementTabProps> = ({
  refreshTrigger,
  onGroupAction,
}) => {
  const [groups, setGroups] = useState<Group[]>([])
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [loading, setLoading] = useState(false)

  const fetchData = async () => {
    setLoading(true)
    try {
      const [groupsData, permissionsData] = await Promise.all([
        getGroups(),
        getPermissions(),
      ])
      setGroups(groupsData)
      setPermissions(permissionsData)
    } catch (error) {
      onGroupAction('Failed to fetch groups and permissions', 'error')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [refreshTrigger])

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
        <CircularProgress />
      </Box>
    )
  }

  return (
    <Box sx={{ maxWidth: '100%', mx: 'auto' }}>
      <Grid container spacing={3}>
        {/* Groups */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  User Groups
                </Typography>
                <Button
                  variant="outlined"
                  startIcon={<AddIcon />}
                  size="small"
                  onClick={() => onGroupAction('Group creation not implemented yet', 'info')}
                >
                  Add Group
                </Button>
              </Box>

              {groups.length === 0 ? (
                <Alert severity="info">
                  No groups found. Groups help organize users with similar permissions.
                </Alert>
              ) : (
                <List>
                  {groups.map((group) => (
                    <ListItem key={group.id} divider>
                      <ListItemIcon>
                        <GroupIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText
                        primary={group.name}
                        secondary={
                          <Box sx={{ mt: 1 }}>
                            <Typography variant="body2" color="text.secondary">
                              {group.userCount} users • {group.permissions.length} permissions
                            </Typography>
                            <Box sx={{ mt: 1, display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                              {group.permissions.slice(0, 3).map((permission, index) => (
                                <Chip
                                  key={index}
                                  label={permission}
                                  size="small"
                                  variant="outlined"
                                />
                              ))}
                              {group.permissions.length > 3 && (
                                <Chip
                                  label={`+${group.permissions.length - 3} more`}
                                  size="small"
                                  variant="outlined"
                                  color="primary"
                                />
                              )}
                            </Box>
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Permissions */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                Available Permissions
              </Typography>

              {permissions.length === 0 ? (
                <Alert severity="info">
                  No permissions found.
                </Alert>
              ) : (
                <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
                  <List dense>
                    {permissions.slice(0, 20).map((permission) => (
                      <ListItem key={permission.id} divider>
                        <ListItemIcon>
                          <SecurityIcon color="action" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText
                          primary={permission.name}
                          secondary={`${permission.contentTypeName} • ${permission.codename}`}
                          primaryTypographyProps={{ variant: 'body2' }}
                          secondaryTypographyProps={{ variant: 'caption' }}
                        />
                      </ListItem>
                    ))}
                    {permissions.length > 20 && (
                      <ListItem>
                        <ListItemText
                          primary={
                            <Typography variant="body2" color="primary" sx={{ textAlign: 'center' }}>
                              +{permissions.length - 20} more permissions available
                            </Typography>
                          }
                        />
                      </ListItem>
                    )}
                  </List>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Stats */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                Access Control Summary
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main' }}>
                      {groups.length}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      User Groups
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'success.main' }}>
                      {permissions.length}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Available Permissions
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'warning.main' }}>
                      {groups.reduce((sum, group) => sum + group.userCount, 0)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Group Memberships
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  )
}

export default GroupManagementTab
