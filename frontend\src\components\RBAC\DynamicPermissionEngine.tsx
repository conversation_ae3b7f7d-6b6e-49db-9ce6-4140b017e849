import React, { useState, useEffect, useMemo } from 'react'
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Switch,
  FormControlLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material'
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
  Code as CodeIcon,
  Security as SecurityIcon,
  Schedule as ScheduleIcon,
  LocationOn as LocationIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  PlayArrow as PlayArrowIcon,
  Stop as StopIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
} from '@mui/icons-material'
import {
  Permission,
  PermissionCondition,
  AccessContext,
  AccessDecision,
  ResourceType,
  ActionType,
} from '../../types/rbac'

interface DynamicPermissionEngineProps {
  permissions: Permission[]
  onUpdatePermission: (permission: Permission) => void
  onTestPermission: (context: AccessContext) => Promise<AccessDecision>
  onCreatePermission: (permission: Partial<Permission>) => void
  onDeletePermission: (permissionId: string) => void
}

interface ConditionBuilderProps {
  conditions: PermissionCondition[]
  onChange: (conditions: PermissionCondition[]) => void
  availableFields: string[]
}

const ConditionBuilder: React.FC<ConditionBuilderProps> = ({
  conditions,
  onChange,
  availableFields,
}) => {
  const addCondition = () => {
    const newCondition: PermissionCondition = {
      field: '',
      operator: 'eq',
      value: '',
      logical_operator: 'and',
    }
    onChange([...conditions, newCondition])
  }

  const updateCondition = (index: number, updates: Partial<PermissionCondition>) => {
    const newConditions = [...conditions]
    newConditions[index] = { ...newConditions[index], ...updates }
    onChange(newConditions)
  }

  const removeCondition = (index: number) => {
    onChange(conditions.filter((_, i) => i !== index))
  }

  const operators = [
    { value: 'eq', label: 'Equals' },
    { value: 'ne', label: 'Not Equals' },
    { value: 'gt', label: 'Greater Than' },
    { value: 'gte', label: 'Greater Than or Equal' },
    { value: 'lt', label: 'Less Than' },
    { value: 'lte', label: 'Less Than or Equal' },
    { value: 'in', label: 'In List' },
    { value: 'not_in', label: 'Not In List' },
    { value: 'contains', label: 'Contains' },
    { value: 'starts_with', label: 'Starts With' },
    { value: 'ends_with', label: 'Ends With' },
  ]

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="subtitle2">Conditions</Typography>
        <Button
          size="small"
          startIcon={<AddIcon />}
          onClick={addCondition}
        >
          Add Condition
        </Button>
      </Box>

      {conditions.map((condition, index) => (
        <Card key={index} sx={{ mb: 2, p: 2 }}>
          <Grid container spacing={2} alignItems="center">
            {index > 0 && (
              <Grid item xs={12}>
                <FormControl size="small" sx={{ minWidth: 100 }}>
                  <InputLabel>Logic</InputLabel>
                  <Select
                    value={condition.logical_operator || 'and'}
                    onChange={(e) => updateCondition(index, { logical_operator: e.target.value as 'and' | 'or' })}
                    label="Logic"
                  >
                    <MenuItem value="and">AND</MenuItem>
                    <MenuItem value="or">OR</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            )}
            
            <Grid item xs={12} sm={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Field</InputLabel>
                <Select
                  value={condition.field}
                  onChange={(e) => updateCondition(index, { field: e.target.value })}
                  label="Field"
                >
                  {availableFields.map(field => (
                    <MenuItem key={field} value={field}>{field}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Operator</InputLabel>
                <Select
                  value={condition.operator}
                  onChange={(e) => updateCondition(index, { operator: e.target.value as any })}
                  label="Operator"
                >
                  {operators.map(op => (
                    <MenuItem key={op.value} value={op.value}>{op.label}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                size="small"
                label="Value"
                value={condition.value}
                onChange={(e) => updateCondition(index, { value: e.target.value })}
              />
            </Grid>
            
            <Grid item xs={12} sm={2}>
              <IconButton
                color="error"
                onClick={() => removeCondition(index)}
              >
                <DeleteIcon />
              </IconButton>
            </Grid>
          </Grid>
        </Card>
      ))}

      {conditions.length === 0 && (
        <Alert severity="info">
          No conditions defined. This permission will apply to all contexts.
        </Alert>
      )}
    </Box>
  )
}

const DynamicPermissionEngine: React.FC<DynamicPermissionEngineProps> = ({
  permissions,
  onUpdatePermission,
  onTestPermission,
  onCreatePermission,
  onDeletePermission,
}) => {
  const [dialogOpen, setDialogOpen] = useState(false)
  const [testDialogOpen, setTestDialogOpen] = useState(false)
  const [selectedPermission, setSelectedPermission] = useState<Permission | null>(null)
  const [testContext, setTestContext] = useState<Partial<AccessContext>>({})
  const [testResult, setTestResult] = useState<AccessDecision | null>(null)
  const [formData, setFormData] = useState<Partial<Permission>>({
    name: '',
    codename: '',
    description: '',
    resource_type: ResourceType.STUDENT,
    action: ActionType.READ,
    conditions: [],
    attributes: {},
    is_system: false,
  })

  const availableFields = [
    'user.id',
    'user.roles',
    'user.department',
    'user.location',
    'resource.id',
    'resource.owner',
    'resource.department',
    'resource.status',
    'context.time',
    'context.day_of_week',
    'context.ip_address',
    'context.location',
    'tenant.id',
    'tenant.settings',
  ]

  const dynamicPermissions = useMemo(() => {
    return permissions.filter(p => p.conditions && p.conditions.length > 0)
  }, [permissions])

  const handleOpenDialog = (permission?: Permission) => {
    if (permission) {
      setSelectedPermission(permission)
      setFormData({
        name: permission.name,
        codename: permission.codename,
        description: permission.description,
        resource_type: permission.resource_type,
        action: permission.action,
        conditions: permission.conditions || [],
        attributes: permission.attributes,
        is_system: permission.is_system,
      })
    } else {
      setSelectedPermission(null)
      setFormData({
        name: '',
        codename: '',
        description: '',
        resource_type: ResourceType.STUDENT,
        action: ActionType.READ,
        conditions: [],
        attributes: {},
        is_system: false,
      })
    }
    setDialogOpen(true)
  }

  const handleCloseDialog = () => {
    setDialogOpen(false)
    setSelectedPermission(null)
  }

  const handleSave = () => {
    if (selectedPermission) {
      onUpdatePermission({ ...selectedPermission, ...formData } as Permission)
    } else {
      onCreatePermission(formData)
    }
    handleCloseDialog()
  }

  const handleTestPermission = async () => {
    if (!selectedPermission || !testContext.user_id || !testContext.resource_type || !testContext.action) {
      return
    }

    const context: AccessContext = {
      user_id: testContext.user_id!,
      resource_type: testContext.resource_type!,
      resource_id: testContext.resource_id,
      action: testContext.action!,
      tenant_id: testContext.tenant_id,
      additional_context: testContext.additional_context,
      timestamp: new Date().toISOString(),
    }

    try {
      const result = await onTestPermission(context)
      setTestResult(result)
    } catch (error) {
      console.error('Error testing permission:', error)
    }
  }

  const getConditionSummary = (conditions: PermissionCondition[]) => {
    if (!conditions || conditions.length === 0) return 'No conditions'
    
    return conditions.map((condition, index) => {
      const prefix = index > 0 ? ` ${condition.logical_operator?.toUpperCase()} ` : ''
      return `${prefix}${condition.field} ${condition.operator} ${condition.value}`
    }).join('')
  }

  const getPermissionComplexity = (permission: Permission) => {
    const conditionCount = permission.conditions?.length || 0
    if (conditionCount === 0) return { level: 'Simple', color: 'success' }
    if (conditionCount <= 2) return { level: 'Medium', color: 'warning' }
    return { level: 'Complex', color: 'error' }
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h6">Dynamic Permission Engine</Typography>
          <Typography variant="body2" color="text.secondary">
            {dynamicPermissions.length} dynamic permissions configured
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
          sx={{
            background: 'linear-gradient(45deg, #6366f1 30%, #8b5cf6 90%)',
          }}
        >
          Create Dynamic Permission
        </Button>
      </Box>

      {/* Dynamic Permissions List */}
      <Grid container spacing={3}>
        {dynamicPermissions.map((permission) => {
          const complexity = getPermissionComplexity(permission)
          
          return (
            <Grid item xs={12} md={6} lg={4} key={permission.id}>
              <Card>
                <CardHeader
                  title={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <CodeIcon color="primary" />
                      <Typography variant="h6">{permission.name}</Typography>
                    </Box>
                  }
                  action={
                    <Box>
                      <Tooltip title="Test Permission">
                        <IconButton
                          size="small"
                          onClick={() => {
                            setSelectedPermission(permission)
                            setTestDialogOpen(true)
                          }}
                        >
                          <PlayArrowIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Edit Permission">
                        <IconButton
                          size="small"
                          onClick={() => handleOpenDialog(permission)}
                          disabled={permission.is_system}
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete Permission">
                        <IconButton
                          size="small"
                          onClick={() => onDeletePermission(permission.id)}
                          disabled={permission.is_system}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  }
                />
                <CardContent>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {permission.description}
                  </Typography>

                  <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
                    <Chip
                      label={`${permission.action.toUpperCase()} ${permission.resource_type.toUpperCase()}`}
                      size="small"
                      color="primary"
                    />
                    <Chip
                      label={complexity.level}
                      size="small"
                      color={complexity.color as any}
                    />
                    {permission.is_system && (
                      <Chip label="System" size="small" color="warning" />
                    )}
                  </Box>

                  <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                    Conditions:
                  </Typography>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.75rem' }}>
                    {getConditionSummary(permission.conditions || [])}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          )
        })}
      </Grid>

      {dynamicPermissions.length === 0 && (
        <Alert severity="info">
          No dynamic permissions configured. Create dynamic permissions with conditions to enable context-aware access control.
        </Alert>
      )}

      {/* Permission Editor Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          {selectedPermission ? 'Edit Dynamic Permission' : 'Create Dynamic Permission'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Permission Name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Codename"
                  value={formData.codename}
                  onChange={(e) => setFormData({ ...formData, codename: e.target.value })}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  multiline
                  rows={2}
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Resource Type</InputLabel>
                  <Select
                    value={formData.resource_type}
                    onChange={(e) => setFormData({ ...formData, resource_type: e.target.value as ResourceType })}
                    label="Resource Type"
                  >
                    {Object.values(ResourceType).map(type => (
                      <MenuItem key={type} value={type}>
                        {type.replace('_', ' ').toUpperCase()}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Action</InputLabel>
                  <Select
                    value={formData.action}
                    onChange={(e) => setFormData({ ...formData, action: e.target.value as ActionType })}
                    label="Action"
                  >
                    {Object.values(ActionType).map(action => (
                      <MenuItem key={action} value={action}>
                        {action.toUpperCase()}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <ConditionBuilder
                  conditions={formData.conditions || []}
                  onChange={(conditions) => setFormData({ ...formData, conditions })}
                  availableFields={availableFields}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button
            variant="contained"
            onClick={handleSave}
            disabled={!formData.name || !formData.codename}
          >
            {selectedPermission ? 'Update' : 'Create'} Permission
          </Button>
        </DialogActions>
      </Dialog>

      {/* Permission Test Dialog */}
      <Dialog
        open={testDialogOpen}
        onClose={() => setTestDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Test Dynamic Permission
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            {selectedPermission && (
              <Alert severity="info" sx={{ mb: 3 }}>
                Testing permission: <strong>{selectedPermission.name}</strong>
              </Alert>
            )}

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="User ID"
                  value={testContext.user_id || ''}
                  onChange={(e) => setTestContext({ ...testContext, user_id: e.target.value })}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Resource ID"
                  value={testContext.resource_id || ''}
                  onChange={(e) => setTestContext({ ...testContext, resource_id: e.target.value })}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Resource Type</InputLabel>
                  <Select
                    value={testContext.resource_type || ''}
                    onChange={(e) => setTestContext({ ...testContext, resource_type: e.target.value as ResourceType })}
                    label="Resource Type"
                  >
                    {Object.values(ResourceType).map(type => (
                      <MenuItem key={type} value={type}>
                        {type.replace('_', ' ').toUpperCase()}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Action</InputLabel>
                  <Select
                    value={testContext.action || ''}
                    onChange={(e) => setTestContext({ ...testContext, action: e.target.value as ActionType })}
                    label="Action"
                  >
                    {Object.values(ActionType).map(action => (
                      <MenuItem key={action} value={action}>
                        {action.toUpperCase()}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>

            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center' }}>
              <Button
                variant="contained"
                startIcon={<PlayArrowIcon />}
                onClick={handleTestPermission}
                disabled={!testContext.user_id || !testContext.resource_type || !testContext.action}
              >
                Test Permission
              </Button>
            </Box>

            {testResult && (
              <Box sx={{ mt: 3 }}>
                <Alert 
                  severity={testResult.granted ? 'success' : 'error'}
                  icon={testResult.granted ? <CheckCircleIcon /> : <ErrorIcon />}
                >
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                    Access {testResult.granted ? 'Granted' : 'Denied'}
                  </Typography>
                  <Typography variant="body2">
                    {testResult.reason}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Evaluation time: {testResult.evaluation_time_ms}ms
                  </Typography>
                </Alert>
              </Box>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTestDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default DynamicPermissionEngine
