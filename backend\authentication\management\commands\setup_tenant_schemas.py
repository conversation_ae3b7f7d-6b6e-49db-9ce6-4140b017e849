import os
import sys
from django.core.management.base import BaseCommand
from django.db import connection
from django_tenants.utils import get_tenant_model, get_tenant_domain_model
from django.conf import settings

class Command(BaseCommand):
    help = 'Set up tenant schemas and apply migrations'

    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant',
            help='Specify a tenant schema name to set up',
        )

    def handle(self, *args, **options):
        tenant_name = options.get('tenant')
        
        # Get the tenant model
        TenantModel = get_tenant_model()
        
        if tenant_name:
            # Set up a specific tenant
            try:
                tenant = TenantModel.objects.get(schema_name=tenant_name)
                self.setup_tenant(tenant)
            except TenantModel.DoesNotExist:
                self.stdout.write(self.style.ERROR(f'Tenant {tenant_name} does not exist'))
        else:
            # Set up all tenants
            tenants = TenantModel.objects.all()
            for tenant in tenants:
                self.setup_tenant(tenant)
    
    def setup_tenant(self, tenant):
        self.stdout.write(self.style.SUCCESS(f'Setting up tenant: {tenant.name} ({tenant.schema_name})'))
        
        # Connect to the tenant schema
        connection.set_tenant(tenant)
        
        # Apply migrations for the tenant apps
        self.stdout.write(self.style.SUCCESS(f'Applying migrations for tenant: {tenant.schema_name}'))
        
        # Use the Django migrate command
        from django.core.management import call_command
        call_command('migrate', '--schema', tenant.schema_name, verbosity=1)
        
        self.stdout.write(self.style.SUCCESS(f'Successfully set up tenant: {tenant.name} ({tenant.schema_name})'))
