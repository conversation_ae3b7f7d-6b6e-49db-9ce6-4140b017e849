from rest_framework import serializers
from .models import (
    Department, Subject, Course, AcademicTerm, CourseOffering, Enrollment,
    LessonPlan, Resource, Assignment, AssignmentSubmission
)
from django.contrib.auth import get_user_model
from students.serializers import StudentListSerializer

User = get_user_model()

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'user_type']
        read_only_fields = ['id', 'user_type']

class DepartmentSerializer(serializers.ModelSerializer):
    head_name = serializers.SerializerMethodField()
    
    class Meta:
        model = Department
        fields = [
            'id', 'name', 'code', 'description', 'head', 'head_name',
            'contact_email', 'contact_phone', 'office_location', 'website',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_head_name(self, obj):
        if obj.head:
            return f"{obj.head.first_name} {obj.head.last_name}"
        return None

class SubjectSerializer(serializers.ModelSerializer):
    department_name = serializers.SerializerMethodField()
    
    class Meta:
        model = Subject
        fields = [
            'id', 'name', 'code', 'description', 'department', 'department_name',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_department_name(self, obj):
        return obj.department.name if obj.department else None

class CourseListSerializer(serializers.ModelSerializer):
    department_name = serializers.SerializerMethodField()
    subject_name = serializers.SerializerMethodField()
    
    class Meta:
        model = Course
        fields = [
            'id', 'name', 'code', 'subject', 'subject_name', 'department',
            'department_name', 'credits', 'level', 'is_active'
        ]
        read_only_fields = ['id']
    
    def get_department_name(self, obj):
        return obj.department.name if obj.department else None
    
    def get_subject_name(self, obj):
        return obj.subject.name if obj.subject else None

class CourseDetailSerializer(serializers.ModelSerializer):
    department = DepartmentSerializer(read_only=True)
    subject = SubjectSerializer(read_only=True)
    prerequisites = CourseListSerializer(many=True, read_only=True)
    created_by_name = serializers.SerializerMethodField()
    
    class Meta:
        model = Course
        fields = [
            'id', 'name', 'code', 'description', 'subject', 'department',
            'credits', 'hours_per_week', 'level', 'prerequisites',
            'syllabus', 'learning_outcomes', 'assessment_methods',
            'is_active', 'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_created_by_name(self, obj):
        if obj.created_by:
            return f"{obj.created_by.first_name} {obj.created_by.last_name}"
        return None

class AcademicTermSerializer(serializers.ModelSerializer):
    class Meta:
        model = AcademicTerm
        fields = [
            'id', 'name', 'term', 'academic_year', 'year', 'start_date',
            'end_date', 'description', 'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

class CourseOfferingListSerializer(serializers.ModelSerializer):
    course_name = serializers.SerializerMethodField()
    term_name = serializers.SerializerMethodField()
    instructor_name = serializers.SerializerMethodField()
    
    class Meta:
        model = CourseOffering
        fields = [
            'id', 'course', 'course_name', 'term', 'term_name', 'section',
            'instructor', 'instructor_name', 'start_date', 'end_date',
            'current_students', 'max_students', 'status', 'is_active'
        ]
        read_only_fields = ['id', 'current_students']
    
    def get_course_name(self, obj):
        return obj.course.name if obj.course else None
    
    def get_term_name(self, obj):
        return obj.term.name if obj.term else None
    
    def get_instructor_name(self, obj):
        if obj.instructor:
            return f"{obj.instructor.first_name} {obj.instructor.last_name}"
        return None

class CourseOfferingDetailSerializer(serializers.ModelSerializer):
    course = CourseDetailSerializer(read_only=True)
    term = AcademicTermSerializer(read_only=True)
    instructor = UserSerializer(read_only=True)
    assistant_instructors = UserSerializer(many=True, read_only=True)
    
    class Meta:
        model = CourseOffering
        fields = [
            'id', 'course', 'term', 'section', 'instructor', 'assistant_instructors',
            'grade_level', 'classroom', 'schedule_days', 'start_time', 'end_time',
            'max_students', 'min_students', 'current_students', 'start_date', 'end_date',
            'syllabus_url', 'status', 'is_active', 'notes', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'current_students', 'created_at', 'updated_at']

class EnrollmentSerializer(serializers.ModelSerializer):
    student_name = serializers.SerializerMethodField()
    course_name = serializers.SerializerMethodField()
    term_name = serializers.SerializerMethodField()
    
    class Meta:
        model = Enrollment
        fields = [
            'id', 'student', 'student_name', 'course_offering', 'course_name',
            'term_name', 'enrollment_date', 'status', 'grade', 'numeric_grade',
            'grade_points', 'comments', 'last_attendance_date', 'completion_date',
            'is_repeat', 'approved_by', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_student_name(self, obj):
        return obj.student.full_name() if obj.student else None
    
    def get_course_name(self, obj):
        return obj.course_offering.course.name if obj.course_offering and obj.course_offering.course else None
    
    def get_term_name(self, obj):
        return obj.course_offering.term.name if obj.course_offering and obj.course_offering.term else None

class LessonPlanSerializer(serializers.ModelSerializer):
    course_name = serializers.SerializerMethodField()
    created_by_name = serializers.SerializerMethodField()
    
    class Meta:
        model = LessonPlan
        fields = [
            'id', 'course_offering', 'course_name', 'title', 'description',
            'lesson_number', 'lesson_date', 'duration_minutes', 'learning_objectives',
            'materials_required', 'teaching_methods', 'assessment_methods',
            'homework_assignment', 'attachments', 'is_published',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_course_name(self, obj):
        return obj.course_offering.course.name if obj.course_offering and obj.course_offering.course else None
    
    def get_created_by_name(self, obj):
        if obj.created_by:
            return f"{obj.created_by.first_name} {obj.created_by.last_name}"
        return None
    
    def create(self, validated_data):
        # Set the created_by field to the current user
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)

class ResourceSerializer(serializers.ModelSerializer):
    course_name = serializers.SerializerMethodField()
    created_by_name = serializers.SerializerMethodField()
    
    class Meta:
        model = Resource
        fields = [
            'id', 'course', 'course_name', 'course_offering', 'title',
            'description', 'resource_type', 'file', 'url', 'is_public',
            'is_active', 'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_course_name(self, obj):
        return obj.course.name if obj.course else None
    
    def get_created_by_name(self, obj):
        if obj.created_by:
            return f"{obj.created_by.first_name} {obj.created_by.last_name}"
        return None
    
    def create(self, validated_data):
        # Set the created_by field to the current user
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)

class AssignmentSerializer(serializers.ModelSerializer):
    course_name = serializers.SerializerMethodField()
    created_by_name = serializers.SerializerMethodField()
    
    class Meta:
        model = Assignment
        fields = [
            'id', 'course_offering', 'course_name', 'title', 'description',
            'instructions', 'due_date', 'total_marks', 'weight_percentage',
            'attachment', 'status', 'is_group_assignment', 'allow_late_submission',
            'late_submission_deadline', 'late_submission_penalty',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_course_name(self, obj):
        return obj.course_offering.course.name if obj.course_offering and obj.course_offering.course else None
    
    def get_created_by_name(self, obj):
        if obj.created_by:
            return f"{obj.created_by.first_name} {obj.created_by.last_name}"
        return None
    
    def create(self, validated_data):
        # Set the created_by field to the current user
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)

class AssignmentSubmissionSerializer(serializers.ModelSerializer):
    student_name = serializers.SerializerMethodField()
    assignment_title = serializers.SerializerMethodField()
    graded_by_name = serializers.SerializerMethodField()
    
    class Meta:
        model = AssignmentSubmission
        fields = [
            'id', 'assignment', 'assignment_title', 'student', 'student_name',
            'submission_date', 'last_updated', 'status', 'submission_text',
            'attachment', 'is_late', 'marks_obtained', 'feedback',
            'graded_by', 'graded_by_name', 'graded_at'
        ]
        read_only_fields = ['id', 'submission_date', 'last_updated', 'is_late', 'graded_at']
    
    def get_student_name(self, obj):
        return obj.student.full_name() if obj.student else None
    
    def get_assignment_title(self, obj):
        return obj.assignment.title if obj.assignment else None
    
    def get_graded_by_name(self, obj):
        if obj.graded_by:
            return f"{obj.graded_by.first_name} {obj.graded_by.last_name}"
        return None
