import axios from 'axios'

const API_BASE_URL = 'http://localhost:8001/api/site'

// Create axios instance for site settings API calls
const siteSettingsApi = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Add auth token to requests
siteSettingsApi.interceptors.request.use((config) => {
  const token = localStorage.getItem('token')
  if (token) {
    config.headers.Authorization = `Token ${token}`
  }
  return config
})

// Response interceptor for error handling
siteSettingsApi.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('Site Settings API Error:', error)
    return Promise.reject(error)
  }
)

// Types
export interface SiteSettings {
  id: number
  site_name: string
  site_tagline: string
  site_description: string
  contact_email: string
  contact_phone: string
  contact_address: string
  facebook_url: string
  twitter_url: string
  linkedin_url: string
  youtube_url: string
  meta_title: string
  meta_description: string
  meta_keywords: string
  enable_registration: boolean
  enable_trial: boolean
  enable_contact_form: boolean
  enable_blog: boolean
  enable_testimonials: boolean
  created_at: string
  updated_at: string
}

export interface BrandingSettings {
  id: number
  primary_color: string
  secondary_color: string
  accent_color: string
  background_color: string
  text_color: string
  font_family: string
  logo_url: string
  favicon_url: string
  custom_css: string
  enable_dark_mode: boolean
  border_radius: number
  created_at: string
  updated_at: string
}

export interface HeroSection {
  id: number
  title: string
  subtitle: string
  description: string
  background_image: string
  background_video: string
  cta_text: string
  cta_url: string
  secondary_cta_text: string
  secondary_cta_url: string
  show_hero: boolean
  created_at: string
  updated_at: string
}

export interface MaintenanceMode {
  id: number
  maintenance_mode: boolean
  maintenance_title: string
  maintenance_message: string
  maintenance_end_time: string | null
  allowed_ips: string
  show_countdown: boolean
  created_at: string
  updated_at: string
}

export interface SiteConfig {
  site_settings: SiteSettings
  branding_settings: BrandingSettings
  hero_section: HeroSection
  maintenance_mode: MaintenanceMode
}

// API Functions
export const getSiteSettings = async (): Promise<SiteSettings> => {
  try {
    const response = await siteSettingsApi.get('/settings/')
    return response.data
  } catch (error) {
    console.error('Error fetching site settings:', error)
    throw error
  }
}

export const updateSiteSettings = async (data: Partial<SiteSettings>): Promise<SiteSettings> => {
  try {
    const response = await siteSettingsApi.patch('/settings/1/', data)
    return response.data
  } catch (error) {
    console.error('Error updating site settings:', error)
    throw error
  }
}

export const getBrandingSettings = async (): Promise<BrandingSettings> => {
  try {
    const response = await siteSettingsApi.get('/branding/')
    return response.data
  } catch (error) {
    console.error('Error fetching branding settings:', error)
    throw error
  }
}

export const updateBrandingSettings = async (data: Partial<BrandingSettings>): Promise<BrandingSettings> => {
  try {
    const response = await siteSettingsApi.patch('/branding/1/', data)
    return response.data
  } catch (error) {
    console.error('Error updating branding settings:', error)
    throw error
  }
}

export const getHeroSection = async (): Promise<HeroSection> => {
  try {
    const response = await siteSettingsApi.get('/hero/')
    return response.data
  } catch (error) {
    console.error('Error fetching hero section:', error)
    throw error
  }
}

export const updateHeroSection = async (data: Partial<HeroSection>): Promise<HeroSection> => {
  try {
    const response = await siteSettingsApi.patch('/hero/1/', data)
    return response.data
  } catch (error) {
    console.error('Error updating hero section:', error)
    throw error
  }
}

export const getMaintenanceMode = async (): Promise<MaintenanceMode> => {
  try {
    const response = await siteSettingsApi.get('/maintenance/')
    return response.data
  } catch (error) {
    console.error('Error fetching maintenance mode:', error)
    throw error
  }
}

export const updateMaintenanceMode = async (data: Partial<MaintenanceMode>): Promise<MaintenanceMode> => {
  try {
    const response = await siteSettingsApi.patch('/maintenance/1/', data)
    return response.data
  } catch (error) {
    console.error('Error updating maintenance mode:', error)
    throw error
  }
}

export const toggleMaintenanceMode = async (): Promise<{ message: string; data: MaintenanceMode }> => {
  try {
    const response = await siteSettingsApi.post('/maintenance/toggle/')
    return response.data
  } catch (error) {
    console.error('Error toggling maintenance mode:', error)
    throw error
  }
}

export const getAllSiteConfig = async (): Promise<SiteConfig> => {
  try {
    const response = await siteSettingsApi.get('/config/')
    return response.data
  } catch (error) {
    console.error('Error fetching site configuration:', error)
    throw error
  }
}

// Utility functions
export const transformSiteSettingsForForm = (settings: SiteSettings) => ({
  siteName: settings.site_name,
  siteTagline: settings.site_tagline,
  siteDescription: settings.site_description,
  contactEmail: settings.contact_email,
  contactPhone: settings.contact_phone,
  contactAddress: settings.contact_address,
  facebookUrl: settings.facebook_url,
  twitterUrl: settings.twitter_url,
  linkedinUrl: settings.linkedin_url,
  youtubeUrl: settings.youtube_url,
  metaTitle: settings.meta_title,
  metaDescription: settings.meta_description,
  metaKeywords: settings.meta_keywords,
  enableRegistration: settings.enable_registration,
  enableTrial: settings.enable_trial,
  enableContactForm: settings.enable_contact_form,
  enableBlog: settings.enable_blog,
  enableTestimonials: settings.enable_testimonials,
})

export const transformFormDataToSiteSettings = (formData: any): Partial<SiteSettings> => ({
  site_name: formData.siteName,
  site_tagline: formData.siteTagline,
  site_description: formData.siteDescription,
  contact_email: formData.contactEmail,
  contact_phone: formData.contactPhone,
  contact_address: formData.contactAddress,
  facebook_url: formData.facebookUrl,
  twitter_url: formData.twitterUrl,
  linkedin_url: formData.linkedinUrl,
  youtube_url: formData.youtubeUrl,
  meta_title: formData.metaTitle,
  meta_description: formData.metaDescription,
  meta_keywords: formData.metaKeywords,
  enable_registration: formData.enableRegistration,
  enable_trial: formData.enableTrial,
  enable_contact_form: formData.enableContactForm,
  enable_blog: formData.enableBlog,
  enable_testimonials: formData.enableTestimonials,
})
