from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django_tenants.utils import schema_context
from tenants.models import School
import secrets
import string

class Command(BaseCommand):
    help = 'Fix existing tenants by creating admin users for them'

    def add_arguments(self, parser):
        parser.add_argument('--email', type=str, help='Default email for admin users (will be prefixed with schema name)')
        parser.add_argument('--password', type=str, help='Default password for admin users')
        parser.add_argument('--force', action='store_true', help='Create admin users even if they already exist')

    def handle(self, *args, **options):
        default_email = options.get('email', '<EMAIL>')
        default_password = options.get('password')
        force = options.get('force', False)
        
        # Get all tenants
        schools = School.objects.all()
        self.stdout.write(f"Found {schools.count()} schools/tenants")
        
        for school in schools:
            self.stdout.write(f"Processing school: {school.name} with schema: {school.schema_name}")
            
            # Generate email for this tenant
            email_prefix = school.schema_name.replace('_', '.')
            email = f"{email_prefix}@{default_email.split('@')[1]}" if '@' in default_email else default_email
            
            # Generate password if not provided
            password = default_password
            if not password:
                password = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(12))
            
            # Create admin user in the schema
            with schema_context(school.schema_name):
                User = get_user_model()
                
                # Check if any users exist
                user_count = User.objects.count()
                self.stdout.write(f"  Found {user_count} users in schema {school.schema_name}")
                
                # Check if admin user already exists
                if User.objects.filter(is_superuser=True).exists() and not force:
                    self.stdout.write(self.style.WARNING(f"  Admin user already exists in schema {school.schema_name}, skipping"))
                    continue
                
                # Create the admin user
                try:
                    # Check if the User model requires a username field
                    if 'username' in [f.name for f in User._meta.get_fields()]:
                        # Generate a username from the email
                        username = email.split('@')[0]
                        # Make sure it's unique
                        base_username = username
                        counter = 1
                        while User.objects.filter(username=username).exists():
                            username = f"{base_username}{counter}"
                            counter += 1
                        
                        admin_user = User.objects.create_user(
                            username=username,
                            email=email,
                            password=password,
                            first_name="Admin",
                            last_name=school.name,
                            user_type='admin',
                            is_staff=True,
                            is_active=True,
                            is_superuser=True
                        )
                    else:
                        admin_user = User.objects.create_user(
                            email=email,
                            password=password,
                            first_name="Admin",
                            last_name=school.name,
                            user_type='admin',
                            is_staff=True,
                            is_active=True,
                            is_superuser=True
                        )
                    
                    self.stdout.write(self.style.SUCCESS(f"  Created admin user with ID {admin_user.id} in schema {school.schema_name}"))
                    self.stdout.write(f"  Email: {email}")
                    self.stdout.write(f"  Password: {password}")
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f"  Error creating admin user in schema {school.schema_name}: {str(e)}"))
                    import traceback
                    self.stdout.write(traceback.format_exc())
