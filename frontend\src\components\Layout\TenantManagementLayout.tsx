import React from 'react';
import { Box, Typography, Button, Grid } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { ArrowBack as ArrowBackIcon } from '@mui/icons-material';

interface TenantManagementLayoutProps {
  children: React.ReactNode;
  title: string;
  subtitle?: string;
  showBackButton?: boolean;
  backButtonText?: string;
  backButtonPath?: string;
  actions?: React.ReactNode;
  headerStats?: Array<{
    label: string;
    value: string | number;
  }>;
  variant?: 'dashboard' | 'list' | 'detail';
}

const TenantManagementLayout: React.FC<TenantManagementLayoutProps> = ({
  children,
  title,
  subtitle,
  showBackButton = false,
  backButtonText = 'Back',
  backButtonPath = '/dashboard/tenant-management',
  actions,
  headerStats,
  variant = 'dashboard'
}) => {
  const navigate = useNavigate();



  return (
    <Box sx={{ bgcolor: '#f5f5f5', minHeight: '100vh' }}>
      {/* White Header Section */}
      <Box
        sx={{
          bgcolor: 'white',
          color: '#1f2937',
          py: 4,
          px: 3,
          mb: 3,
          borderRadius: 0,
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        }}
      >
        <Box sx={{ width: '100%', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {showBackButton && (
              <Button
                variant="outlined"
                startIcon={<ArrowBackIcon />}
                onClick={() => navigate(backButtonPath)}
                sx={{
                  borderColor: '#6366f1',
                  color: '#6366f1',
                  fontWeight: 600,
                  mr: 3,
                  '&:hover': {
                    borderColor: '#4f46e5',
                    bgcolor: '#f3f4f6',
                  },
                }}
              >
                {backButtonText}
              </Button>
            )}
            <Box>
              <Typography variant="h4" component="h1" sx={{ fontWeight: 700, mb: 1 }}>
                {title}
              </Typography>
              {subtitle && (
                <Typography variant="body1" sx={{ color: '#6b7280' }}>
                  {subtitle}
                </Typography>
              )}
            </Box>
          </Box>
          {actions && (
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              {actions}
            </Box>
          )}
        </Box>
      </Box>

      <Box sx={{ px: 3, pb: 4 }}>
        {/* Quick Stats Cards */}
        {headerStats && headerStats.length > 0 && (
          <Grid container spacing={3} sx={{ mb: 4 }}>
            {headerStats.map((stat, index) => (
              <Grid item xs={6} sm={3} key={index}>
                <Box sx={{
                  bgcolor: 'white',
                  p: 3,
                  textAlign: 'center',
                  borderRadius: 0,
                  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
                }}>
                  <Typography variant="h4" sx={{ fontWeight: 700, mb: 0.5, color: '#1f2937' }}>
                    {stat.value}
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#6b7280' }}>
                    {stat.label}
                  </Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
        )}

        {/* Main Content */}
        <Box sx={{ mx: 0 }}>
          {children}
        </Box>
      </Box>
    </Box>
  );
};

export default TenantManagementLayout;
