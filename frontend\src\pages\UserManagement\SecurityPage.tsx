import React, { useState, useEffect, useMemo } from 'react'
import {
  Box,
  Container,
  Typography,
  Paper,
  Card,
  CardContent,
  CardHeader,
  Grid,
  Button,
  TextField,
  Switch,
  FormControlLabel,
  Alert,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Tabs,
  Tab,
  Avatar,
  Tooltip,
  LinearProgress,
  Badge,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material'
import {
  Security as SecurityIcon,
  Shield as ShieldIcon,
  Lock as LockIcon,
  Visibility as VisibilityIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  History as HistoryIcon,
  Computer as ComputerIcon,
  Smartphone as SmartphoneIcon,
  LocationOn as LocationIcon,
  AccessTime as AccessTimeIcon,
  Vpn<PERSON>ey as VpnKeyIcon,
  AdminPanelSettings as AdminIcon,
  Notifications as NotificationsIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Block as BlockIcon,
  ExpandMore as ExpandMoreIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import {
  getSecurityEvents,
  getActiveSessions,
  getSecuritySettings,
  getSecurityMetrics,
  terminateSession,
  updateSecuritySettings,
  type SecurityEvent,
  type ActiveSession,
  type SecuritySettings,
  type SecurityMetrics,
} from '../../services/securityService'
import SecurityMetricsCard from '../../components/Security/SecurityMetricsCard'
import SecurityEventTimeline from '../../components/Security/SecurityEventTimeline'
import SecuritySettingsPanel from '../../components/Security/SecuritySettingsPanel'

const SecurityPage: React.FC = () => {
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState(0)
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([])
  const [activeSessions, setActiveSessions] = useState<ActiveSession[]>([])
  const [securitySettings, setSecuritySettings] = useState<SecuritySettings | null>(null)
  const [securityMetrics, setSecurityMetrics] = useState<SecurityMetrics | null>(null)
  const [loading, setLoading] = useState(true)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [selectedEvent, setSelectedEvent] = useState<SecurityEvent | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState<string>('all')
  const [filterSeverity, setFilterSeverity] = useState<string>('all')

  useEffect(() => {
    fetchSecurityData()
  }, [])

  const fetchSecurityData = async () => {
    try {
      setLoading(true)

      // Try to fetch real data from APIs with fallback to mock data
      try {
        const [eventsData, sessionsData, settingsData, metricsData] = await Promise.all([
          getSecurityEvents(),
          getActiveSessions(),
          getSecuritySettings(),
          getSecurityMetrics(),
        ])

        setSecurityEvents(eventsData.results || eventsData)
        setActiveSessions(sessionsData)
        setSecuritySettings(settingsData)
        setSecurityMetrics(metricsData)
      } catch (apiError) {
        console.warn('API calls failed, using mock data:', apiError)

        // Fallback to mock security events
      setSecurityEvents([
        {
          id: '1',
          type: 'login',
          user: '<EMAIL>',
          description: 'Successful login from new device',
          timestamp: '2024-01-26T10:30:00Z',
          ip_address: '*************',
          user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          location: 'New York, US',
          severity: 'low',
          status: 'success',
        },
        {
          id: '2',
          type: 'failed_login',
          user: 'unknown',
          description: 'Failed login attempt - invalid credentials',
          timestamp: '2024-01-26T10:25:00Z',
          ip_address: '************',
          user_agent: 'curl/7.68.0',
          location: 'Unknown',
          severity: 'medium',
          status: 'failed',
        },
        {
          id: '3',
          type: 'permission_change',
          user: '<EMAIL>',
          description: 'User role changed from Staff to Teacher',
          timestamp: '2024-01-26T09:15:00Z',
          ip_address: '*************',
          user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          location: 'New York, US',
          severity: 'high',
          status: 'success',
        },
        {
          id: '4',
          type: 'data_access',
          user: '<EMAIL>',
          description: 'Accessed student records - Grade 10A',
          timestamp: '2024-01-26T08:45:00Z',
          ip_address: '*************',
          user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
          location: 'New York, US',
          severity: 'low',
          status: 'success',
        },
        {
          id: '5',
          type: 'system_change',
          user: '<EMAIL>',
          description: 'Security settings updated - Password policy changed',
          timestamp: '2024-01-26T08:00:00Z',
          ip_address: '*************',
          user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          location: 'New York, US',
          severity: 'high',
          status: 'success',
        },
      ])

      // Mock active sessions
      setActiveSessions([
        {
          id: '1',
          user: '<EMAIL>',
          ip_address: '*************',
          location: 'New York, US',
          device: 'Windows Desktop',
          browser: 'Chrome 120.0',
          login_time: '2024-01-26T08:00:00Z',
          last_activity: '2024-01-26T10:30:00Z',
          is_current: true,
        },
        {
          id: '2',
          user: '<EMAIL>',
          ip_address: '*************',
          location: 'New York, US',
          device: 'MacBook Pro',
          browser: 'Safari 17.0',
          login_time: '2024-01-26T08:30:00Z',
          last_activity: '2024-01-26T10:25:00Z',
          is_current: false,
        },
        {
          id: '3',
          user: '<EMAIL>',
          ip_address: '*************',
          location: 'New York, US',
          device: 'iPhone 15',
          browser: 'Mobile Safari',
          login_time: '2024-01-26T09:00:00Z',
          last_activity: '2024-01-26T10:20:00Z',
          is_current: false,
        },
      ])

        // Mock security settings
        setSecuritySettings({
          password_policy: {
            min_length: 8,
            require_uppercase: true,
            require_lowercase: true,
            require_numbers: true,
            require_symbols: false,
            password_expiry_days: 90,
            password_history_count: 5,
          },
          session_settings: {
            session_timeout_minutes: 30,
            max_concurrent_sessions: 3,
            require_2fa: false,
            remember_me_duration_days: 30,
          },
          login_security: {
            max_failed_attempts: 5,
            lockout_duration_minutes: 15,
            enable_captcha: true,
            enable_ip_whitelist: false,
            allowed_ips: [],
          },
          audit_settings: {
            log_retention_days: 365,
            enable_real_time_alerts: true,
            alert_email: '<EMAIL>',
            log_failed_logins: true,
            log_data_access: true,
          },
        })

        // Mock security metrics
        setSecurityMetrics({
          total_users: 127,
          active_sessions: 23,
          failed_logins_24h: 8,
          security_events_24h: 45,
          blocked_ips: 3,
          password_expiring_soon: 12,
          users_without_2fa: 89,
          security_score: 78,
          vulnerability_count: 2,
          last_security_scan: '2024-01-25T10:00:00Z',
        })
      }


    } catch (error) {
      console.error('Error fetching security data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue)
  }

  const handleEventClick = (event: SecurityEvent) => {
    setSelectedEvent(event)
    setDialogOpen(true)
  }

  const handleCloseDialog = () => {
    setDialogOpen(false)
    setSelectedEvent(null)
  }

  const handleTerminateSession = async (sessionId: string) => {
    if (window.confirm('Are you sure you want to terminate this session?')) {
      try {
        await terminateSession(sessionId)
        setActiveSessions(prev => prev.filter(session => session.id !== sessionId))
      } catch (error) {
        console.error('Error terminating session:', error)
        // Fallback to local update
        setActiveSessions(prev => prev.filter(session => session.id !== sessionId))
      }
    }
  }

  const handleUpdateSettings = async (newSettings: SecuritySettings) => {
    try {
      const updatedSettings = await updateSecuritySettings(newSettings)
      setSecuritySettings(updatedSettings)
    } catch (error) {
      console.error('Error updating security settings:', error)
      // Fallback to local update
      setSecuritySettings(newSettings)
    }
  }

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'login':
        return <CheckCircleIcon color="success" />
      case 'logout':
        return <InfoIcon color="info" />
      case 'failed_login':
        return <ErrorIcon color="error" />
      case 'password_change':
        return <VpnKeyIcon color="primary" />
      case 'permission_change':
        return <AdminIcon color="warning" />
      case 'data_access':
        return <VisibilityIcon color="info" />
      case 'system_change':
        return <SettingsIcon color="secondary" />
      default:
        return <InfoIcon />
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low':
        return 'success'
      case 'medium':
        return 'warning'
      case 'high':
        return 'error'
      case 'critical':
        return 'error'
      default:
        return 'default'
    }
  }

  const getDeviceIcon = (device: string) => {
    if (device.toLowerCase().includes('iphone') || device.toLowerCase().includes('android')) {
      return <SmartphoneIcon />
    }
    return <ComputerIcon />
  }

  const filteredEvents = useMemo(() => {
    return securityEvents.filter(event => {
      const matchesSearch = event.user.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           event.ip_address.includes(searchTerm)
      const matchesType = filterType === 'all' || event.type === filterType
      const matchesSeverity = filterSeverity === 'all' || event.severity === filterSeverity
      
      return matchesSearch && matchesType && matchesSeverity
    })
  }, [securityEvents, searchTerm, filterType, filterSeverity])

  const getSecurityScoreColor = (score: number) => {
    if (score >= 80) return 'success'
    if (score >= 60) return 'warning'
    return 'error'
  }

  return (
    <Box sx={{ bgcolor: '#f5f5f5', minHeight: '100vh' }}>
      {/* Header Section */}
      <Paper
        elevation={0}
        sx={{
          bgcolor: 'white',
          color: '#1f2937',
          py: 4,
          px: 3,
          mb: 3,
          borderRadius: 0,
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        }}
      >
        <Container maxWidth="xl">
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box>
              <Typography
                variant="h4"
                component="h1"
                sx={{
                  fontWeight: 700,
                  mb: 1,
                  color: '#1f2937',
                }}
              >
                Security Management
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  color: '#6b7280',
                  fontWeight: 400,
                }}
              >
                Monitor security events, manage settings, and protect your system
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={fetchSecurityData}
                sx={{
                  color: '#6366f1',
                  borderColor: '#6366f1',
                }}
              >
                Refresh
              </Button>
              <Button
                variant="contained"
                startIcon={<DownloadIcon />}
                sx={{
                  background: 'linear-gradient(45deg, #6366f1 30%, #8b5cf6 90%)',
                  boxShadow: '0 3px 5px 2px rgba(99, 102, 241, .3)',
                }}
              >
                Export Logs
              </Button>
            </Box>
          </Box>
        </Container>
      </Paper>

      {/* Main Content */}
      <Container maxWidth="xl">
        {/* Security Metrics Cards */}
        {securityMetrics && (
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6} md={3}>
              <SecurityMetricsCard
                title="Security Score"
                value={securityMetrics.security_score}
                icon={<ShieldIcon />}
                color={getSecurityScoreColor(securityMetrics.security_score) as any}
                progress={{
                  value: securityMetrics.security_score,
                  max: 100,
                }}
                info="Overall security health score based on various factors"
              />
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <SecurityMetricsCard
                title="Active Sessions"
                value={securityMetrics.active_sessions}
                subtitle={`of ${securityMetrics.total_users} users`}
                icon={<ComputerIcon />}
                color="primary"
                info="Currently active user sessions"
              />
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <SecurityMetricsCard
                title="Failed Logins (24h)"
                value={securityMetrics.failed_logins_24h}
                icon={<ErrorIcon />}
                color="error"
                trend={{
                  direction: 'down',
                  percentage: 23,
                  period: 'last week',
                }}
                info="Failed login attempts in the last 24 hours"
              />
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <SecurityMetricsCard
                title="Users without 2FA"
                value={securityMetrics.users_without_2fa}
                subtitle={`${Math.round((securityMetrics.users_without_2fa / securityMetrics.total_users) * 100)}% of users`}
                icon={<WarningIcon />}
                color="warning"
                info="Users who haven't enabled two-factor authentication"
              />
            </Grid>
          </Grid>
        )}

        {/* Tabs */}
        <Paper sx={{ borderRadius: 2 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            sx={{ borderBottom: 1, borderColor: 'divider', px: 3 }}
          >
            <Tab
              icon={<HistoryIcon />}
              label="Audit Logs"
              iconPosition="start"
            />
            <Tab
              icon={<ComputerIcon />}
              label="Active Sessions"
              iconPosition="start"
            />
            <Tab
              icon={<SettingsIcon />}
              label="Security Settings"
              iconPosition="start"
            />
            <Tab
              icon={<NotificationsIcon />}
              label="Alerts & Monitoring"
              iconPosition="start"
            />
          </Tabs>

          {/* Tab Content */}
          <Box sx={{ p: 3 }}>
            {/* Audit Logs Tab */}
            {activeTab === 0 && (
              <Box>
                {/* Filters */}
                <Grid container spacing={2} sx={{ mb: 3 }}>
                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      placeholder="Search events..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Event Type</InputLabel>
                      <Select
                        value={filterType}
                        onChange={(e) => setFilterType(e.target.value)}
                        label="Event Type"
                      >
                        <MenuItem value="all">All Types</MenuItem>
                        <MenuItem value="login">Login</MenuItem>
                        <MenuItem value="logout">Logout</MenuItem>
                        <MenuItem value="failed_login">Failed Login</MenuItem>
                        <MenuItem value="password_change">Password Change</MenuItem>
                        <MenuItem value="permission_change">Permission Change</MenuItem>
                        <MenuItem value="data_access">Data Access</MenuItem>
                        <MenuItem value="system_change">System Change</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Severity</InputLabel>
                      <Select
                        value={filterSeverity}
                        onChange={(e) => setFilterSeverity(e.target.value)}
                        label="Severity"
                      >
                        <MenuItem value="all">All Severities</MenuItem>
                        <MenuItem value="low">Low</MenuItem>
                        <MenuItem value="medium">Medium</MenuItem>
                        <MenuItem value="high">High</MenuItem>
                        <MenuItem value="critical">Critical</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>

                {/* Events Table */}
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Event</TableCell>
                        <TableCell>User</TableCell>
                        <TableCell>IP Address</TableCell>
                        <TableCell>Location</TableCell>
                        <TableCell>Severity</TableCell>
                        <TableCell>Time</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {filteredEvents.map((event) => (
                        <TableRow key={event.id} hover>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              {getEventIcon(event.type)}
                              <Box>
                                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                  {event.description}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {event.type.replace('_', ' ').toUpperCase()}
                                </Typography>
                              </Box>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">{event.user}</Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                              {event.ip_address}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                              <LocationIcon fontSize="small" color="action" />
                              <Typography variant="body2">{event.location || 'Unknown'}</Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={event.severity.toUpperCase()}
                              size="small"
                              color={getSeverityColor(event.severity) as any}
                            />
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {new Date(event.timestamp).toLocaleString()}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Tooltip title="View Details">
                              <IconButton
                                size="small"
                                onClick={() => handleEventClick(event)}
                              >
                                <VisibilityIcon />
                              </IconButton>
                            </Tooltip>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            )}

            {/* Active Sessions Tab */}
            {activeTab === 1 && (
              <Box>
                <Typography variant="h6" sx={{ mb: 3 }}>
                  Active User Sessions
                </Typography>

                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>User</TableCell>
                        <TableCell>Device</TableCell>
                        <TableCell>Location</TableCell>
                        <TableCell>Login Time</TableCell>
                        <TableCell>Last Activity</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {activeSessions.map((session) => (
                        <TableRow key={session.id}>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                              <Avatar sx={{ bgcolor: '#6366f1', width: 32, height: 32 }}>
                                {session.user.charAt(0).toUpperCase()}
                              </Avatar>
                              <Typography variant="body2">{session.user}</Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              {getDeviceIcon(session.device)}
                              <Box>
                                <Typography variant="body2">{session.device}</Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {session.browser}
                                </Typography>
                              </Box>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                              <LocationIcon fontSize="small" color="action" />
                              <Typography variant="body2">{session.location}</Typography>
                            </Box>
                            <Typography variant="caption" color="text.secondary" sx={{ fontFamily: 'monospace' }}>
                              {session.ip_address}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {new Date(session.login_time).toLocaleString()}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {new Date(session.last_activity).toLocaleString()}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            {session.is_current ? (
                              <Chip label="Current Session" size="small" color="success" />
                            ) : (
                              <Chip label="Active" size="small" color="primary" />
                            )}
                          </TableCell>
                          <TableCell>
                            <Tooltip title="Terminate Session">
                              <IconButton
                                size="small"
                                onClick={() => handleTerminateSession(session.id)}
                                disabled={session.is_current}
                                color="error"
                              >
                                <BlockIcon />
                              </IconButton>
                            </Tooltip>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            )}

            {/* Security Settings Tab */}
            {activeTab === 2 && securitySettings && (
              <SecuritySettingsPanel
                settings={securitySettings}
                onUpdateSettings={handleUpdateSettings}
                loading={loading}
              />
            )}

            {/* Alerts & Monitoring Tab */}
            {activeTab === 3 && (
              <Box>
                <Typography variant="h6" sx={{ mb: 3 }}>
                  Security Alerts & Monitoring
                </Typography>

                <Grid container spacing={3}>
                  {/* Security Alerts */}
                  <Grid item xs={12} md={8}>
                    <Card>
                      <CardHeader
                        title={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <NotificationsIcon color="primary" />
                            <Typography variant="h6">Recent Security Alerts</Typography>
                          </Box>
                        }
                      />
                      <CardContent>
                        <List>
                          <ListItem>
                            <ListItemIcon>
                              <Badge badgeContent="!" color="error">
                                <WarningIcon color="error" />
                              </Badge>
                            </ListItemIcon>
                            <ListItemText
                              primary="Multiple failed login attempts detected"
                              secondary="IP: ************ - 5 attempts in the last hour"
                            />
                          </ListItem>
                          <Divider />
                          <ListItem>
                            <ListItemIcon>
                              <Badge badgeContent="!" color="warning">
                                <InfoIcon color="warning" />
                              </Badge>
                            </ListItemIcon>
                            <ListItemText
                              primary="12 users have passwords expiring soon"
                              secondary="Passwords will expire within the next 7 days"
                            />
                          </ListItem>
                          <Divider />
                          <ListItem>
                            <ListItemIcon>
                              <Badge badgeContent="!" color="info">
                                <CheckCircleIcon color="success" />
                              </Badge>
                            </ListItemIcon>
                            <ListItemText
                              primary="Security scan completed successfully"
                              secondary="No vulnerabilities detected in the last scan"
                            />
                          </ListItem>
                        </List>
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* Security Trends */}
                  <Grid item xs={12} md={4}>
                    <Card>
                      <CardHeader
                        title={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <TrendingUpIcon color="primary" />
                            <Typography variant="h6">Security Trends</Typography>
                          </Box>
                        }
                      />
                      <CardContent>
                        <Box sx={{ mb: 2 }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                            <Typography variant="body2">Failed Logins</Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                              <TrendingDownIcon fontSize="small" color="success" />
                              <Typography variant="body2" color="success.main">-23%</Typography>
                            </Box>
                          </Box>
                          <LinearProgress variant="determinate" value={15} color="success" />
                        </Box>

                        <Box sx={{ mb: 2 }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                            <Typography variant="body2">Security Events</Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                              <TrendingUpIcon fontSize="small" color="warning" />
                              <Typography variant="body2" color="warning.main">+12%</Typography>
                            </Box>
                          </Box>
                          <LinearProgress variant="determinate" value={65} color="warning" />
                        </Box>

                        <Box>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                            <Typography variant="body2">Active Sessions</Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                              <TrendingUpIcon fontSize="small" color="info" />
                              <Typography variant="body2" color="info.main">+8%</Typography>
                            </Box>
                          </Box>
                          <LinearProgress variant="determinate" value={45} color="info" />
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </Box>
            )}
          </Box>
        </Paper>

        {/* Event Details Dialog */}
        <Dialog
          open={dialogOpen}
          onClose={handleCloseDialog}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            Security Event Details
          </DialogTitle>
          <DialogContent>
            {selectedEvent && (
              <Box sx={{ pt: 2 }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Event Type
                    </Typography>
                    <Typography variant="body1" sx={{ mb: 2 }}>
                      {selectedEvent.type.replace('_', ' ').toUpperCase()}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Severity
                    </Typography>
                    <Chip
                      label={selectedEvent.severity.toUpperCase()}
                      size="small"
                      color={getSeverityColor(selectedEvent.severity) as any}
                      sx={{ mb: 2 }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Description
                    </Typography>
                    <Typography variant="body1" sx={{ mb: 2 }}>
                      {selectedEvent.description}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      User
                    </Typography>
                    <Typography variant="body1" sx={{ mb: 2 }}>
                      {selectedEvent.user}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      IP Address
                    </Typography>
                    <Typography variant="body1" sx={{ fontFamily: 'monospace', mb: 2 }}>
                      {selectedEvent.ip_address}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Location
                    </Typography>
                    <Typography variant="body1" sx={{ mb: 2 }}>
                      {selectedEvent.location || 'Unknown'}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Timestamp
                    </Typography>
                    <Typography variant="body1" sx={{ mb: 2 }}>
                      {new Date(selectedEvent.timestamp).toLocaleString()}
                    </Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" color="text.secondary">
                      User Agent
                    </Typography>
                    <Typography variant="body2" sx={{ fontFamily: 'monospace', wordBreak: 'break-all' }}>
                      {selectedEvent.user_agent}
                    </Typography>
                  </Grid>
                </Grid>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Close</Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Box>
  )
}

export default SecurityPage
