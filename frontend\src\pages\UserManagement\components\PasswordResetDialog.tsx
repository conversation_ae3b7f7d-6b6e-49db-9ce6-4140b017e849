import React, { useState } from 'react'
import {
  <PERSON>alog,
  DialogT<PERSON>le,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  CircularProgress,
  IconButton,
  InputAdornment,
  Alert,
} from '@mui/material'
import {
  Close as CloseIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
} from '@mui/icons-material'
import { resetUserPassword, type User, type PasswordReset } from '../../../services/userManagementService'

interface PasswordResetDialogProps {
  open: boolean
  user: User
  onClose: () => void
  onPasswordReset: () => void
  onError: (error: string) => void
}

const PasswordResetDialog: React.FC<PasswordResetDialogProps> = ({
  open,
  user,
  onClose,
  onPasswordReset,
  onError,
}) => {
  const [loading, setLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showPasswordConfirm, setShowPasswordConfirm] = useState(false)
  const [formData, setFormData] = useState<PasswordReset>({
    newPassword: '',
    newPasswordConfirm: '',
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  const handleInputChange = (field: keyof PasswordReset) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFormData(prev => ({ ...prev, [field]: event.target.value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.newPassword) {
      newErrors.newPassword = 'Password is required'
    } else if (formData.newPassword.length < 8) {
      newErrors.newPassword = 'Password must be at least 8 characters'
    }

    if (!formData.newPasswordConfirm) {
      newErrors.newPasswordConfirm = 'Password confirmation is required'
    } else if (formData.newPassword !== formData.newPasswordConfirm) {
      newErrors.newPasswordConfirm = 'Passwords do not match'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async () => {
    if (!validateForm()) {
      return
    }

    setLoading(true)
    try {
      await resetUserPassword(user.id, formData)
      onPasswordReset()
      handleClose()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || 'Failed to reset password'
      onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setFormData({ newPassword: '', newPasswordConfirm: '' })
    setErrors({})
    setShowPassword(false)
    setShowPasswordConfirm(false)
    onClose()
  }

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Reset Password
          </Typography>
          <IconButton onClick={handleClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        <Alert severity="info" sx={{ mb: 3 }}>
          Resetting password for: <strong>{user.fullName || user.email}</strong>
        </Alert>

        <TextField
          fullWidth
          label="New Password"
          type={showPassword ? 'text' : 'password'}
          value={formData.newPassword}
          onChange={handleInputChange('newPassword')}
          error={!!errors.newPassword}
          helperText={errors.newPassword}
          sx={{ mb: 2 }}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <IconButton
                  onClick={() => setShowPassword(!showPassword)}
                  edge="end"
                >
                  {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                </IconButton>
              </InputAdornment>
            ),
          }}
        />

        <TextField
          fullWidth
          label="Confirm New Password"
          type={showPasswordConfirm ? 'text' : 'password'}
          value={formData.newPasswordConfirm}
          onChange={handleInputChange('newPasswordConfirm')}
          error={!!errors.newPasswordConfirm}
          helperText={errors.newPasswordConfirm}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <IconButton
                  onClick={() => setShowPasswordConfirm(!showPasswordConfirm)}
                  edge="end"
                >
                  {showPasswordConfirm ? <VisibilityOffIcon /> : <VisibilityIcon />}
                </IconButton>
              </InputAdornment>
            ),
          }}
        />
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button onClick={handleClose} disabled={loading}>
          Cancel
        </Button>
        <Button
          variant="contained"
          color="warning"
          onClick={handleSubmit}
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : null}
        >
          Reset Password
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default PasswordResetDialog
