import { useState } from 'react'
import {
  <PERSON>,
  Container,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  TextField,
  Button,
  Paper,
  Alert,
  useTheme,
  useMediaQuery,
} from '@mui/material'
import {
  Email as EmailIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material'

const ContactPage = () => {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    school: '',
    subject: '',
    message: '',
  })
  const [submitted, setSubmitted] = useState(false)

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Here you would typically send the form data to your backend
    console.log('Contact form submitted:', formData)
    setSubmitted(true)
    
    // Reset form after 3 seconds
    setTimeout(() => {
      setSubmitted(false)
      setFormData({
        name: '',
        email: '',
        school: '',
        subject: '',
        message: '',
      })
    }, 3000)
  }

  const contactInfo = [
    {
      icon: <EmailIcon color="primary" sx={{ fontSize: 40 }} />,
      title: 'Email Us',
      content: '<EMAIL>',
      description: 'Send us an email anytime',
    },
    {
      icon: <PhoneIcon color="primary" sx={{ fontSize: 40 }} />,
      title: 'Call Us',
      content: '+251 11 123 4567',
      description: 'Mon-Fri from 8am to 6pm',
    },
    {
      icon: <LocationIcon color="primary" sx={{ fontSize: 40 }} />,
      title: 'Visit Us',
      content: 'Addis Ababa, Ethiopia',
      description: 'Bole Sub City, Woreda 03',
    },
    {
      icon: <ScheduleIcon color="primary" sx={{ fontSize: 40 }} />,
      title: 'Business Hours',
      content: 'Mon - Fri: 8:00 AM - 6:00 PM',
      description: 'Saturday: 9:00 AM - 2:00 PM',
    },
  ]

  return (
    <Box sx={{ bgcolor: 'background.default', minHeight: '100vh', py: 8 }}>
      <Container maxWidth="lg">
        {/* Header */}
        <Box sx={{ textAlign: 'center', mb: 8 }}>
          <Typography
            variant="h2"
            component="h1"
            sx={{
              fontWeight: 700,
              mb: 2,
              fontSize: { xs: '2.5rem', md: '3.5rem' },
            }}
          >
            Contact Us
          </Typography>
          <Typography
            variant="h5"
            color="text.secondary"
            sx={{ mb: 4, maxWidth: 600, mx: 'auto' }}
          >
            Have questions about Kelem SMS? We'd love to hear from you. Get in touch with our team.
          </Typography>
        </Box>

        {/* Contact Info Cards */}
        <Grid container spacing={4} sx={{ mb: 8 }}>
          {contactInfo.map((info, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card
                sx={{
                  height: '100%',
                  textAlign: 'center',
                  p: 3,
                  transition: 'transform 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: theme.shadows[10],
                  },
                }}
              >
                <CardContent>
                  <Box sx={{ mb: 2 }}>
                    {info.icon}
                  </Box>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                    {info.title}
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500, mb: 1 }}>
                    {info.content}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {info.description}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* Contact Form */}
        <Grid container spacing={6}>
          <Grid item xs={12} md={8}>
            <Paper elevation={3} sx={{ p: 4 }}>
              <Typography variant="h4" sx={{ fontWeight: 600, mb: 3 }}>
                Send us a Message
              </Typography>
              
              {submitted && (
                <Alert severity="success" sx={{ mb: 3 }}>
                  Thank you for your message! We'll get back to you within 24 hours.
                </Alert>
              )}

              <Box component="form" onSubmit={handleSubmit}>
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Full Name"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      required
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Email Address"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      required
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="School Name"
                      value={formData.school}
                      onChange={(e) => handleInputChange('school', e.target.value)}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Subject"
                      value={formData.subject}
                      onChange={(e) => handleInputChange('subject', e.target.value)}
                      required
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Message"
                      multiline
                      rows={6}
                      value={formData.message}
                      onChange={(e) => handleInputChange('message', e.target.value)}
                      required
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Button
                      type="submit"
                      variant="contained"
                      size="large"
                      sx={{
                        py: 1.5,
                        px: 4,
                        fontWeight: 600,
                      }}
                      disabled={submitted}
                    >
                      {submitted ? 'Message Sent!' : 'Send Message'}
                    </Button>
                  </Grid>
                </Grid>
              </Box>
            </Paper>
          </Grid>

          <Grid item xs={12} md={4}>
            <Paper
              elevation={3}
              sx={{
                p: 4,
                background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
                color: 'white',
                height: 'fit-content',
              }}
            >
              <Typography variant="h5" sx={{ fontWeight: 600, mb: 3 }}>
                Why Contact Us?
              </Typography>
              <Box sx={{ mb: 3 }}>
                <Typography variant="body1" sx={{ mb: 2, fontWeight: 500 }}>
                  🚀 Free Demo & Consultation
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Get a personalized demo of Kelem SMS tailored to your school's needs.
                </Typography>
              </Box>
              <Box sx={{ mb: 3 }}>
                <Typography variant="body1" sx={{ mb: 2, fontWeight: 500 }}>
                  💡 Expert Guidance
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Our education technology experts will help you choose the right plan.
                </Typography>
              </Box>
              <Box sx={{ mb: 3 }}>
                <Typography variant="body1" sx={{ mb: 2, fontWeight: 500 }}>
                  🎯 Custom Solutions
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Need something specific? We can customize our platform for your requirements.
                </Typography>
              </Box>
              <Box>
                <Typography variant="body1" sx={{ mb: 2, fontWeight: 500 }}>
                  ⚡ Quick Response
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  We typically respond to all inquiries within 24 hours.
                </Typography>
              </Box>
            </Paper>
          </Grid>
        </Grid>

        {/* FAQ Section */}
        <Box sx={{ mt: 10 }}>
          <Typography
            variant="h3"
            component="h2"
            sx={{
              fontWeight: 700,
              textAlign: 'center',
              mb: 6,
              fontSize: { xs: '2rem', md: '2.5rem' },
            }}
          >
            Quick Answers
          </Typography>
          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                  How quickly can we get started?
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Most schools can be up and running within 1-2 weeks, including data migration and staff training.
                </Typography>
              </Box>
              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                  Do you provide training for our staff?
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Yes! We provide comprehensive training for administrators, teachers, and support staff.
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                  Can you help migrate our existing data?
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Absolutely! Our team will help you migrate student records, grades, and other important data.
                </Typography>
              </Box>
              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                  Is there ongoing technical support?
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Yes, all plans include ongoing technical support via phone, email, and our help center.
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </Container>
    </Box>
  )
}

export default ContactPage
