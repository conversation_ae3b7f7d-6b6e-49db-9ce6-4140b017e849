import React, { useState } from 'react'
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardHeader,
  TextField,
  Button,
  Typography,
  Switch,
  FormControlLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Paper,
} from '@mui/material'
import {
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Home as HomeIcon,
} from '@mui/icons-material'

interface HeroSectionTabProps {
  onSaveSuccess: () => void
  onSaveError: (error: string) => void
}

const HeroSectionTab: React.FC<HeroSectionTabProps> = ({
  onSaveSuccess,
  onSaveError,
}) => {
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [formData, setFormData] = useState({
    title: 'Welcome to Kelem SMS',
    subtitle: 'Student Management System',
    description: 'A comprehensive solution for managing students, courses, and academic activities in Ethiopian schools.',
    primaryButtonText: 'Get Started',
    primaryButtonUrl: '/register',
    secondaryButtonText: 'Learn More',
    secondaryButtonUrl: '/about',
    backgroundType: 'gradient',
    showStats: true,
    showFeatures: true,
  })

  const handleInputChange = (field: string) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      onSaveSuccess()
    } catch (error) {
      onSaveError(error instanceof Error ? error.message : 'Unknown error')
    } finally {
      setSaving(false)
    }
  }

  const handleRefresh = async () => {
    setLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))
    } catch (error) {
      onSaveError('Failed to refresh settings')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto' }}>
      <Grid container spacing={3}>
        {/* Hero Content */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Hero Content"
              subheader="Main content for your homepage"
              avatar={<HomeIcon />}
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Hero Title"
                    value={formData.title}
                    onChange={handleInputChange('title')}
                    helperText="Main headline for your homepage"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Hero Subtitle"
                    value={formData.subtitle}
                    onChange={handleInputChange('subtitle')}
                    helperText="Supporting text under the main title"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Hero Description"
                    multiline
                    rows={3}
                    value={formData.description}
                    onChange={handleInputChange('description')}
                    helperText="Detailed description of your service"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Call-to-Action Buttons */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Call-to-Action Buttons"
              subheader="Action buttons in the hero section"
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Primary Button Text"
                    value={formData.primaryButtonText}
                    onChange={handleInputChange('primaryButtonText')}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Primary Button URL"
                    value={formData.primaryButtonUrl}
                    onChange={handleInputChange('primaryButtonUrl')}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Secondary Button Text"
                    value={formData.secondaryButtonText}
                    onChange={handleInputChange('secondaryButtonText')}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Secondary Button URL"
                    value={formData.secondaryButtonUrl}
                    onChange={handleInputChange('secondaryButtonUrl')}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Background Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Background Settings"
              subheader="Hero section background options"
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <InputLabel>Background Type</InputLabel>
                    <Select
                      value={formData.backgroundType}
                      label="Background Type"
                      onChange={(e) => setFormData(prev => ({ ...prev, backgroundType: e.target.value }))}
                    >
                      <MenuItem value="gradient">Gradient</MenuItem>
                      <MenuItem value="image">Image</MenuItem>
                      <MenuItem value="video">Video</MenuItem>
                      <MenuItem value="solid">Solid Color</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Display Options */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Display Options"
              subheader="What to show in the hero section"
            />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.showStats}
                        onChange={handleInputChange('showStats')}
                      />
                    }
                    label="Show Statistics"
                  />
                  <Typography variant="body2" color="text.secondary">
                    Display key metrics and numbers
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.showFeatures}
                        onChange={handleInputChange('showFeatures')}
                      />
                    }
                    label="Show Features"
                  />
                  <Typography variant="body2" color="text.secondary">
                    Display feature highlights
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Preview */}
        <Grid item xs={12}>
          <Card>
            <CardHeader
              title="Hero Section Preview"
              subheader="How your hero section will look"
            />
            <CardContent>
              <Paper
                sx={{
                  p: 4,
                  background: formData.backgroundType === 'gradient' 
                    ? 'linear-gradient(135deg, #2E7D32 0%, #4CAF50 50%, #FFC107 100%)'
                    : '#f5f5f5',
                  color: formData.backgroundType === 'gradient' ? 'white' : 'inherit',
                  textAlign: 'center',
                  minHeight: 300,
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                }}
              >
                <Typography variant="h3" sx={{ fontWeight: 700, mb: 2 }}>
                  {formData.title || 'Hero Title'}
                </Typography>
                <Typography variant="h5" sx={{ mb: 3, opacity: 0.9 }}>
                  {formData.subtitle || 'Hero Subtitle'}
                </Typography>
                <Typography variant="body1" sx={{ mb: 4, maxWidth: 600, mx: 'auto' }}>
                  {formData.description || 'Hero description will appear here'}
                </Typography>
                <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                  <Button
                    variant="contained"
                    size="large"
                    sx={{
                      bgcolor: formData.backgroundType === 'gradient' ? 'white' : 'primary.main',
                      color: formData.backgroundType === 'gradient' ? 'primary.main' : 'white',
                      '&:hover': { 
                        bgcolor: formData.backgroundType === 'gradient' ? 'grey.100' : 'primary.dark' 
                      },
                    }}
                  >
                    {formData.primaryButtonText || 'Primary Button'}
                  </Button>
                  <Button
                    variant="outlined"
                    size="large"
                    sx={{
                      borderColor: formData.backgroundType === 'gradient' ? 'white' : 'primary.main',
                      color: formData.backgroundType === 'gradient' ? 'white' : 'primary.main',
                      '&:hover': { 
                        borderColor: formData.backgroundType === 'gradient' ? 'white' : 'primary.main',
                        bgcolor: formData.backgroundType === 'gradient' ? 'rgba(255,255,255,0.1)' : 'primary.light'
                      },
                    }}
                  >
                    {formData.secondaryButtonText || 'Secondary Button'}
                  </Button>
                </Box>
              </Paper>
            </CardContent>
          </Card>
        </Grid>

        {/* Action Buttons */}
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
            <Button
              variant="outlined"
              startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}
              onClick={handleRefresh}
              disabled={loading || saving}
            >
              Refresh
            </Button>
            <Button
              variant="contained"
              startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
              onClick={handleSave}
              disabled={loading || saving}
            >
              Save Changes
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  )
}

export default HeroSectionTab
