from django.contrib import admin
from .models import Student, Guardian

class GuardianInline(admin.TabularInline):
    model = Guardian
    extra = 1

@admin.register(Student)
class StudentAdmin(admin.ModelAdmin):
    list_display = ('student_id', 'full_name', 'email', 'current_grade_level', 'enrollment_date', 'status')
    list_filter = ('status', 'gender', 'enrollment_date', 'current_grade_level')
    search_fields = ('first_name', 'last_name', 'middle_name', 'student_id', 'email', 'registration_number')
    inlines = [GuardianInline]
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('Personal Information', {
            'fields': ('first_name', 'middle_name', 'last_name', 'date_of_birth', 'place_of_birth', 'gender',
                      'nationality', 'mother_tongue', 'religion', 'photo')
        }),
        ('Contact Information', {
            'fields': ('email', 'phone_number', 'address', 'city', 'state', 'postal_code', 'country',
                      'emergency_contact_name', 'emergency_contact_phone', 'emergency_contact_relationship')
        }),
        ('Academic Information', {
            'fields': ('student_id', 'registration_number', 'enrollment_date', 'current_grade_level', 'current_section',
                      'previous_school', 'admission_type')
        }),
        ('Health Information', {
            'fields': ('blood_group', 'medical_conditions', 'allergies', 'medications'),
            'classes': ('collapse',)
        }),
        ('Status Information', {
            'fields': ('status', 'is_active', 'notes')
        }),
        ('System Information', {
            'fields': ('user', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(Guardian)
class GuardianAdmin(admin.ModelAdmin):
    list_display = ('full_name', 'relationship', 'student', 'phone_number', 'is_primary_contact')
    list_filter = ('relationship', 'is_primary_contact')
    search_fields = ('first_name', 'last_name', 'student__first_name', 'student__last_name')
