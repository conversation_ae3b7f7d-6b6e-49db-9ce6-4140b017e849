import React, { useState, useCallback, useMemo } from 'react'
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Switch,
  FormControlLabel,
  Grid,
  Chip,
  Avatar,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Tooltip,
  Alert,
} from '@mui/material'
import {
  Person as PersonIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Lock as LockIcon,
  LockOpen as LockOpenIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  AdminPanelSettings as AdminIcon,
  SupervisorAccount as SupervisorIcon,
  AccountCircle as UserIcon,
} from '@mui/icons-material'
import { User, Role, transformUserForForm, transformFormDataToUser, getUserRoleColor } from '../../services/userAccessService'

interface UserManagementProps {
  users: User[]
  roles: Role[]
  onCreateUser: (userData: any) => void
  onUpdateUser: (id: string, userData: any) => void
  onDeleteUser: (id: string) => void
  onToggleUserStatus: (id: string) => void
  onResetPassword: (id: string) => void
}

const UserManagement: React.FC<UserManagementProps> = ({
  users,
  roles,
  onCreateUser,
  onUpdateUser,
  onDeleteUser,
  onToggleUserStatus,
  onResetPassword,
}) => {
  const [dialogOpen, setDialogOpen] = useState(false)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [menuUser, setMenuUser] = useState<User | null>(null)
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    firstName: '',
    lastName: '',
    password: '',
    role: '',
    isActive: true,
    groups: [] as string[],
  })

  const handleOpenDialog = useCallback((user?: User) => {
    if (user) {
      setSelectedUser(user)
      setFormData(transformUserForForm(user))
    } else {
      setSelectedUser(null)
      setFormData({
        username: '',
        email: '',
        firstName: '',
        lastName: '',
        password: '',
        role: '',
        isActive: true,
        groups: [],
      })
    }
    setDialogOpen(true)
  }, [])

  const handleCloseDialog = useCallback(() => {
    setDialogOpen(false)
    setSelectedUser(null)
    setFormData({
      username: '',
      email: '',
      firstName: '',
      lastName: '',
      password: '',
      role: '',
      isActive: true,
      groups: [],
    })
  }, [])

  const handleFormChange = useCallback((field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }))
  }, [])

  const handleSave = useCallback(() => {
    const userData = transformFormDataToUser(formData)
    if (selectedUser) {
      onUpdateUser(selectedUser.id, userData)
    } else {
      onCreateUser(userData)
    }
    handleCloseDialog()
  }, [selectedUser, formData, onCreateUser, onUpdateUser, handleCloseDialog])

  const handleMenuOpen = useCallback((event: React.MouseEvent<HTMLElement>, user: User) => {
    setAnchorEl(event.currentTarget)
    setMenuUser(user)
  }, [])

  const handleMenuClose = useCallback(() => {
    setAnchorEl(null)
    setMenuUser(null)
  }, [])

  const handleMenuAction = useCallback((action: string) => {
    if (!menuUser) return

    switch (action) {
      case 'edit':
        handleOpenDialog(menuUser)
        break
      case 'toggle':
        onToggleUserStatus(menuUser.id)
        break
      case 'reset-password':
        if (window.confirm('Are you sure you want to reset this user\'s password?')) {
          onResetPassword(menuUser.id)
        }
        break
      case 'delete':
        if (window.confirm('Are you sure you want to delete this user?')) {
          onDeleteUser(menuUser.id)
        }
        break
    }
    handleMenuClose()
  }, [menuUser, handleOpenDialog, onToggleUserStatus, onResetPassword, onDeleteUser, handleMenuClose])

  const getRoleIcon = useCallback((roleName: string) => {
    switch (roleName.toLowerCase()) {
      case 'super admin':
        return <AdminIcon sx={{ color: getUserRoleColor(roleName) }} />
      case 'admin':
        return <AdminIcon sx={{ color: getUserRoleColor(roleName) }} />
      case 'teacher':
        return <SupervisorIcon sx={{ color: getUserRoleColor(roleName) }} />
      case 'staff':
        return <PersonIcon sx={{ color: getUserRoleColor(roleName) }} />
      default:
        return <UserIcon sx={{ color: getUserRoleColor(roleName) }} />
    }
  }, [])

  const sortedUsers = useMemo(() => {
    return [...users].sort((a, b) => {
      // Sort by role priority, then by name
      const roleOrder = ['Super Admin', 'Admin', 'Teacher', 'Staff', 'Student']
      const aIndex = roleOrder.indexOf(a.role)
      const bIndex = roleOrder.indexOf(b.role)
      
      if (aIndex !== bIndex) {
        return aIndex - bIndex
      }
      
      return `${a.first_name} ${a.last_name}`.localeCompare(`${b.first_name} ${b.last_name}`)
    })
  }, [users])

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">User Management</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
          sx={{
            background: 'linear-gradient(45deg, #6366f1 30%, #8b5cf6 90%)',
          }}
        >
          Add User
        </Button>
      </Box>

      {/* Users Table */}
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>User</TableCell>
              <TableCell>Role</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Last Login</TableCell>
              <TableCell>Permissions</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {sortedUsers.map((user) => (
              <TableRow key={user.id}>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Avatar sx={{ bgcolor: getUserRoleColor(user.role) }}>
                      {user.first_name.charAt(0)}{user.last_name.charAt(0)}
                    </Avatar>
                    <Box>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        {user.first_name} {user.last_name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        @{user.username}
                      </Typography>
                      <br />
                      <Typography variant="caption" color="text.secondary">
                        {user.email}
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {getRoleIcon(user.role)}
                    <Chip
                      label={user.role}
                      size="small"
                      sx={{
                        bgcolor: getUserRoleColor(user.role),
                        color: 'white',
                        fontWeight: 600,
                      }}
                    />
                  </Box>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Switch
                      checked={user.is_active}
                      onChange={() => onToggleUserStatus(user.id)}
                      size="small"
                      disabled={user.role === 'Super Admin'}
                    />
                    <Typography variant="body2">
                      {user.is_active ? 'Active' : 'Inactive'}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never'}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {user.last_login ? new Date(user.last_login).toLocaleTimeString() : ''}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Box>
                    {user.permissions && user.permissions.includes('all') ? (
                      <Chip label="All Permissions" size="small" color="error" />
                    ) : (
                      <Chip label={`${user.permissions?.length || 0} permissions`} size="small" />
                    )}
                  </Box>
                </TableCell>
                <TableCell>
                  <IconButton
                    size="small"
                    onClick={(e) => handleMenuOpen(e, user)}
                  >
                    <MoreVertIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => handleMenuAction('edit')}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit User</ListItemText>
        </MenuItem>
        <MenuItem 
          onClick={() => handleMenuAction('toggle')}
          disabled={menuUser?.role === 'Super Admin'}
        >
          <ListItemIcon>
            {menuUser?.is_active ? <LockIcon fontSize="small" /> : <LockOpenIcon fontSize="small" />}
          </ListItemIcon>
          <ListItemText>
            {menuUser?.is_active ? 'Deactivate' : 'Activate'} User
          </ListItemText>
        </MenuItem>
        <MenuItem onClick={() => handleMenuAction('reset-password')}>
          <ListItemIcon>
            <LockIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Reset Password</ListItemText>
        </MenuItem>
        <MenuItem 
          onClick={() => handleMenuAction('delete')}
          disabled={menuUser?.role === 'Super Admin'}
          sx={{ color: 'error.main' }}
        >
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>Delete User</ListItemText>
        </MenuItem>
      </Menu>

      {/* User Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {selectedUser ? 'Edit User' : 'Create User'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="First Name"
                  value={formData.firstName}
                  onChange={handleFormChange('firstName')}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Last Name"
                  value={formData.lastName}
                  onChange={handleFormChange('lastName')}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Username"
                  value={formData.username}
                  onChange={handleFormChange('username')}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Email"
                  type="email"
                  value={formData.email}
                  onChange={handleFormChange('email')}
                  required
                />
              </Grid>
              {!selectedUser && (
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Password"
                    type="password"
                    value={formData.password}
                    onChange={handleFormChange('password')}
                    required
                    helperText="Password must be at least 8 characters long"
                  />
                </Grid>
              )}
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  select
                  label="Role"
                  value={formData.role}
                  onChange={handleFormChange('role')}
                  SelectProps={{ native: true }}
                  required
                >
                  <option value="">Select Role</option>
                  {roles.map((role) => (
                    <option key={role.id} value={role.name}>
                      {role.name}
                    </option>
                  ))}
                </TextField>
              </Grid>
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.isActive}
                      onChange={handleFormChange('isActive')}
                    />
                  }
                  label="Active User"
                />
              </Grid>
              {selectedUser?.role === 'Super Admin' && (
                <Grid item xs={12}>
                  <Alert severity="warning">
                    Super Admin users have special privileges and cannot be deactivated or deleted.
                  </Alert>
                </Grid>
              )}
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button
            variant="contained"
            onClick={handleSave}
            disabled={!formData.firstName || !formData.lastName || !formData.username || !formData.email || !formData.role || (!selectedUser && !formData.password)}
            sx={{
              background: 'linear-gradient(45deg, #6366f1 30%, #8b5cf6 90%)',
            }}
          >
            {selectedUser ? 'Update' : 'Create'} User
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default UserManagement
