"use client";
import {
  require_createSvgIcon
} from "./chunk-VKJE3PGV.js";
import "./chunk-5PQ5PKEE.js";
import "./chunk-BBWB4YLS.js";
import "./chunk-NSDJ2KRB.js";
import {
  require_interopRequireDefault
} from "./chunk-DIQD2LQY.js";
import "./chunk-HJS24R7O.js";
import "./chunk-Q7CPF5VB.js";
import "./chunk-K6FDVZ65.js";
import "./chunk-KGFDDYBK.js";
import {
  require_jsx_runtime
} from "./chunk-OT5EQO2H.js";
import "./chunk-OU5AQDZK.js";
import {
  __commonJS
} from "./chunk-EWTE5DHJ.js";

// node_modules/@mui/icons-material/HourglassEmpty.js
var require_HourglassEmpty = __commonJS({
  "node_modules/@mui/icons-material/HourglassEmpty.js"(exports) {
    var _interopRequireDefault = require_interopRequireDefault();
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _createSvgIcon = _interopRequireDefault(require_createSvgIcon());
    var _jsxRuntime = require_jsx_runtime();
    var _default = exports.default = (0, _createSvgIcon.default)((0, _jsxRuntime.jsx)("path", {
      d: "M6 2v6h.01L6 8.01 10 12l-4 4 .01.01H6V22h12v-5.99h-.01L18 16l-4-4 4-3.99-.01-.01H18V2zm10 14.5V20H8v-3.5l4-4zm-4-5-4-4V4h8v3.5z"
    }), "HourglassEmpty");
  }
});
export default require_HourglassEmpty();
//# sourceMappingURL=@mui_icons-material_HourglassEmpty.js.map
