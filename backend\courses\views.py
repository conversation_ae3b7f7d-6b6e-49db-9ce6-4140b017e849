from django.shortcuts import render, get_object_or_404
from django.db.models import Count, Q
from django.utils import timezone
from django.http import HttpResponse

from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from .models import (
    Department, Subject, Course, AcademicTerm, CourseOffering,
    Enrollment, LessonPlan, Resource, Assignment, AssignmentSubmission
)
from .serializers import (
    DepartmentSerializer, SubjectSerializer,
    CourseListSerializer, CourseDetailSerializer,
    AcademicTermSerializer, CourseOfferingListSerializer, CourseOfferingDetailSerializer,
    EnrollmentSerializer, LessonPlanSerializer, ResourceSerializer,
    AssignmentSerializer, AssignmentSubmissionSerializer
)

# Custom permissions
class IsAdminOrTeacher(permissions.BasePermission):
    """Permission to check if the user is an admin or a teacher."""

    def has_permission(self, request, view):
        return request.user.is_authenticated and (
            request.user.is_staff or
            hasattr(request.user, 'teacher_profile')
        )

class IsAdmin(permissions.BasePermission):
    """Permission to check if the user is an admin."""

    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.is_staff

# ViewSets
class DepartmentViewSet(viewsets.ModelViewSet):
    """API endpoint for managing departments."""
    queryset = Department.objects.all()
    serializer_class = DepartmentSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'code']
    ordering_fields = ['name', 'code']
    ordering = ['name']

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [IsAdmin]
        return [permission() for permission in permission_classes]

    @swagger_auto_schema(
        operation_summary="Get department statistics",
        operation_description="Returns statistics about the department including course count, teacher count, etc."
    )
    @action(detail=True, methods=['get'])
    def stats(self, request, pk=None):
        """Get statistics for a department."""
        department = self.get_object()

        # Get course count
        course_count = Course.objects.filter(department=department).count()

        # Get subject count
        subject_count = Subject.objects.filter(department=department).count()

        # Get teacher count (if teacher app is installed)
        teacher_count = 0
        try:
            from teachers.models import Teacher
            teacher_count = Teacher.objects.filter(department=department).count()
        except ImportError:
            pass

        # Get active course offerings
        active_offerings = CourseOffering.objects.filter(
            course__department=department,
            is_active=True
        ).count()

        return Response({
            'course_count': course_count,
            'subject_count': subject_count,
            'teacher_count': teacher_count,
            'active_offerings': active_offerings
        })

class SubjectViewSet(viewsets.ModelViewSet):
    """API endpoint for managing subjects."""
    queryset = Subject.objects.all()
    serializer_class = SubjectSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['department', 'is_active']
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['name', 'code', 'department__name']
    ordering = ['department__name', 'name']

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [IsAdmin]
        return [permission() for permission in permission_classes]

    @swagger_auto_schema(
        operation_summary="Get courses for a subject",
        operation_description="Returns all courses associated with this subject."
    )
    @action(detail=True, methods=['get'])
    def courses(self, request, pk=None):
        """Get all courses for a subject."""
        subject = self.get_object()
        courses = Course.objects.filter(subject=subject)
        serializer = CourseListSerializer(courses, many=True)
        return Response(serializer.data)

class CourseViewSet(viewsets.ModelViewSet):
    """API endpoint for managing courses."""
    queryset = Course.objects.all()
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['department', 'subject', 'level', 'is_active']
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['name', 'code', 'department__name', 'subject__name', 'credits']
    ordering = ['department__name', 'subject__name', 'name']

    def get_serializer_class(self):
        if self.action == 'list':
            return CourseListSerializer
        return CourseDetailSerializer

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [IsAdmin]
        return [permission() for permission in permission_classes]

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @swagger_auto_schema(
        operation_summary="Get course offerings",
        operation_description="Returns all offerings for this course."
    )
    @action(detail=True, methods=['get'])
    def offerings(self, request, pk=None):
        """Get all offerings for a course."""
        course = self.get_object()
        offerings = CourseOffering.objects.filter(course=course)
        serializer = CourseOfferingListSerializer(offerings, many=True)
        return Response(serializer.data)

class AcademicTermViewSet(viewsets.ModelViewSet):
    """API endpoint for managing academic terms."""
    queryset = AcademicTerm.objects.all()
    serializer_class = AcademicTermSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['term', 'year', 'is_active']
    search_fields = ['name', 'description']
    ordering_fields = ['year', 'start_date']
    ordering = ['-year', 'start_date']

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [IsAdmin]
        return [permission() for permission in permission_classes]

    @swagger_auto_schema(
        operation_summary="Get course offerings for a term",
        operation_description="Returns all course offerings for this academic term."
    )
    @action(detail=True, methods=['get'])
    def offerings(self, request, pk=None):
        """Get all course offerings for a term."""
        term = self.get_object()
        offerings = CourseOffering.objects.filter(term=term)
        serializer = CourseOfferingListSerializer(offerings, many=True)
        return Response(serializer.data)

class CourseOfferingViewSet(viewsets.ModelViewSet):
    """API endpoint for managing course offerings."""
    queryset = CourseOffering.objects.all()
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['course', 'term', 'status', 'is_active']
    search_fields = ['course__name', 'course__code', 'section', 'classroom']
    ordering_fields = ['course__name', 'term__start_date', 'start_date']
    ordering = ['-term__year', 'term__start_date', 'course__name']

    def get_serializer_class(self):
        if self.action == 'list':
            return CourseOfferingListSerializer
        return CourseOfferingDetailSerializer

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated]
        elif self.action in ['create', 'update', 'partial_update', 'destroy']:
            permission_classes = [IsAdmin]
        else:
            permission_classes = [IsAdminOrTeacher]
        return [permission() for permission in permission_classes]

    @swagger_auto_schema(
        operation_summary="Get enrollments for a course offering",
        operation_description="Returns all student enrollments for this course offering."
    )
    @action(detail=True, methods=['get'])
    def enrollments(self, request, pk=None):
        """Get all enrollments for a course offering."""
        offering = self.get_object()
        enrollments = Enrollment.objects.filter(course_offering=offering)
        serializer = EnrollmentSerializer(enrollments, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        operation_summary="Get lesson plans for a course offering",
        operation_description="Returns all lesson plans for this course offering."
    )
    @action(detail=True, methods=['get'])
    def lesson_plans(self, request, pk=None):
        """Get all lesson plans for a course offering."""
        offering = self.get_object()
        lesson_plans = LessonPlan.objects.filter(course_offering=offering)
        serializer = LessonPlanSerializer(lesson_plans, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        operation_summary="Get resources for a course offering",
        operation_description="Returns all resources for this course offering."
    )
    @action(detail=True, methods=['get'])
    def resources(self, request, pk=None):
        """Get all resources for a course offering."""
        offering = self.get_object()
        resources = Resource.objects.filter(course_offering=offering)
        serializer = ResourceSerializer(resources, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        operation_summary="Get assignments for a course offering",
        operation_description="Returns all assignments for this course offering."
    )
    @action(detail=True, methods=['get'])
    def assignments(self, request, pk=None):
        """Get all assignments for a course offering."""
        offering = self.get_object()
        assignments = Assignment.objects.filter(course_offering=offering)
        serializer = AssignmentSerializer(assignments, many=True)
        return Response(serializer.data)

class EnrollmentViewSet(viewsets.ModelViewSet):
    """API endpoint for managing student enrollments."""
    queryset = Enrollment.objects.all()
    serializer_class = EnrollmentSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['course_offering', 'student', 'status']
    search_fields = ['student__first_name', 'student__last_name', 'student__student_id']
    ordering_fields = ['enrollment_date', 'student__last_name']
    ordering = ['-enrollment_date']

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [IsAdminOrTeacher]
        return [permission() for permission in permission_classes]

    def perform_create(self, serializer):
        enrollment = serializer.save()

        # Update the current_students count for the course offering
        offering = enrollment.course_offering
        offering.current_students = Enrollment.objects.filter(
            course_offering=offering,
            status__in=['ENROLLED', 'ATTENDING']
        ).count()
        offering.save()

class LessonPlanViewSet(viewsets.ModelViewSet):
    """API endpoint for managing lesson plans."""
    queryset = LessonPlan.objects.all()
    serializer_class = LessonPlanSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['course_offering', 'status']
    search_fields = ['title', 'description']
    ordering_fields = ['lesson_date', 'title']
    ordering = ['lesson_date']

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [IsAdminOrTeacher]
        return [permission() for permission in permission_classes]

class ResourceViewSet(viewsets.ModelViewSet):
    """API endpoint for managing course resources."""
    queryset = Resource.objects.all()
    serializer_class = ResourceSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['course_offering', 'resource_type']
    search_fields = ['title', 'description']
    ordering_fields = ['title', 'created_at']
    ordering = ['-created_at']

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [IsAdminOrTeacher]
        return [permission() for permission in permission_classes]

class AssignmentViewSet(viewsets.ModelViewSet):
    """API endpoint for managing assignments."""
    queryset = Assignment.objects.all()
    serializer_class = AssignmentSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['course_offering', 'assignment_type']
    search_fields = ['title', 'description']
    ordering_fields = ['due_date', 'title']
    ordering = ['due_date']

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [IsAdminOrTeacher]
        return [permission() for permission in permission_classes]

    @swagger_auto_schema(
        operation_summary="Get submissions for an assignment",
        operation_description="Returns all student submissions for this assignment."
    )
    @action(detail=True, methods=['get'])
    def submissions(self, request, pk=None):
        """Get all submissions for an assignment."""
        assignment = self.get_object()
        submissions = AssignmentSubmission.objects.filter(assignment=assignment)
        serializer = AssignmentSubmissionSerializer(submissions, many=True)
        return Response(serializer.data)

class AssignmentSubmissionViewSet(viewsets.ModelViewSet):
    """API endpoint for managing assignment submissions."""
    queryset = AssignmentSubmission.objects.all()
    serializer_class = AssignmentSubmissionSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['assignment', 'student', 'status']
    search_fields = ['student__first_name', 'student__last_name', 'student__student_id']
    ordering_fields = ['submitted_date', 'student__last_name']
    ordering = ['-submitted_date']

    def get_permissions(self):
        if self.action in ['create']:
            permission_classes = [permissions.IsAuthenticated]
        elif self.action in ['list', 'retrieve']:
            permission_classes = [IsAdminOrTeacher]
        else:
            permission_classes = [IsAdminOrTeacher]
        return [permission() for permission in permission_classes]


# Template views
def course_dashboard(request):
    """Render the course dashboard template."""
    if not request.user.is_authenticated:
        return HttpResponse("Please log in to access this page", status=401)

    # Get counts for dashboard
    department_count = Department.objects.count()
    subject_count = Subject.objects.count()
    course_count = Course.objects.count()
    active_term_count = AcademicTerm.objects.filter(is_active=True).count()
    active_offering_count = CourseOffering.objects.filter(is_active=True).count()

    # Get recent course offerings
    recent_offerings = CourseOffering.objects.all().order_by('-created_at')[:5]

    # Get departments with most courses
    departments_with_courses = Department.objects.annotate(
        course_count=Count('courses')
    ).order_by('-course_count')[:5]

    context = {
        'department_count': department_count,
        'subject_count': subject_count,
        'course_count': course_count,
        'active_term_count': active_term_count,
        'active_offering_count': active_offering_count,
        'recent_offerings': recent_offerings,
        'departments_with_courses': departments_with_courses
    }

    return render(request, 'courses/dashboard.html', context)
