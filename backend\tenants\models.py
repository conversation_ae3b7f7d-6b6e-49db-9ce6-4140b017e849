from django.db import models
from django_tenants.models import TenantMixin, DomainMixin

class School(TenantMixin):
    name = models.CharField(max_length=100)
    address = models.TextField(blank=True, null=True)
    contact_email = models.EmailField(blank=True, null=True)
    contact_phone = models.CharField(max_length=20, blank=True, null=True)
    created_on = models.DateField(auto_now_add=True)
    is_archived = models.BooleanField(default=False, help_text="Whether this tenant is archived")
    archived_on = models.DateTimeField(null=True, blank=True, help_text="When this tenant was archived")
    archive_reason = models.TextField(blank=True, null=True, help_text="Reason for archiving this tenant")

    # Default true, schema will be automatically created and synced when it is saved
    auto_create_schema = True
    # Set to True to automatically drop the schema when the tenant is deleted
    auto_drop_schema = True

    def delete(self, force_drop=True, *args, **kwargs):
        """Override delete method to ensure schema is dropped."""
        schema_name = self.schema_name
        print(f"Deleting tenant {self.name} with schema {schema_name}")

        # Call the parent delete method to remove the tenant record
        result = super().delete(*args, **kwargs)

        # Now manually drop the schema if it still exists
        if force_drop:
            try:
                from django.db import connection

                # List schemas before deletion
                cursor = connection.cursor()
                cursor.execute("SELECT schema_name FROM information_schema.schemata;")
                schemas_before = [row[0] for row in cursor.fetchall()]
                print(f"Schemas before deletion: {schemas_before}")

                if schema_name in schemas_before:
                    print(f"Dropping schema {schema_name}")
                    # Force drop the schema with CASCADE
                    cursor.execute(f'DROP SCHEMA IF EXISTS "{schema_name}" CASCADE;')
                    connection.commit()

                    # Verify schema was dropped
                    cursor.execute("SELECT schema_name FROM information_schema.schemata;")
                    schemas_after = [row[0] for row in cursor.fetchall()]
                    print(f"Schemas after deletion: {schemas_after}")

                    if schema_name in schemas_after:
                        print(f"WARNING: Schema {schema_name} still exists after deletion")
                    else:
                        print(f"Schema {schema_name} successfully dropped")
                else:
                    print(f"Schema {schema_name} not found in database")
            except Exception as e:
                import traceback
                print(f"Error dropping schema: {str(e)}")
                print(traceback.format_exc())

        return result

    def __str__(self):
        return self.name

class Domain(DomainMixin):
    pass
