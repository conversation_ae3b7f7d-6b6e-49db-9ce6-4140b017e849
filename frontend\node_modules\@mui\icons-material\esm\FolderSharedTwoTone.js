"use client";

import createSvgIcon from './utils/createSvgIcon';
import { jsx as _jsx } from "react/jsx-runtime";
export default createSvgIcon([/*#__PURE__*/_jsx("path", {
  d: "m11.17 8-.59-.59L9.17 6H4v12h16V8zM19 16v1h-8v-1c0-1.33 2.67-2 4-2s4 .67 4 2m-4-7c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2",
  opacity: ".3"
}, "0"), /*#__PURE__*/_jsx("path", {
  d: "M20 6h-8l-2-2H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2m0 12H4V6h5.17l2 2H20zm-5-5c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2m-4 3v1h8v-1c0-1.33-2.67-2-4-2s-4 .67-4 2"
}, "1")], 'FolderSharedTwoTone');